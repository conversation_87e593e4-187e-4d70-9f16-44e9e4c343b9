"""
Logging middleware for FastAPI application.
Provides structured logging for API requests and responses.
"""

import logging
import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse
import json

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    def __init__(self, app, logger_name: str = "api"):
        super().__init__(app)
        self.logger = logging.getLogger(logger_name)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Start timing
        start_time = time.time()
        
        # Log request
        await self._log_request(request, request_id)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            await self._log_response(request, response, request_id, process_time)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            
            # Log error
            self.logger.error(
                "Request failed",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "process_time": process_time,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
            raise
    
    async def _log_request(self, request: Request, request_id: str):
        """Log incoming request details."""
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Get user agent
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Log request
        self.logger.info(
            "Incoming request",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "client_ip": client_ip,
                "user_agent": user_agent,
                "headers": dict(request.headers)
            }
        )
    
    async def _log_response(self, request: Request, response: Response, request_id: str, process_time: float):
        """Log response details."""
        # Determine response size
        response_size = 0
        if hasattr(response, 'body') and response.body:
            response_size = len(response.body)
        elif isinstance(response, StreamingResponse):
            response_size = -1  # Unknown for streaming responses
        
        # Log response
        self.logger.info(
            "Request completed",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "status_code": response.status_code,
                "process_time": process_time,
                "response_size": response_size,
                "response_headers": dict(response.headers)
            }
        )


def setup_logging():
    """Configure application logging."""
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Create file handler for API logs
    try:
        import os
        os.makedirs("logs", exist_ok=True)
        
        file_handler = logging.FileHandler("logs/api.log")
        file_handler.setFormatter(formatter)
        
        api_logger = logging.getLogger("api")
        api_logger.addHandler(file_handler)
        api_logger.setLevel(logging.INFO)
        
    except Exception as e:
        logger.warning(f"Could not create file handler: {e}")


# Initialize logging on import
setup_logging()
