#!/usr/bin/env python3
"""
Test script for the upload functionality.
Creates sample data and tests the upload API endpoints.
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
import io

def create_sample_data():
    """Create sample financial data for testing."""
    # Generate sample stock data
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    
    data = []
    price = 100.0
    
    for date in dates:
        # Simple random walk for price
        import random
        change = random.uniform(-0.05, 0.05)
        price = max(price * (1 + change), 10.0)  # Minimum price of $10
        
        volume = random.randint(100000, 2000000)
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'open': round(price * random.uniform(0.98, 1.02), 2),
            'high': round(price * random.uniform(1.00, 1.05), 2),
            'low': round(price * random.uniform(0.95, 1.00), 2),
            'close': round(price, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_upload_api():
    """Test the upload API endpoints."""
    print("🧪 Testing Upload API Endpoints")
    print("=" * 50)
    
    # Create sample data
    print("📊 Creating sample financial data...")
    df = create_sample_data()
    print(f"✅ Created {len(df)} rows of sample data")
    
    # Convert to CSV
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_content = csv_buffer.getvalue()
    
    # Test 1: Upload file
    print("\n🚀 Testing file upload...")
    
    files = {
        'file': ('sample_stock_data.csv', csv_content, 'text/csv')
    }
    
    data = {
        'data_type': 'financial',
        'table_name': 'test_stock_data',
        'description': 'Sample stock data for testing upload functionality',
        'overwrite_existing': True,
        'validate_data': True
    }
    
    try:
        response = requests.post('http://localhost:8000/upload/', files=files, data=data, timeout=30)
        
        if response.status_code == 201:
            upload_result = response.json()
            upload_id = upload_result['upload_id']
            print(f"✅ Upload successful! Upload ID: {upload_id}")
            print(f"   Status: {upload_result['status']}")
            print(f"   Message: {upload_result['message']}")
            print(f"   Table: {upload_result['table_name']}")
            
            # Test 2: Check upload status
            print(f"\n📋 Checking upload status...")
            
            max_attempts = 30
            attempt = 0
            
            while attempt < max_attempts:
                status_response = requests.get(f'http://localhost:8000/upload/{upload_id}/status', timeout=10)
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"   Status: {status_data['status']} ({status_data['progress']:.1f}%)")
                    print(f"   Message: {status_data['message']}")
                    
                    if status_data['status'] in ['completed', 'failed', 'cancelled']:
                        break
                
                time.sleep(2)
                attempt += 1
            
            if status_data['status'] == 'completed':
                print("✅ Upload completed successfully!")
            else:
                print(f"⚠️ Upload ended with status: {status_data['status']}")
            
            # Test 3: List uploads
            print(f"\n📝 Testing upload list...")
            list_response = requests.get('http://localhost:8000/upload/', timeout=10)
            
            if list_response.status_code == 200:
                list_data = list_response.json()
                print(f"✅ Found {list_data['total']} uploads")
                for upload in list_data['uploads'][:3]:  # Show first 3
                    print(f"   - {upload['upload_id'][:8]}... ({upload['status']})")
            else:
                print(f"❌ Failed to list uploads: {list_response.status_code}")
            
            # Test 4: Preview data (if available)
            print(f"\n👁️ Testing data preview...")
            try:
                preview_response = requests.get(f'http://localhost:8000/upload/{upload_id}/preview?rows=5', timeout=10)
                
                if preview_response.status_code == 200:
                    preview_data = preview_response.json()
                    print(f"✅ Preview successful!")
                    print(f"   Columns: {len(preview_data['columns'])}")
                    print(f"   Sample rows: {len(preview_data['sample_data'])}")
                    print(f"   Total rows: {preview_data['total_rows']}")
                else:
                    print(f"⚠️ Preview not available: {preview_response.status_code}")
            except Exception as e:
                print(f"⚠️ Preview error: {e}")
            
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed: {e}")

def test_health_check():
    """Test the health check endpoint."""
    print("\n🏥 Testing Health Check...")
    
    try:
        response = requests.get('http://localhost:8000/health/', timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print("✅ API server is healthy!")
            print(f"   Status: {health_data.get('status', 'unknown')}")
        else:
            print(f"⚠️ Health check returned: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server")
    except Exception as e:
        print(f"❌ Health check failed: {e}")

if __name__ == "__main__":
    print("🧪 Financial Analysis System - Upload Test Suite")
    print("=" * 60)
    
    # Test health first
    test_health_check()
    
    # Test upload functionality
    test_upload_api()
    
    print("\n" + "=" * 60)
    print("🎉 Test suite completed!")
