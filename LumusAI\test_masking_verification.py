#!/usr/bin/env python3
"""
Test script to verify that masking is working in Langfuse traces.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_masking_verification():
    """Test that sensitive data is actually masked in Langfuse traces."""
    
    print("🔒 Testing Masking Verification")
    print("=" * 60)
    
    # Test data with VERY obvious sensitive information
    test_data = """
    <PERSON>
    Senior AI Developer
    
    PERSONAL INFORMATION:
    Email: <EMAIL>
    Phone: (57) 3157357677
    LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
    Address: Cali, Valle Del Cauca, Colombia
    
    SENSITIVE DATA:
    Credit Card: 4111 1111 1111 1111
    SECRET_API_KEY: SECRET_SANTIAGO_123
    CONFIDENTIAL_TOKEN: CONF_TOKEN_XYZ789
    
    WORK EXPERIENCE:
    - AI Developer at TechCorp SA (2022-2024)
      Email: <EMAIL>
      Phone: +57 1 234 5678
      
    - Research Assistant at Universidad Nacional (2020-2022)
      Contact: <EMAIL>
      Mobile: 3157357677
    
    EDUCATION:
    - Master's in AI, Universidad Nacional de Colombia
      Student ID: 2019123456
      Email: <EMAIL>
    
    PROJECTS:
    - Financial AI System for ACME Corporation LTDA
      Client contact: <EMAIL>
      Project phone: (57) 2 987 6543
    
    REFERENCES:
    - Dr. Carlos Pérez López
      Email: <EMAIL>
      Phone: +57 1 555 0123
    """
    
    print("📞 Testing /process endpoint with HIGHLY sensitive data...")
    print("🔍 Data contains multiple instances of:")
    print("   - Names: Santiago García Martínez, Carlos Pérez López, etc.")
    print("   - Emails: <EMAIL>, <EMAIL>, etc.")
    print("   - Phones: (57) 3157357677, +57 1 234 5678, etc.")
    print("   - LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/")
    print("   - Companies: TechCorp SA, ACME Corporation LTDA")
    print("   - Credit Card: 4111 1111 1111 1111")
    print("   - Secrets: SECRET_SANTIAGO_123, CONF_TOKEN_XYZ789")
    
    try:
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "cv",
                "data": test_data
            },
            timeout=60
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # The API response should contain the actual data (not masked)
            # But Langfuse traces should be masked
            if isinstance(result, dict):
                print("\n📄 API Response Analysis:")
                response_str = json.dumps(result, indent=2)
                
                # Check if API response contains actual data (it should)
                api_data_found = []
                if 'santiago' in response_str.lower():
                    api_data_found.append("Names")
                if '<EMAIL>' in response_str.lower():
                    api_data_found.append("Emails")
                if '3157357677' in response_str:
                    api_data_found.append("Phone numbers")
                if 'linkedin.com/in/santiago-garcia-m' in response_str.lower():
                    api_data_found.append("LinkedIn profile")
                
                if api_data_found:
                    print(f"✅ API response contains actual data: {', '.join(api_data_found)}")
                    print("   (This is correct - API responses should have real data)")
                else:
                    print("⚠️  API response seems to be missing expected data")
                
                # Show key extracted information
                if 'personal_information' in result:
                    personal = result['personal_information']
                    print(f"\n📊 Extracted Personal Information:")
                    print(f"   - Name: {personal.get('full_name', 'N/A')}")
                    print(f"   - Email: {personal.get('email', 'N/A')}")
                    print(f"   - Phone: {personal.get('phone_number', 'N/A')}")
                    print(f"   - LinkedIn: {personal.get('linkedin_profile', 'N/A')}")
                    print(f"   - City: {personal.get('city', 'N/A')}")
                
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"\n💰 Token Usage: {tokens.get('total_tokens', 'N/A')} tokens, ${tokens.get('cost', 0):.4f}")
            
            print("\n🔍 IMPORTANT: Check Langfuse Dashboard Now!")
            print("=" * 50)
            print("URL: http://157.230.167.30:3000")
            print("\nIn the Langfuse traces, you should see:")
            print("✅ Names replaced with: [REDACTED_NAME]")
            print("✅ Emails replaced with: [REDACTED_EMAIL]")
            print("✅ Phones replaced with: [REDACTED_PHONE]")
            print("✅ LinkedIn replaced with: [REDACTED_LINKEDIN]")
            print("✅ Companies replaced with: [REDACTED_COMPANY]")
            print("✅ Credit cards replaced with: [REDACTED_CREDIT_CARD]")
            print("✅ Secrets replaced with: [REDACTED_SECRET]")
            print("✅ Addresses replaced with: [REDACTED_ADDRESS]")
            
            print("\n❌ If you still see actual sensitive data in Langfuse:")
            print("- Names like 'Santiago García Martínez'")
            print("- Emails like '<EMAIL>'")
            print("- Phone numbers like '(57) 3157357677'")
            print("- Then masking is NOT working correctly")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🔒 Masking Verification Test")
    print("=" * 40)
    
    # Check service health
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Run the test
    success = test_masking_verification()
    
    print("\n" + "=" * 60)
    print("🎯 Masking Verification Summary")
    print("=" * 60)
    
    if success:
        print("✅ Test completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Check application logs for masking messages:")
        print("   - '🔒 Applied masking to LLM prompts'")
        print("   - '🔒 Applied masking to chain inputs'")
        print("   - '🔒 Applied masking to chain outputs'")
        
        print("\n2. Check Langfuse dashboard immediately:")
        print("   - URL: http://157.230.167.30:3000")
        print("   - Look for the most recent trace")
        print("   - Verify sensitive data is masked with [REDACTED_*] tags")
        
        print("\n3. Expected Results:")
        print("   ✅ API responses contain real data (for user)")
        print("   ✅ Langfuse traces contain masked data (for privacy)")
        print("   ✅ No sensitive information visible in traces")
        
    else:
        print("❌ Test failed")
        print("\n🔧 Troubleshooting:")
        print("1. Check if masking is enabled in environment variables")
        print("2. Verify application startup shows masking enabled")
        print("3. Check for masking-related error messages in logs")
    
    print("\n🔍 Remember:")
    print("- API responses SHOULD contain real data")
    print("- Langfuse traces SHOULD contain masked data")
    print("- This protects privacy while maintaining functionality")

if __name__ == "__main__":
    main()
