# This file provides a prompt template for evaluating a candidate against a position.
# The LLM must return J<PERSON><PERSON> with the following fields:
# {
#   "LLM_Analysis": {...},        # <PERSON>SO<PERSON> with summary or reasoning
#   "extra_questions": {...},     # <PERSON>SO<PERSON> with additional clarifications or interview questions suggested
#   "highlights": {...},          # JSON with key points or standout features of the candidate for this position
#   "Score": float,               # The final matching score (0 to 10)
# }
#
# Do not include "created_at" or "updated_at" in the LLM output; these are handled by code.
# 

# Import prompts from langsmith - DISABLED to remove cap language
# Using local prompts instead of LangSmith to ensure no cap language

# from langsmith import Client

class TemplatesAnalysis:
    def __init__(self):
        # Use local prompts instead of pulling from LangSmith
        self.batch_prompt = self._get_local_batch_prompt()
        self.match_prompt = self._get_local_match_prompt()

    def update_prompts(self):
        # Override to use local prompts instead of LangSmith
        self.batch_prompt = self._get_local_batch_prompt()
        self.match_prompt = self._get_local_match_prompt()

    def _get_local_match_prompt(self):
        return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential
   - **Related role types**: Medium compatibility (e.g., Developer → DevOps, QA → Test Automation)
   - **Different role types**: Low compatibility due to fundamental role differences (e.g., Developer → QA, Frontend → Backend)

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**COMPATIBILITY CALCULATION RULES:**
- Final Compatibility = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- **ROLE TYPE COMPATIBILITY ASSESSMENT:**
  * Major role mismatch (e.g., Developer vs QA): Low compatibility due to fundamental role differences
  * Moderate role mismatch (e.g., Frontend vs Backend): Moderate compatibility with transferable skills
  * Minor role mismatch (e.g., Junior vs Senior same role): High compatibility with experience considerations
  * Same role type: Full compatibility potential based on technology and experience alignment

**ANALYSIS REQUIREMENTS:**
1. Percentage of compatibility with the position (applying the role type compatibility rules above)
2. Recommendation: Whether the candidate should move forward (consider both role fit and technology alignment)
3. Matches Found: Points where candidate meets requirements (distinguish between role-relevant and technology matches)
4. Missing: Key requirements not evident in candidate's background (prioritize role function gaps over technology gaps)

Return the analysis in strict JSON format according to the PositionCandidateAnalysis model."""

    def _get_local_batch_prompt(self):
        return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and multiple candidates' CVs using a two-factor assessment approach.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: Identify the core job function/role type from both the position and each candidate's background.
   - **Same role type**: High compatibility potential
   - **Related role types**: Medium compatibility with transferable skills
   - **Different role types**: Low compatibility due to fundamental role differences

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**COMPATIBILITY CALCULATION:**
- Final Compatibility = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- Focus on role alignment as the primary factor in compatibility assessment

**ANALYSIS REQUIREMENTS:**
For each candidate, provide:
1. Percentage of compatibility with the position
2. Recommendation: Whether the candidate should move forward
3. Matches Found: Points where candidate meets requirements
4. Missing: Key requirements not evident in candidate's background

Return the analysis in strict JSON format according to the BatchMatchAnalysis model."""

templatesObject = TemplatesAnalysis()


def get_candidate_analysis_prompt():
    # Use local prompt from templatesObject to ensure consistency
    return templatesObject.match_prompt



def get_batch_candidate_analysis_batch_prompt():
    # Use local prompt from templatesObject to ensure consistency
    return templatesObject.batch_prompt
