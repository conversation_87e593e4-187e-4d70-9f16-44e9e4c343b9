"""
Dashboard generator for automated creation of interactive financial dashboards.
Creates dynamic visualizations and layouts based on data analysis results.
"""

import logging
from typing import Dict, Any, List, Optional, Union
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import streamlit as st
from datetime import datetime, timedelta
import json
from dataclasses import dataclass, asdict
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChartType(Enum):
    """Enumeration of available chart types."""
    LINE = "line"
    BAR = "bar"
    SCATTER = "scatter"
    HEATMAP = "heatmap"
    CANDLESTICK = "candlestick"
    HISTOGRAM = "histogram"
    BOX = "box"
    AREA = "area"
    PIE = "pie"

class LayoutType(Enum):
    """Enumeration of dashboard layout types."""
    SINGLE_CHART = "single_chart"
    TWO_COLUMN = "two_column"
    THREE_COLUMN = "three_column"
    GRID_2X2 = "grid_2x2"
    CUSTOM = "custom"

@dataclass
class ChartConfig:
    """Configuration for individual charts."""
    chart_id: str
    chart_type: ChartType
    title: str
    data_source: str
    x_column: str
    y_column: str
    color_column: Optional[str] = None
    size_column: Optional[str] = None
    additional_params: Dict[str, Any] = None
    position: Dict[str, int] = None  # row, col for grid layouts

@dataclass
class DashboardConfig:
    """Configuration for complete dashboard."""
    dashboard_id: str
    title: str
    description: str
    layout_type: LayoutType
    charts: List[ChartConfig]
    theme: str = "plotly_white"
    auto_refresh: bool = False
    refresh_interval: int = 300  # seconds
    created_at: datetime = None
    updated_at: datetime = None

class GeneradorDashboard:
    """Dashboard generator with intelligent chart selection and layout."""
    
    def __init__(self):
        """Initialize dashboard generator."""
        self.dashboard_templates = self._load_dashboard_templates()
        self.chart_generators = {
            ChartType.LINE: self._create_line_chart,
            ChartType.BAR: self._create_bar_chart,
            ChartType.SCATTER: self._create_scatter_chart,
            ChartType.HEATMAP: self._create_heatmap,
            ChartType.CANDLESTICK: self._create_candlestick_chart,
            ChartType.HISTOGRAM: self._create_histogram,
            ChartType.BOX: self._create_box_plot,
            ChartType.AREA: self._create_area_chart,
            ChartType.PIE: self._create_pie_chart
        }
    
    def _load_dashboard_templates(self) -> Dict[str, DashboardConfig]:
        """Load predefined dashboard templates."""
        templates = {}
        
        # Financial Overview Template
        templates["financial_overview"] = DashboardConfig(
            dashboard_id="financial_overview",
            title="Financial Data Overview",
            description="Comprehensive financial data analysis dashboard",
            layout_type=LayoutType.GRID_2X2,
            charts=[
                ChartConfig(
                    chart_id="price_trend",
                    chart_type=ChartType.LINE,
                    title="Price Trend",
                    data_source="financial_data",
                    x_column="date",
                    y_column="close",
                    position={"row": 1, "col": 1}
                ),
                ChartConfig(
                    chart_id="volume_analysis",
                    chart_type=ChartType.BAR,
                    title="Volume Analysis",
                    data_source="financial_data",
                    x_column="date",
                    y_column="volume",
                    position={"row": 1, "col": 2}
                ),
                ChartConfig(
                    chart_id="price_distribution",
                    chart_type=ChartType.HISTOGRAM,
                    title="Price Distribution",
                    data_source="financial_data",
                    x_column="close",
                    y_column="frequency",
                    position={"row": 2, "col": 1}
                ),
                ChartConfig(
                    chart_id="correlation_matrix",
                    chart_type=ChartType.HEATMAP,
                    title="Correlation Matrix",
                    data_source="correlation_data",
                    x_column="variable1",
                    y_column="variable2",
                    position={"row": 2, "col": 2}
                )
            ]
        )
        
        # SQL Analysis Template
        templates["sql_analysis"] = DashboardConfig(
            dashboard_id="sql_analysis",
            title="Database Analysis Dashboard",
            description="Interactive dashboard for SQL query results",
            layout_type=LayoutType.TWO_COLUMN,
            charts=[
                ChartConfig(
                    chart_id="query_results",
                    chart_type=ChartType.BAR,
                    title="Query Results",
                    data_source="sql_results",
                    x_column="category",
                    y_column="value"
                ),
                ChartConfig(
                    chart_id="data_trends",
                    chart_type=ChartType.LINE,
                    title="Data Trends",
                    data_source="sql_results",
                    x_column="date",
                    y_column="metric"
                )
            ]
        )
        
        return templates
    
    def generar_dashboard_automatico(self, datos: Dict[str, pd.DataFrame], 
                                   tipo_analisis: str = "financial") -> Dict[str, Any]:
        """Generate dashboard automatically based on data analysis."""
        try:
            logger.info(f"Generating automatic dashboard for {tipo_analisis} analysis")
            
            # Select appropriate template
            template_name = self._select_template(datos, tipo_analisis)
            template = self.dashboard_templates.get(template_name)
            
            if not template:
                # Create custom dashboard
                template = self._create_custom_dashboard(datos, tipo_analisis)
            
            # Generate charts based on available data
            charts_generated = []
            for chart_config in template.charts:
                try:
                    chart = self._generate_chart(chart_config, datos)
                    if chart:
                        charts_generated.append({
                            "chart_id": chart_config.chart_id,
                            "chart": chart,
                            "config": asdict(chart_config)
                        })
                except Exception as e:
                    logger.warning(f"Failed to generate chart {chart_config.chart_id}: {e}")
            
            # Create dashboard layout
            dashboard_layout = self._create_dashboard_layout(template, charts_generated)
            
            return {
                "success": True,
                "dashboard_id": template.dashboard_id,
                "title": template.title,
                "description": template.description,
                "layout": dashboard_layout,
                "charts": charts_generated,
                "template_used": template_name,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating automatic dashboard: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _select_template(self, datos: Dict[str, pd.DataFrame], tipo_analisis: str) -> str:
        """Select appropriate dashboard template based on data and analysis type."""
        if tipo_analisis == "financial" and any("financial" in key.lower() for key in datos.keys()):
            return "financial_overview"
        elif any("sql" in key.lower() for key in datos.keys()):
            return "sql_analysis"
        else:
            return "financial_overview"  # Default template
    
    def _create_custom_dashboard(self, datos: Dict[str, pd.DataFrame], tipo_analisis: str) -> DashboardConfig:
        """Create custom dashboard configuration based on available data."""
        charts = []
        chart_id_counter = 1
        
        for data_key, df in datos.items():
            if df.empty:
                continue
            
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
            
            # Add time series chart if datetime column exists
            if datetime_cols and numeric_cols:
                charts.append(ChartConfig(
                    chart_id=f"timeseries_{chart_id_counter}",
                    chart_type=ChartType.LINE,
                    title=f"Time Series - {data_key}",
                    data_source=data_key,
                    x_column=datetime_cols[0],
                    y_column=numeric_cols[0]
                ))
                chart_id_counter += 1
            
            # Add distribution chart for numeric columns
            if numeric_cols:
                charts.append(ChartConfig(
                    chart_id=f"distribution_{chart_id_counter}",
                    chart_type=ChartType.HISTOGRAM,
                    title=f"Distribution - {numeric_cols[0]}",
                    data_source=data_key,
                    x_column=numeric_cols[0],
                    y_column="frequency"
                ))
                chart_id_counter += 1
        
        return DashboardConfig(
            dashboard_id="custom_dashboard",
            title="Custom Analysis Dashboard",
            description="Automatically generated dashboard based on available data",
            layout_type=LayoutType.TWO_COLUMN,
            charts=charts[:4]  # Limit to 4 charts
        )
    
    def _generate_chart(self, chart_config: ChartConfig, datos: Dict[str, pd.DataFrame]) -> Optional[str]:
        """Generate individual chart based on configuration."""
        try:
            # Get data source
            df = datos.get(chart_config.data_source)
            if df is None or df.empty:
                logger.warning(f"Data source {chart_config.data_source} not found or empty")
                return None
            
            # Check if required columns exist
            required_cols = [chart_config.x_column, chart_config.y_column]
            available_cols = df.columns.tolist()
            
            # Try to find similar column names if exact match not found
            for i, col in enumerate(required_cols):
                if col not in available_cols:
                    # Find similar column names
                    similar_cols = [c for c in available_cols if col.lower() in c.lower() or c.lower() in col.lower()]
                    if similar_cols:
                        required_cols[i] = similar_cols[0]
                    else:
                        # Use first available column of appropriate type
                        if i == 0:  # x_column
                            datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
                            if datetime_cols:
                                required_cols[i] = datetime_cols[0]
                            else:
                                required_cols[i] = available_cols[0]
                        else:  # y_column
                            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
                            if numeric_cols:
                                required_cols[i] = numeric_cols[0]
                            else:
                                required_cols[i] = available_cols[1] if len(available_cols) > 1 else available_cols[0]
            
            chart_config.x_column, chart_config.y_column = required_cols
            
            # Generate chart using appropriate generator
            chart_generator = self.chart_generators.get(chart_config.chart_type)
            if chart_generator:
                fig = chart_generator(df, chart_config)
                return fig.to_json() if fig else None
            else:
                logger.warning(f"Chart generator for {chart_config.chart_type} not found")
                return None
                
        except Exception as e:
            logger.error(f"Error generating chart {chart_config.chart_id}: {e}")
            return None
    
    def _create_line_chart(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create line chart."""
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=df[config.x_column],
            y=df[config.y_column],
            mode='lines+markers',
            name=config.y_column,
            line=dict(width=2)
        ))
        
        fig.update_layout(
            title=config.title,
            xaxis_title=config.x_column,
            yaxis_title=config.y_column,
            template="plotly_white",
            hovermode='x unified'
        )
        
        return fig
    
    def _create_bar_chart(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create bar chart."""
        fig = go.Figure()
        fig.add_trace(go.Bar(
            x=df[config.x_column],
            y=df[config.y_column],
            name=config.y_column
        ))
        
        fig.update_layout(
            title=config.title,
            xaxis_title=config.x_column,
            yaxis_title=config.y_column,
            template="plotly_white"
        )
        
        return fig
    
    def _create_scatter_chart(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create scatter plot."""
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=df[config.x_column],
            y=df[config.y_column],
            mode='markers',
            name=f"{config.x_column} vs {config.y_column}"
        ))
        
        fig.update_layout(
            title=config.title,
            xaxis_title=config.x_column,
            yaxis_title=config.y_column,
            template="plotly_white"
        )
        
        return fig
    
    def _create_heatmap(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create heatmap."""
        # For correlation matrix
        if df.select_dtypes(include=['number']).shape[1] > 1:
            corr_matrix = df.select_dtypes(include=['number']).corr()
            
            fig = go.Figure(data=go.Heatmap(
                z=corr_matrix.values,
                x=corr_matrix.columns,
                y=corr_matrix.columns,
                colorscale='RdBu',
                zmid=0
            ))
            
            fig.update_layout(
                title=config.title,
                template="plotly_white"
            )
            
            return fig
        return None
    
    def _create_candlestick_chart(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create candlestick chart for financial data."""
        required_cols = ['open', 'high', 'low', 'close']
        available_cols = df.columns.str.lower().tolist()
        
        # Map columns
        col_mapping = {}
        for req_col in required_cols:
            matching_cols = [col for col in df.columns if req_col in col.lower()]
            if matching_cols:
                col_mapping[req_col] = matching_cols[0]
        
        if len(col_mapping) >= 4:
            fig = go.Figure(data=go.Candlestick(
                x=df[config.x_column],
                open=df[col_mapping['open']],
                high=df[col_mapping['high']],
                low=df[col_mapping['low']],
                close=df[col_mapping['close']]
            ))
            
            fig.update_layout(
                title=config.title,
                xaxis_title=config.x_column,
                yaxis_title="Price",
                template="plotly_white"
            )
            
            return fig
        return None
    
    def _create_histogram(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create histogram."""
        fig = go.Figure()
        fig.add_trace(go.Histogram(
            x=df[config.x_column],
            name=config.x_column
        ))
        
        fig.update_layout(
            title=config.title,
            xaxis_title=config.x_column,
            yaxis_title="Frequency",
            template="plotly_white"
        )
        
        return fig
    
    def _create_box_plot(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create box plot."""
        fig = go.Figure()
        fig.add_trace(go.Box(
            y=df[config.y_column],
            name=config.y_column
        ))
        
        fig.update_layout(
            title=config.title,
            yaxis_title=config.y_column,
            template="plotly_white"
        )
        
        return fig
    
    def _create_area_chart(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create area chart."""
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=df[config.x_column],
            y=df[config.y_column],
            fill='tonexty',
            mode='lines',
            name=config.y_column
        ))
        
        fig.update_layout(
            title=config.title,
            xaxis_title=config.x_column,
            yaxis_title=config.y_column,
            template="plotly_white"
        )
        
        return fig
    
    def _create_pie_chart(self, df: pd.DataFrame, config: ChartConfig) -> go.Figure:
        """Create pie chart."""
        fig = go.Figure()
        fig.add_trace(go.Pie(
            labels=df[config.x_column],
            values=df[config.y_column],
            name=config.title
        ))
        
        fig.update_layout(
            title=config.title,
            template="plotly_white"
        )
        
        return fig
    
    def _create_dashboard_layout(self, template: DashboardConfig, charts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create dashboard layout configuration."""
        return {
            "layout_type": template.layout_type.value,
            "theme": template.theme,
            "auto_refresh": template.auto_refresh,
            "refresh_interval": template.refresh_interval,
            "charts_layout": [
                {
                    "chart_id": chart["chart_id"],
                    "position": chart["config"].get("position", {"row": 1, "col": 1}),
                    "size": {"width": 12, "height": 400}  # Default size
                }
                for chart in charts
            ]
        }
