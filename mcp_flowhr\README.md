# FlowHR MCP Server - Azure Functions

This project implements an MCP (Model Context Protocol) tool server using Azure Functions. It provides tools for time management and mathematical calculations that can be discovered and invoked by MCP clients like VS Code Copilot or other AI agents.

## ⚠️ Important Notice

This implementation uses the **experimental** MCP extension for Azure Functions, which is currently in preview. It is not recommended for production use and may be subject to breaking changes.

## Features

### Available Tools

1. **getTime** - Get current time in various formats
   - Supports ISO 8601, Unix timestamp, and human-readable formats
   - Optional timezone specification (defaults to UTC)

2. **calculate** - Perform mathematical calculations
   - Basic arithmetic: add, subtract, multiply, divide, power
   - Mathematical functions: sqrt, sin, cos, tan
   - Input validation and error handling

## Prerequisites

- Azure Functions Core Tools (>= 4.0.7030)
- Python 3.9 or later
- Azure Storage Emulator (Azurite) for local development
- Azure subscription for deployment

## Local Development Setup

1. **Install Azure Functions Core Tools**
   ```bash
   npm install -g azure-functions-core-tools@4 --unsafe-perm true
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start Azurite (Azure Storage Emulator)**
   ```bash
   azurite --silent --location c:\azurite --debug c:\azurite\debug.log
   ```

4. **Start the Function App**
   ```bash
   func start
   ```

## Configuration

### host.json
The `host.json` file contains the MCP extension configuration:
- Uses experimental extension bundle
- Defines server name, version, and instructions
- Configures function timeout and logging

### local.settings.json
Contains local development settings:
- Storage connection strings
- Function runtime configuration
- CORS settings for local testing

## MCP Tool Schemas

### getTime Tool
```json
{
  "format": "iso|timestamp|readable",
  "timezone": "UTC"
}
```

### calculate Tool
```json
{
  "operation": "add|subtract|multiply|divide|power|sqrt|sin|cos|tan",
  "operand1": 123.45,
  "operand2": 67.89
}
```

## Usage Examples

### Using with MCP Inspector
1. Start the function app locally
2. Get the MCP SSE endpoint: `http://localhost:7071/runtime/webhooks/mcp/sse`
3. Connect using MCP Inspector or compatible client

### Tool Invocation Examples

**Get current time in ISO format:**
```json
{
  "tool": "getTime",
  "arguments": {
    "format": "iso"
  }
}
```

**Perform calculation:**
```json
{
  "tool": "calculate",
  "arguments": {
    "operation": "multiply",
    "operand1": 15,
    "operand2": 3.5
  }
}
```

## Deployment

### Using Azure Developer CLI (azd)
1. Initialize the project: `azd init`
2. Deploy: `azd up`
3. Get the SSE endpoint: `https://<your-function-app>.azurewebsites.net/runtime/webhooks/mcp/sse?code=<system_key>`

### Manual Deployment
1. Create an Azure Function App with Python 3.9 runtime
2. Configure application settings (AzureWebJobsStorage, etc.)
3. Deploy using `func azure functionapp publish <function-app-name>`

## Connecting to MCP Clients

### VS Code Copilot
Add the SSE endpoint to your MCP client configuration to enable tool discovery and invocation.

### Custom MCP Clients
Use the Server-Sent Events endpoint to establish a connection and discover available tools.

## Error Handling

All tools implement comprehensive error handling:
- Input validation with descriptive error messages
- Exception catching with proper error responses
- Consistent JSON response format
- Logging for debugging and monitoring

## Troubleshooting

### Common Issues

1. **Function not starting**: Ensure Azure Functions Core Tools version >= 4.0.7030
2. **Storage errors**: Verify Azurite is running and connection strings are correct
3. **MCP tools not discovered**: Check host.json MCP configuration and extension bundle

### Logs
Check function logs for detailed error information:
```bash
func logs
```

## Contributing

This is a preview implementation. Please test thoroughly and report any issues or improvements.

## License

This project is provided as-is for educational and development purposes.
