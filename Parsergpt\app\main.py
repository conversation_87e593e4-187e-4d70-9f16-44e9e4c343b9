"""FastAPI main application for ParserGPT POC."""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from .database import init_database, get_async_session, close_database
from .models import Job
from .schemas import JobRequest, JobResponse, JobStatus, ErrorResponse, FieldSpec
from .orchestrator import run_job
from .config import get_settings, validate_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting ParserGPT application...")
    
    try:
        # Validate settings
        validate_settings()
        
        # Initialize database
        await init_database()
        
        logger.info("Application startup completed")
        yield
        
    except Exception as e:
        logger.error(f"Application startup failed: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down ParserGPT application...")
        await close_database()
        logger.info("Application shutdown completed")


# Create FastAPI app
app = FastAPI(
    title="ParserGPT POC",
    description="Turn messy websites into clean CSVs using LLM-powered extraction",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "ParserGPT POC",
        "version": "0.1.0",
        "description": "Turn messy websites into clean CSVs",
        "endpoints": {
            "create_job": "POST /jobs",
            "get_job_status": "GET /jobs/{job_id}",
            "download_csv": "GET /jobs/{job_id}/csv"
        }
    }


@app.post("/jobs", response_model=JobResponse)
async def create_job(
    job_request: JobRequest,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Create a new scraping job.
    
    Args:
        job_request: Job configuration
        background_tasks: FastAPI background tasks
        session: Database session
        
    Returns:
        JobResponse with job ID and status
    """
    try:
        logger.info(f"Creating new job for {job_request.start_url}")
        
        # Create job record
        job = Job(
            start_url=job_request.start_url,
            allowed_domains=",".join(job_request.allowed_domains) if job_request.allowed_domains else "",
            max_depth=job_request.max_depth,
            max_pages=job_request.max_pages,
            field_spec=[spec.dict() for spec in job_request.field_spec],
            status="created"
        )
        
        session.add(job)
        await session.commit()
        await session.refresh(job)
        
        # Start job processing in background
        background_tasks.add_task(run_job, job.id)
        
        logger.info(f"Job {job.id} created and queued for processing")
        
        return JobResponse(
            job_id=job.id,
            status="created",
            message="Job created and queued for processing"
        )
        
    except Exception as e:
        logger.error(f"Failed to create job: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create job: {str(e)}"
        )


@app.get("/jobs/{job_id}", response_model=JobStatus)
async def get_job_status(
    job_id: int,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Get job status and progress information.
    
    Args:
        job_id: Job ID
        session: Database session
        
    Returns:
        JobStatus with detailed progress information
    """
    try:
        # Get job from database
        job = await session.get(Job, job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        # Convert to response model
        job_status = JobStatus(
            job_id=job.id,
            status=job.status,
            created_at=job.created_at,
            updated_at=job.updated_at,
            completed_at=job.completed_at,
            pages_discovered=job.pages_discovered,
            pages_processed=job.pages_processed,
            pages_extracted=job.pages_extracted,
            error_message=job.error_message,
            start_url=job.start_url,
            max_pages=job.max_pages,
            field_spec=[FieldSpec(**spec) for spec in job.field_spec]
        )
        
        return job_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status for {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )


@app.get("/jobs/{job_id}/csv")
async def download_csv(
    job_id: int,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Download CSV results for a completed job.
    
    Args:
        job_id: Job ID
        session: Database session
        
    Returns:
        FileResponse with CSV data
    """
    try:
        # Get job from database
        job = await session.get(Job, job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        # Check if job is completed
        if job.status != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job {job_id} is not completed (status: {job.status})"
            )
        
        # Check if CSV file exists
        import os
        csv_filename = f"job_{job_id}.csv"
        if not os.path.exists(csv_filename):
            raise HTTPException(
                status_code=404,
                detail=f"CSV file not found for job {job_id}"
            )
        
        # Return file
        return FileResponse(
            path=csv_filename,
            media_type="text/csv",
            filename=f"parsergpt_job_{job_id}.csv"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to download CSV for job {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to download CSV: {str(e)}"
        )


@app.get("/jobs")
async def list_jobs(
    limit: int = 10,
    offset: int = 0,
    session: AsyncSession = Depends(get_async_session)
):
    """
    List recent jobs with pagination.
    
    Args:
        limit: Maximum number of jobs to return
        offset: Number of jobs to skip
        session: Database session
        
    Returns:
        List of job summaries
    """
    try:
        # Get jobs with pagination
        result = await session.execute(
            select(Job)
            .order_by(Job.created_at.desc())
            .limit(limit)
            .offset(offset)
        )
        jobs = result.scalars().all()
        
        # Convert to response format
        job_summaries = []
        for job in jobs:
            job_summaries.append({
                "job_id": job.id,
                "status": job.status,
                "start_url": job.start_url,
                "created_at": job.created_at,
                "pages_extracted": job.pages_extracted,
                "max_pages": job.max_pages
            })
        
        return {
            "jobs": job_summaries,
            "total": len(job_summaries),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Failed to list jobs: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list jobs: {str(e)}"
        )


@app.delete("/jobs/{job_id}")
async def delete_job(
    job_id: int,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Delete a job and its associated data.
    
    Args:
        job_id: Job ID
        session: Database session
        
    Returns:
        Success message
    """
    try:
        # Get job from database
        job = await session.get(Job, job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        # Delete job (cascades to pages and extractions)
        await session.delete(job)
        await session.commit()
        
        # Clean up CSV file if it exists
        import os
        csv_filename = f"job_{job_id}.csv"
        if os.path.exists(csv_filename):
            os.remove(csv_filename)
        
        logger.info(f"Job {job_id} deleted successfully")
        
        return {"message": f"Job {job_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete job {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete job: {str(e)}"
        )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "ParserGPT POC",
        "version": "0.1.0"
    }


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
