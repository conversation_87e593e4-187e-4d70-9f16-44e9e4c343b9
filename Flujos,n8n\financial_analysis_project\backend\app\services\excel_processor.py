"""
Excel file processing service for financial data.
Handles file upload, validation, and data extraction.
"""

import pandas as pd
import io
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ExcelProcessingError(Exception):
    """Custom exception for Excel processing errors."""
    pass


class ExcelProcessor:
    """Service for processing Excel files containing financial data."""
    
    # Expected column mappings (flexible matching)
    COLUMN_MAPPINGS = {
        'supplier': ['supplier', 'supplier_name', 'vendor', 'vendor_name', 'proveedor'],
        'voucher': ['voucher', 'voucher_id', 'invoice', 'invoice_id', 'factura', 'comprobante'],
        'gross_amount': ['gross_amount', 'amount', 'total', 'gross', 'importe', 'monto'],
        'cost': ['cost', 'cost_amount', 'costo', 'cost_price'],
        'date': ['date', 'transaction_date', 'fecha', 'invoice_date']
    }
    
    def __init__(self):
        self.required_columns = ['supplier', 'voucher', 'gross_amount']
    
    def process_file(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """
        Process uploaded Excel file and return cleaned DataFrame.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            
        Returns:
            Cleaned pandas DataFrame
            
        Raises:
            ExcelProcessingError: If file processing fails
        """
        try:
            # Read Excel file
            df = self._read_excel_file(file_content, filename)
            
            # Validate and map columns
            df = self._validate_and_map_columns(df)
            
            # Clean and standardize data
            df = self._clean_data(df)
            
            # Validate data quality
            self._validate_data_quality(df)
            
            logger.info(f"Successfully processed {filename}: {len(df)} records")
            return df
            
        except Exception as e:
            logger.error(f"Error processing file {filename}: {str(e)}")
            raise ExcelProcessingError(f"Failed to process Excel file: {str(e)}")
    
    def _read_excel_file(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """Read Excel file from bytes content."""
        try:
            # Try reading as Excel file
            df = pd.read_excel(io.BytesIO(file_content))
            
            if df.empty:
                raise ExcelProcessingError("Excel file is empty")
                
            return df
            
        except Exception as e:
            # Try reading as CSV if Excel fails
            try:
                df = pd.read_csv(io.BytesIO(file_content))
                if df.empty:
                    raise ExcelProcessingError("File is empty")
                return df
            except:
                raise ExcelProcessingError(f"Cannot read file as Excel or CSV: {str(e)}")
    
    def _validate_and_map_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and map columns to standard names."""
        original_columns = df.columns.tolist()
        column_mapping = {}
        
        # Find matching columns for each required field
        for standard_name, possible_names in self.COLUMN_MAPPINGS.items():
            matched_column = None
            
            for col in df.columns:
                if col.lower().strip() in [name.lower() for name in possible_names]:
                    matched_column = col
                    break
            
            if matched_column:
                column_mapping[matched_column] = standard_name
        
        # Check if we have required columns
        mapped_standard_names = set(column_mapping.values())
        missing_required = set(self.required_columns) - mapped_standard_names
        
        if missing_required:
            available_cols = ", ".join(original_columns)
            raise ExcelProcessingError(
                f"Missing required columns: {missing_required}. "
                f"Available columns: {available_cols}. "
                f"Expected columns like: {self._get_expected_column_examples()}"
            )
        
        # Rename columns
        df = df.rename(columns=column_mapping)
        
        # Add missing optional columns with default values
        if 'cost' not in df.columns:
            df['cost'] = None
        if 'date' not in df.columns:
            df['date'] = datetime.now()
        
        return df
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize the data."""
        df = df.copy()
        
        # Clean supplier names
        if 'supplier' in df.columns:
            df['supplier'] = df['supplier'].astype(str).str.strip().str.title()
        
        # Clean voucher IDs
        if 'voucher' in df.columns:
            df['voucher'] = df['voucher'].astype(str).str.strip()
        
        # Clean and convert amounts
        if 'gross_amount' in df.columns:
            df['gross_amount'] = self._clean_numeric_column(df['gross_amount'])
        
        if 'cost' in df.columns:
            df['cost'] = self._clean_numeric_column(df['cost'])
        
        # Clean dates
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
            # Fill missing dates with current date
            df['date'] = df['date'].fillna(datetime.now())
        
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        return df
    
    def _clean_numeric_column(self, series: pd.Series) -> pd.Series:
        """Clean numeric column by removing currency symbols and converting to float."""
        if series.dtype == 'object':
            # Remove currency symbols and commas
            series = series.astype(str).str.replace(r'[$,€£¥]', '', regex=True)
            series = series.str.replace(',', '')
            
        # Convert to numeric, coercing errors to NaN
        return pd.to_numeric(series, errors='coerce')
    
    def _validate_data_quality(self, df: pd.DataFrame) -> None:
        """Validate data quality and raise warnings."""
        issues = []
        
        # Check for missing required data
        for col in self.required_columns:
            if col in df.columns:
                missing_count = df[col].isnull().sum()
                if missing_count > 0:
                    issues.append(f"{missing_count} missing values in {col}")
        
        # Check for negative amounts
        if 'gross_amount' in df.columns:
            negative_amounts = (df['gross_amount'] < 0).sum()
            if negative_amounts > 0:
                issues.append(f"{negative_amounts} negative gross amounts found")
        
        # Check for duplicate vouchers
        if 'voucher' in df.columns:
            duplicates = df['voucher'].duplicated().sum()
            if duplicates > 0:
                issues.append(f"{duplicates} duplicate voucher IDs found")
        
        if issues:
            logger.warning(f"Data quality issues: {'; '.join(issues)}")
    
    def _get_expected_column_examples(self) -> str:
        """Get examples of expected column names."""
        examples = []
        for standard_name, possible_names in self.COLUMN_MAPPINGS.items():
            if standard_name in self.required_columns:
                examples.append(f"{standard_name}: {', '.join(possible_names[:3])}")
        return "; ".join(examples)
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict:
        """Get summary statistics of the processed data."""
        summary = {
            "total_records": len(df),
            "total_suppliers": df['supplier'].nunique() if 'supplier' in df.columns else 0,
            "total_vouchers": df['voucher'].nunique() if 'voucher' in df.columns else 0,
            "date_range": {
                "start": df['date'].min().isoformat() if 'date' in df.columns else None,
                "end": df['date'].max().isoformat() if 'date' in df.columns else None
            },
            "amount_summary": {
                "total": float(df['gross_amount'].sum()) if 'gross_amount' in df.columns else 0,
                "average": float(df['gross_amount'].mean()) if 'gross_amount' in df.columns else 0,
                "min": float(df['gross_amount'].min()) if 'gross_amount' in df.columns else 0,
                "max": float(df['gross_amount'].max()) if 'gross_amount' in df.columns else 0
            },
            "missing_data": {
                col: int(df[col].isnull().sum()) for col in df.columns
            }
        }
        
        return summary
