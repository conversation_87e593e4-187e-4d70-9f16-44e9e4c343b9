#!/usr/bin/env python3
"""
Langfuse Masking Demo

This script demonstrates how the Langfuse masking feature works with LumusAI.
It shows how sensitive data is automatically masked before being sent to Langfuse.

Usage:
    python examples/langfuse_masking_demo.py

Requirements:
    - Langfuse credentials configured in .env
    - LANGFUSE_ENABLE_MASKING=true in .env
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the parent directory to the path so we can import from the main app
sys.path.append(str(Path(__file__).parent.parent))

from utils.langchain_client import LangChainClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_KEY = os.getenv("API_KEY")
API_VERSION = os.getenv("API_VERSION")
AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
MODEL = os.getenv("MODEL")

# Langfuse configuration
LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")

# Masking configuration
LANGFUSE_ENABLE_MASKING = True  # Force enable for demo
LANGFUSE_MASK_CREDIT_CARDS = True
LANGFUSE_MASK_EMAILS = True
LANGFUSE_MASK_PHONES = True
LANGFUSE_MASK_SECRETS = True


async def demo_masking():
    """
    Demonstrate Langfuse masking with various types of sensitive data.
    """
    print("🔒 Langfuse Masking Demo")
    print("=" * 50)
    
    # Initialize LangChain client with masking enabled
    client = LangChainClient(
        api_key=API_KEY,
        api_version=API_VERSION,
        azure_endpoint=AZURE_ENDPOINT,
        model=MODEL,
        langfuse_public_key=LANGFUSE_PUBLIC_KEY,
        langfuse_secret_key=LANGFUSE_SECRET_KEY,
        langfuse_host=LANGFUSE_HOST,
        langfuse_enable_masking=LANGFUSE_ENABLE_MASKING,
        langfuse_mask_credit_cards=LANGFUSE_MASK_CREDIT_CARDS,
        langfuse_mask_emails=LANGFUSE_MASK_EMAILS,
        langfuse_mask_phones=LANGFUSE_MASK_PHONES,
        langfuse_mask_secrets=LANGFUSE_MASK_SECRETS
    )
    
    print("\n📝 Testing with sensitive data...")
    
    # Test data containing various sensitive information
    test_prompts = [
        {
            "name": "Credit Card Processing",
            "prompt": """
            Process this payment information:
            Customer: John Doe
            Credit Card: 4111 1111 1111 1111
            Expiry: 12/25
            CVV: 123
            Amount: $500.00
            """
        },
        {
            "name": "Contact Information",
            "prompt": """
            Customer contact details:
            Name: Jane Smith
            Email: <EMAIL>
            Phone: ************
            Address: 123 Main St, Anytown, USA
            """
        },
        {
            "name": "Secret Data",
            "prompt": """
            Configuration data:
            SECRET_API_KEY=sk-1234567890abcdef
            CONFIDENTIAL_TOKEN=conf_xyz789
            PUBLIC_INFO=This is not sensitive
            """
        },
        {
            "name": "Mixed Sensitive Data",
            "prompt": """
            Customer support ticket:
            Customer called from ************ about their card 4532 1234 5678 9012.
            They want to update their <NAME_EMAIL> to <EMAIL>.
            Their SECRET_CUSTOMER_ID is SECRET_12345.
            """
        }
    ]
    
    for i, test_case in enumerate(test_prompts, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print("-" * 30)
        
        try:
            # This will be traced by Langfuse with masking applied
            response = await client.get_simple_response(
                prompt=f"Analyze this data and provide a summary: {test_case['prompt']}"
            )
            
            print("✅ Request processed successfully")
            print(f"📊 Response length: {len(response.get('response', ''))} characters")
            print(f"🔢 Tokens used: {response.get('token_usage', {}).get('total_tokens', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Error processing request: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("\n📋 What happened:")
    print("1. All sensitive data in the prompts was automatically masked")
    print("2. Credit cards → [REDACTED_CREDIT_CARD]")
    print("3. Emails → [REDACTED_EMAIL]")
    print("4. Phone numbers → [REDACTED_PHONE]")
    print("5. Secret data → [REDACTED_SECRET]")
    print("\n🔍 Check your Langfuse dashboard to see the masked traces!")
    print(f"   Dashboard: {LANGFUSE_HOST}")
    
    # Flush Langfuse data to ensure it's sent
    if client.langfuse_client:
        client.flush_langfuse()


async def demo_without_masking():
    """
    Demonstrate what happens without masking (for comparison).
    """
    print("\n🔓 Comparison: Without Masking")
    print("=" * 50)
    
    # Initialize client without masking
    client_no_mask = LangChainClient(
        api_key=API_KEY,
        api_version=API_VERSION,
        azure_endpoint=AZURE_ENDPOINT,
        model=MODEL,
        langfuse_public_key=LANGFUSE_PUBLIC_KEY,
        langfuse_secret_key=LANGFUSE_SECRET_KEY,
        langfuse_host=LANGFUSE_HOST,
        langfuse_enable_masking=False  # Masking disabled
    )
    
    print("⚠️  This client has masking DISABLED")
    print("📝 Processing one test case without masking...")
    
    try:
        response = await client_no_mask.get_simple_response(
            prompt="Process this test data: Credit card 4111 1111 1111 1111, email <EMAIL>"
        )
        
        print("✅ Request processed (without masking)")
        print("🔍 Check Langfuse dashboard - this trace will show unmasked data!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Flush data
    if client_no_mask.langfuse_client:
        client_no_mask.flush_langfuse()


async def main():
    """
    Main demo function.
    """
    if not all([API_KEY, AZURE_ENDPOINT, LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY]):
        print("❌ Missing required environment variables!")
        print("Please ensure you have configured:")
        print("- API_KEY, AZURE_ENDPOINT (Azure OpenAI)")
        print("- LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY (Langfuse)")
        return
    
    print("🚀 Starting Langfuse Masking Demo...")
    
    # Run masking demo
    await demo_masking()
    
    # Optional: Run comparison without masking
    print("\n" + "=" * 70)
    response = input("Would you like to see a comparison without masking? (y/N): ")
    if response.lower() == 'y':
        await demo_without_masking()
    
    print("\n✨ Demo finished! Check your Langfuse dashboard for the traces.")


if __name__ == "__main__":
    asyncio.run(main())
