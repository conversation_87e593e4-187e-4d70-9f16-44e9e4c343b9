#!/usr/bin/env python3
"""
Test script to verify that Langfuse masking is working correctly.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_cv_masking():
    """Test CV processing with sensitive data that should be masked."""
    
    print("🔒 Testing CV Processing with Masking")
    print("=" * 60)
    
    # CV data with lots of sensitive information
    cv_data = """
    <PERSON>
    Senior Software Engineer
    
    Personal Information:
    Email: <EMAIL>
    Phone: (57) 3157536767
    Address: Cali, Valle Del Cauca, Colombia
    LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
    Credit Card: 4111 1111 1111 1111
    
    SECRET_API_KEY: SECRET_ABC123XYZ
    CONFIDENTIAL_TOKEN: CONF_TOKEN_789
    
    Professional Experience:
    - Senior Developer at TechCorp SA (2022-2024)
      * Led development team of 10 engineers
      * Implemented microservices architecture
      * Technologies: Python, Kubernetes, <PERSON><PERSON>
    
    - Software Engineer at StartupXYZ LTDA (2020-2022)
      * Built scalable web applications
      * Managed CI/CD pipelines
      * Technologies: Django, PostgreSQL, Docker
    
    Skills:
    - Programming: Python, JavaScript, Java, Go
    - Frameworks: Django, FastAPI, React, Vue.js
    - Cloud: AWS, GCP, Azure
    - Databases: PostgreSQL, MongoDB, Redis
    
    Education:
    - MS Computer Science, Universidad Nacional (2018-2020)
    - BS Software Engineering, Universidad del Valle (2014-2018)
    
    Contact: <EMAIL>
    Mobile: +57 3157536767
    """
    
    print("📞 Testing /process endpoint with sensitive CV data...")
    print(f"📊 Data contains:")
    print("   - Full name: Santiago García Martínez")
    print("   - Email: <EMAIL>")
    print("   - Phone: (57) 3157536767")
    print("   - LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/")
    print("   - Credit card: 4111 1111 1111 1111")
    print("   - Secret keys: SECRET_ABC123XYZ, CONF_TOKEN_789")
    print("   - Company names: TechCorp SA, StartupXYZ LTDA")
    print("   - Address: Cali, Valle Del Cauca, Colombia")
    
    try:
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "cv",
                "data": cv_data
            },
            timeout=60
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check if we got structured data
            if isinstance(result, dict) and 'personal_information' in result:
                personal = result['personal_information']
                print("\n🔍 Checking if sensitive data was masked in response:")
                print(f"   - Name: {personal.get('full_name', 'N/A')}")
                print(f"   - Email: {personal.get('email', 'N/A')}")
                print(f"   - Phone: {personal.get('phone_number', 'N/A')}")
                print(f"   - LinkedIn: {personal.get('linkedin_profile', 'N/A')}")
                print(f"   - Address: {personal.get('address', 'N/A')}")
                print(f"   - City: {personal.get('city', 'N/A')}")
                
                # Check if data appears to be masked
                sensitive_found = []
                if 'santiago' in str(result).lower():
                    sensitive_found.append("Name (Santiago)")
                if '<EMAIL>' in str(result).lower():
                    sensitive_found.append("Email")
                if '3157536767' in str(result):
                    sensitive_found.append("Phone")
                if 'linkedin.com/in/santiago-garcia-m' in str(result).lower():
                    sensitive_found.append("LinkedIn")
                if '4111 1111 1111 1111' in str(result):
                    sensitive_found.append("Credit Card")
                
                if sensitive_found:
                    print(f"\n⚠️  WARNING: Sensitive data found in response: {', '.join(sensitive_found)}")
                    print("🔓 Masking may not be working correctly")
                else:
                    print("\n✅ No obvious sensitive data found in response")
                    print("🔒 Masking appears to be working")
                
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"\n📊 Token Usage: {tokens.get('total_tokens', 'N/A')}")
                    print(f"💰 Cost: ${tokens.get('cost', 0):.4f}")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False

def test_invoice_masking():
    """Test invoice processing with sensitive company data."""
    
    print("\n🔒 Testing Invoice Processing with Masking")
    print("=" * 60)
    
    # Invoice data with sensitive company information
    invoice_data = """
    FACTURA / INVOICE #INV-2024-001
    
    Fecha: 15 de Enero, 2024
    
    Facturar a / Bill To:
    ACME Corporation SA
    Representante: María González Rodríguez
    Email: <EMAIL>
    Teléfono: +57 1 234 5678
    Dirección: Bogotá, Cundinamarca, Colombia
    NIT: *********-7
    
    De / From:
    TechServices LTDA
    Contacto: Carlos Pérez López
    Email: <EMAIL>
    Teléfono: (57) 2 987 6543
    
    Servicios / Services:
    1. Desarrollo de Software - 40 horas @ $150/hora = $6,000.00
    2. Consultoría Técnica - 20 horas @ $200/hora = $4,000.00
    3. Soporte Técnico - Tarifa fija = $1,500.00
    
    Subtotal: $11,500.00
    IVA (19%): $2,185.00
    Total: $13,685.00
    
    Términos de Pago: 30 días
    
    Información Confidencial:
    SECRET_CLIENT_ID: SECRET_ACME_789
    CONFIDENTIAL_PROJECT: CONF_PROJ_2024_001
    API_KEY_BILLING: API_KEY_BILL_XYZ123
    
    Tarjeta de Crédito para pagos: 5555 4444 3333 2222
    """
    
    print("📞 Testing /process endpoint with sensitive invoice data...")
    print("📊 Data contains company names, personal names, emails, phones, secrets")
    
    try:
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "invoice",
                "data": invoice_data
            },
            timeout=60
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check for sensitive data in response
            response_str = str(result).lower()
            sensitive_found = []
            
            if 'maría gonzález' in response_str or 'carlos pérez' in response_str:
                sensitive_found.append("Personal names")
            if 'acme-corp.com' in response_str or 'techservices.co' in response_str:
                sensitive_found.append("Email addresses")
            if '234 5678' in response_str or '987 6543' in response_str:
                sensitive_found.append("Phone numbers")
            if 'secret_acme_789' in response_str or 'api_key_bill_xyz123' in response_str:
                sensitive_found.append("Secret keys")
            if '5555 4444 3333 2222' in response_str:
                sensitive_found.append("Credit card")
            
            if sensitive_found:
                print(f"\n⚠️  WARNING: Sensitive data found: {', '.join(sensitive_found)}")
                print("🔓 Masking may not be working correctly")
            else:
                print("\n✅ No obvious sensitive data found in response")
                print("🔒 Masking appears to be working")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🔒 Testing Langfuse Masking Functionality")
    print("=" * 70)
    
    # Check service health
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Test CV masking
    cv_success = test_cv_masking()
    
    # Wait between tests
    print("\n⏳ Waiting 3 seconds...")
    time.sleep(3)
    
    # Test invoice masking
    invoice_success = test_invoice_masking()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 Masking Test Summary")
    print("=" * 70)
    
    if cv_success and invoice_success:
        print("✅ Both tests completed successfully")
    elif cv_success or invoice_success:
        print("⚠️  One test succeeded, one failed")
    else:
        print("❌ Both tests failed")
    
    print("\n📋 What to check:")
    print("1. Application logs should show:")
    print("   - '🔒 Langfuse masking enabled - protecting personal and sensitive information'")
    print("   - No sensitive data in debug logs")
    
    print("\n2. Langfuse dashboard should show:")
    print("   - URL: http://**************:3000")
    print("   - Traces with masked data:")
    print("     * Names: [REDACTED_NAME]")
    print("     * Emails: [REDACTED_EMAIL]")
    print("     * Phones: [REDACTED_PHONE]")
    print("     * LinkedIn: [REDACTED_LINKEDIN]")
    print("     * Addresses: [REDACTED_ADDRESS]")
    print("     * Companies: [REDACTED_COMPANY]")
    print("     * Credit cards: [REDACTED_CREDIT_CARD]")
    print("     * Secrets: [REDACTED_SECRET]")
    
    print("\n3. API responses should contain actual data (not masked)")
    print("   - Masking only affects Langfuse traces, not API responses")

if __name__ == "__main__":
    main()
