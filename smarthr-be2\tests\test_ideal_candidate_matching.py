"""
Test script for ideal candidate matching functionality.
"""

import sys
import os
import json
import logging
from typing import Dict, Any, List

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.ideal_candidate_matching_service import ideal_candidate_matching_service
from utils.ideal_candidate_query_utils import (
    get_candidate_query_with_ideal_candidate,
    get_position_query_with_ideal_candidate,
    check_ideal_candidate_availability,
    get_matching_statistics
)
from utils.ideal_candidate_validation import validate_matching_parameters

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_query_generation():
    """Test SQL query generation for ideal candidate matching."""
    logger.info("Testing query generation...")
    
    try:
        # Test candidate queries with different feedback options
        feedback_options = [0, 1, 2]
        
        for feedback in feedback_options:
            query = get_candidate_query_with_ideal_candidate(feedback)
            
            if not query or len(query.strip()) == 0:
                logger.error(f"Empty query generated for hasFeedback={feedback}")
                return False
            
            # Basic validation - should contain expected SQL keywords
            required_keywords = ["SELECT", "FROM", "candidates_smarthr", "ORDER BY", "LIMIT"]
            for keyword in required_keywords:
                if keyword not in query:
                    logger.error(f"Query for hasFeedback={feedback} missing keyword: {keyword}")
                    return False
            
            # Feedback-specific validations
            if feedback == 0:
                if "LEFT JOIN" not in query or "HAVING" not in query:
                    logger.error(f"Query for hasFeedback=0 should use LEFT JOIN and HAVING")
                    return False
            elif feedback == 1:
                if "INNER JOIN" not in query or "HAVING" not in query:
                    logger.error(f"Query for hasFeedback=1 should use INNER JOIN and HAVING")
                    return False
            # feedback == 2 doesn't need JOIN with interviews
            
            logger.info(f"✓ Candidate query for hasFeedback={feedback} validated")
        
        # Test position query
        position_query = get_position_query_with_ideal_candidate()
        
        if not position_query or len(position_query.strip()) == 0:
            logger.error("Empty position query generated")
            return False
        
        required_keywords = ["SELECT", "FROM", "positions_smarthr", "INNER JOIN", "ideal_candidates_smarthr"]
        for keyword in required_keywords:
            if keyword not in position_query:
                logger.error(f"Position query missing keyword: {keyword}")
                return False
        
        logger.info("✓ Position query validated")
        
        return True
        
    except Exception as e:
        logger.error(f"Query generation test failed: {e}")
        return False


def test_matching_parameter_validation():
    """Test matching parameter validation."""
    logger.info("Testing matching parameter validation...")
    
    try:
        # Test valid parameters
        test_cases = [
            {
                "params": {"position_id": "123e4567-e89b-12d3-a456-************", "limit": 5, "hasFeedback": 2, "batch_mode": True},
                "should_pass": True,
                "description": "Valid position matching parameters"
            },
            {
                "params": {"candidate_id": "123e4567-e89b-12d3-a456-************", "limit": 10, "hasFeedback": 1, "batch_mode": False},
                "should_pass": True,
                "description": "Valid candidate matching parameters"
            },
            {
                "params": {"limit": 5, "hasFeedback": 2, "batch_mode": True},
                "should_pass": False,
                "description": "Missing both position_id and candidate_id"
            },
            {
                "params": {"position_id": "123e4567-e89b-12d3-a456-************", "candidate_id": "123e4567-e89b-12d3-a456-************", "limit": 5},
                "should_pass": False,
                "description": "Both position_id and candidate_id provided"
            },
            {
                "params": {"position_id": "invalid-uuid", "limit": 5, "hasFeedback": 2},
                "should_pass": False,
                "description": "Invalid UUID format"
            },
            {
                "params": {"position_id": "123e4567-e89b-12d3-a456-************", "limit": 150, "hasFeedback": 2},
                "should_pass": False,
                "description": "Limit too high"
            },
            {
                "params": {"position_id": "123e4567-e89b-12d3-a456-************", "limit": 5, "hasFeedback": 5},
                "should_pass": False,
                "description": "Invalid hasFeedback value"
            }
        ]
        
        for test_case in test_cases:
            params = test_case["params"]
            should_pass = test_case["should_pass"]
            description = test_case["description"]
            
            is_valid, errors = validate_matching_parameters(**params)
            
            if should_pass and not is_valid:
                logger.error(f"Test case '{description}' should have passed but failed: {errors}")
                return False
            elif not should_pass and is_valid:
                logger.error(f"Test case '{description}' should have failed but passed")
                return False
            
            logger.info(f"✓ Test case '{description}' validated correctly")
        
        return True
        
    except Exception as e:
        logger.error(f"Matching parameter validation test failed: {e}")
        return False


def test_availability_checking():
    """Test ideal candidate availability checking."""
    logger.info("Testing availability checking...")
    
    try:
        # Test with a non-existent position ID
        test_position_id = "00000000-0000-0000-0000-000000000000"
        
        availability = check_ideal_candidate_availability(test_position_id)
        
        # Should return a valid structure even for non-existent positions
        required_keys = ["exists", "ideal_candidate_id", "embedding_generated", "ready_for_matching"]
        for key in required_keys:
            if key not in availability:
                logger.error(f"Availability check missing key: {key}")
                return False
        
        # For non-existent position, should return False values
        if availability["exists"] or availability["ready_for_matching"]:
            logger.error("Non-existent position should return False for exists and ready_for_matching")
            return False
        
        logger.info("✓ Availability checking for non-existent position validated")
        
        return True
        
    except Exception as e:
        logger.error(f"Availability checking test failed: {e}")
        return False


def test_statistics_generation():
    """Test statistics generation."""
    logger.info("Testing statistics generation...")
    
    try:
        # Test matching statistics
        stats = get_matching_statistics()
        
        required_keys = [
            "total_positions",
            "positions_with_ideal_candidates",
            "positions_ready_for_matching",
            "positions_without_ideal_candidates",
            "ideal_candidate_coverage_percentage",
            "matching_readiness_percentage"
        ]
        
        for key in required_keys:
            if key not in stats:
                logger.error(f"Statistics missing key: {key}")
                return False
        
        # Validate data types
        numeric_keys = [
            "total_positions",
            "positions_with_ideal_candidates",
            "positions_ready_for_matching",
            "positions_without_ideal_candidates"
        ]
        
        for key in numeric_keys:
            if not isinstance(stats[key], int) or stats[key] < 0:
                logger.error(f"Statistics key {key} should be a non-negative integer")
                return False
        
        # Validate percentages
        percentage_keys = ["ideal_candidate_coverage_percentage", "matching_readiness_percentage"]
        for key in percentage_keys:
            if not isinstance(stats[key], (int, float)) or stats[key] < 0 or stats[key] > 100:
                logger.error(f"Statistics key {key} should be a percentage between 0 and 100")
                return False
        
        logger.info("✓ Statistics generation validated")
        
        return True
        
    except Exception as e:
        logger.error(f"Statistics generation test failed: {e}")
        return False


def test_mock_matching_logic():
    """Test the matching logic structure (without database dependencies)."""
    logger.info("Testing matching logic structure...")
    
    try:
        # Test that the matching service exists and has required methods
        required_methods = [
            "get_or_create_ideal_candidate_for_position",
            "match_candidates_with_ideal_candidate",
            "match_positions_with_candidate"
        ]
        
        for method_name in required_methods:
            if not hasattr(ideal_candidate_matching_service, method_name):
                logger.error(f"Matching service missing method: {method_name}")
                return False
            
            method = getattr(ideal_candidate_matching_service, method_name)
            if not callable(method):
                logger.error(f"Matching service method {method_name} is not callable")
                return False
        
        logger.info("✓ Matching service structure validated")
        
        # Test that methods have proper signatures (basic check)
        import inspect
        
        # Check match_candidates_with_ideal_candidate signature
        sig = inspect.signature(ideal_candidate_matching_service.match_candidates_with_ideal_candidate)
        expected_params = ["position_id", "limit", "hasFeedback", "batch_mode"]
        
        for param in expected_params:
            if param not in sig.parameters:
                logger.error(f"Method match_candidates_with_ideal_candidate missing parameter: {param}")
                return False
        
        logger.info("✓ Method signatures validated")
        
        return True
        
    except Exception as e:
        logger.error(f"Matching logic structure test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in matching functionality."""
    logger.info("Testing error handling...")
    
    try:
        # Test validation with invalid parameters
        from utils.ideal_candidate_validation import IdealCandidateValidationError
        
        # Test invalid UUID validation
        try:
            from utils.ideal_candidate_validation import validate_uuid
            validate_uuid("not-a-uuid", "test_field")
            logger.error("Invalid UUID should have raised an exception")
            return False
        except IdealCandidateValidationError:
            logger.info("✓ Invalid UUID correctly raised validation error")
        except Exception as e:
            logger.error(f"Unexpected exception type for invalid UUID: {e}")
            return False
        
        # Test empty ideal candidate info validation
        from utils.ideal_candidate_validation import validate_ideal_candidate_info_structure
        
        is_valid, errors = validate_ideal_candidate_info_structure({})
        if is_valid:
            logger.error("Empty ideal candidate info should fail validation")
            return False
        
        if not errors:
            logger.error("Empty ideal candidate info should return error messages")
            return False
        
        logger.info("✓ Empty ideal candidate info correctly failed validation")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


def run_all_matching_tests():
    """Run all ideal candidate matching tests."""
    logger.info("Starting ideal candidate matching tests...")
    
    tests = [
        ("Query Generation", test_query_generation),
        ("Matching Parameter Validation", test_matching_parameter_validation),
        ("Availability Checking", test_availability_checking),
        ("Statistics Generation", test_statistics_generation),
        ("Matching Logic Structure", test_mock_matching_logic),
        ("Error Handling", test_error_handling)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("MATCHING TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All matching tests passed!")
        return True
    else:
        logger.error(f"💥 {total - passed} matching tests failed!")
        return False


if __name__ == "__main__":
    success = run_all_matching_tests()
    sys.exit(0 if success else 1)
