#!/usr/bin/env python3
"""
Simple test to verify Langfuse masking is working
"""

import requests
import json
import time

def test_simple_masking():
    """Test basic masking functionality"""
    print("🔒 Simple Masking Test")
    print("=" * 50)
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health")
        if health_response.status_code != 200:
            print("❌ Service not healthy")
            return
        print("✅ Service is healthy")
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    # Test a simple endpoint that might trigger LLM
    test_data = {
        "text": "My name is <PERSON> and my <NAME_EMAIL>. My phone is (57) **********."
    }
    
    print(f"\n📤 Sending test data with personal information...")
    print(f"   Text: {test_data['text'][:50]}...")
    
    try:
        # Try different endpoints to see which one works
        endpoints_to_try = [
            "/health",  # Simple endpoint
            "/process", # Main processing endpoint
        ]
        
        for endpoint in endpoints_to_try:
            print(f"\n🔍 Testing endpoint: {endpoint}")
            
            if endpoint == "/health":
                response = requests.get(f"http://localhost:8000{endpoint}")
            else:
                response = requests.post(
                    f"http://localhost:8000{endpoint}",
                    json=test_data,
                    headers={"Content-Type": "application/json"}
                )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ {endpoint} works!")
                break
            else:
                print(f"   ❌ {endpoint} failed: {response.text[:100]}...")
                
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return
    
    print(f"\n🎯 Masking Verification:")
    print(f"   1. Check Langfuse dashboard: http://**************:3000")
    print(f"   2. Look for traces with masked data:")
    print(f"      - 'Santiago García Martínez' should appear as [REDACTED_NAME]")
    print(f"      - '<EMAIL>' should appear as [REDACTED_EMAIL]")
    print(f"      - '(57) **********' should appear as [REDACTED_PHONE]")
    print(f"   3. API responses should contain real data (not masked)")

if __name__ == "__main__":
    test_simple_masking()
