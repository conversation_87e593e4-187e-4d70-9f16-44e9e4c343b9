"""
Pydantic models for direct agent communication and requests.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from .common_models import BaseResponse, ResponseStatus


class AnalysisType(str, Enum):
    """Types of analysis that can be performed."""
    COMPREHENSIVE = "comprehensive"
    STATISTICAL = "statistical"
    TREND = "trend"
    CORRELATION = "correlation"
    ANOMALY = "anomaly"
    FORECASTING = "forecasting"


class ChartType(str, Enum):
    """Types of charts that can be generated."""
    LINE = "line"
    BAR = "bar"
    SCATTER = "scatter"
    HEATMAP = "heatmap"
    HISTOGRAM = "histogram"
    PIE = "pie"
    BOX = "box"
    CANDLESTICK = "candlestick"


class AgentRequest(BaseModel):
    """Base request model for agent communication."""
    request_id: Optional[str] = Field(None, description="Request tracking identifier")
    timeout: Optional[int] = Field(120, ge=10, le=1800, description="Request timeout in seconds")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional request metadata")


class SQLQueryRequest(AgentRequest):
    """Request model for SQL agent queries."""
    query: str = Field(..., min_length=1, max_length=10000, description="Natural language query to convert to SQL")
    database_schema: Optional[str] = Field(None, description="Specific database schema to use")
    limit_results: Optional[int] = Field(1000, ge=1, le=10000, description="Maximum number of results to return")
    explain_query: bool = Field(False, description="Whether to include query explanation")
    validate_only: bool = Field(False, description="Only validate the query without execution")
    
    @validator('query')
    def validate_query(cls, v):
        """Validate query content."""
        if not v.strip():
            raise ValueError('Query cannot be empty')
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "Show me the top 10 customers by total revenue",
                "limit_results": 10,
                "explain_query": True,
                "timeout": 60
            }
        }


class DirectSQLRequest(AgentRequest):
    """Request model for direct SQL execution."""
    sql_query: str = Field(..., min_length=1, description="SQL query to execute directly")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Query parameters")
    read_only: bool = Field(True, description="Whether query is read-only (safety check)")
    
    @validator('sql_query')
    def validate_sql(cls, v):
        """Basic SQL validation."""
        if not v.strip():
            raise ValueError('SQL query cannot be empty')
        
        # Basic safety check for read-only operations
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
        upper_query = v.upper()
        
        for keyword in dangerous_keywords:
            if keyword in upper_query:
                raise ValueError(f'Potentially dangerous SQL keyword detected: {keyword}')
        
        return v.strip()


class PandasAnalysisRequest(AgentRequest):
    """Request model for Pandas agent data analysis."""
    query: str = Field(..., min_length=1, description="Analysis request in natural language")
    analysis_type: AnalysisType = Field(AnalysisType.COMPREHENSIVE, description="Type of analysis to perform")
    data_source: Optional[str] = Field(None, description="Specific data source or table")
    include_visualizations: bool = Field(True, description="Whether to generate visualizations")
    chart_types: Optional[List[ChartType]] = Field(None, description="Specific chart types to generate")
    statistical_tests: bool = Field(False, description="Whether to include statistical tests")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "Analyze sales trends over the last 12 months",
                "analysis_type": "trend",
                "include_visualizations": True,
                "chart_types": ["line", "bar"],
                "timeout": 180
            }
        }


class SchemaAnalysisRequest(AgentRequest):
    """Request model for database schema analysis."""
    analysis_type: str = Field("comprehensive", description="Type of schema analysis")
    include_relationships: bool = Field(True, description="Include table relationships")
    include_statistics: bool = Field(True, description="Include table statistics")
    specific_tables: Optional[List[str]] = Field(None, description="Analyze only specific tables")
    suggest_optimizations: bool = Field(False, description="Include optimization suggestions")
    
    class Config:
        json_schema_extra = {
            "example": {
                "analysis_type": "comprehensive",
                "include_relationships": True,
                "include_statistics": True,
                "suggest_optimizations": True
            }
        }


class AgentResponse(BaseResponse):
    """Base response model for agent operations."""
    agent_type: str = Field(..., description="Type of agent that processed the request")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    result: Optional[Dict[str, Any]] = Field(None, description="Agent execution result")
    warnings: Optional[List[str]] = Field(None, description="Any warnings generated during execution")


class SQLQueryResponse(AgentResponse):
    """Response model for SQL agent queries."""
    agent_type: str = Field("sql_agent", description="SQL agent identifier")
    sql_query: Optional[str] = Field(None, description="Generated SQL query")
    query_explanation: Optional[str] = Field(None, description="Human-readable query explanation")
    data: Optional[List[Dict[str, Any]]] = Field(None, description="Query result data")
    row_count: Optional[int] = Field(None, description="Number of rows returned")
    columns: Optional[List[str]] = Field(None, description="Column names in result")
    query_plan: Optional[Dict[str, Any]] = Field(None, description="Query execution plan")


class PandasAnalysisResponse(AgentResponse):
    """Response model for Pandas agent analysis."""
    agent_type: str = Field("pandas_agent", description="Pandas agent identifier")
    analysis_summary: Optional[str] = Field(None, description="Summary of analysis performed")
    statistical_results: Optional[Dict[str, Any]] = Field(None, description="Statistical analysis results")
    visualizations: Optional[Dict[str, str]] = Field(None, description="Generated visualizations (JSON format)")
    insights: Optional[List[str]] = Field(None, description="Key insights discovered")
    data_quality_issues: Optional[List[str]] = Field(None, description="Data quality issues found")
    recommendations: Optional[List[str]] = Field(None, description="Analysis recommendations")


class SchemaAnalysisResponse(AgentResponse):
    """Response model for schema analysis."""
    agent_type: str = Field("schema_analyzer", description="Schema analyzer identifier")
    schema_summary: Optional[Dict[str, Any]] = Field(None, description="Database schema summary")
    table_analysis: Optional[List[Dict[str, Any]]] = Field(None, description="Individual table analysis")
    relationships: Optional[List[Dict[str, Any]]] = Field(None, description="Table relationships")
    optimization_suggestions: Optional[List[str]] = Field(None, description="Schema optimization suggestions")
    data_quality_report: Optional[Dict[str, Any]] = Field(None, description="Data quality assessment")


class AgentStatusRequest(BaseModel):
    """Request model for checking agent status."""
    agent_types: Optional[List[str]] = Field(None, description="Specific agent types to check")
    include_metrics: bool = Field(False, description="Include performance metrics")


class AgentStatusResponse(BaseResponse):
    """Response model for agent status information."""
    agents: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Agent status information")
    total_agents: int = Field(0, description="Total number of agents")
    active_agents: int = Field(0, description="Number of active agents")
    system_load: Optional[float] = Field(None, description="Current system load")


class BatchRequest(BaseModel):
    """Request model for batch operations."""
    requests: List[Union[SQLQueryRequest, PandasAnalysisRequest, SchemaAnalysisRequest]] = Field(
        ..., min_items=1, max_items=10, description="List of requests to process"
    )
    parallel_execution: bool = Field(True, description="Whether to execute requests in parallel")
    stop_on_error: bool = Field(False, description="Whether to stop batch on first error")


class BatchResponse(BaseResponse):
    """Response model for batch operations."""
    results: List[Union[SQLQueryResponse, PandasAnalysisResponse, SchemaAnalysisResponse]] = Field(
        default_factory=list, description="List of individual results"
    )
    successful_count: int = Field(0, description="Number of successful operations")
    failed_count: int = Field(0, description="Number of failed operations")
    total_execution_time: Optional[float] = Field(None, description="Total batch execution time")


# Example models for documentation
class AgentExamples:
    """Example agent models for API documentation."""
    
    SQL_REQUEST = SQLQueryRequest(
        query="Find the top 5 products by sales volume",
        limit_results=5,
        explain_query=True
    )
    
    PANDAS_REQUEST = PandasAnalysisRequest(
        query="Analyze customer churn patterns",
        analysis_type=AnalysisType.STATISTICAL,
        include_visualizations=True,
        chart_types=[ChartType.BAR, ChartType.LINE]
    )
    
    SQL_RESPONSE = SQLQueryResponse(
        status=ResponseStatus.SUCCESS,
        message="Query executed successfully",
        agent_type="sql_agent",
        sql_query="SELECT product_name, SUM(quantity) as total_sales FROM sales GROUP BY product_name ORDER BY total_sales DESC LIMIT 5",
        data=[{"product_name": "Product A", "total_sales": 1000}],
        row_count=5,
        execution_time=0.5
    )
