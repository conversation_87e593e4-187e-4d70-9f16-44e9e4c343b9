# ParserGPT POC

Turn messy websites into clean CSVs using LLM-powered extraction with the "teach once, run fast" principle.

## Overview

ParserGPT is a web scraping system that combines the intelligence of Large Language Models (LLMs) with the speed and reliability of deterministic extraction. The system follows a two-phase approach:

1. **Learner Phase (AI-Assisted)**: Uses LLM to analyze sample pages and create "adapters" - JSON files containing CSS selectors, XPath expressions, and regex patterns for each domain.

2. **Runner Phase (Deterministic)**: Uses learned adapters to extract data quickly and cheaply, with LLM fallback only for missing fields.

## Key Features

- **Hybrid Extraction**: Combines deterministic selectors with LLM fallback
- **Adaptive Learning**: Automatically learns extraction patterns from sample pages
- **Domain-Specific Adapters**: Reusable extraction configurations per domain
- **Anti-Detection**: Sophisticated browser automation with Playwright
- **Async Architecture**: Built with FastAPI and async/await throughout
- **Comprehensive Testing**: Unit and integration tests with pytest
- **Type Safety**: Full Pydantic validation and type hints

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI API   │    │   Orchestrator  │    │  Adapter Cache  │
│                 │────│                 │────│                 │
│ Job Management  │    │ Workflow Coord. │    │ JSON Storage    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   LangGraph     │              │
         │              │                 │              │
         └──────────────│ Learning State  │──────────────┘
                        │    Machine      │
                        └─────────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                        │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Page Sampler   │    │ Extraction      │    │ LLM Fallback    │
│                 │    │ Engine          │    │                 │
│ Site Discovery  │    │ CSS/XPath/Regex │    │ Missing Fields  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Web Fetcher   │              │
         │              │                 │              │
         └──────────────│ httpx/Playwright│──────────────┘
                        └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.9+
- OpenAI API key (for LLM functionality)
- Optional: Playwright for bot-protected sites

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd parsergpt-poc
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install Playwright browsers (optional):
```bash
playwright install
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your OpenAI API key and other settings
```

5. Initialize the database:
```bash
python -m app.init_db
```

### Running the Application

Start the FastAPI server:
```bash
python -m app.main
# or
uvicorn app.main:app --reload
```

The API will be available at `http://localhost:8000`

## Usage Examples

### Example 1: Basic Product Scraping

```python
import httpx
import asyncio

async def scrape_products():
    async with httpx.AsyncClient() as client:
        # Create a scraping job
        job_data = {
            "start_url": "https://example-store.com/products",
            "allowed_domains": ["example-store.com"],
            "max_depth": 2,
            "max_pages": 50,
            "field_spec": [
                {
                    "name": "title",
                    "dtype": "string",
                    "description": "Product title",
                    "required": True
                },
                {
                    "name": "price",
                    "dtype": "string",
                    "description": "Product price",
                    "required": True
                },
                {
                    "name": "description",
                    "dtype": "string",
                    "description": "Product description",
                    "required": False
                },
                {
                    "name": "tags",
                    "dtype": "string[]",
                    "description": "Product tags or categories",
                    "required": False
                }
            ]
        }
        
        # Submit job
        response = await client.post("http://localhost:8000/jobs", json=job_data)
        job_id = response.json()["job_id"]
        print(f"Job created: {job_id}")
        
        # Monitor progress
        while True:
            response = await client.get(f"http://localhost:8000/jobs/{job_id}")
            status = response.json()
            
            print(f"Status: {status['status']}")
            print(f"Pages processed: {status['pages_processed']}/{status['pages_discovered']}")
            
            if status["status"] in ["completed", "failed"]:
                break
                
            await asyncio.sleep(5)
        
        # Download results
        if status["status"] == "completed":
            response = await client.get(f"http://localhost:8000/jobs/{job_id}/csv")
            with open(f"products_{job_id}.csv", "wb") as f:
                f.write(response.content)
            print(f"Results saved to products_{job_id}.csv")

# Run the example
asyncio.run(scrape_products())
```

### Example 2: News Article Extraction

```python
# News article scraping configuration
news_job = {
    "start_url": "https://news-site.com",
    "allowed_domains": ["news-site.com"],
    "max_depth": 3,
    "max_pages": 100,
    "field_spec": [
        {
            "name": "headline",
            "dtype": "string",
            "description": "Article headline",
            "required": True
        },
        {
            "name": "author",
            "dtype": "string",
            "description": "Article author",
            "required": False
        },
        {
            "name": "publish_date",
            "dtype": "string",
            "description": "Publication date",
            "required": False
        },
        {
            "name": "content",
            "dtype": "string",
            "description": "Article content/body",
            "required": True
        },
        {
            "name": "categories",
            "dtype": "string[]",
            "description": "Article categories or tags",
            "required": False
        }
    ]
}
```

### Example 3: Real Estate Listings

```python
# Real estate scraping configuration
real_estate_job = {
    "start_url": "https://realty-site.com/listings",
    "allowed_domains": ["realty-site.com"],
    "max_depth": 2,
    "max_pages": 200,
    "field_spec": [
        {
            "name": "address",
            "dtype": "string",
            "description": "Property address",
            "required": True
        },
        {
            "name": "price",
            "dtype": "string",
            "description": "Listing price",
            "required": True
        },
        {
            "name": "bedrooms",
            "dtype": "int",
            "description": "Number of bedrooms",
            "required": False
        },
        {
            "name": "bathrooms",
            "dtype": "float",
            "description": "Number of bathrooms",
            "required": False
        },
        {
            "name": "square_feet",
            "dtype": "int",
            "description": "Property size in square feet",
            "required": False
        },
        {
            "name": "features",
            "dtype": "string[]",
            "description": "Property features and amenities",
            "required": False
        }
    ]
}
```

## API Documentation

### Endpoints

#### `POST /jobs`
Create a new scraping job.

**Request Body:**
```json
{
    "start_url": "https://example.com",
    "allowed_domains": ["example.com"],
    "max_depth": 2,
    "max_pages": 50,
    "field_spec": [
        {
            "name": "title",
            "dtype": "string",
            "description": "Page title",
            "required": true
        }
    ]
}
```

**Response:**
```json
{
    "job_id": 123,
    "status": "created",
    "message": "Job created and queued for processing"
}
```

#### `GET /jobs/{job_id}`
Get job status and progress.

**Response:**
```json
{
    "job_id": 123,
    "status": "running",
    "created_at": "2024-01-01T00:00:00Z",
    "pages_discovered": 25,
    "pages_processed": 25,
    "pages_extracted": 23,
    "start_url": "https://example.com",
    "field_spec": [...]
}
```

#### `GET /jobs/{job_id}/csv`
Download CSV results for completed job.

#### `GET /jobs`
List recent jobs with pagination.

**Query Parameters:**
- `limit`: Number of jobs to return (default: 10)
- `offset`: Number of jobs to skip (default: 0)

#### `DELETE /jobs/{job_id}`
Delete a job and its associated data.

### Field Specification

Fields define what data to extract from each page:

```json
{
    "name": "field_name",
    "dtype": "string|int|float|bool|string[]|int[]|float[]",
    "description": "Human-readable description for LLM",
    "required": true|false
}
```

**Supported Data Types:**
- `string`: Text content
- `int`: Integer numbers
- `float`: Decimal numbers  
- `bool`: Boolean values
- `string[]`: Array of strings
- `int[]`: Array of integers
- `float[]`: Array of floats

## Configuration

Environment variables (`.env` file):

```bash
# Database
DATABASE_URL=sqlite+aiosqlite:///./parsergpt.db

# OpenAI API
OPENAI_API_KEY=your-openai-api-key-here

# Application
DEBUG=true
LOG_LEVEL=INFO

# Scraping
MAX_CONCURRENT_REQUESTS=5
REQUEST_DELAY=1.0
USER_AGENTS=Mozilla/5.0 (compatible; ParserGPT/1.0)

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# Adapters
ADAPTERS_DIR=./adapters
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test categories
pytest tests/test_extractor.py  # Unit tests
pytest tests/test_integration.py  # Integration tests
```

## How It Works

### 1. Learning Phase

When you submit a job for a new domain:

1. **Page Sampling**: Discovers representative pages using sitemap parsing and URL pattern analysis
2. **LLM Analysis**: Uses GPT-4 to analyze sample pages and propose CSS/XPath selectors
3. **Validation**: Tests proposed selectors against sample data
4. **Refinement**: Iteratively improves selectors based on validation results
5. **Adapter Storage**: Saves learned selectors as JSON files for reuse

### 2. Extraction Phase

For subsequent pages on the same domain:

1. **Deterministic Extraction**: Uses learned selectors to extract data quickly
2. **Gap Detection**: Identifies fields with missing or empty values
3. **LLM Fallback**: Uses GPT-4 only for missing fields with strict JSON validation
4. **Result Merging**: Combines deterministic and LLM results intelligently

### 3. Output Generation

1. **Data Validation**: Ensures extracted data matches field specifications
2. **CSV Export**: Generates clean CSV files with proper encoding
3. **Progress Tracking**: Provides real-time status updates via API

## Limitations

This is a Proof of Concept with the following limitations:

- **Single-threaded processing**: Jobs run sequentially
- **Basic crawling**: Simple page discovery without advanced crawling strategies
- **Limited anti-detection**: Basic user agent rotation and delays
- **No distributed processing**: Runs on single machine
- **SQLite database**: Not suitable for high-concurrency production use
- **File-based CSV**: Results stored as local files

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Production Considerations

For production deployment, consider:

- **Horizontal scaling**: Use Redis/Celery for distributed job processing
- **Database**: PostgreSQL with connection pooling
- **Monitoring**: Add metrics, logging, and health checks
- **Rate limiting**: Implement proper rate limiting and backoff strategies
- **Security**: Add authentication, input validation, and CORS configuration
- **Storage**: Use cloud storage for CSV files and adapter persistence
- **Caching**: Redis for adapter caching and session management

## Troubleshooting

### Common Issues

**"OpenAI API key required"**
- Set `OPENAI_API_KEY` in your `.env` file
- Ensure the API key has sufficient credits

**"No samples collected"**
- Check if the start URL is accessible
- Verify allowed_domains includes the target domain
- Try increasing max_depth or max_pages

**"Playwright browser not found"**
- Run `playwright install` to download browsers
- Check if Playwright is needed for your target sites

**"Database connection failed"**
- Ensure SQLite file permissions are correct
- Check DATABASE_URL format in `.env`

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python -m app.main
```

## License

This project is licensed under the MIT License.
