#!/usr/bin/env python3
"""
Basic test to check if <PERSON><PERSON> is working without masking.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the parent directory to the path so we can import from the main app
sys.path.append(str(Path(__file__).parent))

from utils.langchain_client import LangChainClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_basic_langfuse():
    """Test basic Langfuse functionality without masking."""
    
    print("🧪 Testing Basic Langfuse Integration")
    print("=" * 50)
    
    # Configuration
    API_KEY = os.getenv("API_KEY")
    API_VERSION = os.getenv("API_VERSION")
    AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
    MODEL = os.getenv("MODEL")
    
    # Langfuse configuration
    LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
    LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
    LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")
    
    if not all([API_KEY, AZURE_ENDPOINT, LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY]):
        print("❌ Missing required environment variables!")
        return False
    
    print("✅ Environment variables loaded")
    print(f"   - Azure Endpoint: {AZURE_ENDPOINT}")
    print(f"   - Model: {MODEL}")
    print(f"   - Langfuse Host: {LANGFUSE_HOST}")
    
    # Test 1: Initialize client WITHOUT masking
    print("\n🔧 Test 1: Initialize client without masking...")
    try:
        client = LangChainClient(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=AZURE_ENDPOINT,
            model=MODEL,
            langfuse_public_key=LANGFUSE_PUBLIC_KEY,
            langfuse_secret_key=LANGFUSE_SECRET_KEY,
            langfuse_host=LANGFUSE_HOST,
            langfuse_enable_masking=False  # Explicitly disable masking
        )
        print("✅ Client initialized successfully")
    except Exception as e:
        print(f"❌ Client initialization failed: {e}")
        return False
    
    # Test 2: Make a simple API call
    print("\n📞 Test 2: Make a simple API call...")
    try:
        response = await client.get_simple_response("Hello, this is a test message. Please respond with 'Test successful'.")
        print("✅ API call successful")
        print(f"   - Response: {response.get('response', '')[:100]}...")
        print(f"   - Tokens: {response.get('token_usage', {}).get('total_tokens', 'N/A')}")
    except Exception as e:
        print(f"❌ API call failed: {e}")
        return False
    
    # Test 3: Check if callbacks are being used
    print("\n🔍 Test 3: Check callback usage...")
    callbacks = client._get_callbacks()
    print(f"   - Number of callbacks: {len(callbacks)}")
    if callbacks:
        print(f"   - Callback type: {type(callbacks[0])}")
        print("✅ Callbacks are configured")
    else:
        print("⚠️  No callbacks found - Langfuse might not be working")
    
    # Test 4: Flush Langfuse data
    print("\n💾 Test 4: Flush Langfuse data...")
    try:
        client.flush_langfuse()
        print("✅ Langfuse flush completed")
    except Exception as e:
        print(f"⚠️  Langfuse flush warning: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Basic test completed!")
    print("\n📋 Next steps:")
    print("1. Check your Langfuse dashboard for traces")
    print(f"   Dashboard: {LANGFUSE_HOST}")
    print("2. If no traces appear, check Langfuse credentials")
    print("3. If traces appear, the issue is with masking implementation")
    
    return True

async def test_with_masking():
    """Test Langfuse with masking enabled."""
    
    print("\n" + "=" * 50)
    print("🔒 Testing With Masking Enabled")
    print("=" * 50)
    
    # Configuration
    API_KEY = os.getenv("API_KEY")
    API_VERSION = os.getenv("API_VERSION")
    AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
    MODEL = os.getenv("MODEL")
    
    # Langfuse configuration
    LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
    LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
    LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")
    
    # Test with masking enabled
    print("🔧 Initializing client with masking enabled...")
    try:
        client = LangChainClient(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=AZURE_ENDPOINT,
            model=MODEL,
            langfuse_public_key=LANGFUSE_PUBLIC_KEY,
            langfuse_secret_key=LANGFUSE_SECRET_KEY,
            langfuse_host=LANGFUSE_HOST,
            langfuse_enable_masking=True  # Enable masking
        )
        print("✅ Client with masking initialized successfully")
    except Exception as e:
        print(f"❌ Client with masking initialization failed: {e}")
        return False
    
    # Make API call with sensitive data
    print("\n📞 Making API call with sensitive data...")
    try:
        test_prompt = """
        Please analyze this customer data:
        Name: John Doe
        Email: <EMAIL>
        Phone: ************
        Credit Card: 4111 1111 1111 1111
        SECRET_API_KEY: SECRET_12345
        
        Just respond with 'Data analyzed successfully'.
        """
        
        response = await client.get_simple_response(test_prompt)
        print("✅ API call with sensitive data successful")
        print(f"   - Response: {response.get('response', '')[:100]}...")
        print(f"   - Tokens: {response.get('token_usage', {}).get('total_tokens', 'N/A')}")
    except Exception as e:
        print(f"❌ API call with masking failed: {e}")
        return False
    
    # Flush data
    client.flush_langfuse()
    
    print("\n✅ Masking test completed!")
    print("Check Langfuse dashboard to see if sensitive data was masked.")
    
    return True

async def main():
    """Main test function."""
    
    # Test basic functionality first
    basic_success = await test_basic_langfuse()
    
    if basic_success:
        # If basic test works, test with masking
        response = input("\nBasic test completed. Test with masking? (y/N): ")
        if response.lower() == 'y':
            await test_with_masking()
    
    print("\n✨ All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
