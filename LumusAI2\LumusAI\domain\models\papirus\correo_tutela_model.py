from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class CorreoTutela(BaseModel):
    accionante: str = Field(..., description="")
    documento: str = Field(..., description="El número de proceso o codigo del documento, retira los guiones (-) o espacios si hay, en vez de 1000 1234-2024 debes de poner ************")
    correos: List[str] = Field(..., description="ONLY include UNIQUE emails that ends exactly with @cendoj.ramajudicial.gov.co")
    sucursal: str = Field(..., description="Region of the bank [Bogota y Sabana, Caribe, Antioquia, Sur, Centro, ninguno] por ejemplo si hay un correo De: Tutelas Antioquia <<EMAIL>> pones Antioquia, asi con cada región, si no hay ninguno pones ninguno")
    departamento: str = Field(..., description="")
    ciudad: str = Field(..., description="")
    fecha_notificacion: datetime = Field(..., description="")
    fecha_asignacion: datetime = Field(..., description="")

#Bogota y Sabana
#Caribe
#Antioquia
#Sur
#Centro