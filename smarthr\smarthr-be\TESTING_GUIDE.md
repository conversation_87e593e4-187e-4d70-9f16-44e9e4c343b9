# LinkedIn Integration API - Testing Guide

## 🎯 Overview

This guide helps you test the updated LinkedIn Integration API with the new JSON structure that only includes **database-accessible fields**.

## 🚫 **REMOVED FIELDS** (Do NOT use these)

The following fields have been **removed** because they are not accessible from the database:

```json
{
  "current_title": "❌ REMOVED",
  "current_company": "❌ REMOVED", 
  "past_company": "❌ REMOVED"
}
```

## ✅ **AVAILABLE FIELDS** (Use these instead)

### **Search Request Fields:**

```json
{
  "keywords": ["software engineer", "python developer"],
  "location": "San Francisco, CA",
  "experience_level": "mid-level",
  "skills": ["Python", "FastAPI", "PostgreSQL"],
  "school": "Stanford University",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "full_name": "John Smith",
  "country": "United States",
  "city": "San Francisco",
  "degree": "Computer Science",
  "years_of_experience": "5-10",
  "languages": ["English", "Spanish"],
  "past_title": "Software Developer",
  "limit": 25,
  "transform_profiles": true,
  "include_raw_profiles": false
}
```

## 🧪 Testing Methods

### **Method 1: Python Test Script (Recommended)**

```bash
cd smarthr-be
python test_linkedin_integration.py
```

**Features:**
- ✅ Comprehensive endpoint testing
- ✅ Response structure validation
- ✅ Checks for removed fields
- ✅ Generates detailed test report
- ✅ Saves results to `test_results.json`

### **Method 2: cURL Commands**

```bash
cd smarthr-be
./test_curl_commands.sh
```

**Available commands:**
```bash
./test_curl_commands.sh all      # Run all tests
./test_curl_commands.sh basic    # Basic search test
./test_curl_commands.sh detailed # Detailed search test
./test_curl_commands.sh batch    # Batch search test
./test_curl_commands.sh config   # Configuration test
./test_curl_commands.sh status   # Status test
./test_curl_commands.sh removed  # Test removed fields handling
```

### **Method 3: Manual API Testing**

#### **Basic Search Example:**
```bash
curl -X POST http://localhost:8080/api/external_source/search \
  -H "Content-Type: application/json" \
  -d '{
    "keywords": ["software engineer"],
    "location": "San Francisco, CA",
    "skills": ["Python", "FastAPI"],
    "limit": 10,
    "transform_profiles": true
  }'
```

#### **Batch Search Example:**
```bash
curl -X POST http://localhost:8080/api/external_source/search/batch \
  -H "Content-Type: application/json" \
  -d '{
    "search_requests": [
      {
        "keywords": ["data scientist"],
        "location": "New York, NY",
        "skills": ["Python", "TensorFlow"],
        "limit": 5
      }
    ],
    "transform_profiles": true
  }'
```

## 📊 **Available Endpoints**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/external_source/search` | POST | Single people search |
| `/api/external_source/search/batch` | POST | Batch searches |
| `/api/external_source/profiles/{id}` | GET | Single profile |
| `/api/external_source/profiles/batch` | POST | Multiple profiles |
| `/api/external_source/status` | GET | System status |
| `/api/external_source/config` | GET | Configuration info |
| `/api/external_source/test` | POST | Integration testing |

## 🔍 **What to Test**

### **1. Field Validation**
- ✅ Verify removed fields are not accepted
- ✅ Confirm available fields work correctly
- ✅ Test field combinations

### **2. Response Structure**
- ✅ Check response contains expected fields
- ✅ Validate profile structure
- ✅ Verify transformation metadata

### **3. Configuration**
- ✅ Confirm `use_fallback_transformation` is present
- ✅ Verify `confidence_threshold` is available
- ✅ Check Pydantic v2 compatibility

### **4. Error Handling**
- ✅ Test invalid field combinations
- ✅ Verify graceful handling of removed fields
- ✅ Check validation error messages

## 📋 **Expected Response Structure**

```json
{
  "profiles": [
    {
      "first_name": "John",
      "last_name": "Doe",
      "email": "not_provided",
      "phone": "not_provided",
      "location": "San Francisco, CA",
      "current_position": "Senior Software Engineer",
      "professional_summary": "Experienced software engineer...",
      "experience": [...],
      "education": [...],
      "skills": ["Python", "FastAPI", "PostgreSQL"],
      "linkedin_url": "https://www.linkedin.com/in/johndoe"
    }
  ],
  "search_metadata": {
    "total_results": 1,
    "search_parameters": {...},
    "execution_time_ms": 1500
  },
  "transformation_metadata": {
    "profiles_transformed": 1,
    "transformation_success_rate": 100.0,
    "llm_model_used": "gpt-4o"
  }
}
```

## 🚨 **Common Issues & Solutions**

### **Issue: Server not responding**
```bash
# Check if containers are running
docker-compose ps

# Restart if needed
docker-compose down && docker-compose up --build
```

### **Issue: Configuration errors**
```bash
# Check logs
docker-compose logs smarthr-backend

# Look for Pydantic or configuration errors
```

### **Issue: Removed fields still accepted**
- This indicates the field removal wasn't complete
- Check the API request models and validation

## 📈 **Success Criteria**

✅ **All tests pass** with 100% success rate  
✅ **Removed fields** are not accepted or are ignored gracefully  
✅ **Available fields** work correctly  
✅ **Response structure** matches expected format  
✅ **Configuration** includes new fields (`use_fallback_transformation`, `confidence_threshold`)  
✅ **No Pydantic v2 errors** in logs  

## 🎉 **Quick Test Command**

For a quick validation, run:

```bash
cd smarthr-be
python test_linkedin_integration.py
```

This will test all endpoints and validate the JSON structure changes automatically!
