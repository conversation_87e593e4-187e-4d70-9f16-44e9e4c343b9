"""
Service for generating ideal candidate profiles from position data using LLM.
"""

import time
from typing import Optional, Dict, Any
from datetime import datetime
import logging

from models.ideal_candidate import (
    IdealCandidate,
    IdealCandidateCreate,
    IdealCandidateGenerationRequest,
    IdealCandidateGenerationResponse
)
from models.llm import inference_with_fallback
from langchain_core.messages import HumanMessage
from config.config import MODELS_CONFIG

logger = logging.getLogger(__name__)


class IdealCandidateGenerator:
    """Service class for generating ideal candidate profiles from positions."""
    
    def __init__(self):
        self.default_models_order = MODELS_CONFIG.get("ideal_candidate_models_order", ["gpt-4o", "gpt-4o-mini"])
    
    def generate_ideal_candidate_prompt(self) -> str:
        """
        Generate the system prompt for creating ideal candidate profiles.
        """
        return """
        You are an expert talent acquisition specialist and recruiter with deep expertise in creating ideal candidate profiles.
        
        Your task is to analyze a position description and generate a comprehensive ideal candidate profile that would be the perfect match for this position.
        
        ## INSTRUCTIONS:
        
        1. **Analyze the Position**: Carefully review the position requirements, responsibilities, skills, and context.
        
        2. **Generate Ideal Candidate Profile**: Create a detailed candidate profile that includes:
           - Professional summary that aligns perfectly with the position
           - Relevant work experience and career progression
           - Technical skills that match or exceed requirements
           - Soft skills and competencies needed for success
           - Educational background and certifications
           - Industry experience and domain knowledge
           - Leadership and project management experience (if applicable)
           - Cultural fit indicators
        
        3. **Be Specific and Realistic**: 
           - Use concrete examples and specific technologies/tools
           - Ensure the profile is achievable and realistic
           - Include both required and nice-to-have qualifications
           - Consider career progression and growth trajectory
        
        4. **Structure the Response**: Provide a well-organized JSON response in candidate format with the following structure:

        ```json
        {
            "roles": ["Primary Role", "Secondary Role", "Alternative Role"],
            "response": {
                "roles": null,
                "skills": [
                    {
                        "name": "Skill Name",
                        "proficiency_level": "Advanced/Intermediate/Beginner",
                        "years_of_experience": 5
                    }
                ],
                "summary": "Professional summary paragraph that describes the ideal candidate's background and expertise",
                "projects": null,
                "education": [
                    {
                        "degree": "Degree Type",
                        "end_date": "31 Dec 2020",
                        "location": "",
                        "start_date": "01 Sep 2016",
                        "description": "",
                        "field_of_study": "Field of Study",
                        "institution_name": "University Name"
                    }
                ],
                "languages": [
                    {
                        "language": "English",
                        "proficiency_level": "C1"
                    }
                ],
                "references": null,
                "soft_skills": [
                    {
                        "name": "Soft Skill Name",
                        "description": ""
                    }
                ],
                "personal_info": {
                    "city": "",
                    "email": "",
                    "address": "",
                    "country": "",
                    "website": "",
                    "full_name": "Ideal Candidate Name",
                    "phone_number": "",
                    "linkedin_profile": ""
                },
                "certifications": null,
                "work_experience": [
                    {
                        "skills": [
                            {
                                "name": "Skill Used",
                                "proficiency_level": "Advanced",
                                "years_of_experience": 3
                            }
                        ],
                        "end_date": "Currently working",
                        "location": "",
                        "job_title": "Job Title",
                        "start_date": "01 Jan 2022",
                        "company_name": "Company Name",
                        "responsibilities": [
                            "Responsibility 1",
                            "Responsibility 2"
                        ]
                    }
                ]
            }
        ```
        
        ## IMPORTANT GUIDELINES:
        - Base the ideal candidate strictly on the position requirements
        - Be specific with technologies, tools, and frameworks mentioned in the position
        - Ensure the experience level matches the seniority required
        - Include both technical and soft skills relevant to the role
        - Consider the company culture and work environment if mentioned
        - Make the profile comprehensive but realistic
        - Avoid generic statements; be specific and detailed
        
        Return only valid JSON. No additional commentary outside the JSON structure.
        """
    
    def generate_ideal_candidate_from_position(
        self, 
        position_text: str, 
        position_info: Dict[str, Any],
        generation_options: Optional[Dict[str, Any]] = None,
        model_preference: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate an ideal candidate profile from position data.
        
        Args:
            position_text: The text representation of the position for embedding
            position_info: The structured position information
            generation_options: Optional generation parameters
            model_preference: Preferred model for generation
            
        Returns:
            Dictionary containing the ideal candidate profile
        """
        start_time = time.time()
        
        try:
            # Prepare the system prompt
            system_prompt = self.generate_ideal_candidate_prompt()
            
            # Prepare user messages with position information
            user_messages = [
                HumanMessage(content=f"Position Text for Embedding: {position_text}"),
                HumanMessage(content=f"Structured Position Information: {position_info}")
            ]
            
            # Add generation options if provided
            if generation_options:
                user_messages.append(
                    HumanMessage(content=f"Generation Options: {generation_options}")
                )
            
            # Determine models order
            models_order = self.default_models_order
            if model_preference and model_preference in models_order:
                # Move preferred model to front
                models_order = [model_preference] + [m for m in models_order if m != model_preference]
            
            logger.info(f"Generating ideal candidate with models order: {models_order}")
            
            # Generate ideal candidate using LLM
            result = inference_with_fallback(
                task_prompt=system_prompt,
                model_schema=None,  # We'll parse JSON manually for flexibility
                user_messages=user_messages,
                model_schema_text=None,
                models_order=models_order
            )

            if not result:
                logger.error("All LLM providers failed to generate ideal candidate")
                raise RuntimeError("All LLM providers failed to generate ideal candidate. Please check LLM service connectivity.")

            # Extract token usage information if available
            token_usage = {}
            model_used = "unknown"
            if hasattr(result, 'token_usage'):
                token_usage = result.token_usage
                model_used = getattr(result, 'model_name', 'unknown')

                # Log token usage with structured logging
                logger.info(
                    f"Token usage for ideal candidate generation - Model: {model_used}",
                    extra={
                        "custom_dimensions": {
                            "model_name": model_used,
                            "input_tokens": token_usage.get("input_tokens", 0),
                            "output_tokens": token_usage.get("output_tokens", 0),
                            "total_tokens": token_usage.get("total_tokens", 0),
                            "operation": "ideal_candidate_generation"
                        }
                    }
                )

                # Also print token usage for immediate visibility
                total_tokens = token_usage.get("total_tokens", 0)
                input_tokens = token_usage.get("input_tokens", 0)
                output_tokens = token_usage.get("output_tokens", 0)
                print(f"🔢 TOKEN USAGE - Model: {model_used} | Input: {input_tokens} | Output: {output_tokens} | Total: {total_tokens}")

            # Extract content from result
            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)

            # Check if content is empty or None
            if not content or content.strip() == "":
                logger.error("LLM returned empty response")
                raise ValueError("LLM returned empty response. Please check LLM service health.")

            # Parse JSON response
            import json
            try:
                ideal_candidate_data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                logger.error(f"Raw response content (first 500 chars): {content[:500]}")
                logger.error(f"Raw response length: {len(content)}")

                # Try to extract JSON from the response if it's wrapped in other text
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    try:
                        ideal_candidate_data = json.loads(json_match.group())
                        logger.info("Successfully extracted JSON from wrapped response")
                    except json.JSONDecodeError:
                        raise ValueError(f"Invalid JSON response from LLM. Raw response: {content[:200]}...")
                else:
                    raise ValueError(f"No valid JSON found in LLM response. Raw response: {content[:200]}...")
            
            generation_time_ms = int((time.time() - start_time) * 1000)
            
            logger.info(f"Successfully generated ideal candidate in {generation_time_ms}ms")

            return {
                "ideal_candidate_info": ideal_candidate_data,
                "generation_time_ms": generation_time_ms,
                "generation_model": model_used,
                "generation_success": True,
                "token_usage": token_usage
            }
            
        except Exception as e:
            generation_time_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Failed to generate ideal candidate: {e}")

            # Provide a fallback ideal candidate profile when LLM fails
            fallback_profile = self._generate_fallback_profile(position_info)

            return {
                "ideal_candidate_info": fallback_profile,
                "generation_time_ms": generation_time_ms,
                "generation_model": "fallback",
                "generation_success": False,
                "error_message": f"LLM generation failed: {str(e)}. Using fallback profile."
            }

    def _generate_fallback_profile(self, position_info: dict) -> dict:
        """
        Generate a basic fallback ideal candidate profile when LLM fails.

        Args:
            position_info: The position information to base the profile on

        Returns:
            A basic ideal candidate profile dictionary
        """
        # Extract basic information from position if available
        position_title = position_info.get('title', 'Professional')
        position_description = position_info.get('description', '')

        # Create a basic fallback profile
        fallback_profile = {
            "personal_info": {
                "professional_title": f"Senior {position_title}",
                "years_of_experience": "5-8 years",
                "location_preference": "Flexible",
                "summary": f"Experienced professional with strong background in {position_title.lower()} and related technologies."
            },
            "technical_skills": {
                "core_technologies": ["Technology 1", "Technology 2", "Technology 3"],
                "frameworks_tools": ["Framework 1", "Tool 1", "Tool 2"],
                "programming_languages": ["Language 1", "Language 2"],
                "databases": ["Database 1", "Database 2"],
                "cloud_platforms": ["Platform 1", "Platform 2"],
                "other_technical": ["Skill 1", "Skill 2"]
            },
            "professional_experience": [
                {
                    "role": f"Senior {position_title}",
                    "company_type": "Technology company",
                    "duration": "3-4 years",
                    "key_achievements": ["Achievement 1", "Achievement 2"],
                    "technologies_used": ["Tech 1", "Tech 2"]
                }
            ],
            "education": {
                "degree": "Bachelor's degree in relevant field",
                "additional_certifications": ["Certification 1", "Certification 2"],
                "continuous_learning": ["Course 1", "Course 2"]
            },
            "soft_skills": [
                "Communication",
                "Leadership",
                "Problem-solving",
                "Team collaboration"
            ],
            "industry_experience": {
                "domains": ["Domain 1", "Domain 2"],
                "company_sizes": ["startup", "enterprise"],
                "project_types": ["type1", "type2"]
            },
            "leadership_management": {
                "team_leadership": "Some experience",
                "project_management": "Some experience",
                "mentoring": "Some experience"
            },
            "cultural_fit": {
                "work_style": "Collaborative and results-oriented",
                "values_alignment": "Innovation and quality focused",
                "collaboration_style": "Team player with strong communication skills"
            },
            "additional_qualifications": {
                "languages": ["English"],
                "publications": [],
                "speaking_conferences": [],
                "open_source_contributions": []
            }
        }

        return fallback_profile


# Global instance
ideal_candidate_generator = IdealCandidateGenerator()
