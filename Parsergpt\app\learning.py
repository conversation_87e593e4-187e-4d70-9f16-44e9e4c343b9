"""LangGraph-based adapter learning workflow for ParserGPT POC."""

import asyncio
import json
import logging
from typing import Dict, List, Any, TypedDict
from langgraph.graph import StateGraph, END
from .schemas import AdapterDraft, AdapterData, FieldSpec, ValidationResult, AdapterValidationReport
from .sampling import PageSample
from .propose import get_selector_proposer
from .extractor import get_extractor
from .config import get_settings

logger = logging.getLogger(__name__)


class LearningState(TypedDict):
    """State for the adapter learning workflow."""
    domain: str
    field_specs: List[FieldSpec]
    samples: List[PageSample]
    current_draft: AdapterDraft
    validation_report: AdapterValidationReport
    iteration: int
    max_iterations: int
    success_threshold: float
    final_adapter: AdapterData
    error: str


class AdapterLearner:
    """LangGraph-based adapter learning system."""
    
    def __init__(self):
        self.settings = get_settings()
        self.proposer = get_selector_proposer()
        self.extractor = get_extractor()
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph state machine."""
        workflow = StateGraph(LearningState)
        
        # Add nodes
        workflow.add_node("propose", self._propose_selectors)
        workflow.add_node("validate", self._validate_selectors)
        workflow.add_node("repair", self._repair_selectors)
        workflow.add_node("save", self._save_adapter)
        
        # Add edges
        workflow.set_entry_point("propose")
        workflow.add_edge("propose", "validate")
        workflow.add_conditional_edges(
            "validate",
            self._should_repair,
            {
                "repair": "repair",
                "save": "save",
                "end": END
            }
        )
        workflow.add_edge("repair", "validate")
        workflow.add_edge("save", END)
        
        return workflow.compile()
    
    async def _propose_selectors(self, state: LearningState) -> LearningState:
        """Propose initial selectors or refine existing ones."""
        logger.info(f"Proposing selectors for {state['domain']} (iteration {state['iteration']})")
        
        try:
            if state["iteration"] == 0:
                # Initial proposal
                draft = await self.proposer.propose_selectors(
                    state["field_specs"],
                    state["samples"]
                )
            else:
                # Refinement based on validation failures
                failures = self._extract_failures(state["validation_report"])
                draft = await self.proposer.refine_selectors(
                    state["current_draft"],
                    state["field_specs"],
                    failures
                )
            
            state["current_draft"] = draft
            logger.info(f"Proposed {len(draft.selectors)} selectors")
            
        except Exception as e:
            logger.error(f"Error in propose step: {e}")
            state["error"] = f"Proposal failed: {str(e)}"
        
        return state
    
    async def _validate_selectors(self, state: LearningState) -> LearningState:
        """Validate proposed selectors against samples."""
        logger.info(f"Validating selectors for {state['domain']}")
        
        try:
            # Convert draft to adapter data for testing
            adapter_data = state["current_draft"].to_adapter_data(state["domain"])
            
            # Test selectors on all samples
            validation_results = []
            successful_extractions = 0
            
            for sample in state["samples"]:
                # Extract data using current selectors
                extracted = await self.extractor.extract_with_adapter(
                    sample.html,
                    adapter_data.dict(),
                    [spec.dict() for spec in state["field_specs"]]
                )
                
                # Validate each field
                for field_spec in state["field_specs"]:
                    field_name = field_spec.name
                    actual_value = extracted.get(field_name)
                    
                    # Simple validation - check if we got any value
                    success = actual_value is not None and actual_value != ""
                    if field_spec.dtype.endswith("[]"):
                        success = success and isinstance(actual_value, list) and len(actual_value) > 0
                    
                    validation_results.append(ValidationResult(
                        field_name=field_name,
                        success=success,
                        expected="non-empty value",
                        actual=actual_value,
                        confidence=1.0 if success else 0.0
                    ))
                    
                    if success:
                        successful_extractions += 1
            
            # Calculate success rate
            total_validations = len(validation_results)
            success_rate = successful_extractions / total_validations if total_validations > 0 else 0.0
            
            # Calculate coverage per field
            field_coverage = {}
            for field_spec in state["field_specs"]:
                field_results = [r for r in validation_results if r.field_name == field_spec.name]
                field_success = sum(1 for r in field_results if r.success)
                field_coverage[field_spec.name] = field_success / len(field_results) if field_results else 0.0
            
            # Create validation report
            state["validation_report"] = AdapterValidationReport(
                domain=state["domain"],
                overall_success=success_rate >= state["success_threshold"],
                success_rate=success_rate,
                field_results=validation_results,
                coverage=field_coverage
            )
            
            logger.info(f"Validation complete: {success_rate:.2%} success rate")
            
        except Exception as e:
            logger.error(f"Error in validate step: {e}")
            state["error"] = f"Validation failed: {str(e)}"
        
        return state
    
    async def _repair_selectors(self, state: LearningState) -> LearningState:
        """Repair selectors based on validation failures."""
        logger.info(f"Repairing selectors for {state['domain']}")
        
        state["iteration"] += 1
        
        if state["iteration"] >= state["max_iterations"]:
            logger.warning(f"Max iterations reached for {state['domain']}")
            state["error"] = "Max iterations reached without achieving success threshold"
        
        return state
    
    async def _save_adapter(self, state: LearningState) -> LearningState:
        """Save the final adapter."""
        logger.info(f"Saving adapter for {state['domain']}")
        
        try:
            # Convert draft to final adapter data
            adapter_data = state["current_draft"].to_adapter_data(state["domain"])
            
            # Add test cases from samples
            test_cases = []
            for sample in state["samples"][:3]:  # Use first 3 samples as tests
                # Extract expected data (simplified)
                expected_data = {}
                for field_spec in state["field_specs"]:
                    expected_data[field_spec.name] = f"test_{field_spec.name}"
                
                test_cases.append({
                    "url": sample.url,
                    "expects": expected_data,
                    "page_type": sample.page_type
                })
            
            adapter_data.tests = test_cases
            
            # Add metadata
            adapter_data.metadata = {
                "learning_iterations": state["iteration"],
                "success_rate": state["validation_report"].success_rate,
                "sample_count": len(state["samples"]),
                "field_coverage": state["validation_report"].coverage
            }
            
            state["final_adapter"] = adapter_data
            logger.info(f"Adapter saved for {state['domain']}")
            
        except Exception as e:
            logger.error(f"Error in save step: {e}")
            state["error"] = f"Save failed: {str(e)}"
        
        return state
    
    def _should_repair(self, state: LearningState) -> str:
        """Decide whether to repair, save, or end."""
        if state.get("error"):
            return "end"
        
        if not state.get("validation_report"):
            return "end"
        
        if state["validation_report"].overall_success:
            return "save"
        
        if state["iteration"] >= state["max_iterations"]:
            return "end"
        
        return "repair"
    
    def _extract_failures(self, report: AdapterValidationReport) -> List[Dict[str, Any]]:
        """Extract failure information from validation report."""
        failures = []
        
        for result in report.field_results:
            if not result.success:
                failures.append({
                    "field": result.field_name,
                    "reason": "No value extracted",
                    "expected": result.expected,
                    "actual": result.actual,
                    "selector": "unknown"  # Would need to track this
                })
        
        return failures
    
    async def learn_adapter(
        self,
        domain: str,
        field_specs: List[FieldSpec],
        samples: List[PageSample],
        success_threshold: float = 0.8,
        max_iterations: int = 3
    ) -> AdapterData:
        """
        Learn an adapter for a domain using the state machine workflow.
        
        Args:
            domain: Domain to learn adapter for
            field_specs: Fields to extract
            samples: HTML samples for learning
            success_threshold: Minimum success rate required
            max_iterations: Maximum learning iterations
            
        Returns:
            Learned AdapterData
        """
        logger.info(f"Starting adapter learning for {domain}")
        
        # Initialize state
        initial_state = LearningState(
            domain=domain,
            field_specs=field_specs,
            samples=samples,
            current_draft=AdapterDraft(),
            validation_report=None,
            iteration=0,
            max_iterations=max_iterations,
            success_threshold=success_threshold,
            final_adapter=None,
            error=""
        )
        
        # Run the workflow
        try:
            final_state = await self.graph.ainvoke(initial_state)
            
            if final_state.get("error"):
                logger.error(f"Learning failed for {domain}: {final_state['error']}")
                raise Exception(final_state["error"])
            
            if not final_state.get("final_adapter"):
                logger.error(f"No adapter produced for {domain}")
                raise Exception("No adapter was produced")
            
            logger.info(f"Successfully learned adapter for {domain}")
            return final_state["final_adapter"]
            
        except Exception as e:
            logger.error(f"Adapter learning failed for {domain}: {e}")
            raise


# Global learner instance
_adapter_learner = None


def get_adapter_learner() -> AdapterLearner:
    """Get the global adapter learner instance."""
    global _adapter_learner
    if _adapter_learner is None:
        _adapter_learner = AdapterLearner()
    return _adapter_learner


async def learn_adapter(
    domain: str,
    field_specs: List[FieldSpec],
    samples: List[PageSample],
    success_threshold: float = 0.8,
    max_iterations: int = 3
) -> AdapterData:
    """Convenience function to learn an adapter."""
    learner = get_adapter_learner()
    return await learner.learn_adapter(domain, field_specs, samples, success_threshold, max_iterations)
