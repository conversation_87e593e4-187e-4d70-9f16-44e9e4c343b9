"""
Streamlit frontend for financial analysis project.
Provides user interface for file upload and results visualization.
"""

import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime
import io
import sys
import os

# Add components to path
sys.path.append(os.path.dirname(__file__))
from components.file_upload import FileUploadComponent
from components.results_display import ResultsDisplayComponent

# Page configuration
st.set_page_config(
    page_title="Financial Analysis Tool",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Constants
API_BASE_URL = os.getenv("BACKEND_URL", "http://localhost:8000")
SUPPORTED_FORMATS = [".xlsx", ".xls", ".csv"]

# Initialize session state
if 'analysis_results' not in st.session_state:
    st.session_state.analysis_results = None
if 'analysis_history' not in st.session_state:
    st.session_state.analysis_history = []


def main():
    """Main Streamlit application."""
    
    # Header
    st.title("📊 Financial Analysis Tool")
    st.markdown("""
    Upload your Excel files to perform comprehensive financial analysis using AI-powered pandas agents.
    
    **Features:**
    - Total gross amount calculations per supplier and voucher
    - Margin analysis with configurable assumptions
    - Low-margin transaction identification
    - Month-over-month profitability trends
    """)
    
    # Sidebar configuration
    with st.sidebar:
        st.header("⚙️ Analysis Configuration")
        
        assume_cost_percentage = st.slider(
            "Assume Cost Percentage",
            min_value=0.0,
            max_value=100.0,
            value=70.0,
            step=5.0,
            help="Percentage of gross amount to assume as cost when cost data is missing"
        )
        
        low_margin_threshold = st.slider(
            "Low Margin Threshold (%)",
            min_value=0.0,
            max_value=50.0,
            value=10.0,
            step=1.0,
            help="Threshold below which margins are considered low"
        )
        
        st.divider()
        
        # API Status
        st.subheader("🔗 API Status")
        if check_api_health():
            st.success("✅ Backend API is running")
        else:
            st.error("❌ Backend API is not available")
            st.info("Please start the FastAPI backend:\n```bash\ncd backend\nuvicorn app.main:app --reload\n```")
    
    # Main content area
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("📁 File Upload")
        
        # File upload component
        uploaded_file = st.file_uploader(
            "Choose an Excel file",
            type=['xlsx', 'xls', 'csv'],
            help="Upload Excel or CSV files containing financial data"
        )
        
        if uploaded_file is not None:
            # Display file info
            file_size_mb = uploaded_file.size / (1024 * 1024)
            st.info(f"**File:** {uploaded_file.name}")
            st.info(f"**Size:** {file_size_mb:.1f} MB ({uploaded_file.size:,} bytes)")

            # Show processing method info
            if file_size_mb > 5:
                st.warning("⚡ **Large File Detected**: This file will be processed in batches for optimal performance. AI analysis will be simplified to avoid API limits.")
            else:
                st.success("✅ **Standard Processing**: This file will use full AI-powered analysis with LangChain agents.")

            # Analyze button
            if st.button("🚀 Analyze Financial Data", type="primary"):
                analyze_file(uploaded_file, assume_cost_percentage, low_margin_threshold)
    
    with col2:
        st.subheader("📈 Analysis Results")
        
        if st.session_state.analysis_results:
            display_analysis_results(st.session_state.analysis_results)
        else:
            st.info("Upload a file and click 'Analyze' to see results here.")
    
    # Analysis history
    if st.session_state.analysis_history:
        st.divider()
        st.subheader("📋 Analysis History")
        
        for i, result in enumerate(reversed(st.session_state.analysis_history[-5:])):
            with st.expander(f"Analysis {len(st.session_state.analysis_history) - i}: {result['file_name']} - {result['timestamp']}"):
                st.json(result['summary'])


def check_api_health():
    """Check if the FastAPI backend is running."""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False


def analyze_file(uploaded_file, assume_cost_percentage, low_margin_threshold):
    """Analyze uploaded file using the FastAPI backend."""

    # Show different messages based on file size
    file_size_mb = uploaded_file.size / (1024 * 1024)
    if file_size_mb > 5:
        spinner_text = "🔄 Large file detected. Processing in batches... This may take 3-5 minutes."
    else:
        spinner_text = "🔄 Analyzing financial data... This may take 1-2 minutes."

    with st.spinner(spinner_text):
        try:
            # Prepare the request
            files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
            data = {
                "assume_cost_percentage": assume_cost_percentage,
                "low_margin_threshold": low_margin_threshold
            }
            
            # Make API request
            response = requests.post(
                f"{API_BASE_URL}/analyze",
                files=files,
                data=data,
                timeout=300  # 5 minutes timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    st.success("✅ Analysis completed successfully!")
                    
                    # Store results
                    st.session_state.analysis_results = result["data"]
                    
                    # Add to history
                    st.session_state.analysis_history.append({
                        "file_name": uploaded_file.name,
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "summary": {
                            "total_records": result["data"]["total_records_processed"],
                            "total_suppliers": result["data"]["total_suppliers"],
                            "total_gross_amount": result["data"]["total_gross_amount"],
                            "processing_time": result["data"]["processing_time_seconds"]
                        }
                    })
                    
                    # Rerun to display results
                    st.rerun()
                else:
                    st.error(f"❌ Analysis failed: {result['message']}")
                    if result.get("error_details"):
                        st.error(f"Error details: {result['error_details']}")
            else:
                st.error(f"❌ API request failed with status code: {response.status_code}")
                st.error(response.text)
                
        except requests.exceptions.Timeout:
            st.error("⏱️ Analysis timed out. Please try with a smaller file or check your data.")
        except requests.exceptions.ConnectionError:
            st.error("🔌 Cannot connect to the backend API. Please ensure it's running.")
        except Exception as e:
            st.error(f"❌ Unexpected error: {str(e)}")


def display_analysis_results(results):
    """Display comprehensive analysis results."""
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Total Records",
            f"{results['total_records_processed']:,}",
            help="Total number of transactions processed"
        )
    
    with col2:
        st.metric(
            "Total Suppliers",
            f"{results['total_suppliers']:,}",
            help="Number of unique suppliers"
        )
    
    with col3:
        st.metric(
            "Total Gross Amount",
            f"${results['total_gross_amount']:,.2f}",
            help="Sum of all gross amounts"
        )
    
    with col4:
        st.metric(
            "Processing Time",
            f"{results['processing_time_seconds']:.1f}s",
            help="Time taken to complete analysis"
        )
    
    # Tabs for different views
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(["📊 Supplier Analysis", "⚠️ Risk Analysis", "📈 Trends", "📄 Final Report", "📋 Details", "🎯 KPI Dashboard"])

    with tab1:
        display_supplier_analysis(results)

    with tab2:
        display_risk_analysis(results)

    with tab3:
        display_trend_analysis(results)

    with tab4:
        display_final_report(results)

    with tab5:
        display_detailed_results(results)

    with tab6:
        display_kpi_dashboard()


def display_supplier_analysis(results):
    """Display supplier-level analysis."""
    st.subheader("Top Suppliers by Gross Amount")
    
    # Create supplier dataframe
    supplier_data = []
    for supplier in results['supplier_summaries'][:10]:  # Top 10
        supplier_data.append({
            'Supplier': supplier['supplier_name'],
            'Total Gross Amount': supplier['total_gross_amount'],
            'Transactions': supplier['total_transactions'],
            'Avg Margin %': supplier['average_margin'] if supplier['average_margin'] else 0,
            'Low Margin Count': supplier['low_margin_transactions'],
            'Negative Margin Count': supplier['negative_margin_transactions']
        })
    
    if supplier_data:
        df = pd.DataFrame(supplier_data)
        
        # Bar chart
        fig = px.bar(
            df, 
            x='Supplier', 
            y='Total Gross Amount',
            title="Top 10 Suppliers by Gross Amount",
            color='Avg Margin %',
            color_continuous_scale='RdYlGn'
        )
        fig.update_xaxes(tickangle=45)
        st.plotly_chart(fig, use_container_width=True)
        
        # Data table
        st.dataframe(df, use_container_width=True)


def display_risk_analysis(results):
    """Display risk analysis with low and negative margins."""
    st.subheader("Risk Analysis - Low & Negative Margins")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "Low Margin Transactions",
            len(results['low_margin_transactions']),
            help="Transactions with margins below threshold"
        )
    
    with col2:
        st.metric(
            "Negative Margin Transactions",
            len(results['negative_margin_transactions']),
            help="Transactions with negative margins"
        )
    
    # Display problematic transactions
    if results['low_margin_transactions']:
        st.subheader("⚠️ Low Margin Transactions")
        low_margin_data = []
        for transaction in results['low_margin_transactions'][:20]:  # Show top 20
            low_margin_data.append({
                'Voucher ID': transaction['voucher_id'],
                'Supplier': transaction['supplier_name'],
                'Gross Amount': f"${transaction['gross_amount']:,.2f}",
                'Margin %': f"{transaction['margin_percentage']:.1f}%" if transaction['margin_percentage'] else "N/A"
            })
        
        if low_margin_data:
            st.dataframe(pd.DataFrame(low_margin_data), use_container_width=True)
    
    if results['negative_margin_transactions']:
        st.subheader("🚨 Negative Margin Transactions")
        negative_margin_data = []
        for transaction in results['negative_margin_transactions'][:20]:
            negative_margin_data.append({
                'Voucher ID': transaction['voucher_id'],
                'Supplier': transaction['supplier_name'],
                'Gross Amount': f"${transaction['gross_amount']:,.2f}",
                'Margin %': f"{transaction['margin_percentage']:.1f}%" if transaction['margin_percentage'] else "N/A"
            })
        
        if negative_margin_data:
            st.dataframe(pd.DataFrame(negative_margin_data), use_container_width=True)


def display_trend_analysis(results):
    """Display month-over-month trend analysis."""
    st.subheader("Month-over-Month Profitability Trends")
    
    if results['monthly_trends']:
        # Create trends dataframe
        trend_data = []
        for trend in results['monthly_trends']:
            trend_data.append({
                'Month': trend['year_month'],
                'Supplier': trend['supplier_name'],
                'Gross Amount': trend['total_gross_amount'],
                'Profit Margin %': trend['profit_margin'] if trend['profit_margin'] else 0,
                'Transaction Count': trend['transaction_count']
            })
        
        df = pd.DataFrame(trend_data)
        
        # Line chart for trends
        fig = px.line(
            df.groupby('Month')['Gross Amount'].sum().reset_index(),
            x='Month',
            y='Gross Amount',
            title="Monthly Gross Amount Trend"
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Top suppliers trend
        top_suppliers = df.groupby('Supplier')['Gross Amount'].sum().nlargest(5).index
        supplier_trends = df[df['Supplier'].isin(top_suppliers)]
        
        fig2 = px.line(
            supplier_trends,
            x='Month',
            y='Gross Amount',
            color='Supplier',
            title="Top 5 Suppliers - Monthly Trends"
        )
        st.plotly_chart(fig2, use_container_width=True)


def display_detailed_results(results):
    """Display detailed analysis results."""
    st.subheader("Detailed Analysis Information")

    # Debug information (can be removed later)
    with st.expander("🔍 Debug Information", expanded=False):
        st.write("**Available result keys:**", list(results.keys()))
        st.write("**Assumptions count:**", len(results.get('assumptions_used', [])))
        st.write("**Warnings count:**", len(results.get('warnings', [])))
        st.write("**Final report length:**", len(results.get('final_report', '')))

    # Assumptions and warnings
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📝 Assumptions Made")
        assumptions = results.get('assumptions_used', [])
        if assumptions:
            for assumption in assumptions:
                st.info(assumption)
        else:
            st.info("No specific assumptions were made during analysis.")

    with col2:
        st.subheader("⚠️ Warnings")
        warnings = results.get('warnings', [])
        if warnings:
            for warning in warnings:
                st.warning(warning)
        else:
            st.success("No warnings detected during analysis.")
    
    # Download results
    st.subheader("💾 Download Results")
    
    # Convert results to JSON for download
    results_json = json.dumps(results, indent=2, default=str)
    st.download_button(
        label="📥 Download Analysis Results (JSON)",
        data=results_json,
        file_name=f"financial_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )


def display_final_report(results):
    """Display the comprehensive AI-generated final report."""
    st.subheader("📄 Comprehensive Financial Analysis Report")

    final_report = results.get('final_report', '')

    if final_report and final_report.strip():
        # Display the report in a nice formatted way
        st.markdown("### Executive Summary")
        st.info("This comprehensive report was generated by AI agents analyzing your financial data.")

        # Display the full report
        st.markdown("---")
        st.markdown(final_report)

        # Add download option for the report
        st.markdown("---")
        st.subheader("💾 Download Report")

        # Create a formatted report for download
        formatted_report = f"""
# Financial Analysis Report
**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**File:** {results.get('file_name', 'Unknown')}

---

{final_report}

---

**Analysis Metadata:**
- Total Records Processed: {results.get('total_records_processed', 0):,}
- Total Suppliers: {results.get('total_suppliers', 0):,}
- Total Gross Amount: ${results.get('total_gross_amount', 0):,.2f}
- Processing Time: {results.get('processing_time_seconds', 0):.1f} seconds

**Assumptions Used:**
{chr(10).join(f"- {assumption}" for assumption in results.get('assumptions_used', []))}

**Warnings:**
{chr(10).join(f"- {warning}" for warning in results.get('warnings', []))}
        """

        st.download_button(
            label="📥 Download Full Report (Markdown)",
            data=formatted_report,
            file_name=f"financial_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            mime="text/markdown"
        )

    else:
        st.warning("⚠️ No comprehensive report available.")
        st.info("""
        **Possible reasons:**
        - The file was processed using batch processing (for large files > 500 records)
        - There was an error generating the AI report
        - The analysis used simplified processing methods

        **Note:** Large files use simplified processing to avoid API limits, which may not include the comprehensive AI-generated report.
        """)


def prepare_session_data_for_dashboard(analysis_results):
    """Prepare analysis results data for dashboard generation."""
    import json

    # Extract relevant data from analysis results
    data_rows = []

    # Get supplier summaries
    supplier_summaries = analysis_results.get('supplier_summaries', [])
    for supplier in supplier_summaries:
        data_rows.append({
            'supplier': supplier.get('name', ''),
            'gross_amount': supplier.get('total_gross_amount', 0),
            'voucher_count': supplier.get('voucher_count', 0),
            'avg_amount': supplier.get('average_amount', 0)
        })

    # If we have voucher summaries, add product/concept data
    voucher_summaries = analysis_results.get('voucher_summaries', [])
    for voucher in voucher_summaries[:50]:  # Limit to avoid token issues
        data_rows.append({
            'supplier': voucher.get('supplier', ''),
            'product': voucher.get('concept', voucher.get('description', 'Unknown')),
            'gross_amount': voucher.get('gross_amount', 0),
            'voucher': voucher.get('voucher_number', ''),
            'date': voucher.get('date', '')
        })

    # Convert to JSON string
    return json.dumps(data_rows)


def display_kpi_dashboard():
    """Display KPI dashboard generation interface."""
    st.subheader("🎯 KPI Dashboard Generator")
    st.markdown("""
    Generate custom dashboards based on your KPI queries using the data from your current analysis session.
    The AI will create multiple visualizations including supplier analysis, product analysis, summary metrics, and trends.
    """)

    # Check if we have analysis results in session
    if not st.session_state.analysis_results:
        st.warning("⚠️ No analysis data available. Please upload and analyze a file first in the main analysis section.")
        st.info("👆 Go to the file upload section above, upload your Excel file, and click 'Analyze' to get started.")
        return

    # Show current data info
    results = st.session_state.analysis_results
    st.success(f"✅ Using data from current analysis session")

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Records", results.get('total_records', 'N/A'))
    with col2:
        st.metric("Suppliers", len(results.get('supplier_summaries', [])))
    with col3:
        st.metric("Processing Time", f"{results.get('processing_time_seconds', 0):.1f}s")

    # KPI query input
    kpi_query = st.text_input(
        "Enter your KPI query",
        placeholder="e.g., 'income', 'profit dashboard', 'show revenue by supplier'",
        help="Describe what KPI you want to analyze. Examples: income, profit, cost, volume",
        key="kpi_query_input"
    )

    # Generate dashboard button
    if st.button("🚀 Generate Dashboard", type="primary"):
        if not kpi_query.strip():
            st.error("Please enter a KPI query.")
            return

        # Check API health
        if not check_api_health():
            st.error("❌ Backend API is not available. Please ensure the FastAPI server is running on port 8000.")
            return

        with st.spinner(f"🤖 Generating dashboard for '{kpi_query}'..."):
            try:
                # Prepare session data - convert analysis results to DataFrame format
                session_data = prepare_session_data_for_dashboard(results)

                # Make API request using session data
                data = {
                    "kpi_query": kpi_query,
                    "session_data": session_data
                }

                response = requests.post(
                    f"{API_BASE_URL}/generate-dashboard-from-session",
                    data=data,
                    timeout=180  # 3 minutes timeout for dashboard generation with rate limiting
                )

                if response.status_code == 200:
                    dashboard_result = response.json()

                    if dashboard_result.get("success"):
                        st.success("✅ Dashboard generated successfully!")

                        # Display file info
                        file_info = dashboard_result.get("file_info", {})
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Records Analyzed", file_info.get("records", "N/A"))
                        with col2:
                            st.metric("Data Columns", len(file_info.get("columns", [])))
                        with col3:
                            st.metric("KPI", dashboard_result["data"].get("kpi", "N/A").title())

                        # Display dashboard results
                        dashboard_data = dashboard_result["data"]

                        if dashboard_data.get("success"):
                            st.subheader("🤖 AI Agent Dashboard Analysis")

                            # Display agent insights first
                            final_summary = dashboard_data.get("final_summary", "")
                            if final_summary:
                                st.success("**AI Agent Executive Summary:**")
                                st.markdown(final_summary)

                            # Display batch processing info
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("Batches Processed", dashboard_data.get('batches_processed', 0))
                            with col2:
                                st.metric("Successful Batches", dashboard_data.get('successful_batches', 0))
                            with col3:
                                st.metric("Records Analyzed", dashboard_data.get('total_records_analyzed', 0))
                            with col4:
                                st.metric("Charts Generated", dashboard_data.get('charts_generated', 0))

                            # Display agent insights from each batch
                            agent_insights = dashboard_data.get("agent_insights", [])
                            if agent_insights:
                                with st.expander("🔍 Detailed Agent Analysis by Batch"):
                                    for i, insight in enumerate(agent_insights):
                                        st.markdown(f"**{insight}**")
                                        if i < len(agent_insights) - 1:
                                            st.divider()

                            st.subheader("📊 Generated Dashboard Charts")

                            # Display charts if available
                            charts = dashboard_data.get("charts", {})

                            if charts:
                                # Create tabs for different chart types
                                chart_tabs = st.tabs(["📈 Supplier Analysis", "📦 Product Analysis", "📊 Summary", "📉 Trends"])

                                with chart_tabs[0]:
                                    st.markdown("**Supplier-based KPI Analysis**")
                                    supplier_chart = charts.get("supplier_chart", "")
                                    if supplier_chart and not str(supplier_chart).startswith("Error:"):
                                        try:
                                            import plotly.graph_objects as go
                                            import json
                                            fig_data = json.loads(supplier_chart)
                                            fig = go.Figure(fig_data)
                                            st.plotly_chart(fig, use_container_width=True)
                                        except Exception as e:
                                            st.warning(f"Chart rendering issue: {str(e)}")
                                            st.text_area("Supplier Chart Data", supplier_chart, height=200, key="supplier_chart_data")
                                    else:
                                        st.info("Supplier chart will be generated in the first batch of processing.")

                                with chart_tabs[1]:
                                    st.markdown("**Product-based KPI Analysis**")
                                    product_chart = charts.get("product_chart", "")
                                    if product_chart and not str(product_chart).startswith("Error:"):
                                        try:
                                            import plotly.graph_objects as go
                                            import json
                                            fig_data = json.loads(product_chart)
                                            fig = go.Figure(fig_data)
                                            st.plotly_chart(fig, use_container_width=True)
                                        except Exception as e:
                                            st.warning(f"Chart rendering issue: {str(e)}")
                                            st.text_area("Product Chart Data", product_chart, height=200, key="product_chart_data")
                                    else:
                                        st.info("Product chart will be generated in the second batch of processing.")

                                with chart_tabs[2]:
                                    st.markdown("**Summary Metrics**")
                                    summary_chart = charts.get("summary_chart", "")
                                    if summary_chart and not str(summary_chart).startswith("Error:"):
                                        try:
                                            import plotly.graph_objects as go
                                            import json
                                            fig_data = json.loads(summary_chart)
                                            fig = go.Figure(fig_data)
                                            st.plotly_chart(fig, use_container_width=True)
                                        except Exception as e:
                                            st.warning(f"Chart rendering issue: {str(e)}")
                                            st.text_area("Summary Chart Data", summary_chart, height=200, key="summary_chart_data")
                                    else:
                                        st.info("Summary chart will be generated in the final batch of processing.")

                                with chart_tabs[3]:
                                    st.markdown("**Trend Analysis**")
                                    trend_chart = charts.get("trend_chart", "")
                                    if trend_chart and not str(trend_chart).startswith("Error:"):
                                        try:
                                            import plotly.graph_objects as go
                                            import json
                                            fig_data = json.loads(trend_chart)
                                            fig = go.Figure(fig_data)
                                            st.plotly_chart(fig, use_container_width=True)
                                        except Exception as e:
                                            st.warning(f"Chart rendering issue: {str(e)}")
                                            st.text_area("Trend Chart Data", trend_chart, height=200, key="trend_chart_data")
                                    else:
                                        st.info("Trend chart will be generated in the final batch of processing.")

                            # Display technical summary
                            st.info(f"""
                            **Technical Summary:**
                            - KPI Analyzed: {dashboard_data.get('kpi', 'N/A').title()}
                            - Processing Method: Batch Processing with AI Agent Analysis
                            - Data Source: Current analysis session
                            - Agent Model: LangChain with Groq LLaMA 3.1 8B
                            """)

                        else:
                            st.error(f"Dashboard generation failed: {dashboard_data.get('error', 'Unknown error')}")

                    else:
                        st.error(f"Dashboard generation failed: {dashboard_result.get('message', 'Unknown error')}")

                else:
                    error_detail = response.json().get("detail", "Unknown error") if response.headers.get("content-type") == "application/json" else response.text
                    st.error(f"API Error ({response.status_code}): {error_detail}")

            except requests.exceptions.Timeout:
                st.error("⏱️ Request timed out. Dashboard generation is taking longer than expected.")
            except requests.exceptions.ConnectionError:
                st.error("🔌 Connection error. Please check if the backend server is running.")
            except Exception as e:
                st.error(f"❌ Error generating dashboard: {str(e)}")

    # Example queries
    st.markdown("---")
    st.subheader("💡 Example KPI Queries")

    examples = [
        "income - Generate income analysis with supplier and product breakdowns",
        "profit dashboard - Create comprehensive profit analysis",
        "show revenue by supplier - Focus on supplier revenue analysis",
        "cost analysis - Analyze cost patterns and trends",
        "volume metrics - Analyze quantity and volume data"
    ]

    for example in examples:
        if st.button(f"📝 {example}", key=f"example_{example[:10]}"):
            st.session_state.kpi_query = example.split(" - ")[0]
            st.rerun()


if __name__ == "__main__":
    main()
