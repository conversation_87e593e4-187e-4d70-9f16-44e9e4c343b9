# 🔌 API Documentation

Complete REST API reference for the Financial Analysis Project backend.

## 🌐 Base URL
```
http://localhost:8000
```

## 📋 API Overview

The Financial Analysis API provides endpoints for uploading Excel files and performing comprehensive financial analysis using AI-powered pandas agents.

### Authentication
Currently, no authentication is required. API key authentication may be added in future versions.

### Content Types
- **Request**: `multipart/form-data` (for file uploads)
- **Response**: `application/json`

## 🔍 Endpoints

### 1. Health Check

#### `GET /health`
Check if the API service is running and healthy.

**Response:**
```json
{
  "status": "healthy",
  "service": "financial-analysis-api"
}
```

**Status Codes:**
- `200`: Service is healthy
- `503`: Service unavailable

---

### 2. Root Information

#### `GET /`
Get basic API information and available endpoints.

**Response:**
```json
{
  "message": "Financial Analysis API",
  "version": "1.0.0",
  "endpoints": {
    "analyze": "POST /analyze - Upload Excel file for financial analysis",
    "health": "GET /health - Health check endpoint"
  }
}
```

---

### 3. Analysis Capabilities

#### `GET /analysis-info`
Get information about analysis capabilities and requirements.

**Response:**
```json
{
  "supported_formats": [".xlsx", ".xls", ".csv"],
  "max_file_size_mb": 50,
  "analysis_features": [
    "Total gross amount per supplier and voucher",
    "Margin calculations with configurable cost assumptions",
    "Low-margin transaction identification",
    "Negative-margin transaction highlighting",
    "Month-over-month profitability trends",
    "Supplier performance analysis"
  ],
  "required_columns": [
    "supplier (or similar: vendor, proveedor)",
    "voucher (or similar: invoice, factura, comprobante)",
    "gross_amount (or similar: amount, total, importe)"
  ],
  "optional_columns": [
    "cost (or similar: cost_amount, costo)",
    "date (or similar: transaction_date, fecha)"
  ],
  "default_parameters": {
    "assume_cost_percentage": 70.0,
    "low_margin_threshold": 10.0
  }
}
```

---

### 4. Single File Analysis

#### `POST /analyze`
Upload and analyze a single Excel file.

**Request Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file` | File | Yes | - | Excel or CSV file to analyze |
| `assume_cost_percentage` | Float | No | 70.0 | Cost percentage when missing |
| `low_margin_threshold` | Float | No | 10.0 | Low margin threshold (%) |

**Request Example:**
```bash
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@financial_data.xlsx" \
  -F "assume_cost_percentage=75.0" \
  -F "low_margin_threshold=8.0"
```

**Response Schema:**
```json
{
  "success": true,
  "message": "Financial analysis completed successfully",
  "data": {
    "analysis_timestamp": "2024-01-06T10:30:00",
    "file_name": "financial_data.xlsx",
    "total_records_processed": 507,
    "total_gross_amount": 12847392.45,
    "total_suppliers": 12,
    "total_vouchers": 507,
    "supplier_summaries": [...],
    "voucher_summaries": [...],
    "monthly_trends": [...],
    "low_margin_transactions": [...],
    "negative_margin_transactions": [...],
    "assumptions_used": [...],
    "warnings": [...],
    "processing_time_seconds": 45.2
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Analysis failed",
  "error_details": {
    "error": "Missing required columns: ['supplier']",
    "type": "ExcelProcessingError"
  }
}
```

**Status Codes:**
- `200`: Analysis completed successfully
- `400`: Bad request (invalid file, missing parameters)
- `413`: File too large
- `422`: Validation error
- `500`: Internal server error

---

### 5. Batch File Analysis

#### `POST /analyze-batch`
Upload and analyze multiple Excel files in batch.

**Request Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `files` | File[] | Yes | - | Array of Excel/CSV files (max 10) |
| `assume_cost_percentage` | Float | No | 70.0 | Cost percentage when missing |
| `low_margin_threshold` | Float | No | 10.0 | Low margin threshold (%) |

**Request Example:**
```bash
curl -X POST "http://localhost:8000/analyze-batch" \
  -F "files=@file1.xlsx" \
  -F "files=@file2.xlsx" \
  -F "assume_cost_percentage=70.0"
```

**Response:**
```json
{
  "results": [
    {
      "success": true,
      "message": "Analysis completed for file1.xlsx",
      "data": { ... }
    },
    {
      "success": false,
      "message": "Analysis failed for file2.xlsx",
      "error_details": { ... }
    }
  ]
}
```

---

## 📊 Data Models

### FinancialAnalysisResult
Complete analysis result structure.

```json
{
  "analysis_timestamp": "2024-01-06T10:30:00",
  "file_name": "string",
  "total_records_processed": 507,
  "total_gross_amount": 12847392.45,
  "total_suppliers": 12,
  "total_vouchers": 507,
  "supplier_summaries": [SupplierSummary],
  "voucher_summaries": [VoucherSummary],
  "monthly_trends": [MonthlyTrend],
  "low_margin_transactions": [VoucherSummary],
  "negative_margin_transactions": [VoucherSummary],
  "assumptions_used": ["string"],
  "warnings": ["string"],
  "processing_time_seconds": 45.2
}
```

### SupplierSummary
Supplier-level analysis summary.

```json
{
  "supplier_name": "ABC Corporation",
  "total_gross_amount": 125000.50,
  "total_transactions": 25,
  "average_margin": 23.5,
  "low_margin_transactions": 2,
  "negative_margin_transactions": 0
}
```

### VoucherSummary
Individual transaction details.

```json
{
  "voucher_id": "INV-001",
  "supplier_name": "ABC Corporation",
  "gross_amount": 10000.00,
  "cost_amount": 7000.00,
  "margin_percentage": 30.0,
  "is_low_margin": false,
  "is_negative_margin": false,
  "transaction_date": "2024-01-15T00:00:00"
}
```

### MonthlyTrend
Monthly trend analysis data.

```json
{
  "year_month": "2024-01",
  "supplier_name": "ABC Corporation",
  "total_gross_amount": 45000.00,
  "total_cost": 31500.00,
  "profit_margin": 30.0,
  "transaction_count": 8
}
```

## ⚠️ Error Handling

### Error Response Format
```json
{
  "success": false,
  "message": "Human-readable error message",
  "error_details": {
    "error": "Detailed error description",
    "type": "ErrorType"
  }
}
```

### Common Error Types

#### File Validation Errors
- **UnsupportedFileType**: File format not supported
- **FileTooLarge**: File exceeds size limit
- **EmptyFile**: No content in uploaded file

#### Data Processing Errors
- **ExcelProcessingError**: Cannot read or process Excel file
- **MissingRequiredColumns**: Required columns not found
- **InvalidDataFormat**: Data format issues

#### Analysis Errors
- **AnalysisTimeout**: Processing took too long
- **InsufficientData**: Not enough data for analysis
- **LLMError**: AI model processing error

## 🔧 Configuration

### File Upload Limits
- **Maximum file size**: 50MB
- **Supported formats**: .xlsx, .xls, .csv
- **Maximum batch size**: 10 files

### Analysis Parameters
- **assume_cost_percentage**: 0.0 - 100.0 (default: 70.0)
- **low_margin_threshold**: 0.0 - 50.0 (default: 10.0)

### Timeout Settings
- **Analysis timeout**: 300 seconds (5 minutes)
- **File upload timeout**: 60 seconds

## 📝 Usage Examples

### Python Client Example
```python
import requests

# Single file analysis
with open('financial_data.xlsx', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/analyze',
        files={'file': f},
        data={
            'assume_cost_percentage': 75.0,
            'low_margin_threshold': 8.0
        }
    )

if response.status_code == 200:
    result = response.json()
    if result['success']:
        analysis_data = result['data']
        print(f"Processed {analysis_data['total_records_processed']} records")
    else:
        print(f"Analysis failed: {result['message']}")
```

### JavaScript Client Example
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('assume_cost_percentage', '70.0');

fetch('http://localhost:8000/analyze', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Analysis completed:', data.data);
    } else {
        console.error('Analysis failed:', data.message);
    }
});
```

---

**Next**: Check out the [LangChain Agents Documentation](langchain-agents.md) to understand the AI analysis components.
