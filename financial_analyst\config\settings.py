"""
Configuration settings for the Database Analyst System.
Centralized configuration management with environment variable support.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    url: str = os.getenv('DATABASE_URL', 'sqlite:///financial_data.db')
    pool_size: int = int(os.getenv('DB_POOL_SIZE', '5'))
    max_overflow: int = int(os.getenv('DB_MAX_OVERFLOW', '10'))
    pool_timeout: int = int(os.getenv('DB_POOL_TIMEOUT', '30'))
    pool_recycle: int = int(os.getenv('DB_POOL_RECYCLE', '3600'))
    echo: bool = os.getenv('DB_ECHO', 'False').lower() == 'true'

@dataclass
class LLMConfig:
    """LLM configuration settings."""
    groq_api_key: str = os.getenv('GROQ_API_KEY', '')
    default_model: str = os.getenv('DEFAULT_LLM_MODEL', 'llama-3.3-70b-versatile')
    temperature: float = float(os.getenv('LLM_TEMPERATURE', '0'))
    max_tokens: int = int(os.getenv('LLM_MAX_TOKENS', '4000'))

@dataclass
class DashboardConfig:
    """Dashboard configuration settings."""
    default_theme: str = os.getenv('DASHBOARD_THEME', 'plotly_white')
    auto_refresh: bool = os.getenv('DASHBOARD_AUTO_REFRESH', 'False').lower() == 'true'
    refresh_interval: int = int(os.getenv('DASHBOARD_REFRESH_INTERVAL', '300'))
    max_charts: int = int(os.getenv('DASHBOARD_MAX_CHARTS', '10'))
    default_layout: str = os.getenv('DASHBOARD_DEFAULT_LAYOUT', 'two_column')

@dataclass
class AppConfig:
    """Application configuration settings."""
    debug: bool = os.getenv('DEBUG', 'False').lower() == 'true'
    log_level: str = os.getenv('LOG_LEVEL', 'INFO')
    session_timeout: int = int(os.getenv('SESSION_TIMEOUT', '3600'))
    max_concurrent_workflows: int = int(os.getenv('MAX_CONCURRENT_WORKFLOWS', '10'))
    enable_caching: bool = os.getenv('ENABLE_CACHING', 'True').lower() == 'true'

@dataclass
class SecurityConfig:
    """Security configuration settings."""
    secret_key: str = os.getenv('SECRET_KEY', 'your-secret-key-here')
    enable_auth: bool = os.getenv('ENABLE_AUTH', 'False').lower() == 'true'
    max_query_length: int = int(os.getenv('MAX_QUERY_LENGTH', '10000'))
    rate_limit_per_minute: int = int(os.getenv('RATE_LIMIT_PER_MINUTE', '60'))

class Settings:
    """Main settings class that combines all configuration."""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.llm = LLMConfig()
        self.dashboard = DashboardConfig()
        self.app = AppConfig()
        self.security = SecurityConfig()
        
        # Validate critical settings
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate critical configuration settings."""
        if not self.llm.groq_api_key:
            raise ValueError("GROQ_API_KEY environment variable is required")
        
        if self.database.pool_size <= 0:
            raise ValueError("Database pool size must be positive")
        
        if self.dashboard.refresh_interval < 30:
            raise ValueError("Dashboard refresh interval must be at least 30 seconds")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return {
            'database': self.database.__dict__,
            'llm': self.llm.__dict__,
            'dashboard': self.dashboard.__dict__,
            'app': self.app.__dict__,
            'security': self.security.__dict__
        }
    
    def get_database_url(self) -> str:
        """Get database URL with proper formatting."""
        return self.database.url
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration for agent initialization."""
        return {
            'api_key': self.llm.groq_api_key,
            'model': self.llm.default_model,
            'temperature': self.llm.temperature,
            'max_tokens': self.llm.max_tokens
        }
    
    def get_dashboard_config(self) -> Dict[str, Any]:
        """Get dashboard configuration."""
        return {
            'theme': self.dashboard.default_theme,
            'auto_refresh': self.dashboard.auto_refresh,
            'refresh_interval': self.dashboard.refresh_interval,
            'max_charts': self.dashboard.max_charts,
            'default_layout': self.dashboard.default_layout
        }

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings
