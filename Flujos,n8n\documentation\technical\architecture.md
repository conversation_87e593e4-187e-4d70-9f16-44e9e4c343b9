# System Architecture

This document provides a comprehensive overview of the system architecture for both the Financial Analysis Project and Stock Analysis Workflow.

## High-Level Architecture

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI1[Streamlit Frontend]
        UI2[REST API Clients]
        UI3[Direct Python Scripts]
    end
    
    subgraph "API Layer"
        API[FastAPI Backend]
        CORS[CORS Middleware]
        AUTH[Authentication]
    end
    
    subgraph "Business Logic Layer"
        SVC1[Financial Analysis Service]
        SVC2[Stock Analysis Service]
        SVC3[File Processing Service]
    end
    
    subgraph "Agent Orchestration Layer"
        WF1[Financial Analysis Workflow]
        WF2[Stock Analysis Workflow]
        STATE[StateGraph Management]
    end
    
    subgraph "AI Agents Layer"
        AG1[Excel Processor Agent]
        AG2[Gross Amount Analyzer]
        AG3[Margin Analyzer]
        AG4[Trend Analyzer]
        AG5[Report Generator]
        AG6[Query Processor Agent]
        AG7[Data Analysis Agent]
        AG8[Financial Advisor Agent]
    end
    
    subgraph "External Services"
        GROQ[Groq AI Models]
        YAHOO[Yahoo Finance API]
        DDG[DuckDuckGo Search]
    end
    
    subgraph "Data Layer"
        EXCEL[Excel Files]
        TEMP[Temporary Storage]
        CACHE[Result Cache]
    end
    
    UI1 --> API
    UI2 --> API
    UI3 --> SVC1
    UI3 --> SVC2
    
    API --> SVC1
    API --> SVC3
    
    SVC1 --> WF1
    SVC2 --> WF2
    
    WF1 --> AG1
    WF1 --> AG2
    WF1 --> AG3
    WF1 --> AG4
    WF1 --> AG5
    
    WF2 --> AG6
    WF2 --> AG7
    WF2 --> AG8
    
    AG1 --> GROQ
    AG2 --> GROQ
    AG3 --> GROQ
    AG4 --> GROQ
    AG5 --> GROQ
    AG6 --> GROQ
    AG7 --> GROQ
    AG8 --> GROQ
    
    AG7 --> YAHOO
    AG8 --> DDG
    
    SVC1 --> EXCEL
    SVC3 --> TEMP
    WF1 --> CACHE
    WF2 --> CACHE
```

## Component Architecture

### 1. Financial Analysis Project Architecture

```mermaid
graph TB
    subgraph "Frontend (Streamlit)"
        ST_UI[Streamlit UI]
        ST_COMP[UI Components]
        ST_STATE[Session State]
    end
    
    subgraph "Backend (FastAPI)"
        API_MAIN[main.py]
        API_MODELS[Pydantic Models]
        API_ROUTES[API Routes]
    end
    
    subgraph "Services Layer"
        FILE_SVC[File Service]
        ANALYSIS_SVC[Analysis Service]
        VALIDATION_SVC[Validation Service]
    end
    
    subgraph "Agents Layer"
        EXCEL_AGENT[Excel Processor]
        GROSS_AGENT[Gross Amount Analyzer]
        MARGIN_AGENT[Margin Analyzer]
        TREND_AGENT[Trend Analyzer]
        REPORT_AGENT[Report Generator]
    end
    
    subgraph "StateGraph Workflow"
        GRAPH[Financial Analysis Graph]
        NODES[Workflow Nodes]
        EDGES[Execution Flow]
        STATE_MGR[State Manager]
    end
    
    ST_UI --> API_MAIN
    API_MAIN --> API_ROUTES
    API_ROUTES --> FILE_SVC
    API_ROUTES --> ANALYSIS_SVC
    
    ANALYSIS_SVC --> GRAPH
    GRAPH --> EXCEL_AGENT
    GRAPH --> GROSS_AGENT
    GRAPH --> MARGIN_AGENT
    GRAPH --> TREND_AGENT
    GRAPH --> REPORT_AGENT
    
    STATE_MGR --> NODES
    NODES --> EDGES
```

### 2. Stock Analysis Workflow Architecture

```mermaid
graph TB
    subgraph "Entry Point"
        SCRIPT[ejemplo.py]
        MAIN[Main Function]
    end
    
    subgraph "StateGraph Workflow"
        STOCK_GRAPH[Stock Analysis Graph]
        STOCK_STATE[Estado State Management]
    end
    
    subgraph "Processing Agents"
        QUERY_AGENT[Query Processor]
        DATA_AGENT[Data Analysis Agent]
        ADVISOR_AGENT[Financial Advisor]
    end
    
    subgraph "Data Sources"
        YAHOO_API[Yahoo Finance]
        NEWS_API[DuckDuckGo News]
    end
    
    subgraph "External Tools"
        FINANCE_TOOL[Financial Data Tool]
        NEWS_TOOL[News Search Tool]
    end
    
    SCRIPT --> MAIN
    MAIN --> STOCK_GRAPH
    STOCK_GRAPH --> QUERY_AGENT
    STOCK_GRAPH --> DATA_AGENT
    STOCK_GRAPH --> ADVISOR_AGENT
    
    DATA_AGENT --> FINANCE_TOOL
    ADVISOR_AGENT --> NEWS_TOOL
    
    FINANCE_TOOL --> YAHOO_API
    NEWS_TOOL --> NEWS_API
```

## Data Flow Architecture

### Financial Analysis Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Service
    participant Workflow
    participant Agents
    participant AI
    
    User->>Frontend: Upload Excel File
    Frontend->>API: POST /analyze
    API->>Service: Process File
    Service->>Workflow: Initialize StateGraph
    
    Workflow->>Agents: Process Excel Data
    Agents->>AI: Validate & Clean Data
    AI-->>Agents: Processed Data
    
    par Parallel Analysis
        Workflow->>Agents: Analyze Suppliers
        Agents->>AI: Calculate Supplier Totals
        AI-->>Agents: Supplier Analysis
    and
        Workflow->>Agents: Analyze Vouchers
        Agents->>AI: Calculate Voucher Totals
        AI-->>Agents: Voucher Analysis
    end
    
    Workflow->>Agents: Calculate Margins
    Agents->>AI: Margin Analysis
    AI-->>Agents: Margin Results
    
    Workflow->>Agents: Analyze Trends
    Agents->>AI: Trend Analysis
    AI-->>Agents: Trend Results
    
    Workflow->>Agents: Generate Report
    Agents->>AI: Synthesize Results
    AI-->>Agents: Final Report
    
    Agents-->>Workflow: Complete Analysis
    Workflow-->>Service: Analysis Results
    Service-->>API: JSON Response
    API-->>Frontend: Analysis Data
    Frontend-->>User: Display Results
```

### Stock Analysis Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Script
    participant Workflow
    participant Agents
    participant Yahoo
    participant News
    participant AI
    
    User->>Script: Run Analysis Query
    Script->>Workflow: Initialize Estado
    
    Workflow->>Agents: Extract Ticker
    Agents->>AI: Parse Query
    AI-->>Agents: Ticker Symbol
    
    par Parallel Data Collection
        Workflow->>Agents: Get Financial Data
        Agents->>Yahoo: Fetch Historical Data
        Yahoo-->>Agents: Stock Data
    and
        Workflow->>Agents: Get News
        Agents->>News: Search Recent News
        News-->>Agents: News Articles
    end
    
    Workflow->>Agents: Analyze Data
    Agents->>AI: Technical Analysis
    AI-->>Agents: Analysis Results
    
    Workflow->>Agents: Generate Report
    Agents->>AI: Synthesize All Data
    AI-->>Agents: Final Report
    
    Agents-->>Workflow: Complete Analysis
    Workflow-->>Script: Final Results
    Script-->>User: Display Report
```

## Technology Stack

### Core Technologies
- **Python 3.8+**: Primary programming language
- **LangChain**: AI agent framework
- **LangGraph**: Workflow orchestration
- **FastAPI**: REST API framework
- **Streamlit**: Web UI framework
- **Pandas**: Data processing
- **OpenPyXL**: Excel file handling

### AI/ML Stack
- **Groq**: AI model hosting platform
- **ChatGroq**: LLM integration
- **Models Used**:
  - `llama-3.1-8b-instant`: Fast processing tasks
  - `llama3-70b-8192`: Complex analysis tasks
  - `gemma2-9b-it`: Report generation

### External APIs
- **Yahoo Finance**: Historical stock data
- **DuckDuckGo Search**: Financial news
- **Groq API**: AI model inference

### Development Tools
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **pytest**: Testing framework
- **uvicorn**: ASGI server

## Security Architecture

### API Security
```mermaid
graph LR
    CLIENT[Client] --> CORS[CORS Middleware]
    CORS --> RATE[Rate Limiting]
    RATE --> AUTH[Authentication]
    AUTH --> VALID[Input Validation]
    VALID --> API[API Endpoints]
```

### Data Security
- **File Upload Validation**: Type and size restrictions
- **Input Sanitization**: SQL injection prevention
- **API Key Management**: Environment variable storage
- **Temporary File Cleanup**: Automatic cleanup of uploaded files

### AI Security
- **Prompt Injection Protection**: Input sanitization
- **Code Execution Safety**: Controlled pandas agent execution
- **Rate Limiting**: API call throttling
- **Error Handling**: Graceful failure modes

## Scalability Considerations

### Horizontal Scaling
- **Stateless Design**: No server-side session storage
- **Microservices Ready**: Modular component architecture
- **Container Support**: Docker-based deployment
- **Load Balancer Ready**: Multiple instance support

### Performance Optimization
- **Async Processing**: FastAPI async endpoints
- **Parallel Workflows**: Concurrent agent execution
- **Caching Strategy**: Result caching for repeated analyses
- **Resource Management**: Memory-efficient data processing

### Monitoring & Observability
- **Logging**: Structured logging throughout the system
- **Health Checks**: API health monitoring endpoints
- **Error Tracking**: Comprehensive error handling and reporting
- **Performance Metrics**: Response time and throughput monitoring

## Deployment Architecture

### Development Environment
```
Local Machine
├── Python Virtual Environment
├── Local File Storage
├── Environment Variables (.env)
└── Development Servers (uvicorn, streamlit)
```

### Production Environment
```
Production Server
├── Docker Containers
│   ├── Backend Container (FastAPI)
│   ├── Frontend Container (Streamlit)
│   └── Reverse Proxy (nginx)
├── Persistent Storage
├── Environment Variables
└── Process Management (systemd/supervisor)
```

### Cloud Deployment Options
- **AWS**: ECS, Lambda, S3
- **Google Cloud**: Cloud Run, Cloud Storage
- **Azure**: Container Instances, Blob Storage
- **Docker**: Any Docker-compatible platform

## Next Steps

- [API Reference](./api-reference.md) - Detailed API documentation
- [Security](./security.md) - Security implementation details
- [Deployment](../development/deployment.md) - Production deployment guide
