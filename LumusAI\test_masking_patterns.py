#!/usr/bin/env python3
"""
Test script to verify masking patterns work correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.langchain_client import create_langfuse_masking_function

def test_masking_patterns():
    """Test that masking patterns work correctly."""
    
    print("🔧 Testing Masking Patterns")
    print("=" * 50)
    
    # Create masking function
    masking_function = create_langfuse_masking_function(
        mask_credit_cards=True,
        mask_emails=True,
        mask_phones=True,
        mask_secrets=True,
        mask_custom_patterns=None
    )
    
    # Test data with various sensitive information
    test_cases = [
        {
            "name": "Personal Name",
            "input": "<PERSON> is a developer",
            "should_contain": "[REDACTED_NAME]"
        },
        {
            "name": "Email Address",
            "input": "Contact <NAME_EMAIL> for more info",
            "should_contain": "[REDACTED_EMAIL]"
        },
        {
            "name": "Phone Number",
            "input": "Call me at (57) 3157357677 or +57 1 234 5678",
            "should_contain": "[REDACTED_PHONE]"
        },
        {
            "name": "LinkedIn Profile",
            "input": "My profile: https://www.linkedin.com/in/santiago-garcia-m/",
            "should_contain": "[REDACTED_LINKEDIN]"
        },
        {
            "name": "Credit Card",
            "input": "Payment with card 4111 1111 1111 1111",
            "should_contain": "[REDACTED_CREDIT_CARD]"
        },
        {
            "name": "Secret Key",
            "input": "Use SECRET_API_KEY_123 and CONFIDENTIAL_TOKEN_XYZ",
            "should_contain": "[REDACTED_SECRET]"
        },
        {
            "name": "Company Name",
            "input": "Working at TechCorp SA and ACME Corporation LTDA",
            "should_contain": "[REDACTED_COMPANY]"
        },
        {
            "name": "Address",
            "input": "Located in Cali, Valle Del Cauca, Colombia",
            "should_contain": "[REDACTED_ADDRESS]"
        },
        {
            "name": "Mixed Sensitive Data",
            "input": "Santiago García (<EMAIL>) works at TechCorp SA. Phone: (57) 3157357677",
            "should_contain": ["[REDACTED_NAME]", "[REDACTED_EMAIL]", "[REDACTED_COMPANY]", "[REDACTED_PHONE]"]
        }
    ]
    
    print("🧪 Running masking tests...")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Input: {test_case['input']}")
        
        try:
            # Apply masking
            result = masking_function({'data': test_case['input']})
            print(f"   Output: {result}")
            
            # Check if masking was applied
            if isinstance(test_case['should_contain'], list):
                # Multiple patterns expected
                all_found = True
                for pattern in test_case['should_contain']:
                    if pattern not in result:
                        all_found = False
                        break
                
                if all_found:
                    print("   ✅ PASS - All expected patterns found")
                    passed += 1
                else:
                    print("   ❌ FAIL - Not all expected patterns found")
                    failed += 1
            else:
                # Single pattern expected
                if test_case['should_contain'] in result:
                    print("   ✅ PASS - Expected pattern found")
                    passed += 1
                else:
                    print("   ❌ FAIL - Expected pattern not found")
                    failed += 1
                    
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("🎯 Masking Pattern Test Results")
    print("=" * 50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All masking patterns are working correctly!")
        print("✅ Ready to test with actual Langfuse integration")
    else:
        print(f"\n⚠️  {failed} test(s) failed - masking patterns need adjustment")
        print("🔧 Check regex patterns in create_langfuse_masking_function()")
    
    return failed == 0

def test_specific_case():
    """Test the specific case from user feedback."""
    
    print("\n🎯 Testing Specific User Case")
    print("=" * 50)
    
    # Create masking function
    masking_function = create_langfuse_masking_function(
        mask_credit_cards=True,
        mask_emails=True,
        mask_phones=True,
        mask_secrets=True,
        mask_custom_patterns=None
    )
    
    # Exact data from user feedback
    user_data = '''
    full_name: "Santiago García Martínez"
    country: ""
    city: "Cali, Valle Del Cauca"
    address: ""
    phone_number: "(57) 3157357677"
    email: "<EMAIL>"
    linkedin_profile: "https://www.linkedin.com/in/santiago-garcia-m/"
    '''
    
    print("📋 Original data:")
    print(user_data)
    
    try:
        masked_result = masking_function({'data': user_data})
        print("\n🔒 Masked result:")
        print(masked_result)
        
        # Check if sensitive data is still present
        sensitive_data_found = []
        if 'Santiago García Martínez' in masked_result:
            sensitive_data_found.append("Full name")
        if '<EMAIL>' in masked_result:
            sensitive_data_found.append("Email")
        if '3157357677' in masked_result:
            sensitive_data_found.append("Phone")
        if 'linkedin.com/in/santiago-garcia-m' in masked_result:
            sensitive_data_found.append("LinkedIn")
        
        if sensitive_data_found:
            print(f"\n❌ MASKING FAILED - Still contains: {', '.join(sensitive_data_found)}")
            return False
        else:
            print("\n✅ MASKING SUCCESS - No sensitive data found")
            return True
            
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        return False

def main():
    """Main test function."""
    
    print("🔒 Masking Pattern Verification")
    print("=" * 60)
    
    # Test general patterns
    general_success = test_masking_patterns()
    
    # Test specific user case
    specific_success = test_specific_case()
    
    print("\n" + "=" * 60)
    print("🏁 Final Results")
    print("=" * 60)
    
    if general_success and specific_success:
        print("✅ ALL TESTS PASSED!")
        print("🚀 Masking patterns are working correctly")
        print("📋 Next step: Test with actual Langfuse integration")
    else:
        print("❌ SOME TESTS FAILED")
        if not general_success:
            print("🔧 General masking patterns need fixing")
        if not specific_success:
            print("🔧 Specific user case masking needs fixing")
        print("📋 Fix patterns before testing Langfuse integration")

if __name__ == "__main__":
    main()
