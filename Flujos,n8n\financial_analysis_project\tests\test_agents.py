"""
Unit tests for the Lang<PERSON>hain pandas agents.
"""

import pytest
import pandas as pd
import sys
import os
from unittest.mock import Mock, patch

# Add the backend to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.agents.pandas_agents import (
    ExcelProcessorAgent, GrossAmountAnalyzer, MarginCalculator,
    TrendAnalyzer, FinancialReportGenerator, run_financial_analysis
)


class TestExcelProcessorAgent:
    """Test cases for the Excel processor agent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.agent = ExcelProcessorAgent()
        
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd', 'Global Inc'],
            'voucher': ['INV-001', 'INV-002', 'INV-003'],
            'gross_amount': [10000, 15000, 8000],
            'cost': [7000, None, 6400],  # One missing value
            'date': pd.date_range('2024-01-01', periods=3)
        })
    
    @patch('app.agents.pandas_agents.ChatGroq')
    def test_process_method(self, mock_llm):
        """Test the process method of ExcelProcessorAgent."""
        # Mock the LLM response
        mock_chain = Mock()
        mock_chain.invoke.return_value = '{"required_columns": ["supplier", "voucher", "gross_amount"], "issues": ["Missing cost data"], "cleaning_steps": ["Fill missing values"]}'
        
        with patch.object(self.agent, 'chain', mock_chain):
            result = self.agent.process(self.sample_data)
        
        assert 'analysis' in result
        assert 'processed_df' in result
        assert isinstance(result['processed_df'], pd.DataFrame)
        mock_chain.invoke.assert_called_once()


class TestGrossAmountAnalyzer:
    """Test cases for the gross amount analyzer agent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd', 'ABC Corp', 'Global Inc'],
            'voucher': ['INV-001', 'INV-002', 'INV-003', 'INV-004'],
            'gross_amount': [10000, 15000, 8000, 12000],
            'cost': [7000, 10500, 6400, 8400],
            'date': pd.date_range('2024-01-01', periods=4)
        })
    
    @patch('app.agents.pandas_agents.create_pandas_dataframe_agent')
    @patch('app.agents.pandas_agents.ChatGroq')
    def test_execute_method(self, mock_llm, mock_agent_creator):
        """Test the execute method of GrossAmountAnalyzer."""
        # Mock the pandas agent
        mock_agent = Mock()
        mock_agent.invoke.return_value = {"output": "Analysis complete: Total by supplier calculated"}
        mock_agent_creator.return_value = mock_agent
        
        analyzer = GrossAmountAnalyzer()
        result = analyzer.execute(self.sample_data)
        
        assert isinstance(result, str)
        assert "Analysis complete" in result
        mock_agent.invoke.assert_called_once()


class TestMarginCalculator:
    """Test cases for the margin calculator agent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd', 'Problem Corp'],
            'voucher': ['INV-001', 'INV-002', 'INV-003'],
            'gross_amount': [10000, 15000, 1000],
            'cost': [7000, 10500, 1200],  # Last one has negative margin
            'date': pd.date_range('2024-01-01', periods=3)
        })
    
    @patch('app.agents.pandas_agents.create_pandas_dataframe_agent')
    @patch('app.agents.pandas_agents.ChatGroq')
    def test_execute_with_cost_assumption(self, mock_llm, mock_agent_creator):
        """Test margin calculation with cost assumption."""
        # Mock the pandas agent
        mock_agent = Mock()
        mock_agent.invoke.return_value = {"output": "Margin analysis complete: 1 negative margin found"}
        mock_agent_creator.return_value = mock_agent
        
        calculator = MarginCalculator()
        result = calculator.execute(self.sample_data, assume_cost_percentage=75.0)
        
        assert isinstance(result, str)
        mock_agent.invoke.assert_called_once()
        
        # Check that the system message includes the cost assumption
        call_args = mock_agent.invoke.call_args[0][0]
        assert "75.0%" in str(call_args)


class TestTrendAnalyzer:
    """Test cases for the trend analyzer agent."""
    
    def setup_method(self):
        """Set up test fixtures with multi-month data."""
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp'] * 6 + ['XYZ Ltd'] * 6,
            'voucher': [f'INV-{i:03d}' for i in range(1, 13)],
            'gross_amount': [10000 + i*1000 for i in range(12)],  # Increasing trend
            'cost': [7000 + i*700 for i in range(12)],
            'date': pd.date_range('2024-01-01', periods=12, freq='M')
        })
    
    @patch('app.agents.pandas_agents.create_pandas_dataframe_agent')
    @patch('app.agents.pandas_agents.ChatGroq')
    def test_execute_trend_analysis(self, mock_llm, mock_agent_creator):
        """Test trend analysis execution."""
        # Mock the pandas agent
        mock_agent = Mock()
        mock_agent.invoke.return_value = {"output": "Trend analysis complete: ABC Corp showing growth"}
        mock_agent_creator.return_value = mock_agent
        
        analyzer = TrendAnalyzer()
        result = analyzer.execute(self.sample_data)
        
        assert isinstance(result, str)
        mock_agent.invoke.assert_called_once()


class TestFinancialReportGenerator:
    """Test cases for the financial report generator."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.generator = FinancialReportGenerator()
        
        self.sample_analyses = {
            'supplier_analysis': "ABC Corp: $18,000 total, XYZ Ltd: $15,000 total",
            'voucher_analysis': "4 vouchers processed successfully",
            'margin_analysis': "Average margin: 25%, 1 low margin transaction found",
            'trend_analysis': "Positive growth trend observed for ABC Corp",
            'assumptions': ["Cost assumed at 70% when missing"],
            'warnings': ["1 negative margin transaction detected"]
        }
    
    @patch('app.agents.pandas_agents.ChatGroq')
    def test_generate_report(self, mock_llm):
        """Test report generation."""
        # Mock the LLM response
        mock_chain = Mock()
        mock_chain.invoke.return_value = "Executive Summary: Financial analysis shows positive trends..."
        
        with patch.object(self.generator, 'chain', mock_chain):
            result = self.generator.generate(
                self.sample_analyses['supplier_analysis'],
                self.sample_analyses['voucher_analysis'],
                self.sample_analyses['margin_analysis'],
                self.sample_analyses['trend_analysis'],
                self.sample_analyses['assumptions'],
                self.sample_analyses['warnings']
            )
        
        assert isinstance(result, str)
        assert "Executive Summary" in result
        mock_chain.invoke.assert_called_once()


class TestWorkflowIntegration:
    """Test cases for the complete workflow integration."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd', 'ABC Corp', 'Global Inc'],
            'voucher': ['INV-001', 'INV-002', 'INV-003', 'INV-004'],
            'gross_amount': [10000, 15000, 8000, 12000],
            'cost': [7000, 10500, 6400, None],
            'date': pd.date_range('2024-01-01', periods=4)
        })
    
    @patch('app.agents.pandas_agents.create_pandas_dataframe_agent')
    @patch('app.agents.pandas_agents.ChatGroq')
    def test_run_financial_analysis_workflow(self, mock_llm, mock_agent_creator):
        """Test the complete financial analysis workflow."""
        # Mock all the pandas agents
        mock_agent = Mock()
        mock_agent.invoke.return_value = {"output": "Analysis complete"}
        mock_agent_creator.return_value = mock_agent
        
        # Mock the report generator chain
        mock_chain = Mock()
        mock_chain.invoke.return_value = "Complete financial analysis report"
        
        with patch('app.agents.pandas_agents.FinancialReportGenerator') as mock_generator_class:
            mock_generator = Mock()
            mock_generator.generate.return_value = "Complete financial analysis report"
            mock_generator_class.return_value = mock_generator
            
            # Run the workflow
            config = {"assume_cost_percentage": 70.0, "low_margin_threshold": 10.0}
            result = run_financial_analysis(self.sample_data, config)
        
        # Verify results structure
        assert isinstance(result, dict)
        assert 'final_report' in result
        assert 'supplier_analysis' in result
        assert 'margin_analysis' in result
        assert 'trend_analysis' in result
        assert 'assumptions' in result
        assert 'warnings' in result
        
        # Verify that the report was generated
        assert isinstance(result['final_report'], str)
    
    def test_workflow_state_management(self):
        """Test that the workflow state is managed correctly."""
        from app.agents.pandas_agents import FinancialAnalysisState
        
        # Test state structure
        initial_state = {
            "raw_data": [self.sample_data],
            "processed_data": [],
            "supplier_analysis": [],
            "voucher_analysis": [],
            "margin_analysis": [],
            "trend_analysis": [],
            "final_report": [],
            "assumptions": [],
            "warnings": [],
            "config": [{"assume_cost_percentage": 70.0}]
        }
        
        # Verify state structure matches TypedDict
        assert isinstance(initial_state["raw_data"], list)
        assert isinstance(initial_state["assumptions"], list)
        assert isinstance(initial_state["config"], list)


class TestErrorHandling:
    """Test cases for error handling in agents."""
    
    def test_empty_dataframe_handling(self):
        """Test handling of empty DataFrame."""
        empty_df = pd.DataFrame()
        
        # The workflow should handle empty data gracefully
        config = {"assume_cost_percentage": 70.0}
        
        # This should not raise an exception but may produce warnings
        try:
            result = run_financial_analysis(empty_df, config)
            assert isinstance(result, dict)
        except Exception as e:
            # If an exception is raised, it should be informative
            assert "empty" in str(e).lower() or "no data" in str(e).lower()
    
    def test_missing_columns_handling(self):
        """Test handling of DataFrames with missing required columns."""
        incomplete_df = pd.DataFrame({
            'random_column': [1, 2, 3],
            'another_column': ['a', 'b', 'c']
        })
        
        config = {"assume_cost_percentage": 70.0}
        
        # This should handle missing columns gracefully
        try:
            result = run_financial_analysis(incomplete_df, config)
            # Should still return a result structure
            assert isinstance(result, dict)
        except Exception as e:
            # Exception should be informative about missing columns
            assert any(word in str(e).lower() for word in ['column', 'missing', 'required'])


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
