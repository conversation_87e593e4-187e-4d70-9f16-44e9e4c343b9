"""
Lang<PERSON>hain pandas agents for financial analysis.
Based on the pattern from ejemplo.py but adapted for Excel financial data processing.
"""

from langgraph.graph import StateGraph, END, START
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_groq import ChatGroq
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from typing import List, TypedDict, Annotated, Optional
import operator
import pandas as pd
from datetime import datetime
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class FinancialAnalysisState(TypedDict):
    """State for the financial analysis workflow."""
    raw_data: Annotated[List[pd.DataFrame], operator.add]
    processed_data: Annotated[List[pd.DataFrame], operator.add]
    supplier_analysis: Annotated[List[str], operator.add]
    voucher_analysis: Annotated[List[str], operator.add]
    margin_analysis: Annotated[List[str], operator.add]
    trend_analysis: Annotated[List[str], operator.add]
    final_report: Annotated[List[str], operator.add]
    assumptions: Annotated[List[str], operator.add]
    warnings: Annotated[List[str], operator.add]
    config: Annotated[List[dict], operator.add]


# Workflow functions for StateGraph
def process_excel_data(state: FinancialAnalysisState) -> FinancialAnalysisState:
    """Process and validate Excel data."""
    if state["raw_data"]:
        processor = ExcelProcessorAgent()
        df = state["raw_data"][-1]
        result = processor.process(df)

        # Add processing assumptions
        state["assumptions"].append("Data validation and cleaning performed")
        if df.isnull().sum().sum() > 0:
            state["warnings"].append("Missing values detected in dataset")

        state["processed_data"].append(result["processed_df"])
    return state


def analyze_supplier_amounts(state: FinancialAnalysisState) -> FinancialAnalysisState:
    """Analyze gross amounts per supplier."""
    if state["processed_data"]:
        analyzer = GrossAmountAnalyzer()
        df = state["processed_data"][-1]
        analysis = analyzer.execute(df)
        state["supplier_analysis"].append(analysis)
    return state


def analyze_voucher_amounts(state: FinancialAnalysisState) -> FinancialAnalysisState:
    """Analyze gross amounts per voucher."""
    if state["processed_data"]:
        # Use the same analyzer for voucher analysis
        analyzer = GrossAmountAnalyzer()
        df = state["processed_data"][-1]
        # This will be included in the supplier analysis
        state["voucher_analysis"].append("Voucher analysis included in supplier analysis")
    return state


def calculate_margins(state: FinancialAnalysisState) -> FinancialAnalysisState:
    """Calculate margins and identify problematic transactions."""
    if state["processed_data"]:
        calculator = MarginCalculator()
        df = state["processed_data"][-1]
        config = state["config"][-1] if state["config"] else {}
        assume_cost_percentage = config.get("assume_cost_percentage", 70.0)

        analysis = calculator.execute(df, assume_cost_percentage)
        state["margin_analysis"].append(analysis)

        # Add assumption about cost calculation
        state["assumptions"].append(f"When cost data missing, assumed {assume_cost_percentage}% of gross amount")
    return state


def analyze_trends(state: FinancialAnalysisState) -> FinancialAnalysisState:
    """Analyze month-over-month trends."""
    if state["processed_data"]:
        analyzer = TrendAnalyzer()
        df = state["processed_data"][-1]
        analysis = analyzer.execute(df)
        state["trend_analysis"].append(analysis)
    return state


def generate_final_report(state: FinancialAnalysisState) -> FinancialAnalysisState:
    """Generate the comprehensive financial report."""
    generator = FinancialReportGenerator()

    supplier_analysis = state["supplier_analysis"][-1] if state["supplier_analysis"] else ""
    voucher_analysis = state["voucher_analysis"][-1] if state["voucher_analysis"] else ""
    margin_analysis = state["margin_analysis"][-1] if state["margin_analysis"] else ""
    trend_analysis = state["trend_analysis"][-1] if state["trend_analysis"] else ""

    report = generator.generate(
        supplier_analysis, voucher_analysis, margin_analysis, trend_analysis,
        state["assumptions"], state["warnings"]
    )

    state["final_report"].append(report)
    return state


# Create the StateGraph workflow
financial_analysis_graph = StateGraph(FinancialAnalysisState)

# Add nodes
financial_analysis_graph.add_node("process_data", process_excel_data)
financial_analysis_graph.add_node("analyze_suppliers", analyze_supplier_amounts)
financial_analysis_graph.add_node("analyze_vouchers", analyze_voucher_amounts)
financial_analysis_graph.add_node("calculate_margins", calculate_margins)
financial_analysis_graph.add_node("analyze_trends", analyze_trends)
financial_analysis_graph.add_node("generate_report", generate_final_report)

# Add edges to define workflow
financial_analysis_graph.add_edge(START, "process_data")
financial_analysis_graph.add_edge("process_data", "analyze_suppliers")
financial_analysis_graph.add_edge("process_data", "analyze_vouchers")
financial_analysis_graph.add_edge("analyze_suppliers", "calculate_margins")
financial_analysis_graph.add_edge("analyze_vouchers", "calculate_margins")
financial_analysis_graph.add_edge("calculate_margins", "analyze_trends")
financial_analysis_graph.add_edge("analyze_trends", "generate_report")
financial_analysis_graph.add_edge("generate_report", END)

# Compile the workflow
financial_analysis_app = financial_analysis_graph.compile()


def run_financial_analysis(df: pd.DataFrame, config: dict = None) -> dict:
    """
    Run the complete financial analysis workflow.

    Args:
        df: DataFrame with financial data
        config: Configuration dictionary with analysis parameters

    Returns:
        Dictionary with analysis results
    """
    if config is None:
        config = {"assume_cost_percentage": 70.0, "low_margin_threshold": 10.0}

    initial_state = {
        "raw_data": [df],
        "processed_data": [],
        "supplier_analysis": [],
        "voucher_analysis": [],
        "margin_analysis": [],
        "trend_analysis": [],
        "final_report": [],
        "assumptions": [],
        "warnings": [],
        "config": [config]
    }

    final_state = financial_analysis_app.invoke(initial_state)

    return {
        "final_report": final_state["final_report"][-1] if final_state["final_report"] else "",
        "supplier_analysis": final_state["supplier_analysis"][-1] if final_state["supplier_analysis"] else "",
        "margin_analysis": final_state["margin_analysis"][-1] if final_state["margin_analysis"] else "",
        "trend_analysis": final_state["trend_analysis"][-1] if final_state["trend_analysis"] else "",
        "assumptions": final_state["assumptions"],
        "warnings": final_state["warnings"]
    }


class ExcelProcessorAgent:
    """Agent for processing and validating Excel financial data."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama-3.1-8b-instant")
        self.prompt = PromptTemplate(
            input_variables=["data_info"],
            template="""
            You are a financial data validation expert. Analyze the provided Excel data structure and identify:
            1. Required columns for financial analysis (supplier, voucher, amounts, dates)
            2. Data quality issues (missing values, invalid formats)
            3. Suggested data cleaning steps
            
            Data structure info: {data_info}
            
            Provide a JSON response with:
            - "required_columns": list of essential column names found
            - "issues": list of data quality issues
            - "cleaning_steps": list of recommended cleaning actions
            """
        )
        self.chain = self.prompt | self.llm | StrOutputParser()
    
    def process(self, df: pd.DataFrame) -> dict:
        """Process and validate the Excel data."""
        data_info = {
            "columns": list(df.columns),
            "shape": df.shape,
            "dtypes": df.dtypes.to_dict(),
            "missing_values": df.isnull().sum().to_dict(),
            "sample_data": df.head(3).to_dict()
        }
        
        analysis = self.chain.invoke({"data_info": str(data_info)})
        return {"analysis": analysis, "processed_df": df}


class GrossAmountAnalyzer:
    """Agent for calculating gross amounts per supplier and voucher."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
    
    def execute(self, df: pd.DataFrame) -> str:
        """Analyze gross amounts using pandas agent."""
        system_message = """
        You are a financial analyst specializing in gross amount calculations.
        
        Your tasks:
        1. Calculate total gross amount per supplier
        2. Calculate total gross amount per voucher
        3. Identify the top 10 suppliers by gross amount
        4. Provide summary statistics
        
        The data contains financial transactions with suppliers and vouchers.
        Use pandas methods to perform these calculations efficiently.
        Return results in a structured format with clear summaries.
        """
        
        agent = create_pandas_dataframe_agent(
            llm=self.llm,
            df=df,
            verbose=True,
            allow_dangerous_code=True,
            agent_executor_kwargs={"handle_parsing_errors": True}
        )
        
        query = [
            "Calculate total gross amount per supplier and per voucher. "
            "Also provide top 10 suppliers by gross amount and summary statistics.",
            system_message
        ]
        
        response = agent.invoke({"input": query})
        return response["output"]


class MarginCalculator:
    """Agent for calculating margins and identifying low-margin transactions."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
    
    def execute(self, df: pd.DataFrame, assume_cost_percentage: float = 70.0) -> str:
        """Calculate margins using pandas agent."""
        system_message = f"""
        You are a financial analyst specializing in margin calculations.
        
        Your tasks:
        1. Calculate profit margins for each transaction
        2. If cost data is missing, assume cost = {assume_cost_percentage}% of gross amount
        3. Identify transactions with margin < 10%
        4. Identify transactions with negative margins
        5. Calculate average margin per supplier
        
        Margin formula: ((gross_amount - cost) / gross_amount) * 100
        
        Provide detailed analysis with counts and examples of problematic transactions.
        """
        
        agent = create_pandas_dataframe_agent(
            llm=self.llm,
            df=df,
            verbose=True,
            allow_dangerous_code=True,
            agent_executor_kwargs={"handle_parsing_errors": True}
        )
        
        query = [
            f"Calculate profit margins, identify low margins (<10%) and negative margins. "
            f"Assume cost = {assume_cost_percentage}% of gross amount when cost is missing.",
            system_message
        ]
        
        response = agent.invoke({"input": query})
        return response["output"]


class TrendAnalyzer:
    """Agent for analyzing month-over-month profitability trends."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
    
    def execute(self, df: pd.DataFrame) -> str:
        """Analyze trends using pandas agent."""
        system_message = """
        You are a financial analyst specializing in trend analysis.
        
        Your tasks:
        1. Group data by supplier and month
        2. Calculate month-over-month profitability trends
        3. Identify suppliers with improving/declining trends
        4. Calculate growth rates and seasonal patterns
        
        Focus on suppliers with significant transaction volumes.
        Provide insights on trend patterns and recommendations.
        """
        
        agent = create_pandas_dataframe_agent(
            llm=self.llm,
            df=df,
            verbose=True,
            allow_dangerous_code=True,
            agent_executor_kwargs={"handle_parsing_errors": True}
        )
        
        query = [
            "Analyze month-over-month profitability trends by supplier. "
            "Identify improving and declining trends with growth rates.",
            system_message
        ]
        
        response = agent.invoke({"input": query})
        return response["output"]


class FinancialReportGenerator:
    """Agent for generating comprehensive financial analysis reports."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0.3, model="gemma2-9b-it")
        self.prompt = PromptTemplate(
            input_variables=["supplier_analysis", "voucher_analysis", "margin_analysis", "trend_analysis", "assumptions", "warnings"],
            template="""
            You are a senior financial analyst preparing a comprehensive financial analysis report.
            
            Based on the following analyses, create a professional executive summary:
            
            Supplier Analysis: {supplier_analysis}
            Voucher Analysis: {voucher_analysis}
            Margin Analysis: {margin_analysis}
            Trend Analysis: {trend_analysis}
            
            Assumptions Made: {assumptions}
            Warnings: {warnings}
            
            Structure your report as follows:
            1. Executive Summary
            2. Key Findings
            3. Financial Performance Highlights
            4. Risk Areas (low/negative margins)
            5. Supplier Performance Trends
            6. Recommendations
            7. Assumptions and Limitations
            
            Make the report professional, actionable, and accessible to business stakeholders.
            """
        )
        self.chain = self.prompt | self.llm | StrOutputParser()
    
    def generate(self, supplier_analysis: str, voucher_analysis: str, 
                margin_analysis: str, trend_analysis: str, 
                assumptions: List[str], warnings: List[str]) -> str:
        """Generate the final financial report."""
        return self.chain.invoke({
            "supplier_analysis": supplier_analysis,
            "voucher_analysis": voucher_analysis,
            "margin_analysis": margin_analysis,
            "trend_analysis": trend_analysis,
            "assumptions": "\n".join(assumptions),
            "warnings": "\n".join(warnings)
        })
