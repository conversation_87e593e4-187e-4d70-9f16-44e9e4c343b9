# Financial Analysis Agents

This document explains the agent workflow system used in the Financial Analysis Project for processing Excel financial data.

## Overview

The Financial Analysis system uses a **StateGraph workflow** built with LangGraph to orchestrate multiple specialized AI agents that work together to analyze financial data from Excel files.

## Workflow Architecture

```mermaid
graph TD
    Start([START]) --> Process[Process Excel Data]
    Process --> Supplier[Analyze Suppliers]
    Process --> Voucher[Analyze Vouchers]
    Supplier --> Margin[Calculate Margins]
    Voucher --> Mar<PERSON>
    Margin --> Trend[Analyze Trends]
    Trend --> Report[Generate Report]
    Report --> End([END])
```

## State Management

The workflow uses a shared state (`FinancialAnalysisState`) that contains:

```python
class FinancialAnalysisState(TypedDict):
    raw_data: Annotated[List[pd.DataFrame], operator.add]
    processed_data: Annotated[List[pd.DataFrame], operator.add]
    supplier_analysis: Annotated[List[str], operator.add]
    voucher_analysis: Annotated[List[str], operator.add]
    margin_analysis: Annotated[List[str], operator.add]
    trend_analysis: Annotated[List[str], operator.add]
    final_report: Annotated[List[str], operator.add]
    assumptions: Annotated[List[str], operator.add]
    warnings: Annotated[List[str], operator.add]
```

## Specialized Agents

### 1. ExcelProcessorAgent
**Purpose**: Data validation and preprocessing
**Model**: `llama-3.1-8b-instant`
**Responsibilities**:
- Validate Excel data structure
- Identify required columns (supplier, voucher, amounts, dates)
- Detect data quality issues
- Suggest cleaning steps
- Perform initial data preprocessing

**Key Features**:
- Automatic column detection
- Missing value analysis
- Data type validation
- Quality assessment reporting

### 2. GrossAmountAnalyzer
**Purpose**: Calculate gross amounts per supplier and voucher
**Model**: `llama3-70b-8192`
**Responsibilities**:
- Calculate total gross amount per supplier
- Calculate total gross amount per voucher
- Identify top 10 suppliers by gross amount
- Provide summary statistics

**Analysis Output**:
- Supplier-level gross amount totals
- Voucher-level gross amount totals
- Top performers identification
- Statistical summaries

### 3. MarginAnalyzer
**Purpose**: Profit margin calculations and analysis
**Model**: `llama3-70b-8192`
**Responsibilities**:
- Calculate profit margins for each transaction
- Identify low-margin transactions (<10%)
- Detect negative margin transactions
- Apply cost assumptions when cost data is missing

**Key Features**:
- Configurable cost percentage assumptions
- Margin threshold alerts
- Profitability scoring
- Risk identification

### 4. TrendAnalyzer
**Purpose**: Month-over-month profitability trend analysis
**Model**: `llama3-70b-8192`
**Responsibilities**:
- Group data by supplier and month
- Calculate month-over-month profitability trends
- Identify suppliers with improving/declining trends
- Calculate growth rates and seasonal patterns

**Analysis Features**:
- Temporal trend analysis
- Supplier performance tracking
- Growth rate calculations
- Seasonal pattern detection

### 5. ReportGenerator
**Purpose**: Generate comprehensive financial reports
**Model**: `gemma2-9b-it`
**Responsibilities**:
- Synthesize all analysis results
- Create executive summary
- Provide actionable insights
- Generate professional reports

**Report Sections**:
- Executive Summary
- Key Findings
- Risk Assessment
- Recommendations
- Detailed Analysis

## Workflow Execution

### Step 1: Data Processing
```python
def process_excel_data(state: FinancialAnalysisState) -> FinancialAnalysisState:
    processor = ExcelProcessorAgent()
    df = state["raw_data"][-1]
    result = processor.process(df)
    state["processed_data"].append(result["processed_df"])
    return state
```

### Step 2: Parallel Analysis
The workflow runs supplier and voucher analysis in parallel:
- **Supplier Analysis**: Calculates totals and rankings by supplier
- **Voucher Analysis**: Analyzes voucher-level transactions

### Step 3: Margin Calculation
Uses results from both analyses to calculate comprehensive margin metrics.

### Step 4: Trend Analysis
Performs temporal analysis to identify patterns and trends.

### Step 5: Report Generation
Synthesizes all results into a comprehensive financial report.

## Configuration

### Environment Variables
```bash
GROQ_API_KEY=your_groq_api_key_here
```

### Model Configuration
- **Data Processing**: `llama-3.1-8b-instant` (fast, efficient)
- **Financial Analysis**: `llama3-70b-8192` (high accuracy)
- **Report Generation**: `gemma2-9b-it` (creative, comprehensive)

## Error Handling

The system includes comprehensive error handling:
- **Data Validation**: Checks for required columns and data types
- **Missing Data**: Handles missing values with configurable strategies
- **Parsing Errors**: Graceful handling of LLM parsing errors
- **Workflow Errors**: State recovery and error reporting

## Performance Considerations

- **Parallel Processing**: Supplier and voucher analysis run concurrently
- **Model Selection**: Different models optimized for specific tasks
- **Memory Management**: Efficient DataFrame handling
- **Caching**: Results cached in state for downstream agents

## Usage Example

```python
from financial_analysis_project.backend.app.agents.pandas_agents import financial_analysis_app

# Initialize state with Excel data
initial_state = {
    "raw_data": [your_dataframe],
    "processed_data": [],
    "supplier_analysis": [],
    "voucher_analysis": [],
    "margin_analysis": [],
    "trend_analysis": [],
    "final_report": [],
    "assumptions": [],
    "warnings": []
}

# Execute workflow
result = financial_analysis_app.invoke(initial_state)

# Get final report
final_report = result["final_report"][-1]
```

## Next Steps

- [Stock Analysis Agents](./stock-analysis-agents.md) - Learn about the stock analysis workflow
- [Agent Architecture](./agent-architecture.md) - Understand the overall agent design
- [StateGraph Workflows](./stategraph-workflows.md) - Deep dive into LangGraph implementation
