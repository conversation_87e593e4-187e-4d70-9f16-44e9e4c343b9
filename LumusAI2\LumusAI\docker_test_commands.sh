#!/bin/bash

# Docker Test Commands for LumusAI Background Processing
# This script provides easy commands to test the Docker deployment

set -e

echo "🐳 LumusAI Docker Testing Commands"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="lumusai-test"
IMAGE_NAME="lumusai:latest"
PORT="8000"

print_step() {
    echo -e "\n${BLUE}🔹 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to build the image
build_image() {
    print_step "Building Docker image"
    
    if [ ! -f "requirements_webhook.txt" ]; then
        print_error "requirements_webhook.txt not found. Creating it..."
        echo "aiohttp==3.10.10" > requirements_webhook.txt
        echo "aiosignal==1.3.1" >> requirements_webhook.txt
        print_success "Created requirements_webhook.txt"
    fi
    
    docker build -t $IMAGE_NAME .
    print_success "Docker image built successfully"
}

# Function to start the container
start_container() {
    print_step "Starting Docker container"
    
    # Stop existing container if running
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    
    # Check for .env file
    if [ ! -f ".env" ]; then
        print_info "Creating .env file template..."
        cat > .env << EOF
# Azure OpenAI Configuration
API_KEY=your_azure_openai_api_key_here
API_VERSION=2024-02-15-preview
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
MODEL=gpt-4o
EOF
        print_error "Please update .env file with your Azure OpenAI credentials"
        print_info "Edit .env file and run this script again"
        exit 1
    fi
    
    # Start container
    docker run -d \
        --name $CONTAINER_NAME \
        -p $PORT:8000 \
        --env-file .env \
        -v "$(pwd)/logs:/app/logs" \
        $IMAGE_NAME
    
    print_success "Container started: $CONTAINER_NAME"
    print_info "Waiting for service to be ready..."
    
    # Wait for service to be ready
    for i in {1..30}; do
        if curl -s http://localhost:$PORT/health > /dev/null 2>&1; then
            print_success "Service is ready!"
            break
        fi
        echo -n "."
        sleep 2
    done
    echo ""
}

# Function to test background processing
test_background() {
    print_step "Testing background processing"
    
    # Create test data
    TEST_DATA="John Doe, Software Engineer, Email: <EMAIL>, Skills: Python, FastAPI, Docker"
    
    print_info "Submitting background processing request..."
    
    # Time the request
    START_TIME=$(date +%s.%N)
    
    RESPONSE=$(curl -s -X POST "http://localhost:$PORT/process" \
        -F "action=cv" \
        -F "data=$TEST_DATA" \
        -F "background=true" \
        -F "webhook_url=http://localhost:$PORT/webhook/test")
    
    END_TIME=$(date +%s.%N)
    DURATION=$(echo "$END_TIME - $START_TIME" | bc -l)
    
    print_success "Request completed in ${DURATION} seconds"
    
    # Parse response
    TASK_ID=$(echo $RESPONSE | python3 -c "import sys, json; print(json.load(sys.stdin).get('task_id', 'ERROR'))")
    
    if [ "$TASK_ID" = "ERROR" ]; then
        print_error "Failed to get task ID"
        echo "Response: $RESPONSE"
        return 1
    fi
    
    print_success "Task ID: $TASK_ID"
    print_info "🎯 PROOF: Background request completed in ${DURATION} seconds (no timeout!)"
    
    # Check status
    print_info "Checking task status..."
    STATUS_RESPONSE=$(curl -s "http://localhost:$PORT/task/status/$TASK_ID")
    echo "Status: $STATUS_RESPONSE"
    
    return 0
}

# Function to monitor a task
monitor_task() {
    if [ -z "$1" ]; then
        print_error "Usage: monitor_task <task_id>"
        return 1
    fi
    
    TASK_ID=$1
    print_step "Monitoring task: $TASK_ID"
    
    for i in {1..20}; do
        STATUS_RESPONSE=$(curl -s "http://localhost:$PORT/task/status/$TASK_ID")
        STATUS=$(echo $STATUS_RESPONSE | python3 -c "import sys, json; print(json.load(sys.stdin).get('status', 'unknown'))" 2>/dev/null || echo "unknown")
        
        print_info "Check $i: Status = $STATUS"
        
        if [ "$STATUS" = "completed" ] || [ "$STATUS" = "failed" ]; then
            print_success "Task finished with status: $STATUS"
            
            # Get result
            RESULT_RESPONSE=$(curl -s "http://localhost:$PORT/task/result/$TASK_ID")
            echo "Result: $RESULT_RESPONSE"
            break
        fi
        
        sleep 10
    done
}

# Function to show container logs
show_logs() {
    print_step "Showing container logs"
    docker logs --tail 20 $CONTAINER_NAME
}

# Function to stop container
stop_container() {
    print_step "Stopping container"
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    print_success "Container stopped and removed"
}

# Function to run comprehensive test
run_comprehensive_test() {
    print_step "Running comprehensive Docker test"
    
    check_docker
    build_image
    start_container
    
    # Wait a bit more for service to be fully ready
    sleep 5
    
    # Test health
    print_info "Testing health endpoint..."
    if curl -s http://localhost:$PORT/health | grep -q "status"; then
        print_success "Health endpoint working"
    else
        print_error "Health endpoint failed"
        show_logs
        return 1
    fi
    
    # Test webhook
    print_info "Testing webhook endpoint..."
    WEBHOOK_RESPONSE=$(curl -s -X POST "http://localhost:$PORT/webhook/test" \
        -H "Content-Type: application/json" \
        -d '{"task_id":"test","status":"completed","data":{}}')
    
    if echo $WEBHOOK_RESPONSE | grep -q "received"; then
        print_success "Webhook endpoint working"
    else
        print_error "Webhook endpoint failed"
    fi
    
    # Test background processing
    if test_background; then
        print_success "🎉 All Docker tests passed!"
        print_info "Your Docker deployment is working correctly!"
        print_info "Background processing eliminates timeout issues!"
    else
        print_error "Background processing test failed"
        show_logs
        return 1
    fi
}

# Main script logic
case "${1:-help}" in
    "build")
        check_docker
        build_image
        ;;
    "start")
        check_docker
        start_container
        ;;
    "test")
        test_background
        ;;
    "monitor")
        monitor_task $2
        ;;
    "logs")
        show_logs
        ;;
    "stop")
        stop_container
        ;;
    "full-test")
        run_comprehensive_test
        ;;
    "clean")
        stop_container
        docker rmi $IMAGE_NAME 2>/dev/null || true
        print_success "Cleaned up Docker resources"
        ;;
    *)
        echo "Usage: $0 {build|start|test|monitor <task_id>|logs|stop|full-test|clean}"
        echo ""
        echo "Commands:"
        echo "  build      - Build the Docker image"
        echo "  start      - Start the Docker container"
        echo "  test       - Test background processing"
        echo "  monitor    - Monitor a specific task"
        echo "  logs       - Show container logs"
        echo "  stop       - Stop and remove container"
        echo "  full-test  - Run complete test suite"
        echo "  clean      - Clean up all Docker resources"
        echo ""
        echo "Quick test: $0 full-test"
        ;;
esac
