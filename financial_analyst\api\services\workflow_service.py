"""
Workflow service layer for managing agent workflows through the API.
Interfaces with the existing agent coordinator system.
"""

import logging
import async<PERSON>
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid

from agents.agent_coordinator import get_agent_coordinator, TaskStatus as CoordinatorTaskStatus
from api.models.workflow_models import (
    WorkflowCreateRequest, WorkflowResponse, WorkflowStatusResponse,
    WorkflowStatus, TaskStatus, TaskResponse, AgentType
)
from api.models.common_models import ResponseStatus

logger = logging.getLogger(__name__)


class WorkflowService:
    """Service for managing workflows through the API layer."""
    
    def __init__(self):
        """Initialize workflow service."""
        self.coordinator = get_agent_coordinator()
        self._active_workflows: Dict[str, Dict[str, Any]] = {}
    
    async def create_workflow(self, request: WorkflowCreateRequest) -> WorkflowResponse:
        """Create a new workflow from a request."""
        try:
            # Generate workflow ID
            workflow_id = f"api_wf_{uuid.uuid4().hex[:12]}"
            
            logger.info(f"Creating workflow {workflow_id} with query: {request.query}")
            
            # Create workflow using coordinator
            workflow = self.coordinator.crear_workflow(workflow_id, request.query)
            
            # Store workflow metadata
            self._active_workflows[workflow_id] = {
                "created_at": datetime.now(),
                "request": request,
                "status": WorkflowStatus.PENDING
            }
            
            # Convert coordinator workflow to API response
            response = await self._convert_workflow_to_response(workflow_id, workflow)
            response.message = "Workflow created successfully"
            
            logger.info(f"Workflow {workflow_id} created successfully")
            return response
            
        except Exception as e:
            logger.error(f"Failed to create workflow: {e}")
            raise
    
    async def execute_workflow(self, workflow_id: str, async_execution: bool = True) -> WorkflowResponse:
        """Execute a workflow."""
        try:
            logger.info(f"Executing workflow {workflow_id}")
            
            if workflow_id not in self._active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            # Update status
            self._active_workflows[workflow_id]["status"] = WorkflowStatus.IN_PROGRESS
            self._active_workflows[workflow_id]["started_at"] = datetime.now()
            
            if async_execution:
                # Execute asynchronously
                asyncio.create_task(self._execute_workflow_async(workflow_id))
                
                # Return immediate response
                workflow = self.coordinator.workflows.get(workflow_id)
                response = await self._convert_workflow_to_response(workflow_id, workflow)
                response.message = "Workflow execution started"
                return response
            else:
                # Execute synchronously
                result = self.coordinator.ejecutar_workflow(workflow_id)
                
                # Update status based on result
                if result.get("status") == "completed":
                    self._active_workflows[workflow_id]["status"] = WorkflowStatus.COMPLETED
                    self._active_workflows[workflow_id]["completed_at"] = datetime.now()
                else:
                    self._active_workflows[workflow_id]["status"] = WorkflowStatus.FAILED
                    self._active_workflows[workflow_id]["error"] = result.get("errors", "Unknown error")
                
                # Convert result to response
                workflow = self.coordinator.workflows.get(workflow_id)
                response = await self._convert_workflow_to_response(workflow_id, workflow, result)
                response.message = "Workflow execution completed"
                return response
                
        except Exception as e:
            logger.error(f"Failed to execute workflow {workflow_id}: {e}")
            
            # Update status to failed
            if workflow_id in self._active_workflows:
                self._active_workflows[workflow_id]["status"] = WorkflowStatus.FAILED
                self._active_workflows[workflow_id]["error"] = str(e)
            
            raise
    
    async def get_workflow_status(self, workflow_id: str) -> WorkflowStatusResponse:
        """Get workflow status and progress."""
        try:
            if workflow_id not in self._active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            workflow_data = self._active_workflows[workflow_id]
            coordinator_workflow = self.coordinator.workflows.get(workflow_id)
            
            # Calculate progress
            progress = 0.0
            tasks_completed = 0
            total_tasks = 0
            current_task = None
            
            if coordinator_workflow:
                total_tasks = len(coordinator_workflow.tasks)
                for task in coordinator_workflow.tasks.values():
                    if task.status.value == "completed":
                        tasks_completed += 1
                    elif task.status.value == "in_progress":
                        current_task = task.task_id
                
                if total_tasks > 0:
                    progress = tasks_completed / total_tasks
            
            # Estimate completion time
            estimated_completion = None
            if workflow_data["status"] == WorkflowStatus.IN_PROGRESS and progress > 0:
                started_at = workflow_data.get("started_at")
                if started_at:
                    elapsed = (datetime.now() - started_at).total_seconds()
                    if progress > 0:
                        total_estimated = elapsed / progress
                        remaining = total_estimated - elapsed
                        estimated_completion = datetime.now().timestamp() + remaining
            
            return WorkflowStatusResponse(
                message="Workflow status retrieved successfully",
                workflow_id=workflow_id,
                status=workflow_data["status"],
                progress=progress,
                tasks_completed=tasks_completed,
                total_tasks=total_tasks,
                estimated_completion=datetime.fromtimestamp(estimated_completion) if estimated_completion else None,
                current_task=current_task
            )
            
        except Exception as e:
            logger.error(f"Failed to get workflow status for {workflow_id}: {e}")
            raise
    
    async def get_workflow(self, workflow_id: str) -> WorkflowResponse:
        """Get complete workflow information."""
        try:
            if workflow_id not in self._active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            coordinator_workflow = self.coordinator.workflows.get(workflow_id)
            response = await self._convert_workflow_to_response(workflow_id, coordinator_workflow)
            response.message = "Workflow retrieved successfully"
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to get workflow {workflow_id}: {e}")
            raise
    
    async def cancel_workflow(self, workflow_id: str, reason: Optional[str] = None) -> WorkflowResponse:
        """Cancel a running workflow."""
        try:
            logger.info(f"Cancelling workflow {workflow_id}, reason: {reason}")
            
            if workflow_id not in self._active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            # Cancel using coordinator
            success = self.coordinator.cancelar_workflow(workflow_id)
            
            if success:
                # Update local status
                self._active_workflows[workflow_id]["status"] = WorkflowStatus.CANCELLED
                self._active_workflows[workflow_id]["cancelled_at"] = datetime.now()
                if reason:
                    self._active_workflows[workflow_id]["cancellation_reason"] = reason
                
                coordinator_workflow = self.coordinator.workflows.get(workflow_id)
                response = await self._convert_workflow_to_response(workflow_id, coordinator_workflow)
                response.message = "Workflow cancelled successfully"
                
                return response
            else:
                raise ValueError("Failed to cancel workflow")
                
        except Exception as e:
            logger.error(f"Failed to cancel workflow {workflow_id}: {e}")
            raise
    
    async def list_workflows(self, status_filter: Optional[WorkflowStatus] = None) -> List[WorkflowResponse]:
        """List all workflows with optional status filtering."""
        try:
            workflows = []
            
            for workflow_id, workflow_data in self._active_workflows.items():
                if status_filter and workflow_data["status"] != status_filter:
                    continue
                
                coordinator_workflow = self.coordinator.workflows.get(workflow_id)
                response = await self._convert_workflow_to_response(workflow_id, coordinator_workflow)
                workflows.append(response)
            
            return workflows
            
        except Exception as e:
            logger.error(f"Failed to list workflows: {e}")
            raise
    
    async def _execute_workflow_async(self, workflow_id: str):
        """Execute workflow asynchronously."""
        try:
            result = self.coordinator.ejecutar_workflow(workflow_id)
            
            # Update status based on result
            if result.get("status") == "completed":
                self._active_workflows[workflow_id]["status"] = WorkflowStatus.COMPLETED
                self._active_workflows[workflow_id]["completed_at"] = datetime.now()
                self._active_workflows[workflow_id]["result"] = result
            else:
                self._active_workflows[workflow_id]["status"] = WorkflowStatus.FAILED
                self._active_workflows[workflow_id]["error"] = result.get("errors", "Unknown error")
            
            logger.info(f"Async workflow {workflow_id} completed with status: {result.get('status')}")
            
        except Exception as e:
            logger.error(f"Async workflow {workflow_id} failed: {e}")
            self._active_workflows[workflow_id]["status"] = WorkflowStatus.FAILED
            self._active_workflows[workflow_id]["error"] = str(e)
    
    async def _convert_workflow_to_response(
        self, 
        workflow_id: str, 
        coordinator_workflow: Any = None,
        execution_result: Optional[Dict[str, Any]] = None
    ) -> WorkflowResponse:
        """Convert coordinator workflow to API response format."""
        workflow_data = self._active_workflows.get(workflow_id, {})
        request = workflow_data.get("request")
        
        # Convert tasks
        tasks = []
        if coordinator_workflow and hasattr(coordinator_workflow, 'tasks'):
            for task in coordinator_workflow.tasks.values():
                task_response = TaskResponse(
                    task_id=task.task_id,
                    agent_type=AgentType(task.agent_type.value),
                    status=TaskStatus(task.status.value),
                    started_at=task.started_at,
                    completed_at=task.completed_at,
                    duration=task.duration,
                    result=getattr(task, 'result', None),
                    error=getattr(task, 'error', None),
                    retry_count=getattr(task, 'retry_count', 0)
                )
                tasks.append(task_response)
        
        # Calculate duration
        duration = None
        started_at = workflow_data.get("started_at")
        completed_at = workflow_data.get("completed_at")
        if started_at and completed_at:
            duration = (completed_at - started_at).total_seconds()
        
        return WorkflowResponse(
            message="",  # Will be set by caller
            workflow_id=workflow_id,
            status=workflow_data.get("status", WorkflowStatus.PENDING),
            query=request.query if request else None,
            created_at=workflow_data.get("created_at", datetime.now()),
            started_at=workflow_data.get("started_at"),
            completed_at=workflow_data.get("completed_at"),
            duration=duration,
            priority=request.priority if request else 1,
            tasks=tasks,
            results=execution_result,
            error=workflow_data.get("error"),
            metadata=request.metadata if request else {}
        )
