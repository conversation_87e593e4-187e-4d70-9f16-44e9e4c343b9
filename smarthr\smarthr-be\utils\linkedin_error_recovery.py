import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable, TypeVar, Union
from datetime import datetime, timedelta
from enum import Enum
import traceback

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ErrorSeverity(str, Enum):
    """Error severity levels for LinkedIn integration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryStrategy(str, Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    FALLBACK = "fallback"
    SKIP = "skip"
    ABORT = "abort"


class LinkedInError:
    """Represents a LinkedIn integration error with recovery context."""
    
    def __init__(
        self,
        error_type: str,
        message: str,
        severity: ErrorSeverity,
        recovery_strategy: RecoveryStrategy,
        context: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        self.error_type = error_type
        self.message = message
        self.severity = severity
        self.recovery_strategy = recovery_strategy
        self.context = context or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()
        self.recovery_attempts = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "error_type": self.error_type,
            "message": self.message,
            "severity": self.severity.value,
            "recovery_strategy": self.recovery_strategy.value,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "recovery_attempts": self.recovery_attempts
        }


class LinkedInErrorRecoveryManager:
    """Manages error recovery for LinkedIn integration operations."""
    
    def __init__(self):
        self.error_patterns = self._initialize_error_patterns()
        self.recovery_handlers = self._initialize_recovery_handlers()
        self.error_history: List[LinkedInError] = []
        self.max_history_size = 1000
    
    def _initialize_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize error pattern recognition."""
        return {
            "rate_limit": {
                "keywords": ["rate limit", "too many requests", "429"],
                "severity": ErrorSeverity.MEDIUM,
                "recovery_strategy": RecoveryStrategy.RETRY,
                "retry_delay": 60,
                "max_retries": 3
            },
            "authentication": {
                "keywords": ["unauthorized", "authentication", "401", "403"],
                "severity": ErrorSeverity.HIGH,
                "recovery_strategy": RecoveryStrategy.ABORT,
                "retry_delay": 0,
                "max_retries": 0
            },
            "network_timeout": {
                "keywords": ["timeout", "connection", "network"],
                "severity": ErrorSeverity.MEDIUM,
                "recovery_strategy": RecoveryStrategy.RETRY,
                "retry_delay": 5,
                "max_retries": 3
            },
            "linkedin_api_error": {
                "keywords": ["linkedin api", "api error", "500", "502", "503"],
                "severity": ErrorSeverity.HIGH,
                "recovery_strategy": RecoveryStrategy.FALLBACK,
                "retry_delay": 30,
                "max_retries": 2
            },
            "transformation_error": {
                "keywords": ["transformation", "schema", "validation"],
                "severity": ErrorSeverity.MEDIUM,
                "recovery_strategy": RecoveryStrategy.FALLBACK,
                "retry_delay": 0,
                "max_retries": 1
            },
            "llm_error": {
                "keywords": ["llm", "language model", "inference"],
                "severity": ErrorSeverity.MEDIUM,
                "recovery_strategy": RecoveryStrategy.FALLBACK,
                "retry_delay": 2,
                "max_retries": 2
            }
        }
    
    def _initialize_recovery_handlers(self) -> Dict[RecoveryStrategy, Callable]:
        """Initialize recovery strategy handlers."""
        return {
            RecoveryStrategy.RETRY: self._handle_retry_recovery,
            RecoveryStrategy.FALLBACK: self._handle_fallback_recovery,
            RecoveryStrategy.SKIP: self._handle_skip_recovery,
            RecoveryStrategy.ABORT: self._handle_abort_recovery
        }
    
    def classify_error(self, exception: Exception, context: Optional[Dict[str, Any]] = None) -> LinkedInError:
        """Classify an exception and determine recovery strategy."""
        error_message = str(exception).lower()
        error_type = type(exception).__name__
        
        # Try to match error patterns
        for pattern_name, pattern_config in self.error_patterns.items():
            if any(keyword in error_message for keyword in pattern_config["keywords"]):
                return LinkedInError(
                    error_type=pattern_name,
                    message=str(exception),
                    severity=pattern_config["severity"],
                    recovery_strategy=pattern_config["recovery_strategy"],
                    context=context,
                    original_exception=exception
                )
        
        # Default classification for unknown errors
        return LinkedInError(
            error_type="unknown_error",
            message=str(exception),
            severity=ErrorSeverity.MEDIUM,
            recovery_strategy=RecoveryStrategy.RETRY,
            context=context,
            original_exception=exception
        )
    
    async def handle_error_with_recovery(
        self,
        operation: Callable[[], T],
        operation_name: str,
        context: Optional[Dict[str, Any]] = None,
        max_recovery_attempts: int = 3
    ) -> Union[T, LinkedInError]:
        """
        Execute an operation with automatic error recovery.
        
        Args:
            operation: The operation to execute
            operation_name: Name of the operation for logging
            context: Additional context for error handling
            max_recovery_attempts: Maximum number of recovery attempts
            
        Returns:
            Either the successful result or a LinkedInError
        """
        last_error = None
        
        for attempt in range(max_recovery_attempts + 1):
            try:
                logger.info(f"Executing {operation_name} (attempt {attempt + 1})")
                result = await operation() if asyncio.iscoroutinefunction(operation) else operation()
                
                if attempt > 0:
                    logger.info(f"{operation_name} succeeded after {attempt} recovery attempts")
                
                return result
                
            except Exception as e:
                logger.error(f"{operation_name} failed on attempt {attempt + 1}: {str(e)}")
                
                # Classify the error
                linkedin_error = self.classify_error(e, context)
                linkedin_error.recovery_attempts = attempt
                last_error = linkedin_error
                
                # Add to error history
                self._add_to_error_history(linkedin_error)
                
                # If this is the last attempt, don't try recovery
                if attempt >= max_recovery_attempts:
                    logger.error(f"{operation_name} failed after {max_recovery_attempts} attempts")
                    break
                
                # Try recovery
                recovery_success = await self._attempt_recovery(linkedin_error, operation_name)
                
                if not recovery_success:
                    logger.error(f"Recovery failed for {operation_name}, aborting")
                    break
        
        return last_error
    
    async def _attempt_recovery(self, error: LinkedInError, operation_name: str) -> bool:
        """Attempt to recover from an error."""
        try:
            recovery_handler = self.recovery_handlers.get(error.recovery_strategy)
            
            if not recovery_handler:
                logger.warning(f"No recovery handler for strategy: {error.recovery_strategy}")
                return False
            
            logger.info(f"Attempting {error.recovery_strategy.value} recovery for {operation_name}")
            return await recovery_handler(error, operation_name)
            
        except Exception as e:
            logger.error(f"Recovery attempt failed: {str(e)}")
            return False
    
    async def _handle_retry_recovery(self, error: LinkedInError, operation_name: str) -> bool:
        """Handle retry recovery strategy."""
        pattern_config = self.error_patterns.get(error.error_type, {})
        retry_delay = pattern_config.get("retry_delay", 5)
        
        if retry_delay > 0:
            logger.info(f"Waiting {retry_delay} seconds before retry")
            await asyncio.sleep(retry_delay)
        
        return True
    
    async def _handle_fallback_recovery(self, error: LinkedInError, operation_name: str) -> bool:
        """Handle fallback recovery strategy."""
        logger.info(f"Using fallback recovery for {operation_name}")
        
        # For LinkedIn integration, fallback strategies might include:
        # - Using mock data for testing
        # - Using cached results
        # - Using alternative API endpoints
        # - Using rule-based transformation instead of LLM
        
        # This is a placeholder - specific fallback logic would be implemented
        # in the calling code based on the operation type
        return True
    
    async def _handle_skip_recovery(self, error: LinkedInError, operation_name: str) -> bool:
        """Handle skip recovery strategy."""
        logger.info(f"Skipping failed operation: {operation_name}")
        return False  # Don't retry, but don't abort the entire workflow
    
    async def _handle_abort_recovery(self, error: LinkedInError, operation_name: str) -> bool:
        """Handle abort recovery strategy."""
        logger.error(f"Aborting due to critical error in {operation_name}")
        return False
    
    def _add_to_error_history(self, error: LinkedInError):
        """Add error to history with size management."""
        self.error_history.append(error)
        
        # Maintain history size limit
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        if not self.error_history:
            return {"total_errors": 0, "error_types": {}, "recovery_success_rate": 0.0}
        
        error_types = {}
        recovery_attempts = 0
        successful_recoveries = 0
        
        for error in self.error_history:
            error_type = error.error_type
            error_types[error_type] = error_types.get(error_type, 0) + 1
            
            if error.recovery_attempts > 0:
                recovery_attempts += 1
                # Consider it successful if it was retried (implies eventual success)
                if error.recovery_strategy in [RecoveryStrategy.RETRY, RecoveryStrategy.FALLBACK]:
                    successful_recoveries += 1
        
        recovery_success_rate = (
            successful_recoveries / recovery_attempts if recovery_attempts > 0 else 0.0
        )
        
        return {
            "total_errors": len(self.error_history),
            "error_types": error_types,
            "recovery_attempts": recovery_attempts,
            "successful_recoveries": successful_recoveries,
            "recovery_success_rate": recovery_success_rate,
            "recent_errors": [error.to_dict() for error in self.error_history[-10:]]
        }
    
    def clear_error_history(self):
        """Clear error history."""
        self.error_history.clear()
        logger.info("Error history cleared")


# Global error recovery manager instance
error_recovery_manager = LinkedInErrorRecoveryManager()


# Convenience functions
async def execute_with_recovery(
    operation: Callable[[], T],
    operation_name: str,
    context: Optional[Dict[str, Any]] = None,
    max_attempts: int = 3
) -> Union[T, LinkedInError]:
    """Execute operation with automatic error recovery."""
    return await error_recovery_manager.handle_error_with_recovery(
        operation, operation_name, context, max_attempts
    )


def get_linkedin_error_stats() -> Dict[str, Any]:
    """Get LinkedIn integration error statistics."""
    return error_recovery_manager.get_error_statistics()


def clear_linkedin_error_history():
    """Clear LinkedIn integration error history."""
    error_recovery_manager.clear_error_history()
