import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import traceback

from models.linkedin import (
    LinkedInSearchRequest,
    LinkedInSearchResponse,
    SchemaTransformationRequest,
    SchemaTransformationResponse,
    LinkedInProfile,
    TransformedCandidate,
    AgentError
)
from models.linkedin_config import LinkedInIntegrationConfig
from controllers.linkedin_agents import LinkedInSearchAgent, SchemaTransformationAgent
from utils.linkedin_agent_communication import LinkedInAgentOrchestrator
from utils.linkedin_transformation_validator import validate_smarthr_candidate

logger = logging.getLogger(__name__)


class LinkedInWorkflowOrchestrator:
    """
    Main orchestrator for LinkedIn candidate search and transformation workflow.
    
    Similar to the 4-agent interview evaluation system, this orchestrator coordinates
    the LinkedIn Search Agent and Schema Transformation Agent to provide a complete
    candidate sourcing pipeline.
    """
    
    def __init__(self, config: LinkedInIntegrationConfig):
        self.config = config
        self.agent_orchestrator = LinkedInAgentOrchestrator(config)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Performance tracking
        self.workflow_stats = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "total_profiles_found": 0,
            "total_profiles_transformed": 0,
            "average_processing_time_ms": 0
        }
    
    async def execute_candidate_sourcing_workflow(
        self,
        search_request: LinkedInSearchRequest,
        workflow_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute the complete LinkedIn candidate sourcing workflow.
        
        This is the main entry point that coordinates:
        1. LinkedIn Search Agent - Find candidates
        2. Schema Transformation Agent - Transform to smartHR format
        3. Validation and quality assurance
        4. Results compilation and reporting
        
        Args:
            search_request: LinkedIn search parameters
            workflow_options: Optional workflow configuration
            
        Returns:
            Complete workflow results with search and transformation data
        """
        workflow_start_time = datetime.now()
        workflow_id = f"linkedin_workflow_{int(workflow_start_time.timestamp())}"
        
        try:
            self.logger.info(f"LINKEDIN WORKFLOW: Starting workflow {workflow_id}")
            self.logger.info(f"LINKEDIN WORKFLOW: Search filters - {len(search_request.filters.keywords)} keywords")
            
            # Initialize workflow options
            options = self._initialize_workflow_options(workflow_options)
            
            # Step 1: Execute LinkedIn search
            self.logger.info(f"LINKEDIN WORKFLOW: Step 1 - Executing LinkedIn search")
            search_response = await self._execute_search_step(search_request, workflow_id)
            
            if not search_response.success:
                return self._create_workflow_failure_result(
                    workflow_id, "Search step failed", search_response.error_message,
                    workflow_start_time
                )
            
            self.logger.info(f"LINKEDIN WORKFLOW: Search completed - {len(search_response.profiles)} profiles found")
            
            # Step 2: Execute schema transformation (if profiles found)
            transformation_response = None
            if search_response.profiles and options.get("transform_profiles", True):
                self.logger.info(f"LINKEDIN WORKFLOW: Step 2 - Executing schema transformation")
                transformation_response = await self._execute_transformation_step(
                    search_response.profiles, workflow_id
                )
                
                if transformation_response and transformation_response.success:
                    self.logger.info(
                        f"LINKEDIN WORKFLOW: Transformation completed - "
                        f"{len(transformation_response.transformed_candidates)} candidates transformed"
                    )
                else:
                    self.logger.warning(f"LINKEDIN WORKFLOW: Transformation failed or skipped")
            
            # Step 3: Quality assurance and validation
            self.logger.info(f"LINKEDIN WORKFLOW: Step 3 - Quality assurance")
            quality_report = await self._execute_quality_assurance_step(
                search_response, transformation_response, workflow_id
            )
            
            # Step 4: Compile final results
            self.logger.info(f"LINKEDIN WORKFLOW: Step 4 - Compiling results")
            workflow_result = self._compile_workflow_results(
                workflow_id, search_response, transformation_response, 
                quality_report, workflow_start_time, options
            )
            
            # Update statistics
            self._update_workflow_stats(workflow_result, workflow_start_time)
            
            self.logger.info(f"LINKEDIN WORKFLOW: Workflow {workflow_id} completed successfully")
            return workflow_result
            
        except Exception as e:
            self.logger.error(f"LINKEDIN WORKFLOW: Workflow {workflow_id} failed: {str(e)}")
            self.logger.error(f"LINKEDIN WORKFLOW: Full traceback: {traceback.format_exc()}")
            
            self.workflow_stats["failed_workflows"] += 1
            
            return self._create_workflow_failure_result(
                workflow_id, "Workflow execution failed", str(e), workflow_start_time
            )

    # Public methods for individual workflow steps
    async def execute_search_step(self, search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """
        Execute only the search step of the workflow.

        Args:
            search_request: LinkedIn search request parameters

        Returns:
            LinkedIn search response with profiles
        """
        workflow_id = f"search_only_{int(time.time() * 1000)}"
        return await self._execute_search_step(search_request, workflow_id)

    async def execute_transformation_step(
        self,
        transformation_requests: List[SchemaTransformationRequest]
    ) -> Optional[SchemaTransformationResponse]:
        """
        Execute only the transformation step of the workflow.

        Args:
            transformation_requests: List of transformation requests

        Returns:
            Schema transformation response with transformed candidates
        """
        workflow_id = f"transform_only_{int(time.time() * 1000)}"

        try:
            self.logger.info(f"TRANSFORM WORKFLOW [{workflow_id}]: Transforming {len(transformation_requests)} profiles")

            # Execute transformations through agent
            transformation_agent = SchemaTransformationAgent(self.config)
            all_transformed_candidates = []

            for transform_request in transformation_requests:
                try:
                    transformation_response = await transformation_agent.process_request(transform_request)
                    if transformation_response and transformation_response.success:
                        all_transformed_candidates.extend(transformation_response.transformed_candidates)
                except Exception as e:
                    self.logger.error(f"Individual transformation failed: {str(e)}")
                    continue

            # Create combined response
            return SchemaTransformationResponse(
                success=len(all_transformed_candidates) > 0,
                candidates_transformed=len(all_transformed_candidates),
                transformed_candidates=all_transformed_candidates,
                transformation_metadata={
                    "workflow_id": workflow_id,
                    "total_requests": len(transformation_requests),
                    "successful_transformations": len(all_transformed_candidates)
                },
                execution_time_ms=0,
                error_message=None if all_transformed_candidates else "No profiles were successfully transformed"
            )

        except Exception as e:
            self.logger.error(f"TRANSFORM WORKFLOW [{workflow_id}]: Transformation error: {str(e)}")
            return SchemaTransformationResponse(
                success=False,
                candidates_transformed=0,
                transformed_candidates=[],
                transformation_metadata={"workflow_id": workflow_id, "error": str(e)},
                execution_time_ms=0,
                error_message=f"Transformation step error: {str(e)}"
            )

    async def execute_quality_assessment_step(
        self,
        search_results: Optional[LinkedInSearchResponse] = None,
        transformation_results: Optional[List[Any]] = None
    ) -> Optional[Any]:
        """
        Execute only the quality assessment step of the workflow.

        Args:
            search_results: Optional search results to assess
            transformation_results: Optional transformation results to assess

        Returns:
            Quality assessment results
        """
        workflow_id = f"quality_only_{int(time.time() * 1000)}"

        try:
            self.logger.info(f"QUALITY WORKFLOW [{workflow_id}]: Executing quality assessment")

            # Create quality assessment
            quality_assessment = {
                "search_quality": None,
                "transformation_quality": None,
                "overall_quality_score": 0.0,
                "recommendations": []
            }

            # Assess search quality if provided
            if search_results:
                search_quality = self._assess_search_quality(search_results)
                quality_assessment["search_quality"] = search_quality

            # Assess transformation quality if provided
            if transformation_results:
                transformation_quality = self._assess_transformation_quality(transformation_results)
                quality_assessment["transformation_quality"] = transformation_quality

            # Calculate overall score
            scores = []
            if quality_assessment["search_quality"]:
                scores.append(quality_assessment["search_quality"]["score"])
            if quality_assessment["transformation_quality"]:
                scores.append(quality_assessment["transformation_quality"]["score"])

            if scores:
                quality_assessment["overall_quality_score"] = sum(scores) / len(scores)

            # Generate recommendations
            quality_assessment["recommendations"] = self._generate_quality_recommendations(quality_assessment)

            return quality_assessment

        except Exception as e:
            self.logger.error(f"QUALITY WORKFLOW [{workflow_id}]: Quality assessment error: {str(e)}")
            return None

    async def _execute_search_step(
        self, 
        search_request: LinkedInSearchRequest, 
        workflow_id: str
    ) -> LinkedInSearchResponse:
        """Execute the LinkedIn search step."""
        try:
            self.logger.info(f"LINKEDIN WORKFLOW [{workflow_id}]: Executing search with agent orchestrator")
            
            # Use the agent orchestrator for search
            search_response, _ = await self.agent_orchestrator.search_and_transform_candidates(
                search_request, transform_immediately=False
            )
            
            if search_response.success:
                self.logger.info(
                    f"LINKEDIN WORKFLOW [{workflow_id}]: Search successful - "
                    f"{search_response.total_results} total results, "
                    f"{len(search_response.profiles)} profiles returned"
                )
            else:
                self.logger.error(
                    f"LINKEDIN WORKFLOW [{workflow_id}]: Search failed - {search_response.error_message}"
                )
            
            return search_response
            
        except Exception as e:
            self.logger.error(f"LINKEDIN WORKFLOW [{workflow_id}]: Search step error: {str(e)}")
            return LinkedInSearchResponse(
                search_id=f"error_{workflow_id}",
                profiles=[],
                total_results=0,
                returned_results=0,
                search_filters_used=search_request.filters,
                search_metadata={"workflow_id": workflow_id, "error": str(e)},
                execution_time_ms=0,
                success=False,
                error_message=f"Search step execution error: {str(e)}"
            )
    
    async def _execute_transformation_step(
        self, 
        profiles: List[LinkedInProfile], 
        workflow_id: str
    ) -> Optional[SchemaTransformationResponse]:
        """Execute the schema transformation step."""
        try:
            self.logger.info(f"LINKEDIN WORKFLOW [{workflow_id}]: Transforming {len(profiles)} profiles")
            
            # Create transformation request
            transformation_request = SchemaTransformationRequest(
                linkedin_profiles=profiles,
                target_schema="smarthr_candidate",
                transformation_options={
                    "use_llm_enhancement": True,
                    "validate_output": True,
                    "include_confidence_scores": True,
                    "workflow_id": workflow_id
                }
            )
            
            # Execute transformation through agent
            transformation_agent = SchemaTransformationAgent(self.config)
            transformation_response = await transformation_agent.process_request(transformation_request)
            
            if transformation_response.success:
                self.logger.info(
                    f"LINKEDIN WORKFLOW [{workflow_id}]: Transformation successful - "
                    f"{len(transformation_response.transformed_candidates)} candidates transformed"
                )
                
                # Log confidence statistics
                if transformation_response.transformed_candidates:
                    confidences = [c.transformation_confidence for c in transformation_response.transformed_candidates]
                    avg_confidence = sum(confidences) / len(confidences)
                    self.logger.info(f"LINKEDIN WORKFLOW [{workflow_id}]: Average confidence: {avg_confidence:.2f}")
            else:
                self.logger.error(
                    f"LINKEDIN WORKFLOW [{workflow_id}]: Transformation failed - {transformation_response.error_message}"
                )
            
            return transformation_response
            
        except Exception as e:
            self.logger.error(f"LINKEDIN WORKFLOW [{workflow_id}]: Transformation step error: {str(e)}")
            return SchemaTransformationResponse(
                transformed_candidates=[],
                success=False,
                error_message=f"Transformation step execution error: {str(e)}",
                processing_time_ms=0
            )
    
    async def _execute_quality_assurance_step(
        self,
        search_response: LinkedInSearchResponse,
        transformation_response: Optional[SchemaTransformationResponse],
        workflow_id: str
    ) -> Dict[str, Any]:
        """Execute quality assurance and validation step."""
        try:
            self.logger.info(f"LINKEDIN WORKFLOW [{workflow_id}]: Executing quality assurance")
            
            quality_report = {
                "search_quality": self._assess_search_quality(search_response),
                "transformation_quality": None,
                "overall_quality_score": 0.0,
                "recommendations": []
            }
            
            if transformation_response and transformation_response.success:
                quality_report["transformation_quality"] = self._assess_transformation_quality(
                    transformation_response
                )
            
            # Calculate overall quality score
            search_score = quality_report["search_quality"]["score"]
            transformation_score = (
                quality_report["transformation_quality"]["score"] 
                if quality_report["transformation_quality"] else 0.5
            )
            
            quality_report["overall_quality_score"] = (search_score + transformation_score) / 2
            
            # Generate recommendations
            quality_report["recommendations"] = self._generate_quality_recommendations(quality_report)
            
            self.logger.info(
                f"LINKEDIN WORKFLOW [{workflow_id}]: Quality assessment completed - "
                f"Overall score: {quality_report['overall_quality_score']:.2f}"
            )
            
            return quality_report
            
        except Exception as e:
            self.logger.error(f"LINKEDIN WORKFLOW [{workflow_id}]: Quality assurance error: {str(e)}")
            return {
                "search_quality": {"score": 0.0, "issues": [str(e)]},
                "transformation_quality": None,
                "overall_quality_score": 0.0,
                "recommendations": ["Quality assessment failed - manual review recommended"]
            }
    
    def _assess_search_quality(self, search_response: LinkedInSearchResponse) -> Dict[str, Any]:
        """Assess the quality of search results."""
        quality_assessment = {
            "score": 0.0,
            "profile_count": len(search_response.profiles),
            "total_results": search_response.total_results,
            "issues": [],
            "strengths": []
        }
        
        # Base score from success
        if search_response.success:
            quality_assessment["score"] = 0.5
            quality_assessment["strengths"].append("Search executed successfully")
        else:
            quality_assessment["issues"].append("Search execution failed")
            return quality_assessment
        
        # Profile count assessment
        profile_count = len(search_response.profiles)
        if profile_count == 0:
            quality_assessment["issues"].append("No profiles found")
        elif profile_count < 5:
            quality_assessment["issues"].append("Very few profiles found")
            quality_assessment["score"] += 0.1
        elif profile_count < 15:
            quality_assessment["score"] += 0.2
            quality_assessment["strengths"].append("Moderate number of profiles found")
        else:
            quality_assessment["score"] += 0.3
            quality_assessment["strengths"].append("Good number of profiles found")
        
        # Profile completeness assessment
        if search_response.profiles:
            complete_profiles = sum(
                1 for profile in search_response.profiles
                if profile.first_name and profile.last_name and profile.headline
            )
            completeness_ratio = complete_profiles / len(search_response.profiles)
            
            if completeness_ratio > 0.8:
                quality_assessment["score"] += 0.2
                quality_assessment["strengths"].append("High profile completeness")
            elif completeness_ratio > 0.5:
                quality_assessment["score"] += 0.1
                quality_assessment["strengths"].append("Moderate profile completeness")
            else:
                quality_assessment["issues"].append("Low profile completeness")
        
        return quality_assessment
    
    def _assess_transformation_quality(self, transformation_response: SchemaTransformationResponse) -> Dict[str, Any]:
        """Assess the quality of transformation results."""
        quality_assessment = {
            "score": 0.0,
            "transformed_count": len(transformation_response.transformed_candidates),
            "average_confidence": 0.0,
            "validation_issues": 0,
            "issues": [],
            "strengths": []
        }
        
        if not transformation_response.success:
            quality_assessment["issues"].append("Transformation execution failed")
            return quality_assessment
        
        candidates = transformation_response.transformed_candidates
        if not candidates:
            quality_assessment["issues"].append("No candidates transformed")
            return quality_assessment
        
        # Base score for successful transformation
        quality_assessment["score"] = 0.4
        quality_assessment["strengths"].append("Transformation executed successfully")
        
        # Confidence assessment
        confidences = [c.transformation_confidence for c in candidates]
        avg_confidence = sum(confidences) / len(confidences)
        quality_assessment["average_confidence"] = avg_confidence
        
        if avg_confidence > 0.8:
            quality_assessment["score"] += 0.3
            quality_assessment["strengths"].append("High transformation confidence")
        elif avg_confidence > 0.6:
            quality_assessment["score"] += 0.2
            quality_assessment["strengths"].append("Moderate transformation confidence")
        else:
            quality_assessment["score"] += 0.1
            quality_assessment["issues"].append("Low transformation confidence")
        
        # Validation assessment
        validation_issues_count = 0
        for candidate in candidates:
            is_valid, validation_issues = validate_smarthr_candidate(candidate.candidate_info)
            if not is_valid:
                validation_issues_count += 1
        
        quality_assessment["validation_issues"] = validation_issues_count
        
        if validation_issues_count == 0:
            quality_assessment["score"] += 0.3
            quality_assessment["strengths"].append("All candidates pass validation")
        elif validation_issues_count < len(candidates) * 0.2:
            quality_assessment["score"] += 0.2
            quality_assessment["strengths"].append("Most candidates pass validation")
        else:
            quality_assessment["issues"].append("Many candidates have validation issues")
        
        return quality_assessment
    
    def _generate_quality_recommendations(self, quality_report: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on quality assessment."""
        recommendations = []
        
        search_quality = quality_report["search_quality"]
        transformation_quality = quality_report["transformation_quality"]
        
        # Search recommendations
        if search_quality["profile_count"] == 0:
            recommendations.append("Consider broadening search criteria or trying different keywords")
        elif search_quality["profile_count"] < 10:
            recommendations.append("Consider expanding search parameters to find more candidates")
        
        if "Low profile completeness" in search_quality.get("issues", []):
            recommendations.append("Consider using different LinkedIn data sources for more complete profiles")
        
        # Transformation recommendations
        if transformation_quality:
            if transformation_quality["average_confidence"] < 0.6:
                recommendations.append("Review transformation logic and consider manual validation")
            
            if transformation_quality["validation_issues"] > 0:
                recommendations.append("Review and fix validation issues in transformed candidates")
        
        # Overall recommendations
        overall_score = quality_report["overall_quality_score"]
        if overall_score < 0.5:
            recommendations.append("Overall quality is low - consider manual review of all results")
        elif overall_score < 0.7:
            recommendations.append("Consider spot-checking results for quality assurance")
        
        return recommendations
    
    def _initialize_workflow_options(self, options: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Initialize workflow options with defaults."""
        default_options = {
            "transform_profiles": True,
            "validate_transformations": True,
            "include_quality_assessment": True,
            "max_profiles_to_transform": 100,
            "enable_parallel_processing": True
        }
        
        if options:
            default_options.update(options)
        
        return default_options
    
    def _compile_workflow_results(
        self,
        workflow_id: str,
        search_response: LinkedInSearchResponse,
        transformation_response: Optional[SchemaTransformationResponse],
        quality_report: Dict[str, Any],
        start_time: datetime,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compile final workflow results."""
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return {
            "workflow_id": workflow_id,
            "success": True,
            "processing_time_ms": processing_time,
            "search_results": {
                "success": search_response.success,
                "total_results": search_response.total_results,
                "profiles_returned": len(search_response.profiles),
                "profiles": search_response.profiles if options.get("include_raw_profiles", False) else None
            },
            "transformation_results": {
                "success": transformation_response.success if transformation_response else False,
                "candidates_transformed": len(transformation_response.transformed_candidates) if transformation_response else 0,
                "transformed_candidates": transformation_response.transformed_candidates if transformation_response else []
            } if transformation_response else None,
            "quality_assessment": quality_report,
            "workflow_summary": {
                "profiles_found": len(search_response.profiles),
                "candidates_transformed": len(transformation_response.transformed_candidates) if transformation_response else 0,
                "overall_quality_score": quality_report["overall_quality_score"],
                "recommendations": quality_report["recommendations"]
            },
            "metadata": {
                "workflow_options": options,
                "execution_timestamp": start_time.isoformat(),
                "config_summary": {
                    "api_provider": self.config.api_config.provider,
                    "transformation_models": self.config.transformation_config.llm_models_order
                }
            }
        }
    
    def _create_workflow_failure_result(
        self,
        workflow_id: str,
        failure_reason: str,
        error_message: str,
        start_time: datetime
    ) -> Dict[str, Any]:
        """Create workflow failure result."""
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return {
            "workflow_id": workflow_id,
            "success": False,
            "error_message": error_message,
            "failure_reason": failure_reason,
            "processing_time_ms": processing_time,
            "search_results": None,
            "transformation_results": None,
            "quality_assessment": None,
            "workflow_summary": {
                "profiles_found": 0,
                "candidates_transformed": 0,
                "overall_quality_score": 0.0,
                "recommendations": ["Workflow failed - investigate error and retry"]
            },
            "metadata": {
                "execution_timestamp": start_time.isoformat(),
                "failure_timestamp": datetime.now().isoformat()
            }
        }
    
    def _update_workflow_stats(self, workflow_result: Dict[str, Any], start_time: datetime):
        """Update workflow statistics."""
        self.workflow_stats["total_workflows"] += 1
        
        if workflow_result["success"]:
            self.workflow_stats["successful_workflows"] += 1
            self.workflow_stats["total_profiles_found"] += workflow_result["workflow_summary"]["profiles_found"]
            self.workflow_stats["total_profiles_transformed"] += workflow_result["workflow_summary"]["candidates_transformed"]
        else:
            self.workflow_stats["failed_workflows"] += 1
        
        # Update average processing time
        processing_time = workflow_result["processing_time_ms"]
        current_avg = self.workflow_stats["average_processing_time_ms"]
        total_workflows = self.workflow_stats["total_workflows"]
        
        self.workflow_stats["average_processing_time_ms"] = (
            (current_avg * (total_workflows - 1) + processing_time) / total_workflows
        )
    
    def get_workflow_stats(self) -> Dict[str, Any]:
        """Get workflow performance statistics."""
        return {
            "orchestrator_stats": self.workflow_stats.copy(),
            "agent_stats": self.agent_orchestrator.get_agent_status(),
            "timestamp": datetime.now().isoformat()
        }


# Convenience functions
async def execute_linkedin_candidate_search(
    config: LinkedInIntegrationConfig,
    keywords: List[str],
    **search_params
) -> Dict[str, Any]:
    """Convenience function for LinkedIn candidate search workflow."""
    from utils.linkedin_agent_communication import LinkedInAgentCommunicationProtocol
    
    orchestrator = LinkedInWorkflowOrchestrator(config)
    
    search_request = LinkedInAgentCommunicationProtocol.create_search_request(
        keywords=keywords,
        **search_params
    )
    
    return await orchestrator.execute_candidate_sourcing_workflow(search_request)


def create_linkedin_workflow_orchestrator(config: LinkedInIntegrationConfig) -> LinkedInWorkflowOrchestrator:
    """Create LinkedIn workflow orchestrator with configuration."""
    return LinkedInWorkflowOrchestrator(config)
