"""Database connection and session management for ParserGPT POC."""

import logging
from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from .config import get_settings
from .models import Base

logger = logging.getLogger(__name__)

# Global variables for database engine and session factory
engine = None
async_session_factory = None


def create_database_engine():
    """Create and configure the database engine."""
    global engine
    
    settings = get_settings()
    
    # Configure engine based on database type
    if "sqlite" in settings.database_url:
        # SQLite configuration
        engine = create_async_engine(
            settings.database_url,
            echo=settings.debug,
            poolclass=StaticPool,
            connect_args={
                "check_same_thread": False,
            },
        )
    else:
        # PostgreSQL configuration
        engine = create_async_engine(
            settings.database_url,
            echo=settings.debug,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
    
    logger.info(f"Database engine created for: {settings.database_url}")
    return engine


def create_session_factory():
    """Create the async session factory."""
    global async_session_factory, engine
    
    if engine is None:
        engine = create_database_engine()
    
    async_session_factory = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autoflush=True,
        autocommit=False,
    )
    
    logger.info("Async session factory created")
    return async_session_factory


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function to get async database session.
    
    This function is designed to be used with FastAPI's dependency injection.
    """
    global async_session_factory
    
    if async_session_factory is None:
        async_session_factory = create_session_factory()
    
    async with async_session_factory() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_database():
    """Initialize the database by creating all tables."""
    global engine
    
    if engine is None:
        engine = create_database_engine()
    
    try:
        async with engine.begin() as conn:
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_database():
    """Close the database engine and clean up connections."""
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("Database engine disposed")


# Context manager for manual session management
class DatabaseSession:
    """Context manager for manual database session management."""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self) -> AsyncSession:
        global async_session_factory
        
        if async_session_factory is None:
            async_session_factory = create_session_factory()
        
        self.session = async_session_factory()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type:
                await self.session.rollback()
            else:
                await self.session.commit()
            await self.session.close()


# Utility functions for common database operations
async def get_session() -> AsyncSession:
    """Get a new database session (for use outside FastAPI)."""
    global async_session_factory
    
    if async_session_factory is None:
        async_session_factory = create_session_factory()
    
    return async_session_factory()


async def execute_with_session(func, *args, **kwargs):
    """Execute a function with a database session."""
    async with DatabaseSession() as session:
        return await func(session, *args, **kwargs)
