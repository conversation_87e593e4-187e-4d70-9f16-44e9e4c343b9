# 🐳 Docker Deployment Guide

This guide explains how to deploy the Financial Analysis Project using Docker and Docker Compose.

## 📋 Prerequisites

### Required Software
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For cloning the repository

### System Requirements
- **RAM**: 4GB minimum (8GB recommended)
- **Disk Space**: 2GB free space
- **Network**: Internet connection for API calls

## 🚀 Quick Start with Docker

### 1. <PERSON><PERSON> and Setup
```bash
# Clone the repository
git clone <repository-url>
cd financial_analysis_project

# Copy environment template
cp backend/.env.example backend/.env

# Edit .env and add your GROQ API key
nano backend/.env  # or use your preferred editor
```

### 2. Deploy with One Command
```bash
# Make deploy script executable
chmod +x deploy.sh

# Deploy in production mode
./deploy.sh prod
```

### 3. Access the Application
- **Frontend**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔧 Deployment Options

### Production Deployment
```bash
# Deploy in production mode
./deploy.sh prod

# Or manually with docker-compose
docker-compose up --build -d
```

**Features:**
- Optimized for performance
- Health checks enabled
- Automatic restarts
- Production-ready configuration

### Development Deployment
```bash
# Deploy in development mode
./deploy.sh dev

# Or manually
docker-compose -f docker-compose.dev.yml up --build -d
```

**Features:**
- Auto-reload on code changes
- Debug mode enabled
- Volume mounts for live editing
- Development-friendly logging

## 📊 Managing Services

### View Service Status
```bash
./deploy.sh status
# Or
docker-compose ps
```

### View Logs
```bash
# Production logs
./deploy.sh logs

# Development logs
./deploy.sh logs dev

# Follow logs in real-time
docker-compose logs -f
```

### Stop Services
```bash
./deploy.sh stop
# Or
docker-compose down
```

### Restart Services
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart backend
docker-compose restart frontend
```

## 🔍 Troubleshooting

### Common Issues

#### Issue: "GROQ_API_KEY not set"
```bash
# Check environment file
cat backend/.env

# Should contain:
GROQ_API_KEY=gsk_your_actual_api_key_here
```

#### Issue: "Port already in use"
```bash
# Check what's using the ports
lsof -i :8000  # Backend port
lsof -i :8501  # Frontend port

# Stop conflicting services
./deploy.sh stop
```

#### Issue: "Service unhealthy"
```bash
# Check service logs
docker-compose logs backend
docker-compose logs frontend

# Check health status
docker-compose ps
```

#### Issue: "Cannot connect to backend"
```bash
# Verify backend is running
curl http://localhost:8000/health

# Check network connectivity
docker network ls
docker network inspect financial-analysis-project_financial-analysis-network
```

### Debug Commands

#### Access Container Shell
```bash
# Backend container
docker-compose exec backend bash

# Frontend container
docker-compose exec frontend bash
```

#### Check Container Resources
```bash
# View resource usage
docker stats

# View container details
docker-compose ps -a
```

#### Reset Everything
```bash
# Complete cleanup
./deploy.sh clean

# Remove all containers and volumes
docker-compose down --volumes --remove-orphans
docker system prune -a
```

## ⚙️ Configuration

### Environment Variables

#### Backend (.env file)
```env
# Required
GROQ_API_KEY=gsk_your_api_key_here

# Optional
DEBUG=False
MAX_FILE_SIZE_MB=50
BATCH_PROCESSING_THRESHOLD=500
BATCH_SIZE=200
GROQ_MODEL_FAST=llama-3.1-8b-instant
GROQ_MODEL_ANALYSIS=llama3-70b-8192
GROQ_MODEL_REPORT=gemma2-9b-it
```

#### Docker Compose Variables
```env
# Create .env in project root for docker-compose
GROQ_API_KEY=gsk_your_api_key_here
```

### Port Configuration

#### Default Ports
- **Backend**: 8000
- **Frontend**: 8501

#### Custom Ports
```yaml
# In docker-compose.yml
services:
  backend:
    ports:
      - "8080:8000"  # Custom backend port
  frontend:
    ports:
      - "8502:8501"  # Custom frontend port
```

### Volume Mounts

#### Development Mode
```yaml
volumes:
  - ./backend:/app      # Live code editing
  - ./frontend:/app     # Live code editing
```

#### Production Mode
```yaml
volumes:
  - backend_data:/app/data  # Persistent data storage
```

## 🔒 Security Considerations

### Production Security
- Services run as non-root users
- Environment variables for sensitive data
- Health checks for service monitoring
- Network isolation between services

### Recommended Practices
```bash
# Use secrets for API keys (Docker Swarm)
echo "gsk_your_api_key" | docker secret create groq_api_key -

# Limit container resources
docker-compose up --scale backend=1 --scale frontend=1
```

## 📈 Performance Optimization

### Resource Limits
```yaml
# Add to docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### Caching
```dockerfile
# Multi-stage builds for smaller images
FROM python:3.11-slim as builder
# ... build dependencies

FROM python:3.11-slim as runtime
# ... copy only necessary files
```

## 🚀 Production Deployment

### Using Docker Swarm
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml financial-analysis
```

### Using Kubernetes
```bash
# Convert docker-compose to k8s
kompose convert

# Deploy to k8s
kubectl apply -f .
```

### Cloud Deployment
- **AWS**: Use ECS or EKS
- **Google Cloud**: Use Cloud Run or GKE
- **Azure**: Use Container Instances or AKS

## 📝 Maintenance

### Regular Tasks
```bash
# Update images
docker-compose pull
docker-compose up -d

# Clean up unused resources
docker system prune -f

# Backup volumes
docker run --rm -v financial-analysis-project_backend_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data
```

### Monitoring
```bash
# View resource usage
docker stats

# Monitor logs
docker-compose logs -f --tail=100
```

## 🆘 Support

### Getting Help
1. Check service logs: `./deploy.sh logs`
2. Verify configuration: `./deploy.sh status`
3. Review this documentation
4. Check the main [Troubleshooting Guide](troubleshooting.md)

### Useful Commands Reference
```bash
# Quick reference
./deploy.sh prod     # Deploy production
./deploy.sh dev      # Deploy development
./deploy.sh logs     # View logs
./deploy.sh stop     # Stop services
./deploy.sh status   # Check status
./deploy.sh clean    # Clean up
```

---

**Next**: Check the [Deployment Guide](deployment.md) for advanced deployment scenarios.
