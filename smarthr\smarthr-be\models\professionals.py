from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, field_validator


class Professional(BaseModel):
    id: Optional[str] = Field(None, description="Unique identifier for the professional.")
    professional_info: Optional[dict] = Field(None, description="Information about the professional.")
    source: Optional[str] = Field(None, description="Source of the professional information.")
    reason: Optional[str] = Field(None, description="Reason for adding the professional.")
    to_be_embebbed: Optional[str] = Field(None, description="Text to be embedded for the professional.")
    embedding: Optional[List[float]] = Field(None, description="Embedding vector for the professional.")
    is_active: Optional[bool] = Field(None, description="Indicates if the professional is active.")
    is_deleted: Optional[bool] = Field(None, description="Indicates if the professional is deleted.")
    created_at: Optional[datetime] = Field(None, description="Timestamp when the professional was created.")
    created_by: Optional[str] = Field(None, description="User who created the professional.")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the professional was last updated.")
    updated_by: Optional[str] = Field(None, description="User who last updated the professional.")

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class Professionals(BaseModel):
    professionals: List[Professional] = Field(..., description="List of professionals.")
    total_items: int = Field(description='Number of items returned in the response')

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ProfessionalCreate(BaseModel):
    professional_info: Optional[dict] = Field(None, description="Information about the professional.")
    to_be_embebbed: Optional[str] = Field(None, description="Text to be embedded for the professional.")
    embedding: Optional[List[float]] = Field(None, description="Embedding vector for the professional.")
    source: Optional[str] = Field(None, description="Source of the professional information.")
    created_by: Optional[str] = Field(None, description="User who created the professional.")

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ProfessionalUpdate(BaseModel):
    id: Optional[str] = Field(None, description="Unique identifier for the professional.")
    professional_info: Optional[dict] = Field(None, description="Information about the professional.")
    to_be_embebbed: Optional[str] = Field(None, description="Text to be embedded for the professional.")
    embedding: Optional[List[float]] = Field(None, description="Embedding vector for the professional.")
    source: Optional[str] = Field(None, description="Source of the professional information.")
    updated_by: Optional[str] = Field(None, description="User who last updated the professional.")

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ProfessionalFilters(BaseModel):
    search_term: Optional[str] = None  # Search term for professional info
    status: Optional[bool] = None  # True for active, False for inactive
    source: Optional[str] = None  # e.g. "LinkedIn"
    created_by: Optional[str] = None  # e.g. "John Doe"
    created_from: Optional[datetime] = None
    created_to: Optional[datetime] = None

    @field_validator('created_from', 'created_to', mode='before')
    @classmethod
    def parse_empty_string_as_none(cls, v):
        if v == '':
            return None
        return v
