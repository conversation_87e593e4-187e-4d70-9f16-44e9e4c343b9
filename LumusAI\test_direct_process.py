#!/usr/bin/env python3
"""
Test script to call the regular /process endpoint directly to check Langfuse tracing.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_direct_cv_processing():
    """Test the regular /process endpoint with CV data."""
    
    print("🧪 Testing Direct CV Processing (/process endpoint)")
    print("=" * 60)
    
    # Test data - simple CV text with sensitive information
    cv_text = """
    <PERSON>
    Senior Software Engineer
    
    Contact Information:
    Email: <EMAIL>
    Phone: ************
    Credit Card: 4111 1111 1111 1111
    SECRET_API_KEY: SECRET_12345
    
    Professional Experience:
    - Senior Developer at Tech Corp (2020-2023)
      * Led development of microservices architecture
      * Managed team of 5 developers
      * Technologies: Python, React, AWS
    
    - Junior Developer at StartupXYZ (2018-2020)
      * Built web applications using Django
      * Implemented REST APIs
      * Technologies: Python, JavaScript, PostgreSQL
    
    Skills:
    - Programming: Python, JavaScript, Java, C++
    - Frameworks: React, Django, Flask, Spring Boot
    - Databases: PostgreSQL, MongoDB, Redis
    - Cloud: AWS, Docker, Kubernetes
    - Machine Learning: TensorFlow, PyTorch, scikit-learn
    
    Education:
    - BS Computer Science, University ABC (2014-2018)
      * GPA: 3.8/4.0
      * Relevant coursework: Data Structures, Algorithms, Machine Learning
    
    Certifications:
    - AWS Certified Solutions Architect
    - Google Cloud Professional Developer
    
    Languages:
    - English (Native)
    - Spanish (Fluent)
    - French (Conversational)
    """
    
    # Prepare the request
    url = f"{BASE_URL}/process"
    
    data = {
        "action": "cv",
        "data": cv_text
    }
    
    print(f"📞 Making request to: {url}")
    print(f"📝 Action: {data['action']}")
    print(f"📊 Data length: {len(cv_text)} characters")
    print("🔍 This should trigger Langfuse tracing immediately (not background)")
    
    try:
        # Make the request
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(url, data=data, timeout=60)  # Longer timeout for processing
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check if we got structured CV data
            if isinstance(result, dict):
                print("📄 Structured data received:")
                
                # Show key fields if available
                if 'personal_information' in result:
                    personal = result['personal_information']
                    print(f"   - Name: {personal.get('full_name', 'N/A')}")
                    print(f"   - Email: {personal.get('email', 'N/A')}")
                    print(f"   - Phone: {personal.get('phone_number', 'N/A')}")
                
                if 'work_experience' in result:
                    experiences = result['work_experience']
                    print(f"   - Work Experiences: {len(experiences) if experiences else 0}")
                
                if 'skills' in result:
                    skills = result['skills']
                    print(f"   - Skills: {len(skills) if skills else 0}")
                
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"   - Tokens Used: {tokens.get('total_tokens', 'N/A')}")
                    print(f"   - Cost: ${tokens.get('cost', 0):.4f}")
                
                print(f"\n📊 Full response size: {len(json.dumps(result))} characters")
            else:
                print(f"📄 Response: {result}")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (>60 seconds)")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    
    return True

def test_direct_invoice_processing():
    """Test the regular /process endpoint with invoice data."""
    
    print("\n🧪 Testing Direct Invoice Processing (/process endpoint)")
    print("=" * 60)
    
    # Test data - simple invoice text with sensitive information
    invoice_text = """
    INVOICE #INV-2024-001
    
    Date: January 15, 2024
    Due Date: February 15, 2024
    
    Bill To:
    ACME Corporation
    123 Business Street
    New York, NY 10001
    
    Contact: <EMAIL>
    Phone: ************
    Payment Card: 5555 4444 3333 2222
    
    Items:
    1. Software Development Services
       - Hours: 40
       - Rate: $150/hour
       - Amount: $6,000.00
    
    2. Cloud Infrastructure Setup
       - Flat rate service
       - Amount: $2,500.00
    
    3. Technical Support (3 months)
       - Monthly rate: $500
       - Amount: $1,500.00
    
    Subtotal: $10,000.00
    Tax (8.25%): $825.00
    Total: $10,825.00
    
    Payment Terms: Net 30
    
    Internal Notes:
    SECRET_CLIENT_ID: SECRET_ACME_789
    CONFIDENTIAL_PROJECT_CODE: CONF_PROJ_2024_001
    """
    
    # Prepare the request
    url = f"{BASE_URL}/process"
    
    data = {
        "action": "invoice",
        "data": invoice_text
    }
    
    print(f"📞 Making request to: {url}")
    print(f"📝 Action: {data['action']}")
    print(f"📊 Data length: {len(invoice_text)} characters")
    
    try:
        # Make the request
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(url, data=data, timeout=60)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check if we got structured invoice data
            if isinstance(result, dict):
                print("📄 Structured data received:")
                
                # Show key fields if available
                if 'invoice_number' in result:
                    print(f"   - Invoice Number: {result.get('invoice_number', 'N/A')}")
                
                if 'total_amount' in result:
                    print(f"   - Total Amount: {result.get('total_amount', 'N/A')}")
                
                if 'customer_info' in result:
                    customer = result['customer_info']
                    print(f"   - Customer: {customer.get('name', 'N/A')}")
                
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"   - Tokens Used: {tokens.get('total_tokens', 'N/A')}")
                    print(f"   - Cost: ${tokens.get('cost', 0):.4f}")
                
                print(f"\n📊 Full response size: {len(json.dumps(result))} characters")
            else:
                print(f"📄 Response: {result}")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (>60 seconds)")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    
    return True

def main():
    """Main test function."""
    
    print("🚀 Testing Direct Process Endpoints with Langfuse Tracing")
    print("=" * 70)
    
    # Check if the service is running
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Test CV processing
    print("\n" + "=" * 70)
    cv_success = test_direct_cv_processing()
    
    # Wait a bit between tests
    print("\n⏳ Waiting 3 seconds before next test...")
    time.sleep(3)
    
    # Test invoice processing
    print("\n" + "=" * 70)
    invoice_success = test_direct_invoice_processing()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 Test Summary")
    print("=" * 70)
    
    if cv_success and invoice_success:
        print("✅ Both tests completed successfully")
    elif cv_success or invoice_success:
        print("⚠️  One test succeeded, one failed")
    else:
        print("❌ Both tests failed")
    
    print("\n📋 What to check now:")
    print("1. Application console logs for debug messages:")
    print("   - Look for '🔍' debug messages during processing")
    print("   - Check if callbacks are being used:")
    print("     * '🔍 _get_callbacks: Added Langfuse handler'")
    print("     * '🔍 get_structured_data: Using X callback(s)'")
    print("     * '🔍 About to call structured_model.ainvoke'")
    print("     * '🔍 structured_model.ainvoke completed successfully'")
    
    print("\n2. Langfuse dashboard:")
    print("   - http://157.230.167.30:3000")
    print("   - Look for new traces with timestamps matching the test")
    print("   - Check if sensitive data is masked (if masking was enabled)")
    
    print("\n3. If no debug messages appear:")
    print("   - The LangChain client might not be the same instance")
    print("   - There might be an issue with application state")
    print("   - The processors might not be calling the LangChain methods")
    
    print("\n4. If debug messages appear but no traces in Langfuse:")
    print("   - Network connectivity issue to Langfuse")
    print("   - Langfuse server processing issue")
    print("   - CallbackHandler configuration issue")

if __name__ == "__main__":
    main()
