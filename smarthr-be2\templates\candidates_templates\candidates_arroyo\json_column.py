def format_education(education_list):
    if not education_list:
        return ""
    edu_str_list = []
    for edu in education_list:
        institution = edu.get("institution_name", "Unknown Institution")
        degree = edu.get("degree", "")
        field = edu.get("field_of_study", "")
        # We omit start/end dates or location if needed, or just keep them:
        description = edu.get("description", "")
        edu_str = f"Institution: {institution}, Degree: {degree}, Field: {field}, Details: {description}"
        edu_str_list.append(edu_str)
    return "\n".join(edu_str_list)


def format_work_experience(work_list):
    if not work_list:
        return ""
    work_str_list = []
    for job in work_list:
        title = job.get("job_title", "Unknown Title")
        company = job.get("company_name", "Unknown Company")
        responsibilities = job.get("responsibilities", [])
        resp_text = "; ".join(responsibilities) if responsibilities else ""
        work_str = f"Title: {title} at {company}. Responsibilities: {resp_text}"
        work_str_list.append(work_str)
    return "\n".join(work_str_list)


def format_skills(skills_list):
    if not skills_list:
        return ""
    skills_str_list = []
    for skill in skills_list:
        name = skill.get("name", "Unknown Skill")
        level = skill.get("proficiency_level", "")
        years = skill.get("years_of_experience", "")
        skills_str_list.append(f"{name} (Level: {level}, Experience: {years} years)")
    return ", ".join(skills_str_list)


def format_soft_skills(soft_list):
    if not soft_list:
        return ""
    soft_str_list = []
    for s in soft_list:
        name = s.get("name", "Unknown Soft Skill")
        desc = s.get("description", "")
        soft_str_list.append(f"{name}: {desc}")
    return "; ".join(soft_str_list)


def format_certifications(cert_list):
    if not cert_list:
        return ""
    cert_str_list = []
    for c in cert_list:
        name = c.get("name", "Unknown Certification")
        org = c.get("issuing_organization", "")
        cert_str_list.append(f"{name} from {org}")
    return "; ".join(cert_str_list)


def format_languages(lang_list):
    if not lang_list:
        return ""
    lang_str_list = []
    for lang in lang_list:
        language = lang.get("language", "Unknown Language")
        level = lang.get("proficiency_level", "")
        lang_str_list.append(f"{language} ({level})")
    return ", ".join(lang_str_list)


def format_projects(project_list):
    if not project_list:
        return ""
    proj_str_list = []
    for p in project_list:
        name = p.get("name", "Unknown Project")
        desc = p.get("description", "")
        role = p.get("role", "")
        techs = p.get("technologies_used", [])
        techs_str = ", ".join(techs)
        proj_str_list.append(
            f"Project: {name}, Role: {role}, Description: {desc}, Technologies: {techs_str}"
        )
    return "\n".join(proj_str_list)


from models.llm import inference_with_fallback
from langchain_core.messages import HumanMessage, SystemMessage
import json


def prepare_candidate_for_embedding(candidate_info: dict) -> str:
    """
    Extracts non-PII and relevant professional info from candidate_info
    and returns a formatted text for embedding.
    """
    main_task = """
    You are an expert text formatter and content enricher for candidate data to be used in embedding or semantic analysis.
    You will receive a candidate’s information in JSON format. Your task is to enrich and structure this information into a clean, valuable, and human-readable text summary.

    Suggested output format:
    Summary: <summary>
    Education: <Education>
    Work Experience: <Work Experience>
    Technical Skills: <Technical Skills>
    Soft Skills: <Soft Skills>
    Certifications: <Certifications>
    Languages: <Languages>
    Projects: <Projects>
    -----------------------
    Important Instructions:
    • Do not include any personal information such as full name, email, phone number, address, references, or hobbies.
    • Avoid repetition of the same content across multiple sections.
    • Use concise and professional language.
    • Where data is missing, simply omit the section or leave it empty.
    • The output should be optimized for readability and downstream processing (e.g., semantic search, candidate comparison, etc.).
    """
    messages = [
        HumanMessage(content=f"Candidate Info in JSON: {json.dumps(candidate_info)}"),
    ]
    response = inference_with_fallback(
        task_prompt=main_task,
        user_messages=messages,
        models_order=["gpt-4o", "gpt-4o-mini", "llama4-pro", "llama4-light"],
    )

    # Trim whitespace
    return response.content
