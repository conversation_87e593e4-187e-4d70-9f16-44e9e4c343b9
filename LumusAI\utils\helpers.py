import csv
from openpyxl import load_workbook
import pandas as pd
from io import StringIO, BytesIO
import pyexcel as p
import os
from PIL import Image
import fitz
from pypdf import PdfReader
from docx import Document

def convert_excel_to_csv(excel_path: str) -> list:

    """
    Convert an XLSX file to CSV format, handling multiple sheets.

    Processes each sheet in the XLSX file separately and converts it to CSV format.
    Maintains data integrity and handles various cell types appropriately.

    Args:
        excel_path (str): Full path to the XLSX file to convert

    Returns:
        list[str]: List of CSV strings, one for each sheet in the XLSX file

    Raises:
        FileNotFoundError: If the XLSX file doesn't exist
        ValueError: If the file is corrupted or in invalid format
    """

    output_list = []
    workbook = load_workbook(excel_path, data_only=True)
    for sheet_name in workbook.sheetnames:
        sheet = workbook[sheet_name]
        output = StringIO()
        csv_writer = csv.writer(output)
        for row in sheet.iter_rows(values_only=True):
            csv_writer.writerow(row)
        csv_content = output.getvalue()
        output.close()
        output_list.append(csv_content)
    return output_list

def convert_xls_to_csv(xls_path: str) -> list:

    """
    Convert an XLS file to CSV format, handling multiple sheets.

    Processes each sheet in the XLS file separately and converts it to CSV format.
    Maintains data integrity and handles various cell types appropriately.

    Args:
        xls_path (str): Full path to the XLS file to convert

    Returns:
        list[str]: List of CSV strings, one for each sheet in the XLS file

    Raises:
        FileNotFoundError: If the XLS file doesn't exist
        ValueError: If the file is corrupted or in invalid format
    """

    output_list = []
    sheets = p.get_book(file_name=xls_path)
    for sheet in sheets:
        output = StringIO()
        for row in sheet:
            output.write(','.join(str(cell) for cell in row) + '\n')
        csv_content = output.getvalue()
        output.close()
        output_list.append(csv_content)
    return output_list

def format_float_values(obj):

    """
    Recursively format all float values within a nested structure to two decimal places.

    Traverses through nested dictionaries and lists, formatting any float values
    encountered to maintain consistent decimal precision throughout the data structure.

    Features:
    - Preserves original data structure
    - Handles nested dictionaries and lists of any depth
    - Only modifies float values, leaving other types unchanged
    - Maintains data type consistency

    Args:
        obj (Any): Input data structure to process. Can be:
            - dict: Processes all values recursively
            - list: Processes all elements recursively
            - float: Formats to 2 decimal places
            - other types: Returns unchanged

    Returns:
        Any: New data structure with identical layout but formatted float values

    Examples:
        >>> format_float_values({'price': 10.123, 'items': [1.234, 2.345]})
        {'price': 10.12, 'items': [1.23, 2.35]}
        >>> format_float_values([1.234, {'value': 2.345}])
        [1.23, {'value': 2.35}]
    """

    if isinstance(obj, dict):
        return {k: format_float_values(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [format_float_values(v) for v in obj]
    elif isinstance(obj, float):
        return float(f"{obj:.2f}")
    else:
        return obj


def compress_image_quality(
    image_path: str,
    temp_dir: str,
    max_size_mb: int = 10,
    initial_quality: int = 90,
    step_quality: int = 10
) -> str:
    """
    Compresses an image by iteratively reducing quality until it meets size requirements.

    Implements an adaptive compression algorithm that:
    1. Checks if compression is needed
    2. Iteratively reduces image quality
    3. Maintains aspect ratio and color fidelity
    4. Optimizes file size while preserving acceptable quality

    Args:
        image_path (str): Path to the original image file
        temp_dir (str): Directory for storing the compressed image
        max_size_mb (int, optional): Maximum allowed file size in megabytes. Defaults to 10.
        initial_quality (int, optional): Starting compression quality (1-100). Defaults to 90.
        step_quality (int, optional): Quality reduction per iteration. Defaults to 10.

    Returns:
        str: Path to the resulting image (either compressed or original)

    Raises:
        ValueError: If compression cannot achieve target size
        IOError: If image file operations fail
        Image.UnidentifiedImageError: If input file is not a valid image

    Example:
        >>> compressed_path = compress_image_quality('large.jpg', '/tmp', max_size_mb=5)
        >>> os.path.getsize(compressed_path) <= 5 * 1024 * 1024
        True
    """
    original_size_bytes = os.path.getsize(image_path)
    original_size_mb = original_size_bytes / (1024 * 1024)

    if original_size_mb <= max_size_mb:
        return image_path  # Return original if already below the threshold

    compressed_image_path = os.path.join(temp_dir, "compressed_image.jpg")
    current_quality = initial_quality

    while current_quality > step_quality:
        with Image.open(image_path) as img:
            img = img.convert("RGB")  # Ensure compatibility with JPEG
            img.save(compressed_image_path, "JPEG", quality=current_quality, optimize=True)

        compressed_size_bytes = os.path.getsize(compressed_image_path)
        compressed_size_mb = compressed_size_bytes / (1024 * 1024)

        if compressed_size_mb <= max_size_mb:
            return compressed_image_path  # Return compressed image path

        current_quality -= step_quality

    raise ValueError("Could not reduce the image size below the allowed limit.")


def render_pdf_page_with_dpi(
    page: fitz.Page,
    temp_dir: str,
    initial_dpi: int = 600,
    max_size_mb: int = 10,
    step_dpi: int = 50
) -> str:
    """
    Renders a PDF page to image with adaptive DPI to meet size constraints.

    Implements an intelligent rendering algorithm that:
    1. Starts with high DPI for maximum quality
    2. Progressively reduces DPI if size limit is exceeded
    3. Optimizes for OCR readability
    4. Balances quality and file size

    Args:
        page (fitz.Page): PyMuPDF page object to render
        temp_dir (str): Directory for temporary image storage
        initial_dpi (int, optional): Starting resolution in DPI. Defaults to 600.
        max_size_mb (int, optional): Maximum allowed image size in MB. Defaults to 10.
        step_dpi (int, optional): DPI reduction per iteration. Defaults to 50.

    Returns:
        str: Path to the rendered image file

    Raises:
        ValueError: If target size cannot be achieved
        RuntimeError: If rendering fails
        IOError: If file operations fail

    Note:
        The function prioritizes text clarity for OCR while maintaining
        reasonable file sizes. Higher initial_dpi may improve OCR accuracy
        but increases processing time.
    """
    current_dpi = initial_dpi

    while current_dpi > step_dpi:
        image_filename = f"page-{page.number}-dpi-{current_dpi}.png"
        image_path = os.path.join(temp_dir, image_filename)

        # Render the PDF page at the current DPI
        pix = page.get_pixmap(dpi=current_dpi)
        pix.save(image_path)

        # Check image size
        image_size_bytes = os.path.getsize(image_path)
        image_size_mb = image_size_bytes / (1024 * 1024)

        if image_size_mb <= max_size_mb:
            return image_path

        os.remove(image_path)
        current_dpi -= step_dpi

    raise ValueError("❌ Could not render the image below the allowed limit.")

async def get_text_from_pdf(pdf_file_path: str) -> str:
    """
    Extracts text content from a PDF file with robust error handling.

    Features:
    - Maintains document structure and formatting
    - Handles multi-page documents
    - Preserves paragraph breaks
    - Robust against malformed PDFs
    - Memory-efficient processing

    Args:
        pdf_file_path (str): Path to the PDF file to process

    Returns:
        str: Extracted text content with preserved formatting

    Raises:
        ValueError: If file not found or invalid path
        RuntimeError: If PDF processing fails
        PermissionError: If file access is denied

    Example:
        >>> text = await get_text_from_pdf('document.pdf')
        >>> len(text) > 0
        True
    """
    text = ""
    try:
        with open(pdf_file_path, "rb") as f:
            pdf_bytes_stream = BytesIO(f.read())
            pdf_reader = PdfReader(pdf_bytes_stream)

            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n\n"

        return text

    except FileNotFoundError:
        raise ValueError(f"No se encontró el archivo: {pdf_file_path}")
    except Exception as e:
        raise RuntimeError(f"Error al leer el PDF: {e}")


async def get_text_from_word(word_file_path: str) -> str:
    """
    Extracts text content from a Word document, including headers and footers.

    Features:
    - Comprehensive content extraction:
        - Main body text
        - Headers and footers
        - Paragraph formatting
    - Maintains document structure
    - Preserves text formatting
    - Memory-efficient processing

    Args:
        word_file_path (str): Path to the Word document (.docx)

    Returns:
        str: Complete text content with preserved structure

    Raises:
        RuntimeError: If document processing fails
        ValueError: If file format is invalid
        PermissionError: If file access is denied

    Note:
        Only supports .docx format (modern Word documents).
        For .doc files, conversion to .docx is required first.
    """
    text = ""
    try:
        # Load the Word document
        document = Document(word_file_path)

        # Extract main body text
        for paragraph in document.paragraphs:
            text += paragraph.text + "\n\n"

        # Extract text from headers and footers
        for section in document.sections:
            header = section.header
            footer = section.footer

            if header and header.paragraphs:
                text += "\nHEADER:\n"
                for paragraph in header.paragraphs:
                    text += paragraph.text + "\n"

            if footer and footer.paragraphs:
                text += "\nFOOTER:\n"
                for paragraph in footer.paragraphs:
                    text += paragraph.text + "\n"

        return text
    except Exception as e:
        raise RuntimeError(f"Error while reading the Word file: {e}")


async def get_text_from_word_by_pages(word_file_path: str) -> list:
    """
    Extracts text content from a Word document page by page.

    Features:
    - Splits content by pages
    - Maintains document structure within each page
    - Preserves text formatting
    - Memory-efficient processing

    Args:
        word_file_path (str): Path to the Word document (.docx)

    Returns:
        list: List of strings, each containing text from one page

    Raises:
        RuntimeError: If document processing fails
        ValueError: If file format is invalid
        PermissionError: If file access is denied

    Note:
        Only supports .docx format (modern Word documents).
        For .doc files, conversion to .docx is required first.
        Page detection is approximate as DOCX doesn't have explicit page breaks.
    """
    try:
        # Load the Word document
        document = Document(word_file_path)

        # Get all paragraphs
        paragraphs = document.paragraphs

        # Estimate pages based on paragraph count (approximate method)
        # A typical page might have around 15-20 paragraphs
        paragraphs_per_page = 15

        # Calculate estimated page count
        total_paragraphs = len(paragraphs)
        page_count = max(1, (total_paragraphs + paragraphs_per_page - 1) // paragraphs_per_page)

        # Split paragraphs into pages
        pages_text = []
        for page_idx in range(page_count):
            start_idx = page_idx * paragraphs_per_page
            end_idx = min(start_idx + paragraphs_per_page, total_paragraphs)

            # Combine paragraphs for this page
            page_text = ""
            for i in range(start_idx, end_idx):
                page_text += paragraphs[i].text + "\n\n"

            # Add headers and footers to the first page only
            if page_idx == 0 and document.sections:
                section = document.sections[0]
                header = section.header
                footer = section.footer

                if header and header.paragraphs:
                    page_text += "\nHEADER:\n"
                    for paragraph in header.paragraphs:
                        page_text += paragraph.text + "\n"

                if footer and footer.paragraphs:
                    page_text += "\nFOOTER:\n"
                    for paragraph in footer.paragraphs:
                        page_text += paragraph.text + "\n"

            pages_text.append(page_text)

        return pages_text
    except Exception as e:
        raise RuntimeError(f"Error while reading the Word file by pages: {e}")