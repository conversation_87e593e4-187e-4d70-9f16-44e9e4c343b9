"""
Validation utilities for ideal candidate functionality.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import uuid
import json

logger = logging.getLogger(__name__)


class IdealCandidateValidationError(Exception):
    """Custom exception for ideal candidate validation errors."""
    pass


def validate_uuid(value: str, field_name: str) -> bool:
    """
    Validate that a string is a valid UUID.
    
    Args:
        value: The string to validate
        field_name: The name of the field being validated
        
    Returns:
        True if valid
        
    Raises:
        IdealCandidateValidationError: If validation fails
    """
    try:
        uuid.UUID(value)
        return True
    except (ValueError, TypeError) as e:
        raise IdealCandidateValidationError(f"Invalid UUID for {field_name}: {value}")


def validate_ideal_candidate_info_structure(ideal_candidate_info: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate the structure of ideal candidate information.
    
    Args:
        ideal_candidate_info: The ideal candidate data to validate
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    if not isinstance(ideal_candidate_info, dict):
        errors.append("ideal_candidate_info must be a dictionary")
        return False, errors
    
    # Required top-level keys
    required_keys = [
        "personal_info",
        "technical_skills",
        "professional_experience",
        "education",
        "soft_skills"
    ]
    
    for key in required_keys:
        if key not in ideal_candidate_info:
            errors.append(f"Missing required key: {key}")
    
    # Validate personal_info structure
    if "personal_info" in ideal_candidate_info:
        personal_info = ideal_candidate_info["personal_info"]
        if not isinstance(personal_info, dict):
            errors.append("personal_info must be a dictionary")
        else:
            # Check for required personal_info fields
            personal_required = ["professional_title", "years_of_experience"]
            for field in personal_required:
                if field not in personal_info:
                    errors.append(f"Missing required field in personal_info: {field}")
    
    # Validate technical_skills structure
    if "technical_skills" in ideal_candidate_info:
        technical_skills = ideal_candidate_info["technical_skills"]
        if not isinstance(technical_skills, dict):
            errors.append("technical_skills must be a dictionary")
        else:
            # Check that at least one technical skill category exists
            skill_categories = [
                "core_technologies", "frameworks_tools", "programming_languages",
                "databases", "cloud_platforms", "other_technical"
            ]
            has_skills = any(
                category in technical_skills and technical_skills[category]
                for category in skill_categories
            )
            if not has_skills:
                errors.append("technical_skills must contain at least one skill category with values")
    
    # Validate professional_experience structure
    if "professional_experience" in ideal_candidate_info:
        experience = ideal_candidate_info["professional_experience"]
        if not isinstance(experience, list):
            errors.append("professional_experience must be a list")
        elif len(experience) == 0:
            errors.append("professional_experience cannot be empty")
        else:
            for i, exp in enumerate(experience):
                if not isinstance(exp, dict):
                    errors.append(f"professional_experience[{i}] must be a dictionary")
                else:
                    exp_required = ["role", "duration"]
                    for field in exp_required:
                        if field not in exp:
                            errors.append(f"Missing required field in professional_experience[{i}]: {field}")
    
    # Validate education structure
    if "education" in ideal_candidate_info:
        education = ideal_candidate_info["education"]
        if not isinstance(education, dict):
            errors.append("education must be a dictionary")
    
    # Validate soft_skills structure
    if "soft_skills" in ideal_candidate_info:
        soft_skills = ideal_candidate_info["soft_skills"]
        if not isinstance(soft_skills, list):
            errors.append("soft_skills must be a list")
        elif len(soft_skills) == 0:
            errors.append("soft_skills cannot be empty")
    
    return len(errors) == 0, errors


def validate_generation_request(request_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate an ideal candidate generation request.
    
    Args:
        request_data: The request data to validate
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Validate position_id
    if "position_id" not in request_data:
        errors.append("position_id is required")
    else:
        try:
            validate_uuid(request_data["position_id"], "position_id")
        except IdealCandidateValidationError as e:
            errors.append(str(e))
    
    # Validate optional fields
    if "generation_options" in request_data:
        if not isinstance(request_data["generation_options"], dict):
            errors.append("generation_options must be a dictionary")
    
    if "model_preference" in request_data:
        if not isinstance(request_data["model_preference"], str):
            errors.append("model_preference must be a string")
        elif len(request_data["model_preference"].strip()) == 0:
            errors.append("model_preference cannot be empty")
    
    return len(errors) == 0, errors


def validate_matching_parameters(
    position_id: Optional[str] = None,
    candidate_id: Optional[str] = None,
    limit: int = 5,
    hasFeedback: int = 2,
    batch_mode: bool = True
) -> Tuple[bool, List[str]]:
    """
    Validate parameters for ideal candidate matching.
    
    Args:
        position_id: Position ID for candidate matching
        candidate_id: Candidate ID for position matching
        limit: Maximum number of results
        hasFeedback: Feedback filter
        batch_mode: Batch processing mode
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Validate mutual exclusivity
    if not position_id and not candidate_id:
        errors.append("Either position_id or candidate_id must be provided")
    elif position_id and candidate_id:
        errors.append("Cannot provide both position_id and candidate_id")
    
    # Validate UUIDs
    if position_id:
        try:
            validate_uuid(position_id, "position_id")
        except IdealCandidateValidationError as e:
            errors.append(str(e))
    
    if candidate_id:
        try:
            validate_uuid(candidate_id, "candidate_id")
        except IdealCandidateValidationError as e:
            errors.append(str(e))
    
    # Validate limit
    if not isinstance(limit, int) or limit < 1 or limit > 100:
        errors.append("limit must be an integer between 1 and 100")
    
    # Validate hasFeedback
    if hasFeedback not in [0, 1, 2]:
        errors.append("hasFeedback must be 0 (without), 1 (with), or 2 (both)")
    
    # Validate batch_mode
    if not isinstance(batch_mode, bool):
        errors.append("batch_mode must be a boolean")
    
    return len(errors) == 0, errors


def validate_json_serializable(data: Any, field_name: str) -> bool:
    """
    Validate that data is JSON serializable.
    
    Args:
        data: The data to validate
        field_name: The name of the field being validated
        
    Returns:
        True if valid
        
    Raises:
        IdealCandidateValidationError: If validation fails
    """
    try:
        json.dumps(data)
        return True
    except (TypeError, ValueError) as e:
        raise IdealCandidateValidationError(f"Field {field_name} is not JSON serializable: {e}")


def sanitize_ideal_candidate_info(ideal_candidate_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize ideal candidate information by removing potentially harmful content.
    
    Args:
        ideal_candidate_info: The ideal candidate data to sanitize
        
    Returns:
        Sanitized ideal candidate data
    """
    try:
        # Create a deep copy to avoid modifying the original
        import copy
        sanitized = copy.deepcopy(ideal_candidate_info)
        
        # Remove any keys that might contain sensitive information
        sensitive_keys = [
            "password", "secret", "token", "key", "credential",
            "ssn", "social_security", "tax_id", "bank_account"
        ]
        
        def remove_sensitive_keys(obj, path=""):
            if isinstance(obj, dict):
                keys_to_remove = []
                for key, value in obj.items():
                    key_lower = key.lower()
                    if any(sensitive in key_lower for sensitive in sensitive_keys):
                        keys_to_remove.append(key)
                        logger.warning(f"Removed sensitive key: {path}.{key}")
                    else:
                        remove_sensitive_keys(value, f"{path}.{key}")
                
                for key in keys_to_remove:
                    del obj[key]
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    remove_sensitive_keys(item, f"{path}[{i}]")
        
        remove_sensitive_keys(sanitized)
        
        # Validate JSON serializability after sanitization
        validate_json_serializable(sanitized, "sanitized_ideal_candidate_info")
        
        return sanitized
        
    except Exception as e:
        logger.error(f"Error sanitizing ideal candidate info: {e}")
        raise IdealCandidateValidationError(f"Failed to sanitize ideal candidate info: {e}")


def validate_embedding_vector(embedding: List[float], expected_dimension: int = 1536) -> bool:
    """
    Validate an embedding vector.
    
    Args:
        embedding: The embedding vector to validate
        expected_dimension: Expected dimension of the embedding
        
    Returns:
        True if valid
        
    Raises:
        IdealCandidateValidationError: If validation fails
    """
    if not isinstance(embedding, list):
        raise IdealCandidateValidationError("Embedding must be a list")
    
    if len(embedding) != expected_dimension:
        raise IdealCandidateValidationError(
            f"Embedding dimension mismatch: expected {expected_dimension}, got {len(embedding)}"
        )
    
    if not all(isinstance(x, (int, float)) for x in embedding):
        raise IdealCandidateValidationError("All embedding values must be numbers")
    
    # Check for NaN or infinite values
    import math
    for i, value in enumerate(embedding):
        if math.isnan(value) or math.isinf(value):
            raise IdealCandidateValidationError(f"Invalid embedding value at index {i}: {value}")
    
    return True
