import azure.functions as func
import json
import logging
from datetime import datetime
import math

# Initialize the Azure Functions app
app = func.FunctionApp()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# JSON schemas for MCP tool properties
tool_properties_get_time_json = {
    "type": "object",
    "properties": {
        "format": {
            "type": "string",
            "description": "Time format: 'iso' for ISO 8601, 'timestamp' for Unix timestamp, 'readable' for human-readable format",
            "enum": ["iso", "timestamp", "readable"],
            "default": "iso"
        },
        "timezone": {
            "type": "string",
            "description": "Timezone (optional, defaults to UTC)",
            "default": "UTC"
        }
    },
    "required": []
}

tool_properties_calculate_json = {
    "type": "object",
    "properties": {
        "operation": {
            "type": "string",
            "description": "Mathematical operation to perform",
            "enum": ["add", "subtract", "multiply", "divide", "power", "sqrt", "sin", "cos", "tan"],
            "default": "add"
        },
        "operand1": {
            "type": "number",
            "description": "First operand for the calculation"
        },
        "operand2": {
            "type": "number",
            "description": "Second operand for the calculation (not required for unary operations like sqrt)"
        }
    },
    "required": ["operation", "operand1"]
}

# MCP Tool Functions

@app.generic_trigger(
    arg_name="context",
    type="mcpToolTrigger",
    toolName="getTime",
    description="Get current time in various formats (ISO 8601, Unix timestamp, or human-readable)",
    toolProperties=tool_properties_get_time_json,
)
def get_time(context) -> str:
    """
    Get current time in the specified format.

    Args:
        context: MCP context containing arguments

    Returns:
        str: Current time in the requested format
    """
    try:
        content = json.loads(context)
        arguments = content.get("arguments", {})

        time_format = arguments.get("format", "iso")
        timezone = arguments.get("timezone", "UTC")

        current_time = datetime.utcnow()

        if time_format == "iso":
            result = current_time.isoformat() + "Z"
        elif time_format == "timestamp":
            result = str(int(current_time.timestamp()))
        elif time_format == "readable":
            result = current_time.strftime("%Y-%m-%d %H:%M:%S UTC")
        else:
            result = current_time.isoformat() + "Z"

        logger.info(f"getTime called with format: {time_format}, result: {result}")
        return json.dumps({
            "success": True,
            "result": result,
            "format": time_format,
            "timezone": timezone
        })

    except Exception as e:
        logger.error(f"Error in getTime: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Failed to get time: {str(e)}"
        })


@app.generic_trigger(
    arg_name="context",
    type="mcpToolTrigger",
    toolName="calculate",
    description="Perform mathematical calculations including basic arithmetic and trigonometric functions",
    toolProperties=tool_properties_calculate_json,
)
def calculate(context) -> str:
    """
    Perform mathematical calculations.

    Args:
        context: MCP context containing arguments

    Returns:
        str: Calculation result or error message
    """
    try:
        content = json.loads(context)
        arguments = content.get("arguments", {})

        operation = arguments.get("operation", "add")
        operand1 = arguments.get("operand1")
        operand2 = arguments.get("operand2")

        # Validate required inputs
        if operand1 is None:
            return json.dumps({
                "success": False,
                "error": "operand1 is required"
            })

        # Convert to float for calculations
        try:
            operand1 = float(operand1)
            if operand2 is not None:
                operand2 = float(operand2)
        except (ValueError, TypeError):
            return json.dumps({
                "success": False,
                "error": "Invalid numeric input"
            })

        # Perform calculations
        result = None

        if operation == "add":
            if operand2 is None:
                return json.dumps({"success": False, "error": "operand2 required for addition"})
            result = operand1 + operand2

        elif operation == "subtract":
            if operand2 is None:
                return json.dumps({"success": False, "error": "operand2 required for subtraction"})
            result = operand1 - operand2

        elif operation == "multiply":
            if operand2 is None:
                return json.dumps({"success": False, "error": "operand2 required for multiplication"})
            result = operand1 * operand2

        elif operation == "divide":
            if operand2 is None:
                return json.dumps({"success": False, "error": "operand2 required for division"})
            if operand2 == 0:
                return json.dumps({"success": False, "error": "Division by zero"})
            result = operand1 / operand2

        elif operation == "power":
            if operand2 is None:
                return json.dumps({"success": False, "error": "operand2 required for power operation"})
            result = math.pow(operand1, operand2)

        elif operation == "sqrt":
            if operand1 < 0:
                return json.dumps({"success": False, "error": "Cannot calculate square root of negative number"})
            result = math.sqrt(operand1)

        elif operation == "sin":
            result = math.sin(math.radians(operand1))

        elif operation == "cos":
            result = math.cos(math.radians(operand1))

        elif operation == "tan":
            result = math.tan(math.radians(operand1))

        else:
            return json.dumps({
                "success": False,
                "error": f"Unsupported operation: {operation}"
            })

        logger.info(f"calculate called with operation: {operation}, operand1: {operand1}, operand2: {operand2}, result: {result}")

        return json.dumps({
            "success": True,
            "result": result,
            "operation": operation,
            "operand1": operand1,
            "operand2": operand2
        })

    except Exception as e:
        logger.error(f"Error in calculate: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Calculation failed: {str(e)}"
        })
