"""SQLAlchemy models for ParserGPT POC."""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, 
    ForeignKey, JSON, Float, Index
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Job(Base):
    """Job model representing a scraping job."""
    
    __tablename__ = "jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    start_url = Column(String(2048), nullable=False)
    allowed_domains = Column(Text)  # Comma-separated domains
    max_depth = Column(Integer, default=1)
    max_pages = Column(Integer, default=10)
    field_spec = Column(JSON, nullable=False)  # List of field specifications
    status = Column(String(50), default="created", index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Progress tracking
    pages_discovered = Column(Integer, default=0)
    pages_processed = Column(Integer, default=0)
    pages_extracted = Column(Integer, default=0)
    
    # Relationships
    pages = relationship("Page", back_populates="job", cascade="all, delete-orphan")
    extractions = relationship("Extraction", back_populates="job", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Job(id={self.id}, status={self.status}, url={self.start_url})>"
    
    @property
    def allowed_domains_list(self) -> List[str]:
        """Get allowed domains as a list."""
        if not self.allowed_domains:
            return []
        return [d.strip() for d in self.allowed_domains.split(",") if d.strip()]
    
    @allowed_domains_list.setter
    def allowed_domains_list(self, domains: List[str]) -> None:
        """Set allowed domains from a list."""
        self.allowed_domains = ",".join(domains)


class Page(Base):
    """Page model representing a scraped web page."""
    
    __tablename__ = "pages"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, ForeignKey("jobs.id"), nullable=False, index=True)
    url = Column(String(2048), nullable=False, index=True)
    domain = Column(String(255), nullable=False, index=True)
    html_content = Column(Text, nullable=False)
    status_code = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=True)
    content_length = Column(Integer, nullable=True)
    fetch_method = Column(String(20), default="httpx")  # httpx or playwright
    fetched_at = Column(DateTime(timezone=True), server_default=func.now())
    processing_time = Column(Float, nullable=True)  # Time in seconds
    
    # Relationships
    job = relationship("Job", back_populates="pages")
    extractions = relationship("Extraction", back_populates="page", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index("idx_page_job_domain", "job_id", "domain"),
        Index("idx_page_url_hash", "url"),
    )
    
    def __repr__(self):
        return f"<Page(id={self.id}, job_id={self.job_id}, url={self.url})>"


class Extraction(Base):
    """Extraction model representing extracted data from a page."""
    
    __tablename__ = "extractions"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, ForeignKey("jobs.id"), nullable=False, index=True)
    page_id = Column(Integer, ForeignKey("pages.id"), nullable=False, index=True)
    extracted_data = Column(JSON, nullable=False)  # The actual extracted data
    extraction_method = Column(String(20), default="deterministic")  # deterministic or llm
    adapter_version = Column(String(50), nullable=True)
    confidence_score = Column(Float, nullable=True)  # 0.0 to 1.0
    extracted_at = Column(DateTime(timezone=True), server_default=func.now())
    processing_time = Column(Float, nullable=True)  # Time in seconds
    
    # Relationships
    job = relationship("Job", back_populates="extractions")
    page = relationship("Page", back_populates="extractions")
    
    # Indexes
    __table_args__ = (
        Index("idx_extraction_job_page", "job_id", "page_id"),
        Index("idx_extraction_method", "extraction_method"),
    )
    
    def __repr__(self):
        return f"<Extraction(id={self.id}, job_id={self.job_id}, page_id={self.page_id})>"


class Adapter(Base):
    """Adapter model representing learned extraction patterns for a domain."""
    
    __tablename__ = "adapters"
    
    id = Column(Integer, primary_key=True, index=True)
    domain = Column(String(255), nullable=False, unique=True, index=True)
    version = Column(Integer, default=1)
    adapter_data = Column(JSON, nullable=False)  # The adapter JSON structure
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Performance metrics
    success_rate = Column(Float, nullable=True)  # 0.0 to 1.0
    avg_confidence = Column(Float, nullable=True)  # 0.0 to 1.0
    total_extractions = Column(Integer, default=0)
    successful_extractions = Column(Integer, default=0)
    
    # Learning metadata
    sample_urls = Column(JSON, nullable=True)  # URLs used for learning
    field_coverage = Column(JSON, nullable=True)  # Coverage per field
    
    def __repr__(self):
        return f"<Adapter(id={self.id}, domain={self.domain}, version={self.version})>"
    
    @property
    def adapter_dict(self) -> Dict[str, Any]:
        """Get adapter data as dictionary."""
        return self.adapter_data or {}
    
    @adapter_dict.setter
    def adapter_dict(self, data: Dict[str, Any]) -> None:
        """Set adapter data from dictionary."""
        self.adapter_data = data
