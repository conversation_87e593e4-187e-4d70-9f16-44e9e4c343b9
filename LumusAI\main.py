import os
import time
import asyncio
import logging
import psutil

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse, FileResponse
from dotenv import load_dotenv
from logging_config import setup_logging

# Load environment variables and configure logging
load_dotenv()
logger = setup_logging()

# --------------------------------------------------------------------
# Environment variables verification
# --------------------------------------------------------------------

API_KEY = os.getenv("API_KEY")
API_VERSION = os.getenv("API_VERSION")
AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
MODEL = os.getenv("MODEL")
MAX_CONCURRENT_TASKS = int(os.getenv("MAX_CONCURRENT_TASKS", "4"))

# Langfuse configuration (optional)
LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")

# Langfuse masking configuration (enabled by default for privacy)
LANGFUSE_ENABLE_MASKING = os.getenv("LANGFUSE_ENABLE_MASKING", "true").lower() == "true"
LANGFUSE_MASK_CREDIT_CARDS = os.getenv("LANGFUSE_MASK_CREDIT_CARDS", "true").lower() == "true"
LANGFUSE_MASK_EMAILS = os.getenv("LANGFUSE_MASK_EMAILS", "true").lower() == "true"
LANGFUSE_MASK_PHONES = os.getenv("LANGFUSE_MASK_PHONES", "true").lower() == "true"
LANGFUSE_MASK_SECRETS = os.getenv("LANGFUSE_MASK_SECRETS", "true").lower() == "true"

print(MODEL)
print(API_VERSION)

# Log Langfuse configuration status
if LANGFUSE_PUBLIC_KEY and LANGFUSE_SECRET_KEY:
    logger.info("Langfuse observability enabled")
    print("✅ Langfuse observability enabled")
else:
    logger.info("Langfuse observability disabled (credentials not provided)")
    print("ℹ️  Langfuse observability disabled (credentials not provided)")


if not all([API_KEY, API_VERSION, AZURE_ENDPOINT, MODEL]):
    missing_vars = [
        var for var, value in {
            "API_KEY": API_KEY,
            "API_VERSION": API_VERSION,
            "AZURE_ENDPOINT": AZURE_ENDPOINT,
            "MODEL": MODEL
        }.items() if not value
    ]
    logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
    raise Exception("Missing .env variables")

# --------------------------------------------------------------------
# FastAPI application initialization
# --------------------------------------------------------------------

root_path = os.environ.get("ROOT_PATH", "")
app = FastAPI(root_path=root_path)



# --------------------------------------------------------------------
# CORS configuration
# --------------------------------------------------------------------

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --------------------------------------------------------------------
# Global request logging middleware
# --------------------------------------------------------------------

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """
    Logs each incoming request and its processing time.
    """
    start_time = time.time()

    try:
        response = await call_next(request)
        process_time = time.time() - start_time

        logger.info(
            f"Request: {request.method} {request.url} - Status: {response.status_code} - Time: {process_time:.2f}s"
        )

        response.headers["X-Process-Time"] = f"{process_time:.2f}"
        return response

    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"Error processing {request.method} {request.url} - {str(e)} - Time: {process_time:.2f}s",
            exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={"message": "Internal server error"}
        )

# --------------------------------------------------------------------
# Shared clients and resources
# --------------------------------------------------------------------

from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient

openai_client = OpenAIClient(
    api_key=API_KEY,
    api_version=API_VERSION,
    azure_endpoint=AZURE_ENDPOINT,
    model=MODEL
)

try:
    langchain_client = LangChainClient(
        api_key=API_KEY,
        api_version=API_VERSION,
        azure_endpoint=AZURE_ENDPOINT,
        model=MODEL,
        langfuse_public_key=LANGFUSE_PUBLIC_KEY,
        langfuse_secret_key=LANGFUSE_SECRET_KEY,
        langfuse_host=LANGFUSE_HOST,
        langfuse_enable_masking=LANGFUSE_ENABLE_MASKING,
        langfuse_mask_credit_cards=LANGFUSE_MASK_CREDIT_CARDS,
        langfuse_mask_emails=LANGFUSE_MASK_EMAILS,
        langfuse_mask_phones=LANGFUSE_MASK_PHONES,
        langfuse_mask_secrets=LANGFUSE_MASK_SECRETS
    )
    logger.info("LangChain client initialized successfully")

    # Debug: Verify Langfuse integration
    if hasattr(langchain_client, 'langfuse_handler') and langchain_client.langfuse_handler:
        print("🔍 STARTUP DEBUG: LangChain client has Langfuse handler")
        print(f"🔍 STARTUP DEBUG: Handler type: {type(langchain_client.langfuse_handler)}")

        # Test callback retrieval
        callbacks = langchain_client._get_callbacks()
        print(f"🔍 STARTUP DEBUG: _get_callbacks() returns {len(callbacks)} callback(s)")
    else:
        print("❌ STARTUP DEBUG: LangChain client has NO Langfuse handler!")
        print("❌ This means Langfuse integration failed during initialization")
except Exception as e:
    logger.error(f"Failed to initialize LangChain client: {e}")
    raise Exception(f"LangChain client initialization failed: {e}")

# Semaphore for concurrency control and active tasks registry
semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)
active_tasks = {}

app.state.logger = logger
app.state.openai_client = openai_client
app.state.langchain_client = langchain_client
app.state.semaphore = semaphore
app.state.active_tasks = active_tasks

# --------------------------------------------------------------------
# Routers
# --------------------------------------------------------------------

from routes.process import router as process_router
from routes.process_test import router as process_test_router
from routes.health import router as health_router
from routes.maintenance import router as maintenance_router
from routes.webhook_test import router as webhook_test_router
from routes.task_management import router as task_management_router

app.include_router(process_router)
app.include_router(health_router)
app.include_router(maintenance_router)
app.include_router(webhook_test_router)
app.include_router(process_test_router)
app.include_router(task_management_router)

