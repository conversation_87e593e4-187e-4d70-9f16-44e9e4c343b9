#!/usr/bin/env python3
"""
Test script to verify token usage tracking in inference_with_fallback function.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.llm import inference_with_fallback
from langchain_core.messages import HumanMessage

def test_token_usage_openai():
    """Test token usage tracking with OpenAI models."""
    print("🧪 Testing token usage with OpenAI models...")
    
    try:
        result = inference_with_fallback(
            task_prompt="You are a helpful assistant. Respond briefly.",
            model_schema=None,
            user_messages=[HumanMessage(content="Hello, how are you?")],
            model_schema_text=None,
            models_order=["gpt-4o-mini", "gpt-4o"]  # Start with cheaper model
        )
        
        if result:
            print(f"✅ OpenAI test successful!")
            print(f"   Result type: {type(result)}")
            print(f"   Has token_usage: {hasattr(result, 'token_usage')}")
            if hasattr(result, 'token_usage'):
                print(f"   Token usage: {result.token_usage}")
                print(f"   Model used: {getattr(result, 'model_name', 'unknown')}")
            print(f"   Content: {result.content[:100]}...")
            return True
        else:
            print("❌ OpenAI test failed - no result returned")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI test failed with error: {e}")
        return False

def test_token_usage_groq():
    """Test token usage tracking with Groq models."""
    print("\n🧪 Testing token usage with Groq models...")
    
    try:
        result = inference_with_fallback(
            task_prompt="You are a helpful assistant. Respond briefly.",
            model_schema=None,
            user_messages=[HumanMessage(content="Hello, how are you?")],
            model_schema_text=None,
            models_order=["llama4-light", "llama4-pro"]
        )
        
        if result:
            print(f"✅ Groq test successful!")
            print(f"   Result type: {type(result)}")
            print(f"   Has token_usage: {hasattr(result, 'token_usage')}")
            if hasattr(result, 'token_usage'):
                print(f"   Token usage: {result.token_usage}")
                print(f"   Model used: {getattr(result, 'model_name', 'unknown')}")
            print(f"   Content: {result.content[:100]}...")
            return True
        else:
            print("❌ Groq test failed - no result returned")
            return False
            
    except Exception as e:
        print(f"❌ Groq test failed with error: {e}")
        return False

def test_backward_compatibility():
    """Test that existing code patterns still work."""
    print("\n🧪 Testing backward compatibility...")
    
    try:
        result = inference_with_fallback(
            task_prompt="You are a helpful assistant. Respond briefly.",
            model_schema=None,
            user_messages=[HumanMessage(content="Hello")],
            model_schema_text=None,
            models_order=["gpt-4o-mini"]
        )
        
        # Test common usage patterns
        if not result:
            print("❌ Backward compatibility test failed - result is falsy")
            return False
            
        # Test content access
        content = result.content
        print(f"✅ Content access works: {content[:50]}...")
        
        # Test string conversion
        str_result = str(result)
        print(f"✅ String conversion works: {str_result[:50]}...")
        
        # Test truthiness
        if result:
            print("✅ Truthiness test passed")
        else:
            print("❌ Truthiness test failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed with error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting token usage tests...\n")
    
    # Run tests
    openai_success = test_token_usage_openai()
    groq_success = test_token_usage_groq()
    compat_success = test_backward_compatibility()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"   OpenAI token tracking: {'✅' if openai_success else '❌'}")
    print(f"   Groq token tracking: {'✅' if groq_success else '❌'}")
    print(f"   Backward compatibility: {'✅' if compat_success else '❌'}")
    
    if all([openai_success, groq_success, compat_success]):
        print(f"\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print(f"\n💥 Some tests failed!")
        sys.exit(1)
