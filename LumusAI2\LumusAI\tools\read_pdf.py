"""
<PERSON><PERSON>t to read and print the content of a PDF file.
"""

import sys
import os
import PyPDF2
from pathlib import Path

def read_pdf(pdf_path):
    """
    Read and print the content of a PDF file.
    
    Args:
        pdf_path (str): Path to the PDF file
    """
    try:
        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f"Error: File not found: {pdf_path}")
            return
        
        # Open the PDF file
        with open(pdf_path, 'rb') as file:
            # Create a PDF reader object
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Get the number of pages
            num_pages = len(pdf_reader.pages)
            print(f"PDF has {num_pages} pages\n")
            
            # Extract and print text from each page
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                
                print(f"--- Page {page_num + 1} ---")
                print(text)
                print("\n" + "-" * 80 + "\n")
                
    except Exception as e:
        print(f"Error reading PDF: {str(e)}")

if __name__ == "__main__":
    # Check if a file path was provided
    if len(sys.argv) < 2:
        print("Usage: python read_pdf.py <path_to_pdf_file>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    read_pdf(pdf_path)
