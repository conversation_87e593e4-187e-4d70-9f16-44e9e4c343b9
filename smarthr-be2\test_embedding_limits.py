#!/usr/bin/env python3
"""
Embedding Limits Diagnostic Test

This script helps diagnose whether you're hitting rate limits or context window limits
for your Azure OpenAI embeddings model by running targeted tests.

Usage: python test_embedding_limits.py
"""

import time
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the embedding function with proper Azure configuration
embedding_function = None
try:
    # Try to import from utils.embedding_utils first (has proper Azure config)
    from utils.embedding_utils import generate_openai_embedding
    embedding_function = generate_openai_embedding
    print("✓ Successfully imported generate_openai_embedding from utils.embedding_utils")
except ImportError:
    try:
        # Fallback to utils.utils_embeddings
        from utils.utils_embeddings import generate_openai_embedding
        embedding_function = generate_openai_embedding
        print("✓ Successfully imported generate_openai_embedding from utils.utils_embeddings")
        print("⚠️  Note: Using utils_embeddings - may need proper Azure configuration")
    except ImportError as e:
        print(f"✗ Failed to import generate_openai_embedding: {e}")
        print("Make sure you're running this script from the project root directory")
        sys.exit(1)

# Create a wrapper function to use the imported embedding function
def generate_openai_embedding(text: str):
    return embedding_function(text)

class EmbeddingDiagnostics:
    def __init__(self):
        self.results = {
            'rate_limit_test': {},
            'context_window_test': {},
            'timestamp': datetime.now().isoformat()
        }
    
    def log_result(self, test_type: str, test_name: str, success: bool, error: str = None, details: Dict = None):
        """Log test results for analysis"""
        if test_type not in self.results:
            self.results[test_type] = {}
        
        self.results[test_type][test_name] = {
            'success': success,
            'error': error,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
    
    def print_separator(self, title: str):
        """Print a formatted separator"""
        print(f"\n{'='*60}")
        print(f" {title}")
        print(f"{'='*60}")
    
    def test_basic_functionality(self) -> bool:
        """Test basic embedding functionality with minimal text"""
        self.print_separator("BASIC FUNCTIONALITY TEST")
        
        test_text = "Hello world"
        print(f"Testing basic embedding with text: '{test_text}'")
        
        try:
            start_time = time.time()
            embedding = generate_openai_embedding(test_text)
            end_time = time.time()
            
            if embedding is not None:
                print(f"✓ Basic test PASSED")
                print(f"  - Embedding dimensions: {len(embedding)}")
                print(f"  - Response time: {end_time - start_time:.2f} seconds")
                self.log_result('basic', 'functionality', True, details={
                    'dimensions': len(embedding),
                    'response_time': end_time - start_time
                })
                return True
            else:
                print(f"✗ Basic test FAILED - embedding is None")
                self.log_result('basic', 'functionality', False, error="Embedding returned None")
                return False
                
        except Exception as e:
            print(f"✗ Basic test FAILED with exception: {e}")
            self.log_result('basic', 'functionality', False, error=str(e))
            return False
    
    def test_rate_limits(self):
        """Test for rate limiting by making multiple rapid requests"""
        self.print_separator("RATE LIMIT TEST")

        print("Testing rate limits with multiple rapid requests...")
        print("This will help identify if you're hitting requests-per-minute limits")

        # Test 1: Rapid burst requests
        print(f"\n📊 Test 1: Rapid burst (10 requests with minimal delay)")
        self._test_rapid_burst()

        # Test 2: Sustained load
        print(f"\n📊 Test 2: Sustained load (20 requests over 1 minute)")
        self._test_sustained_load()

    def _test_rapid_burst(self):
        """Test rapid burst requests to detect immediate rate limiting"""
        test_text = "This is a test for rate limiting - rapid burst"
        successful_requests = 0
        failed_requests = 0
        errors = []
        response_times = []

        for i in range(10):
            print(f"Request {i+1}/10...", end=" ")

            try:
                start_time = time.time()
                embedding = generate_openai_embedding(test_text)
                end_time = time.time()

                response_time = end_time - start_time
                response_times.append(response_time)

                if embedding is not None:
                    successful_requests += 1
                    print(f"✓ ({response_time:.2f}s)")
                else:
                    failed_requests += 1
                    print(f"✗ (None returned)")
                    errors.append(f"Request {i+1}: Embedding returned None")

            except Exception as e:
                failed_requests += 1
                error_msg = str(e)
                errors.append(f"Request {i+1}: {error_msg}")
                print(f"✗ ({error_msg})")

            # Minimal delay to avoid overwhelming the API
            time.sleep(0.05)

        self._analyze_rate_limit_results("rapid_burst", successful_requests, failed_requests, errors, response_times)

    def _test_sustained_load(self):
        """Test sustained load to detect per-minute rate limiting"""
        test_text = "This is a test for rate limiting - sustained load"
        successful_requests = 0
        failed_requests = 0
        errors = []
        response_times = []

        print("Testing sustained load (this will take about 1 minute)...")
        start_test_time = time.time()

        for i in range(20):
            print(f"Request {i+1}/20...", end=" ")

            try:
                start_time = time.time()
                embedding = generate_openai_embedding(test_text)
                end_time = time.time()

                response_time = end_time - start_time
                response_times.append(response_time)

                if embedding is not None:
                    successful_requests += 1
                    print(f"✓ ({response_time:.2f}s)")
                else:
                    failed_requests += 1
                    print(f"✗ (None returned)")
                    errors.append(f"Request {i+1}: Embedding returned None")

            except Exception as e:
                failed_requests += 1
                error_msg = str(e)
                errors.append(f"Request {i+1}: {error_msg}")
                print(f"✗ ({error_msg})")

            # Spread requests over 1 minute (3 seconds between requests)
            time.sleep(3)

        total_test_time = time.time() - start_test_time
        print(f"Total test time: {total_test_time:.1f} seconds")

        self._analyze_rate_limit_results("sustained_load", successful_requests, failed_requests, errors, response_times)

    def _analyze_rate_limit_results(self, test_name: str, successful: int, failed: int, errors: List[str], response_times: List[float]):
        """Analyze rate limit test results"""
        total_requests = successful + failed

        print(f"\n{test_name.replace('_', ' ').title()} Results:")
        print(f"  - Successful requests: {successful}/{total_requests}")
        print(f"  - Failed requests: {failed}/{total_requests}")

        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            print(f"  - Average response time: {avg_response_time:.2f}s")
            print(f"  - Max response time: {max_response_time:.2f}s")

        if errors:
            print(f"  - Errors encountered:")
            for error in errors[:3]:  # Show first 3 errors
                print(f"    • {error}")
            if len(errors) > 3:
                print(f"    • ... and {len(errors) - 3} more errors")

        # Analyze error patterns for rate limiting indicators
        rate_limit_keywords = ["rate limit", "too many requests", "429", "quota", "throttle"]
        rate_limit_errors = []

        for error in errors:
            error_lower = error.lower()
            if any(keyword in error_lower for keyword in rate_limit_keywords):
                rate_limit_errors.append(error)

        if rate_limit_errors:
            print(f"🚨 RATE LIMITING DETECTED in {test_name}")
            print(f"   Rate limit errors: {len(rate_limit_errors)}/{len(errors)}")
        elif failed > total_requests * 0.5:  # More than 50% failure rate
            print(f"⚠️  HIGH FAILURE RATE in {test_name} - Possible rate limiting")
        else:
            print(f"✓ No obvious rate limiting detected in {test_name}")

        self.log_result('rate_limit_test', test_name, successful > total_requests * 0.5,
                       details={
                           'successful': successful,
                           'failed': failed,
                           'total': total_requests,
                           'errors': errors,
                           'rate_limit_errors': rate_limit_errors,
                           'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
                           'max_response_time': max(response_times) if response_times else 0
                       })

    def test_context_window_limits(self):
        """Test context window limits with progressively larger text inputs"""
        self.print_separator("CONTEXT WINDOW TEST")

        print("Testing context window limits with progressively larger text inputs...")
        print("This will help identify if you're hitting token/context window limits")

        # Test different text sizes (approximate token counts)
        test_cases = [
            ("Small text", self._generate_text(100), "~100 tokens"),
            ("Medium text", self._generate_text(500), "~500 tokens"),
            ("Large text", self._generate_text(1000), "~1000 tokens"),
            ("Very large text", self._generate_text(2000), "~2000 tokens"),
            ("Extremely large text", self._generate_text(4000), "~4000 tokens"),
            ("Maximum test", self._generate_text(8000), "~8000 tokens")
        ]

        successful_tests = 0
        failed_tests = 0
        context_limit_reached = False

        for test_name, test_text, token_estimate in test_cases:
            print(f"\n📊 Testing {test_name} ({token_estimate})...")
            print(f"   Text length: {len(test_text)} characters")

            try:
                start_time = time.time()
                embedding = generate_openai_embedding(test_text)
                end_time = time.time()

                response_time = end_time - start_time

                if embedding is not None:
                    successful_tests += 1
                    print(f"   ✓ SUCCESS - Embedding generated ({response_time:.2f}s)")
                    print(f"     Embedding dimensions: {len(embedding)}")

                    self.log_result('context_window_test', test_name.lower().replace(' ', '_'), True,
                                   details={
                                       'text_length': len(test_text),
                                       'token_estimate': token_estimate,
                                       'response_time': response_time,
                                       'embedding_dimensions': len(embedding)
                                   })
                else:
                    failed_tests += 1
                    print(f"   ✗ FAILED - Embedding returned None")
                    self.log_result('context_window_test', test_name.lower().replace(' ', '_'), False,
                                   error="Embedding returned None",
                                   details={
                                       'text_length': len(test_text),
                                       'token_estimate': token_estimate
                                   })

            except Exception as e:
                failed_tests += 1
                error_msg = str(e)
                print(f"   ✗ FAILED - Exception: {error_msg}")

                # Check if this looks like a context window error
                context_keywords = ["token", "context", "length", "too long", "maximum", "limit", "exceed"]
                if any(keyword in error_msg.lower() for keyword in context_keywords):
                    context_limit_reached = True
                    print(f"   🚨 CONTEXT WINDOW LIMIT DETECTED")

                self.log_result('context_window_test', test_name.lower().replace(' ', '_'), False,
                               error=error_msg,
                               details={
                                   'text_length': len(test_text),
                                   'token_estimate': token_estimate,
                                   'context_limit_suspected': any(keyword in error_msg.lower() for keyword in context_keywords)
                               })

            # Add delay between tests to avoid rate limiting
            time.sleep(1)

        # Analyze results
        print(f"\nContext Window Test Summary:")
        print(f"  - Successful tests: {successful_tests}/{len(test_cases)}")
        print(f"  - Failed tests: {failed_tests}/{len(test_cases)}")

        if context_limit_reached:
            print(f"🚨 CONTEXT WINDOW LIMITS DETECTED")
            print(f"   Your embeddings model has token/context window limits")
            print(f"   Consider splitting large texts into smaller chunks")
        elif failed_tests == 0:
            print(f"✓ No context window limits detected in tested range")
            print(f"   Your model can handle texts up to ~8000 tokens")
        else:
            print(f"⚠️  Some tests failed - check error messages for details")

    def _generate_text(self, target_tokens: int) -> str:
        """Generate text with approximately the target number of tokens"""
        # Rough estimate: 1 token ≈ 4 characters for English text
        target_chars = target_tokens * 4

        base_text = """This is a comprehensive test document designed to evaluate the context window limits of the Azure OpenAI embeddings model.
        The purpose of this test is to determine whether the system can handle progressively larger text inputs without encountering
        token limits or context window restrictions. This text contains various types of content including technical descriptions,
        business scenarios, and general information to simulate real-world usage patterns. The embeddings model should be able to
        process this content and generate meaningful vector representations that capture the semantic meaning of the text. """

        # Repeat and extend the base text to reach target length
        repeated_text = base_text
        while len(repeated_text) < target_chars:
            repeated_text += f" Section {len(repeated_text) // len(base_text) + 1}: " + base_text

        # Trim to approximate target length
        return repeated_text[:target_chars]

    def generate_comprehensive_report(self):
        """Generate a comprehensive diagnostic report"""
        self.print_separator("COMPREHENSIVE DIAGNOSTIC REPORT")

        print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Analyze all test results
        issues_found = []
        recommendations = []

        # Check basic functionality
        if 'basic' in self.results and not self.results['basic'].get('functionality', {}).get('success', False):
            issues_found.append("❌ Basic embedding functionality is not working")
            recommendations.append("• Check Azure OpenAI credentials and deployment configuration")
            recommendations.append("• Verify API key is valid and deployment name is correct")

        # Check rate limiting
        rate_limit_issues = 0
        if 'rate_limit_test' in self.results:
            for test_name, result in self.results['rate_limit_test'].items():
                if not result.get('success', False):
                    rate_limit_issues += 1
                if result.get('details', {}).get('rate_limit_errors', []):
                    issues_found.append(f"🚨 Rate limiting detected in {test_name}")

        if rate_limit_issues > 0:
            recommendations.append("• Implement exponential backoff in your application")
            recommendations.append("• Reduce request frequency or batch requests")
            recommendations.append("• Consider upgrading to higher tier for increased limits")

        # Check context window issues
        context_issues = 0
        max_successful_tokens = 0
        if 'context_window_test' in self.results:
            for test_name, result in self.results['context_window_test'].items():
                if result.get('success', False):
                    # Extract token estimate from test details
                    token_str = result.get('details', {}).get('token_estimate', '~0 tokens')
                    try:
                        tokens = int(token_str.split('~')[1].split(' ')[0])
                        max_successful_tokens = max(max_successful_tokens, tokens)
                    except:
                        pass
                else:
                    context_issues += 1
                    if result.get('details', {}).get('context_limit_suspected', False):
                        issues_found.append(f"🚨 Context window limit detected in {test_name}")

        if context_issues > 0:
            recommendations.append("• Split large texts into smaller chunks before embedding")
            recommendations.append(f"• Maximum successful token size was approximately {max_successful_tokens}")
            recommendations.append("• Consider preprocessing text to remove unnecessary content")

        # Print summary
        print(f"\n📊 ISSUES IDENTIFIED:")
        if issues_found:
            for issue in issues_found:
                print(f"   {issue}")
        else:
            print("   ✅ No major issues detected in the tested scenarios")

        print(f"\n💡 RECOMMENDATIONS:")
        if recommendations:
            for rec in recommendations:
                print(f"   {rec}")
        else:
            print("   ✅ Your embedding setup appears to be working well")

        # Print technical details
        print(f"\n🔧 TECHNICAL DETAILS:")
        if 'basic' in self.results and 'functionality' in self.results['basic']:
            basic_result = self.results['basic']['functionality']
            if basic_result.get('success'):
                details = basic_result.get('details', {})
                print(f"   • Embedding dimensions: {details.get('dimensions', 'Unknown')}")
                print(f"   • Average response time: {details.get('response_time', 0):.2f}s")

        # Rate limit summary
        if 'rate_limit_test' in self.results:
            total_requests = 0
            total_successful = 0
            for test_name, result in self.results['rate_limit_test'].items():
                details = result.get('details', {})
                total_requests += details.get('total', 0)
                total_successful += details.get('successful', 0)

            if total_requests > 0:
                success_rate = (total_successful / total_requests) * 100
                print(f"   • Overall request success rate: {success_rate:.1f}% ({total_successful}/{total_requests})")

        # Context window summary
        if 'context_window_test' in self.results:
            successful_context_tests = sum(1 for result in self.results['context_window_test'].values() if result.get('success', False))
            total_context_tests = len(self.results['context_window_test'])
            print(f"   • Context window tests passed: {successful_context_tests}/{total_context_tests}")
            if max_successful_tokens > 0:
                print(f"   • Maximum tokens successfully processed: ~{max_successful_tokens}")

def main():
    """Main function to run all diagnostic tests"""
    print("🔍 Azure OpenAI Embeddings Diagnostic Tool")
    print("This tool will help determine if you're hitting rate limits or context window limits")
    print("=" * 80)

    try:
        diagnostics = EmbeddingDiagnostics()

        # Test 1: Basic functionality
        print("\n🚀 Starting diagnostic tests...")
        if not diagnostics.test_basic_functionality():
            print("\n❌ Basic functionality test failed. Cannot proceed with other tests.")
            print("\n🔧 TROUBLESHOOTING STEPS:")
            print("  1. Check your .env file has correct Azure OpenAI credentials")
            print("  2. Verify AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS is correct")
            print("  3. Ensure AZURE_OPENAI_API_KEY is valid and not expired")
            print("  4. Check AZURE_OPENAI_ENDPOINT is properly formatted")
            print("  5. Verify your Azure OpenAI resource is active and accessible")
            return

        # Test 2: Rate limits
        print("\n⏱️  Proceeding with rate limit tests...")
        diagnostics.test_rate_limits()

        # Test 3: Context window limits
        print("\n📏 Proceeding with context window tests...")
        diagnostics.test_context_window_limits()

        # Generate comprehensive report
        diagnostics.generate_comprehensive_report()

        print(f"\n{'='*80}")
        print("🎯 QUICK DIAGNOSIS GUIDE:")
        print("• Rate limit errors (429, 'too many requests') → You're hitting API rate limits")
        print("• Context/token errors → You're hitting context window limits")
        print("• Connection errors → Check network/credentials")
        print("• None returned → Check deployment name and model availability")
        print(f"{'='*80}")

    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Unexpected error during testing: {e}")
        print("This might indicate a configuration or connectivity issue.")
        print("\n🔧 Please check:")
        print("  • Your internet connection")
        print("  • Azure OpenAI service status")
        print("  • Environment variables in .env file")
        import traceback
        print(f"\nFull error details:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
