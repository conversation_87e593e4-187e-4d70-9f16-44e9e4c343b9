#!/usr/bin/env python3
"""
Test script for LinkedIn Integration API
Tests the updated JSON structure with only database-accessible fields
"""

import json
import requests
import asyncio
from typing import Dict, Any, List
from datetime import datetime

# Base URL for the API
BASE_URL = "http://localhost:8080/api/external_source"

class LinkedInIntegrationTester:
    """Test class for LinkedIn integration endpoints."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def test_search_endpoint(self) -> Dict[str, Any]:
        """Test the main search endpoint with updated JSON structure."""
        print("🔍 Testing Search Endpoint...")
        
        # Updated test payload - ONLY database-accessible fields
        test_payload = {
            "keywords": [
                "software engineer",
                "python developer",
                "backend developer"
            ],
            "location": "San Francisco, CA",
            "experience_level": "mid-level",
            "skills": [
                "Python",
                "FastAPI",
                "PostgreSQL",
                "Docker",
                "REST APIs"
            ],
            "school": "Stanford University",
            "limit": 10,
            "transform_profiles": True,
            "include_raw_profiles": False
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/search",
                json=test_payload,
                timeout=30
            )
            
            result = {
                "endpoint": "/search",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "payload_sent": test_payload
            }
            
            if response.status_code == 200:
                result["response_data"] = response.json()
                result["profiles_found"] = len(result["response_data"].get("profiles", []))
            else:
                result["error"] = response.text
            
            return result
            
        except Exception as e:
            return {
                "endpoint": "/search",
                "success": False,
                "error": str(e),
                "payload_sent": test_payload
            }
    
    def test_batch_search_endpoint(self) -> Dict[str, Any]:
        """Test batch search endpoint."""
        print("📦 Testing Batch Search Endpoint...")
        
        batch_payload = {
            "search_requests": [
                {
                    "keywords": ["data scientist", "machine learning"],
                    "location": "New York, NY",
                    "skills": ["Python", "TensorFlow", "SQL"],
                    "limit": 5
                },
                {
                    "keywords": ["frontend developer", "react"],
                    "location": "Austin, TX", 
                    "skills": ["React", "JavaScript", "CSS"],
                    "limit": 5
                }
            ],
            "transform_profiles": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/search/batch",
                json=batch_payload,
                timeout=60
            )
            
            result = {
                "endpoint": "/search/batch",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "payload_sent": batch_payload
            }
            
            if response.status_code == 200:
                result["response_data"] = response.json()
            else:
                result["error"] = response.text
            
            return result
            
        except Exception as e:
            return {
                "endpoint": "/search/batch",
                "success": False,
                "error": str(e),
                "payload_sent": batch_payload
            }
    
    def test_config_endpoint(self) -> Dict[str, Any]:
        """Test configuration endpoint."""
        print("⚙️ Testing Configuration Endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/config")
            
            result = {
                "endpoint": "/config",
                "status_code": response.status_code,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                config_data = response.json()
                result["response_data"] = config_data
                
                # Check for the new configuration fields we added
                transformation_config = config_data.get("transformation_config", {})
                result["has_fallback_config"] = "use_fallback_transformation" in transformation_config
                result["has_confidence_threshold"] = "confidence_threshold" in transformation_config
            else:
                result["error"] = response.text
            
            return result
            
        except Exception as e:
            return {
                "endpoint": "/config",
                "success": False,
                "error": str(e)
            }
    
    def test_status_endpoint(self) -> Dict[str, Any]:
        """Test status endpoint."""
        print("📊 Testing Status Endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/status")
            
            result = {
                "endpoint": "/status",
                "status_code": response.status_code,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                result["response_data"] = response.json()
            else:
                result["error"] = response.text
            
            return result
            
        except Exception as e:
            return {
                "endpoint": "/status",
                "success": False,
                "error": str(e)
            }
    
    def validate_response_structure(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the response structure matches expected format."""
        validation_result = {
            "structure_valid": True,
            "issues": []
        }
        
        # Check for expected top-level fields
        expected_fields = ["profiles", "search_metadata", "transformation_metadata"]
        for field in expected_fields:
            if field not in response_data:
                validation_result["issues"].append(f"Missing expected field: {field}")
                validation_result["structure_valid"] = False
        
        # Validate profiles structure
        profiles = response_data.get("profiles", [])
        if profiles:
            first_profile = profiles[0]
            
            # Check that removed fields are NOT present
            removed_fields = ["current_title", "current_company", "past_company"]
            for field in removed_fields:
                if field in first_profile:
                    validation_result["issues"].append(f"Removed field still present: {field}")
                    validation_result["structure_valid"] = False
            
            # Check that expected fields ARE present
            expected_profile_fields = ["first_name", "last_name", "location", "skills", "experience"]
            for field in expected_profile_fields:
                if field not in first_profile:
                    validation_result["issues"].append(f"Expected profile field missing: {field}")
        
        return validation_result
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        print("🚀 Starting Comprehensive LinkedIn Integration Tests")
        print("=" * 60)
        
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "summary": {}
        }
        
        # Run individual tests
        test_results["tests"]["search"] = self.test_search_endpoint()
        test_results["tests"]["batch_search"] = self.test_batch_search_endpoint()
        test_results["tests"]["config"] = self.test_config_endpoint()
        test_results["tests"]["status"] = self.test_status_endpoint()
        
        # Validate response structures
        if test_results["tests"]["search"]["success"]:
            response_data = test_results["tests"]["search"]["response_data"]
            test_results["tests"]["search"]["structure_validation"] = self.validate_response_structure(response_data)
        
        # Generate summary
        total_tests = len(test_results["tests"])
        successful_tests = sum(1 for test in test_results["tests"].values() if test["success"])
        
        test_results["summary"] = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": total_tests - successful_tests,
            "success_rate": f"{(successful_tests/total_tests)*100:.1f}%"
        }
        
        return test_results


def print_test_results(results: Dict[str, Any]):
    """Print formatted test results."""
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    summary = results["summary"]
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Successful: {summary['successful_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Success Rate: {summary['success_rate']}")
    
    print("\n" + "-" * 40)
    print("DETAILED RESULTS:")
    print("-" * 40)
    
    for test_name, test_result in results["tests"].items():
        status = "✅ PASS" if test_result["success"] else "❌ FAIL"
        print(f"\n{test_name.upper()}: {status}")
        print(f"  Status Code: {test_result.get('status_code', 'N/A')}")
        
        if not test_result["success"]:
            print(f"  Error: {test_result.get('error', 'Unknown error')}")
        
        if "structure_validation" in test_result:
            validation = test_result["structure_validation"]
            if validation["structure_valid"]:
                print("  Structure: ✅ Valid")
            else:
                print("  Structure: ❌ Invalid")
                for issue in validation["issues"]:
                    print(f"    - {issue}")


if __name__ == "__main__":
    print("LinkedIn Integration API Tester")
    print("Testing updated JSON structure (database-accessible fields only)")
    print("=" * 60)
    
    tester = LinkedInIntegrationTester()
    results = tester.run_comprehensive_test()
    
    print_test_results(results)
    
    # Save results to file
    with open("test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Full results saved to: test_results.json")
