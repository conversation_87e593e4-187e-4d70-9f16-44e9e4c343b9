from typing import List
from fastapi import APIRouter, HTTPException
from controllers.positions_controller import (
    fetch_all_positions_by_project_id,
)
from controllers.project_controller import create_project, get_all_projects, get_project_by_id
from models.models import (
    Position,
    Project,
    ProjectCreate,
)
# from utils.match_evaluations import evaluate_candidate, evaluate_position

from opentelemetry import trace  # NEW
# Telemetry Section
import logging


# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__) 


router = APIRouter()

###################################################
# Project endpoints
###################################################


@router.post("/", response_model=Project)
def create_project_endpoint(project: ProjectCreate):
    logger.info(f"Creating project: {project}")
    return create_project(project)


@router.get("/{proj_id}", response_model=Project)
def get_project_endpoint(proj_id: str):
    logger.info(f"Fetching project with ID: {proj_id}")
    proj = get_project_by_id(proj_id)
    if proj is None:
        logger.warning(f"Project {proj_id} not found")
        raise HTTPException(status_code=404, detail="Project not found")
    return proj


@router.get("/", response_model=List[Project])
def get_all_projects_endpoint():
    logger.info("Fetching all projects")
    return get_all_projects()


@router.get("/{project_id}/positions/", response_model=List[Position])
def get_position_endpoint(project_id: str):
    return fetch_all_positions_by_project_id(project_id)
