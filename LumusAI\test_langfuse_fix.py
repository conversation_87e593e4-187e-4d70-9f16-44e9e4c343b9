#!/usr/bin/env python3
"""
Test script to verify that Langfuse integration is working after fixes.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_langfuse_integration():
    """Test that Langfuse integration is working properly."""
    
    print("🔧 Testing Langfuse Integration After Fixes")
    print("=" * 60)
    
    # Simple test data
    test_data = """
    <PERSON>
    Senior Software Engineer
    
    Contact:
    Email: <EMAIL>
    Phone: ************
    Credit Card: 4000 1234 5678 9010
    SECRET_API_KEY: SECRET_XYZ789
    
    Experience:
    - Senior Engineer at TechCorp (2022-2024)
      * Led microservices architecture
      * Managed team of 8 developers
      * Technologies: Python, Kubernetes, AWS
    
    - Software Engineer at StartupABC (2020-2022)
      * Built scalable web applications
      * Implemented CI/CD pipelines
      * Technologies: Django, PostgreSQL, Docker
    
    Skills:
    - Languages: Python, JavaScript, Go, Rust
    - Frameworks: Django, FastAPI, React, Vue.js
    - Cloud: AWS, GCP, Azure
    - Databases: PostgreSQL, MongoDB, Redis
    - DevOps: Docker, Kubernetes, Terraform
    
    Education:
    - MS Computer Science, Tech University (2018-2020)
    - BS Software Engineering, State College (2014-2018)
    """
    
    print("📞 Testing /process endpoint with CV data...")
    print(f"📊 Data length: {len(test_data)} characters")
    print("🔍 This should now work with proper Langfuse tracing")
    
    try:
        # Make the request
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "cv",
                "data": test_data
            },
            timeout=60
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check if we got structured data
            if isinstance(result, dict):
                print("📄 Structured data received:")
                
                # Show key fields
                if 'personal_information' in result:
                    personal = result['personal_information']
                    print(f"   - Name: {personal.get('full_name', 'N/A')}")
                    print(f"   - Email: {personal.get('email', 'N/A')}")
                
                if 'work_experience' in result:
                    experiences = result['work_experience']
                    print(f"   - Work Experiences: {len(experiences) if experiences else 0}")
                
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"   - Tokens Used: {tokens.get('total_tokens', 'N/A')}")
                    print(f"   - Cost: ${tokens.get('cost', 0):.4f}")
                
                print(f"\n📊 Response size: {len(json.dumps(result))} characters")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False

def main():
    """Main test function."""
    
    # Check service health
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Run the test
    success = test_langfuse_integration()
    
    print("\n" + "=" * 60)
    print("🎯 Summary")
    print("=" * 60)
    
    if success:
        print("✅ Test completed successfully!")
        print("\n📋 What to check now:")
        print("1. Application startup logs should show:")
        print("   - '✅ STARTUP DEBUG: LangChain client has Langfuse handler'")
        print("   - '🔍 STARTUP DEBUG: _get_callbacks() returns 1 callback(s)'")
        
        print("\n2. Processing logs should show:")
        print("   - '🔍 _get_callbacks: Added Langfuse handler, total callbacks: 1'")
        print("   - '🔍 get_structured_data: Using config with callbacks for LLM call'")
        print("   - '🔍 About to call structured_model.ainvoke with config'")
        print("   - '🔍 structured_model.ainvoke completed successfully'")
        
        print("\n3. Langfuse dashboard should show new traces:")
        print("   - URL: http://157.230.167.30:3000")
        print("   - Look for traces with current timestamp")
        print("   - Should show CV processing operations")
        
        print("\n4. If masking is enabled, sensitive data should be masked:")
        print("   - Credit cards: [REDACTED_CREDIT_CARD]")
        print("   - Emails: [REDACTED_EMAIL]")
        print("   - Phone numbers: [REDACTED_PHONE]")
        print("   - Secrets: [REDACTED_SECRET]")
        
    else:
        print("❌ Test failed")
        print("\n🔧 Troubleshooting:")
        print("1. Check if Docker container has Langfuse installed")
        print("2. Restart the application to see startup debug messages")
        print("3. Check application logs for errors")
        print("4. Verify environment variables are set correctly")
    
    print("\n🔄 Next Steps:")
    if success:
        print("1. Verify traces appear in Langfuse dashboard")
        print("2. If traces work, we can re-enable masking")
        print("3. Test masking functionality")
    else:
        print("1. Check Docker container logs")
        print("2. Verify Langfuse installation")
        print("3. Check network connectivity to Langfuse server")

if __name__ == "__main__":
    main()
