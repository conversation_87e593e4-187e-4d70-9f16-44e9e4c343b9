#!/usr/bin/env python3
"""
Test to demonstrate dual masking behavior:
1. API responses contain REAL data (for client functionality)
2. Langfuse traces contain MASKED data (for privacy protection)
"""

import requests
import json
import time
import tempfile
import os

def test_dual_masking_behavior():
    """Test that masking works correctly for both API responses and Langfuse traces"""
    print("🔒 Dual Masking Behavior Test")
    print("=" * 60)
    print("This test demonstrates that:")
    print("✅ API responses contain REAL data (for client functionality)")
    print("🔒 Langfuse traces contain MASKED data (for privacy protection)")
    print("=" * 60)
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health")
        if health_response.status_code != 200:
            print("❌ Service not healthy")
            return
        print("✅ Service is healthy")
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    # Create a test CV with sensitive data
    cv_content = """<PERSON>
Senior Software Developer

Contact Information:
Email: <EMAIL>
Phone: (57) **********
LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
Location: Cali, Valle Del Cauca, Colombia

Professional Experience:
- Senior Developer at TechCorp SA (2022-2024)
  * Led development team of 5 engineers
  * Contact: <EMAIL>
  * Phone: +57 1 234-5678

- Software Engineer at StartupXYZ LTDA (2020-2022)
  * Full-stack development
  * Company email: <EMAIL>

Education:
- Master's in Computer Science
  Universidad Nacional de Colombia (2018-2020)
  Student Email: <EMAIL>

Skills: Python, JavaScript, React, Django
Credit Card: 4532-1234-5678-9012 (for expense reports)"""

    print(f"\n📤 Testing with CV containing sensitive data:")
    print(f"   👤 Name: Santiago García Martínez")
    print(f"   📧 Email: <EMAIL>")
    print(f"   📞 Phone: (57) **********")
    print(f"   🔗 LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/")
    print(f"   🏢 Companies: TechCorp SA, StartupXYZ LTDA")
    print(f"   💳 Credit Card: 4532-1234-5678-9012")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(cv_content)
        temp_file_path = f.name
    
    try:
        print(f"\n🔍 Sending request to /process-test endpoint...")
        
        with open(temp_file_path, 'rb') as file:
            files = {'file': ('cv.txt', file, 'text/plain')}
            data = {
                'action': 'cv',
                'callback_url': 'http://httpbin.org/post'  # Real callback URL for testing
            }
            
            response = requests.post(
                "http://localhost:8000/process-test",
                files=files,
                data=data
            )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Request successful!")
            response_data = response.json()
            task_id = response_data.get('task_id', 'unknown')
            print(f"   📋 Task ID: {task_id}")
            print(f"   ⏰ Timestamp: {response_data.get('timestamp', 'unknown')}")
            
            # Wait for processing to complete
            print(f"\n⏳ Waiting for processing to complete...")
            time.sleep(15)
            
            print(f"\n🎯 VERIFICATION INSTRUCTIONS:")
            print(f"=" * 60)
            print(f"1. 📊 API Response Verification:")
            print(f"   ✅ The API response above contains REAL data")
            print(f"   ✅ Task ID and timestamp are unmasked")
            print(f"   ✅ This is correct - clients need real data!")
            
            print(f"\n2. 🔒 Langfuse Traces Verification:")
            print(f"   🌐 Open: http://157.230.167.30:3000")
            print(f"   🔍 Find trace for task: {task_id}")
            print(f"   🔒 Verify these are MASKED in traces:")
            print(f"      • 'Santiago García Martínez' → [REDACTED_NAME]")
            print(f"      • '<EMAIL>' → [REDACTED_EMAIL]")
            print(f"      • '(57) **********' → [REDACTED_PHONE]")
            print(f"      • 'https://www.linkedin.com/in/santiago-garcia-m/' → [REDACTED_LINKEDIN]")
            print(f"      • 'TechCorp SA' → [REDACTED_COMPANY]")
            print(f"      • 'StartupXYZ LTDA' → [REDACTED_COMPANY]")
            print(f"      • '4532-1234-5678-9012' → [REDACTED_CREDIT_CARD]")
            
            print(f"\n3. 🎯 Expected Behavior:")
            print(f"   ✅ API responses: REAL data (for functionality)")
            print(f"   🔒 Langfuse traces: MASKED data (for privacy)")
            print(f"   🎉 This protects privacy while maintaining functionality!")
            
        else:
            print(f"   ❌ Request failed: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    test_dual_masking_behavior()
