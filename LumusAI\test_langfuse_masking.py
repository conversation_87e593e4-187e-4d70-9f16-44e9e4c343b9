#!/usr/bin/env python3
"""
Test Langfuse masking functionality with real LLM calls
"""

import requests
import json
import time
import tempfile
import os

def test_langfuse_masking():
    """Test that Langfuse masking works with real LLM calls"""
    print("🔒 Langfuse Masking Test")
    print("=" * 50)
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health")
        if health_response.status_code != 200:
            print("❌ Service not healthy")
            return
        print("✅ Service is healthy")
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    # Create a test CV with sensitive data
    cv_content = """<PERSON> Martínez
Senior Software Developer

Contact Information:
Email: <EMAIL>
Phone: (57) **********
LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
Location: Cali, Valle Del Cauca, Colombia

Professional Experience:
- Senior Developer at TechCorp SA (2022-2024)
  * Led development team
  * Built scalable applications
  * Contact: <EMAIL>

- Software Engineer at StartupXYZ LTDA (2020-2022)
  * Full-stack development
  * DevOps implementation

Education:
- Master's in Computer Science
  Universidad Nacional de Colombia (2018-2020)
  Student Email: <EMAIL>

Skills: Python, JavaScript, React, Django"""

    print(f"\n📤 Testing with CV containing sensitive data...")
    print(f"   Name: Santiago García Martínez")
    print(f"   Email: <EMAIL>")
    print(f"   Phone: (57) **********")
    print(f"   LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(cv_content)
        temp_file_path = f.name
    
    try:
        # Test the process-test endpoint
        print(f"\n🔍 Testing /process-test endpoint...")
        
        with open(temp_file_path, 'rb') as file:
            files = {'file': ('cv.txt', file, 'text/plain')}
            data = {
                'action': 'cv',
                'callback_url': 'http://example.com/callback'  # Dummy callback
            }
            
            response = requests.post(
                "http://localhost:8000/process-test",
                files=files,
                data=data
            )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Request successful!")
            response_data = response.json()
            print(f"   📋 Response: {json.dumps(response_data, indent=2)[:200]}...")
        else:
            print(f"   ❌ Request failed: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print(f"\n🎯 Masking Verification Instructions:")
    print(f"=" * 50)
    print(f"1. 🌐 Open Langfuse dashboard: http://**************:3000")
    print(f"2. 🔍 Look for the most recent trace")
    print(f"3. 🔒 Verify that sensitive data is masked:")
    print(f"   ✅ 'Santiago García Martínez' → [REDACTED_NAME]")
    print(f"   ✅ '<EMAIL>' → [REDACTED_EMAIL]")
    print(f"   ✅ '(57) **********' → [REDACTED_PHONE]")
    print(f"   ✅ 'https://www.linkedin.com/in/santiago-garcia-m/' → [REDACTED_LINKEDIN]")
    print(f"   ✅ 'TechCorp SA' → [REDACTED_COMPANY]")
    print(f"   ✅ 'StartupXYZ LTDA' → [REDACTED_COMPANY]")
    print(f"4. 📊 Check both input AND output traces for masking")
    print(f"5. ⚠️  API responses should contain real data (not masked)")

if __name__ == "__main__":
    test_langfuse_masking()
