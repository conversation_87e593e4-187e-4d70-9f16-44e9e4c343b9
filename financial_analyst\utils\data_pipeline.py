"""
Data transformation and validation pipeline for the database analyst system.
Handles data cleaning, validation, transformation, and quality assurance.
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import json
import re
from pydantic import BaseModel, validator, ValidationError
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataQualityIssue(Enum):
    """Enumeration of data quality issues."""
    MISSING_VALUES = "missing_values"
    DUPLICATES = "duplicates"
    OUTLIERS = "outliers"
    INVALID_FORMAT = "invalid_format"
    INCONSISTENT_TYPES = "inconsistent_types"
    DATE_ISSUES = "date_issues"
    NEGATIVE_VALUES = "negative_values"
    ZERO_VALUES = "zero_values"

@dataclass
class DataQualityReport:
    """Data class for data quality assessment results."""
    total_rows: int
    total_columns: int
    missing_values: Dict[str, int]
    duplicate_rows: int
    outliers: Dict[str, int]
    data_types: Dict[str, str]
    quality_score: float
    issues: List[Dict[str, Any]]
    recommendations: List[str]

class FinancialDataValidator(BaseModel):
    """Pydantic model for financial data validation."""
    date: Optional[datetime] = None
    open_price: Optional[float] = None
    high_price: Optional[float] = None
    low_price: Optional[float] = None
    close_price: Optional[float] = None
    volume: Optional[int] = None
    
    @validator('open_price', 'high_price', 'low_price', 'close_price')
    def validate_prices(cls, v):
        if v is not None and v < 0:
            raise ValueError('Prices cannot be negative')
        return v
    
    @validator('volume')
    def validate_volume(cls, v):
        if v is not None and v < 0:
            raise ValueError('Volume cannot be negative')
        return v

class PipelineDatos:
    """Data transformation and validation pipeline."""
    
    def __init__(self):
        """Initialize data pipeline."""
        self.transformation_history: List[Dict[str, Any]] = []
        self.quality_thresholds = {
            'missing_values_threshold': 0.1,  # 10% missing values threshold
            'outlier_threshold': 0.05,        # 5% outliers threshold
            'duplicate_threshold': 0.01       # 1% duplicates threshold
        }
    
    def procesar_datos(self, datos: Union[pd.DataFrame, Dict[str, Any], List[Dict[str, Any]]], 
                      tipo_datos: str = "financial") -> Dict[str, Any]:
        """Process and validate data through the complete pipeline."""
        try:
            logger.info(f"Starting data processing pipeline for {tipo_datos} data")
            
            # Convert input to DataFrame if needed
            df = self._convertir_a_dataframe(datos)
            
            # Step 1: Initial data assessment
            assessment_inicial = self._evaluar_calidad_datos(df)
            
            # Step 2: Data cleaning
            df_limpio = self._limpiar_datos(df, tipo_datos)
            
            # Step 3: Data validation
            df_validado, validation_errors = self._validar_datos(df_limpio, tipo_datos)
            
            # Step 4: Data transformation
            df_transformado = self._transformar_datos(df_validado, tipo_datos)
            
            # Step 5: Final quality assessment
            assessment_final = self._evaluar_calidad_datos(df_transformado)
            
            # Step 6: Generate processing report
            reporte_procesamiento = self._generar_reporte_procesamiento(
                assessment_inicial, assessment_final, validation_errors
            )
            
            return {
                "success": True,
                "datos_procesados": df_transformado,
                "datos_originales_shape": df.shape,
                "datos_finales_shape": df_transformado.shape,
                "assessment_inicial": assessment_inicial,
                "assessment_final": assessment_final,
                "validation_errors": validation_errors,
                "reporte_procesamiento": reporte_procesamiento,
                "transformation_history": self.transformation_history,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in data processing pipeline: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _convertir_a_dataframe(self, datos: Union[pd.DataFrame, Dict, List]) -> pd.DataFrame:
        """Convert various data formats to pandas DataFrame."""
        if isinstance(datos, pd.DataFrame):
            return datos.copy()
        elif isinstance(datos, dict):
            return pd.DataFrame([datos])
        elif isinstance(datos, list):
            return pd.DataFrame(datos)
        else:
            raise ValueError(f"Unsupported data type: {type(datos)}")
    
    def _evaluar_calidad_datos(self, df: pd.DataFrame) -> DataQualityReport:
        """Assess data quality and identify issues."""
        try:
            # Basic statistics
            total_rows, total_columns = df.shape
            missing_values = df.isnull().sum().to_dict()
            duplicate_rows = df.duplicated().sum()
            
            # Data types
            data_types = df.dtypes.astype(str).to_dict()
            
            # Outlier detection for numeric columns
            outliers = {}
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_cols:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                outlier_mask = (df[col] < Q1 - 1.5 * IQR) | (df[col] > Q3 + 1.5 * IQR)
                outliers[col] = outlier_mask.sum()
            
            # Identify issues
            issues = []
            
            # Missing values issues
            for col, missing_count in missing_values.items():
                if missing_count > 0:
                    missing_pct = missing_count / total_rows
                    if missing_pct > self.quality_thresholds['missing_values_threshold']:
                        issues.append({
                            "type": DataQualityIssue.MISSING_VALUES.value,
                            "column": col,
                            "count": missing_count,
                            "percentage": missing_pct,
                            "severity": "high" if missing_pct > 0.2 else "medium"
                        })
            
            # Duplicate rows
            if duplicate_rows > 0:
                dup_pct = duplicate_rows / total_rows
                if dup_pct > self.quality_thresholds['duplicate_threshold']:
                    issues.append({
                        "type": DataQualityIssue.DUPLICATES.value,
                        "count": duplicate_rows,
                        "percentage": dup_pct,
                        "severity": "medium" if dup_pct < 0.05 else "high"
                    })
            
            # Outliers
            for col, outlier_count in outliers.items():
                if outlier_count > 0:
                    outlier_pct = outlier_count / total_rows
                    if outlier_pct > self.quality_thresholds['outlier_threshold']:
                        issues.append({
                            "type": DataQualityIssue.OUTLIERS.value,
                            "column": col,
                            "count": outlier_count,
                            "percentage": outlier_pct,
                            "severity": "low" if outlier_pct < 0.1 else "medium"
                        })
            
            # Calculate quality score
            quality_score = self._calcular_puntuacion_calidad(issues, total_rows, total_columns)
            
            # Generate recommendations
            recommendations = self._generar_recomendaciones(issues)
            
            return DataQualityReport(
                total_rows=total_rows,
                total_columns=total_columns,
                missing_values=missing_values,
                duplicate_rows=duplicate_rows,
                outliers=outliers,
                data_types=data_types,
                quality_score=quality_score,
                issues=issues,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error in data quality assessment: {e}")
            raise
    
    def _limpiar_datos(self, df: pd.DataFrame, tipo_datos: str) -> pd.DataFrame:
        """Clean data based on data type and identified issues."""
        df_clean = df.copy()
        transformations = []
        
        try:
            # Remove exact duplicates
            initial_rows = len(df_clean)
            df_clean = df_clean.drop_duplicates()
            if len(df_clean) < initial_rows:
                transformations.append(f"Removed {initial_rows - len(df_clean)} duplicate rows")
            
            # Handle missing values based on data type
            if tipo_datos == "financial":
                # For financial data, forward fill prices, drop if too many missing
                price_cols = [col for col in df_clean.columns if any(keyword in col.lower() 
                             for keyword in ['price', 'open', 'high', 'low', 'close'])]
                
                for col in price_cols:
                    if df_clean[col].isnull().sum() > 0:
                        if df_clean[col].isnull().sum() / len(df_clean) < 0.1:
                            df_clean[col] = df_clean[col].fillna(method='ffill')
                            transformations.append(f"Forward filled missing values in {col}")
                        else:
                            df_clean = df_clean.dropna(subset=[col])
                            transformations.append(f"Dropped rows with missing {col} (>10% missing)")
            
            # Convert data types appropriately
            for col in df_clean.columns:
                if 'date' in col.lower() or 'time' in col.lower():
                    try:
                        df_clean[col] = pd.to_datetime(df_clean[col])
                        transformations.append(f"Converted {col} to datetime")
                    except:
                        pass
                elif col.lower() in ['volume', 'count']:
                    try:
                        df_clean[col] = df_clean[col].astype(int)
                        transformations.append(f"Converted {col} to integer")
                    except:
                        pass
            
            # Record transformations
            self.transformation_history.extend(transformations)
            logger.info(f"Data cleaning completed: {len(transformations)} transformations applied")
            
            return df_clean
            
        except Exception as e:
            logger.error(f"Error in data cleaning: {e}")
            return df
    
    def _validar_datos(self, df: pd.DataFrame, tipo_datos: str) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Validate data using appropriate validators."""
        validation_errors = []
        df_valid = df.copy()
        
        try:
            if tipo_datos == "financial":
                # Validate financial data using Pydantic model
                for idx, row in df.iterrows():
                    try:
                        # Map DataFrame columns to validator fields
                        validator_data = {}
                        
                        # Map common financial data columns
                        column_mapping = {
                            'date': ['date', 'Date', 'timestamp'],
                            'open_price': ['open', 'Open', 'open_price'],
                            'high_price': ['high', 'High', 'high_price'],
                            'low_price': ['low', 'Low', 'low_price'],
                            'close_price': ['close', 'Close', 'close_price', 'price'],
                            'volume': ['volume', 'Volume']
                        }
                        
                        for field, possible_cols in column_mapping.items():
                            for col in possible_cols:
                                if col in row.index and pd.notna(row[col]):
                                    validator_data[field] = row[col]
                                    break
                        
                        # Validate using Pydantic model
                        FinancialDataValidator(**validator_data)
                        
                    except ValidationError as e:
                        validation_errors.append({
                            "row_index": idx,
                            "errors": [{"field": err["loc"][0], "message": err["msg"]} for err in e.errors()],
                            "severity": "medium"
                        })
                        
                        # Remove invalid rows or fix them
                        if len(e.errors()) > 2:  # Too many errors, remove row
                            df_valid = df_valid.drop(idx)
            
            # Additional business logic validations
            numeric_cols = df_valid.select_dtypes(include=[np.number]).columns
            
            for col in numeric_cols:
                # Check for extreme outliers (beyond 5 standard deviations)
                if len(df_valid[col].dropna()) > 0:
                    mean_val = df_valid[col].mean()
                    std_val = df_valid[col].std()
                    
                    if std_val > 0:
                        extreme_outliers = df_valid[
                            (df_valid[col] < mean_val - 5 * std_val) | 
                            (df_valid[col] > mean_val + 5 * std_val)
                        ]
                        
                        if len(extreme_outliers) > 0:
                            validation_errors.append({
                                "column": col,
                                "issue": "extreme_outliers",
                                "count": len(extreme_outliers),
                                "severity": "high"
                            })
            
            logger.info(f"Data validation completed: {len(validation_errors)} issues found")
            return df_valid, validation_errors
            
        except Exception as e:
            logger.error(f"Error in data validation: {e}")
            return df, validation_errors
    
    def _transformar_datos(self, df: pd.DataFrame, tipo_datos: str) -> pd.DataFrame:
        """Apply data transformations based on data type."""
        df_transformed = df.copy()
        
        try:
            if tipo_datos == "financial":
                # Add common financial indicators
                if 'close' in df_transformed.columns or 'Close' in df_transformed.columns:
                    price_col = 'close' if 'close' in df_transformed.columns else 'Close'
                    
                    # Add returns
                    df_transformed['returns'] = df_transformed[price_col].pct_change()
                    
                    # Add moving averages
                    df_transformed['ma_7'] = df_transformed[price_col].rolling(window=7).mean()
                    df_transformed['ma_30'] = df_transformed[price_col].rolling(window=30).mean()
                    
                    # Add volatility
                    df_transformed['volatility'] = df_transformed['returns'].rolling(window=30).std()
            
            # Ensure proper index
            if 'date' in df_transformed.columns:
                df_transformed = df_transformed.set_index('date')
            elif 'Date' in df_transformed.columns:
                df_transformed = df_transformed.set_index('Date')
            
            logger.info("Data transformation completed")
            return df_transformed
            
        except Exception as e:
            logger.error(f"Error in data transformation: {e}")
            return df
    
    def _calcular_puntuacion_calidad(self, issues: List[Dict[str, Any]], total_rows: int, total_columns: int) -> float:
        """Calculate overall data quality score (0-100)."""
        base_score = 100.0
        
        for issue in issues:
            severity = issue.get("severity", "low")
            if severity == "high":
                base_score -= 20
            elif severity == "medium":
                base_score -= 10
            else:
                base_score -= 5
        
        return max(0.0, base_score)
    
    def _generar_recomendaciones(self, issues: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on identified issues."""
        recommendations = []
        
        for issue in issues:
            issue_type = issue.get("type")
            
            if issue_type == DataQualityIssue.MISSING_VALUES.value:
                recommendations.append(f"Consider imputation strategies for column {issue.get('column')}")
            elif issue_type == DataQualityIssue.DUPLICATES.value:
                recommendations.append("Remove duplicate rows to improve data quality")
            elif issue_type == DataQualityIssue.OUTLIERS.value:
                recommendations.append(f"Investigate outliers in column {issue.get('column')}")
        
        return recommendations
    
    def _generar_reporte_procesamiento(self, assessment_inicial: DataQualityReport, 
                                     assessment_final: DataQualityReport, 
                                     validation_errors: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive processing report."""
        return {
            "quality_improvement": assessment_final.quality_score - assessment_inicial.quality_score,
            "initial_quality_score": assessment_inicial.quality_score,
            "final_quality_score": assessment_final.quality_score,
            "issues_resolved": len(assessment_inicial.issues) - len(assessment_final.issues),
            "validation_errors_count": len(validation_errors),
            "transformations_applied": len(self.transformation_history),
            "processing_summary": {
                "rows_processed": assessment_inicial.total_rows,
                "rows_final": assessment_final.total_rows,
                "columns_processed": assessment_inicial.total_columns,
                "columns_final": assessment_final.total_columns
            }
        }
