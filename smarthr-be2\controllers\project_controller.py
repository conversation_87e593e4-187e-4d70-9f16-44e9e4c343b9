#project_controller.py
from typing import Optional

import psycopg2

from core.config import settings
from models.models import Project, ProjectCreate


def create_project(proj: ProjectCreate) -> Project:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        INSERT INTO projects (client_name, name, description, status) 
        VALUES (%s, %s, %s, %s) 
        RETURNING id, client_name, name, description, status, created_at, updated_at
        """,
        (proj.client_name, proj.name, proj.description, proj.status),
    )
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    return Project(
        id=str(row[0]),
        client_name=row[1],
        name=row[2],
        description=row[3],
        status=row[4],
        created_at=row[5],
        updated_at=row[6],
    )


def get_project_by_id(proj_id: str) -> Optional[Project]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        "SELECT id, client_name, name, description, status, created_at, updated_at FROM projects WHERE id::text=%s",
        (proj_id,),
    )
    row = cur.fetchone()
    cur.close()
    conn.close()
    if not row:
        return None
    return Project(
        id=str(row[0]),
        client_name=row[1],
        name=row[2],
        description=row[3],
        status=row[4],
        created_at=row[5],
        updated_at=row[6],
    )
    
def get_all_projects() -> list[Project]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        "SELECT id, client_name, name, description, status, created_at, updated_at FROM projects",
    )
    
    rows = cur.fetchall()
    cur.close()
    conn.close()
    projects = []
    
    for row in rows:
        projects.append(
            Project(
                id=str(row[0]),
                client_name=row[1],
                name=row[2],
                description=row[3],
                status=row[4],
                created_at=row[5],
                updated_at=row[6],
            )
        )
        
    return projects