"""
Agent communication endpoints for direct agent interaction.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.responses import JSONResponse

from api.models.agent_models import (
    SQLQueryRequest, SQLQueryResponse, DirectSQLRequest,
    PandasAnalysisRequest, PandasAnalysisResponse,
    SchemaAnalysisRequest, SchemaAnalysisResponse,
    AgentStatusRequest, AgentStatusResponse,
    BatchRequest, BatchResponse
)
from api.models.common_models import ResponseStatus, SuccessResponse
from api.services.agent_service import AgentService
from api.dependencies import (
    get_agent_service, require_database, require_agents,
    validate_agent_type
)
from api.middleware.rate_limiting import limiter
from fastapi import Request

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/sql/query", response_model=SQLQueryResponse, status_code=status.HTTP_200_OK)
@limiter.limit("30/minute")
async def execute_sql_query(
    request: Request,
    sql_request: SQLQueryRequest,
    agent_service: AgentService = Depends(get_agent_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Execute a natural language SQL query.
    
    Converts natural language to SQL and executes it against the database.
    
    - **query**: Natural language description of the data you want
    - **database_schema**: Optional specific schema to use
    - **limit_results**: Maximum number of results to return
    - **explain_query**: Include SQL explanation in response
    - **validate_only**: Only validate the query without execution
    """
    try:
        logger.info(f"Executing SQL query: {sql_request.query[:100]}...")
        
        response = await agent_service.execute_sql_query(sql_request)
        
        if response.status == ResponseStatus.SUCCESS:
            logger.info(f"SQL query executed successfully, returned {response.row_count} rows")
        else:
            logger.warning(f"SQL query failed: {response.message}")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to execute SQL query: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute SQL query"
        )


@router.post("/sql/direct", response_model=SQLQueryResponse, status_code=status.HTTP_200_OK)
@limiter.limit("20/minute")
async def execute_direct_sql(
    request: Request,
    sql_request: DirectSQLRequest,
    agent_service: AgentService = Depends(get_agent_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Execute a direct SQL query.
    
    Executes SQL directly against the database with safety validation.
    
    - **sql_query**: SQL query to execute
    - **parameters**: Optional query parameters
    - **read_only**: Safety flag for read-only operations
    """
    try:
        logger.info(f"Executing direct SQL: {sql_request.sql_query[:100]}...")
        
        response = await agent_service.execute_direct_sql(sql_request)
        
        if response.status == ResponseStatus.SUCCESS:
            logger.info(f"Direct SQL executed successfully, returned {response.row_count} rows")
        else:
            logger.warning(f"Direct SQL failed: {response.message}")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to execute direct SQL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute direct SQL"
        )


@router.post("/pandas/analyze", response_model=PandasAnalysisResponse, status_code=status.HTTP_200_OK)
@limiter.limit("15/minute")
async def execute_pandas_analysis(
    request: Request,
    analysis_request: PandasAnalysisRequest,
    agent_service: AgentService = Depends(get_agent_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Execute advanced data analysis using pandas.
    
    Performs comprehensive data analysis with statistical insights and visualizations.
    
    - **query**: Natural language description of the analysis
    - **analysis_type**: Type of analysis (comprehensive, statistical, trend, etc.)
    - **data_source**: Optional specific data source
    - **include_visualizations**: Generate charts and graphs
    - **chart_types**: Specific chart types to generate
    - **statistical_tests**: Include statistical test results
    """
    try:
        logger.info(f"Executing pandas analysis: {analysis_request.query[:100]}...")
        
        response = await agent_service.execute_pandas_analysis(analysis_request)
        
        if response.status == ResponseStatus.SUCCESS:
            logger.info("Pandas analysis completed successfully")
        else:
            logger.warning(f"Pandas analysis failed: {response.message}")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to execute pandas analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute pandas analysis"
        )


@router.post("/schema/analyze", response_model=SchemaAnalysisResponse, status_code=status.HTTP_200_OK)
@limiter.limit("10/minute")
async def execute_schema_analysis(
    request: Request,
    schema_request: SchemaAnalysisRequest,
    agent_service: AgentService = Depends(get_agent_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Execute database schema analysis.
    
    Analyzes database structure, relationships, and provides optimization suggestions.
    
    - **analysis_type**: Type of schema analysis
    - **include_relationships**: Include table relationship analysis
    - **include_statistics**: Include table statistics
    - **specific_tables**: Analyze only specific tables
    - **suggest_optimizations**: Include optimization recommendations
    """
    try:
        logger.info(f"Executing schema analysis: {schema_request.analysis_type}")
        
        response = await agent_service.execute_schema_analysis(schema_request)
        
        if response.status == ResponseStatus.SUCCESS:
            logger.info("Schema analysis completed successfully")
        else:
            logger.warning(f"Schema analysis failed: {response.message}")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to execute schema analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute schema analysis"
        )


@router.get("/status", response_model=AgentStatusResponse, status_code=status.HTTP_200_OK)
async def get_agent_status(
    agent_types: Optional[List[str]] = Query(None, description="Specific agent types to check"),
    include_metrics: bool = Query(False, description="Include performance metrics"),
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    Get the status of all agents or specific agent types.
    
    Returns health and availability information for agents.
    
    - **agent_types**: Optional list of specific agent types to check
    - **include_metrics**: Include performance metrics in response
    """
    try:
        logger.info(f"Getting agent status for: {agent_types or 'all agents'}")
        
        response = await agent_service.get_agent_status(agent_types)
        
        logger.info(f"Agent status retrieved: {response.active_agents}/{response.total_agents} active")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent status"
        )


@router.post("/batch", response_model=BatchResponse, status_code=status.HTTP_200_OK)
@limiter.limit("5/minute")
async def execute_batch_requests(
    request: Request,
    batch_request: BatchRequest,
    agent_service: AgentService = Depends(get_agent_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Execute multiple agent requests in batch.
    
    Allows executing multiple different agent operations in a single request.
    Can be executed in parallel or sequentially.
    
    - **requests**: List of agent requests to execute
    - **parallel_execution**: Execute requests in parallel (default: true)
    - **stop_on_error**: Stop batch execution on first error (default: false)
    """
    try:
        logger.info(f"Executing batch of {len(batch_request.requests)} requests")
        
        if len(batch_request.requests) > 10:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Batch size cannot exceed 10 requests"
            )
        
        response = await agent_service.execute_batch_requests(batch_request)
        
        logger.info(f"Batch execution completed: {response.successful_count} successful, {response.failed_count} failed")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to execute batch requests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute batch requests"
        )


@router.get("/{agent_type}/health", status_code=status.HTTP_200_OK)
async def check_agent_health(
    agent_type: str = Path(..., description="Agent type to check"),
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    Check the health of a specific agent type.
    
    Returns health status for a single agent type.
    
    - **agent_type**: Type of agent to check (sql_agent, pandas_agent, schema_analyzer)
    """
    try:
        validated_type = validate_agent_type(agent_type)
        
        response = await agent_service.get_agent_status([validated_type])
        
        if validated_type in response.agents:
            agent_info = response.agents[validated_type]
            if agent_info.get("available", False):
                return {
                    "agent_type": validated_type,
                    "status": "healthy",
                    "available": True,
                    "last_check": agent_info.get("last_check")
                }
            else:
                return JSONResponse(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    content={
                        "agent_type": validated_type,
                        "status": "unhealthy",
                        "available": False,
                        "error": agent_info.get("error"),
                        "last_check": agent_info.get("last_check")
                    }
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent type {validated_type} not found"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to check agent health for {agent_type}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check agent health"
        )
