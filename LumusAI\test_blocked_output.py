#!/usr/bin/env python3
"""
Test that sensitive outputs are completely blocked from Langfuse
"""

import requests
import json
import time
import tempfile
import os

def test_blocked_output():
    """Test that sensitive outputs don't appear in Langfuse at all"""
    print("🚫 Blocked Output Test")
    print("=" * 60)
    print("Testing that sensitive data is BLOCKED from Langfuse entirely")
    print("(Not masked, but completely absent from traces)")
    print("=" * 60)
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health")
        if health_response.status_code != 200:
            print("❌ Service not healthy")
            return
        print("✅ Service is healthy")
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    # Create a test CV with sensitive data
    cv_content = """<PERSON> García Martínez
Senior Software Developer

Contact Information:
Email: <EMAIL>
Phone: (57) **********
LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
Location: Cali, Valle Del Cauca, Colombia

Professional Experience:
- Senior Developer at TechCorp SA (2022-2024)
  * Led development team
  * Contact: <EMAIL>

- Software Engineer at StartupXYZ LTDA (2020-2022)
  * Full-stack development

Education:
- Master's in Computer Science
  Universidad Nacional de Colombia (2018-2020)
  Student Email: <EMAIL>

Skills: Python, JavaScript, React, Django"""

    print(f"\n📤 Testing with CV containing sensitive data:")
    print(f"   👤 Name: Santiago García Martínez")
    print(f"   📧 Email: <EMAIL>")
    print(f"   📞 Phone: (57) **********")
    print(f"   🔗 LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(cv_content)
        temp_file_path = f.name
    
    try:
        print(f"\n🔍 Sending request to /process-test endpoint...")
        
        with open(temp_file_path, 'rb') as file:
            files = {'file': ('cv.txt', file, 'text/plain')}
            data = {
                'action': 'cv',
                'callback_url': 'http://httpbin.org/post'
            }
            
            response = requests.post(
                "http://localhost:8000/process-test",
                files=files,
                data=data
            )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Request successful!")
            response_data = response.json()
            task_id = response_data.get('task_id', 'unknown')
            print(f"   📋 Task ID: {task_id}")
            
            # Wait for processing to complete
            print(f"\n⏳ Waiting for processing to complete...")
            time.sleep(20)
            
            print(f"\n🎯 VERIFICATION INSTRUCTIONS:")
            print(f"=" * 60)
            print(f"1. 📊 API Response (above):")
            print(f"   ✅ Contains REAL data - this is correct!")
            
            print(f"\n2. 🚫 Langfuse Dashboard Check:")
            print(f"   🌐 URL: http://**************:3000")
            print(f"   🔍 Find trace for task: {task_id}")
            print(f"   🚫 You should see:")
            print(f"      • Input traces (prompts) - may be present")
            print(f"      • Output traces - SHOULD BE MISSING/BLOCKED")
            print(f"      • No personal data visible anywhere")
            
            print(f"\n3. 🎯 Expected Behavior:")
            print(f"   ✅ API responses: REAL data (for functionality)")
            print(f"   🚫 Langfuse traces: NO sensitive outputs (blocked entirely)")
            print(f"   🔒 Privacy fully protected!")
            
            print(f"\n4. 📋 What to Look For:")
            print(f"   ❌ NO 'Santiago García Martínez' in traces")
            print(f"   ❌ NO '<EMAIL>' in traces")
            print(f"   ❌ NO '(57) **********' in traces")
            print(f"   ❌ NO LinkedIn URLs in traces")
            print(f"   ❌ NO structured CV output in traces")
            print(f"   ✅ Only safe, non-sensitive data (if any)")
            
        else:
            print(f"   ❌ Request failed: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    test_blocked_output()
