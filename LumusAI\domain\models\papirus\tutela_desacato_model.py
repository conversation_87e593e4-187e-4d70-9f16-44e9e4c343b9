from pydantic import BaseModel, Field
from typing import List, Optional

class TutelaDesacatoDocument(BaseModel):
    fecha_documento: str = Field("", description="Fecha en que se emitió el documento, en formato 'dd/mm/aaaa', si está disponible.")
    radicado: str = Field("", description="El número de proceso o código del documento. Debe ser un número continuo de al menos 18 dígitos, sin espacios ni guiones (ejemplo: '1000 1234-2024' → '100012342024'). Si no se encuentra, devuelve 'unknown'.")
    correo: List[str] = Field(default_factory=list, description="Lista de todas las direcciones de correo electrónico presentes en el documento. Asegúrate de eliminar duplicados. Si no hay correos, devuelve este campo vacío ('[]').")
    juzgado: str = Field("", description="El juzgado que presenta el documento legal. Si no se encuentra, devuelve cadena vacía ('').")
    accionante: str = Field("", description="La persona o empresa que solicita la acción de tutela.")
    accionado: List[str] = Field(default_factory=list, description="Las personas o empresas señaladas por el accionante.")
    decision: str = Field("", description="Decisión a la que llegó el juzgado en el incidente de desacato. Los únicos valores válidos son: 'Abierto', 'Cerrado', 'Sanción', 'Sanción revocada', 'Sanción inaplicada' o 'Sanción confirmada'.")
    termino: str = Field("", description="El término (en horas o días) declarado por el juzgado para cumplir con la decisión, si está indicado en la sección 'Resuelve' o equivalente. Si no hay término, este campo debe quedar vacío ('').")