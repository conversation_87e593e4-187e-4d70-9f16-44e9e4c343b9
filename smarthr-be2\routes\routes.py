from datetime import datetime
import psycopg2
from fastapi import APIRouter, HTTPException, Query
from core.config import settings
# from utils.match_evaluations import evaluate_candidate, evaluate_position
from opentelemetry import trace  # NEW
from opentelemetry.trace.status import Status, StatusCode
from models.match_analysis_models import CompatibilityEvaluation
from utils import match_evaluations as match_functions 
# Telemetry Section
import logging
import concurrent.futures
from functools import partial
from core.config import settings
from contextlib import contextmanager
import time
from models.llm import models_pool

# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__) 

router = APIRouter()
from templates.candidates_templates.candidate_analysis import templatesObject


@router.get("/test_llm_match")
def test_llm_match(model_name: str, position_id: str, candidate_id: str = None):  # Candidate ID is optional
    """
    Endpoint to test LLM output for a single candidate match.
    """
    try:
        templatesObject.uptate_prompts()
        from controllers.candidates_controller import test_llm_match_logic
        return test_llm_match_logic(model_name, position_id, candidate_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_current_models")
def get_current_models():
    """
    Endpoint to get the current models available in the pool.
    """
    try:
        # Return the keys and its values from the models_pool
        models_info = {name: model.model_dump() for name, model in models_pool.items()}
        return models_info
    except Exception as e:
        logger.error(f"Error retrieving models: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving models")

# Check LLM health status


@router.get('/check_llm_health')
def check_llm_health():
    logger.info("Checking LLM health status")
    try:
        response1 = models_pool["llama4-pro"].invoke("1+1?").content
        logger.info("LLM llama is healthy")
        response2 = models_pool["gpt-4o-mini"].invoke("1+1?").content
        logger.info("LLM is gpt is healthy")

        response = f"LLM llama is healthy: {response1}, LLM gpt is healthy: {response2}"
        return {"status": "Healthy", "response": response}
    except HTTPException as http_exc:
        logger.error(f"LLM health check failed: {http_exc.detail}")
        return {"status": "Unhealthy", "error": http_exc.detail}
    except Exception as e:
        logger.error(f"LLM health check failed: {str(e)}")
        return {"status": "Unhealthy", "error": str(e)}


###################################################
# Match endpoint (execute_match)
###################################################


# Context manager to log time taken for a block of code
@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


# This endpoint matches a position with candidates or a candidate with positions.
@router.post("/match")
def execute_match(
    position_id: str = Query(None), 
    candidate_id: str = Query(None),
    limit: int = 5,
    hasFeedback: int = Query(2, description="If 0, without feedback. If 1, with feedback. If 2, both."),
    batch_mode: bool = Query(True, description="If True, analyze all candidates in one prompt. If False, analyze in parallel.")
):
    try:
        """
        Endpoint para match entre una posición y candidatos, o un candidato con posiciones.
        Supports batch (single prompt) or parallel processing modes.
        """
        logger.info("Iniciando endpoint /match con position_id=%s candidate_id=%s limit=%d batch_mode=%s",
                    position_id, candidate_id, limit, batch_mode)

        with tracer.start_as_current_span("execute_match_logic") as span:
            # Podemos setear atributos en el span
            if position_id:
                span.set_attribute("match.position_id", position_id)
            if candidate_id:
                span.set_attribute("match.candidate_id", candidate_id)
            span.set_attribute("match.limit", limit)

            if not position_id and not candidate_id:
                logger.warning("No se proporcionó position_id ni candidate_id")
                raise HTTPException(
                    status_code=400,
                    detail="You must provide either position_id or candidate_id.",
                )

            # Conectamos a la DB
            # Añadimos un sub-span adicional para la query
            with tracer.start_as_current_span("db_connect_and_query") as db_span:
                db_span.set_attribute("database.url", settings.DATABASE_URL)

                try:
                    conn = psycopg2.connect(settings.DATABASE_URL)
                    cur = conn.cursor()
                except Exception as e:
                    logger.error("Error conectando a la DB: %s", e, exc_info=True)
                    db_span.record_exception(e)
                    db_span.set_status(Status(StatusCode.ERROR))
                    raise HTTPException(status_code=500, detail="Database connection error")

                if position_id:
                    # Retrieve position embedding & text
                    cur.execute(
                        "SELECT embedding, to_be_embebbed FROM positions_smarthr WHERE id=%s",
                        (position_id,),
                    )
                    row = cur.fetchone()
                    if not row:
                        cur.close()
                        conn.close()
                        logger.warning("Position no encontrada con id=%s", position_id)
                        raise HTTPException(status_code=404, detail="Position not found")

                    position_embedding = row[0]
                    processed_position = row[1]

                    # Log: info
                    logger.info("Recuperado embedding de posición ID=%s", position_id)

                    if hasFeedback not in [0, 1, 2]:
                        logger.warning("Valor de hasFeedback no válido: %d", hasFeedback)
                        raise HTTPException(status_code=400, detail="Invalid hasFeedback value")
                    # Search for candidates
                    query = get_candidate_query(hasFeedback)
                    if hasFeedback in [0, 1]:
                        # If hasFeedback is 0 or 1, we need to filter by position_id
                        cur.execute(query, (position_embedding, position_id, limit))
                    else:
                        cur.execute(query, (position_embedding, limit))
                    # cur.execute(query, (position_embedding, limit))
                    results = cur.fetchall()
                    logger.info("Cantidad de candidatos recuperados: %d", len(results))

                    cur.close()
                    conn.close()

                else:
                    # Retrieve candidate embedding & text
                    cur.execute(
                        "SELECT embedding, to_be_embebbed FROM candidates_smarthr WHERE id=%s AND is_deleted = false and is_active = true",
                        (candidate_id,),
                    )
                    row = cur.fetchone()
                    if not row:
                        cur.close()
                        conn.close()
                        logger.warning("Candidate no encontrado con id=%s", candidate_id)
                        raise HTTPException(status_code=404, detail="Candidate not found")

                    candidate_embedding = row[0]
                    processed_candidate = row[1]

                    logger.info("Recuperado embedding de candidato ID=%s", candidate_id)

                    # Search for positions
                    query = """
                        SELECT id, proj_id, position_info, 1 - (embedding <=> %s) AS cosine_similarity, to_be_embebbed
                        FROM positions_smarthr
                        ORDER BY cosine_similarity DESC
                        LIMIT %s
                    """
                    cur.execute(query, (candidate_embedding, limit))
                    results = cur.fetchall()
                    logger.info("Cantidad de posiciones recuperadas: %d", len(results))

                    cur.close()
                    conn.close()

            # Procesamos los resultados (otro sub-span, por ejemplo, para la lógica de matching con LLM)
            with tracer.start_as_current_span("matching_evaluation") as eval_span:
                if position_id:
                    candidates_result = []

                    if batch_mode:
                        try:
                            # Prepare all candidates for batch analysis
                            candidates_texts = [r[4] for r in results]  # Get all processed texts

                            # Attempt batch analysis
                            batch_analysis = match_functions.evaluate_candidates_batch(
                                candidates_texts, 
                                processed_position
                            )

                            candidates_analysis = batch_analysis.model_dump()
                            candidates_analysis["created_at"] = datetime.now()
                            candidates_analysis["updated_at"] = datetime.now()

                            # Map batch results back to individual candidates
                            for idx, r in enumerate(results):
                                candidate_id_db = r[0]
                                proj_id_db = r[1]
                                candidate_info_db = r[2]
                                similarity_score = r[3]

                                analysis = candidates_analysis["candidates_analysis"][idx]

                                custom_analysis_0 = match_functions.get_candidate_analysis_custom_prompt(
                                    candidate_text=candidate_info_db,
                                    processed_position=processed_position
                                )

                                custom_analysis_1 = match_functions.get_candidate_analysis_custom_prompt(
                                    candidate_text=r[4],
                                    processed_position=processed_position
                                )

                                candidates_result.append({
                                    "id": candidate_id_db,
                                    "proj_id": proj_id_db,
                                    "candidate_info": candidate_info_db,
                                    "cosine_similarity": similarity_score,
                                    "analysis": analysis,
                                    "custom_analysis": custom_analysis_0 if custom_analysis_0.compatibilityPercentage > custom_analysis_1.compatibilityPercentage else custom_analysis_1,
                                    "custom_analysis_0": custom_analysis_1 if custom_analysis_0.compatibilityPercentage > custom_analysis_1.compatibilityPercentage else custom_analysis_0,
                                })

                            # Add batch summary to result
                            resultado_final = {
                                "matched_candidates": candidates_result,
                                "processed_position": processed_position,
                                "timestamp": datetime.now(),
                            }
                            
                            return resultado_final

                        except Exception as e:
                            logger.warning(f"Batch analysis failed, falling back to parallel: {str(e)}")
                            batch_mode = True  # Fall back to parallel processing

                    if not batch_mode:  # Either by choice or fallback
                        # Process candidates in parallel using ThreadPoolExecutor
                        with log_time_block("Parallel Candidate Analysis"):
                            with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(results))) as executor:
                                # Create a partial function with the position already set
                                evaluate_func = partial(process_candidate_parallel, processed_position=processed_position)

                                # Map the function to all results
                                parallel_results = list(executor.map(evaluate_func, results))

                                # Add results to candidates_result
                                candidates_result = parallel_results

                    resultado_final = {
                        "matched_candidates": candidates_result,
                        "processed_position": processed_position,
                        "timestamp": datetime.now(),
                    }

                    logger.info("Devolviendo %d candidatos matcheados (modo: %s)", 
                                len(candidates_result),
                                "batch" if batch_mode else "parallel")
                    return resultado_final

                else:
                    positions_result = []
                    for r in results:
                        pos_id_db = r[0]
                        proj_id_db = r[1]
                        position_info_db = r[2]
                        similarity_score = r[3]
                        position_text = r[4]

                        analysis = match_functions.evaluate_position(
                            processed_candidate, position_text, 
                            # si quisieras pasar initial_score=similarity_score:
                            # initial_score=similarity_score
                        )

                        positions_result.append(
                            {
                                "id": pos_id_db,
                                "proj_id": proj_id_db,
                                "position_info": position_info_db,
                                "cosine_similarity": similarity_score,
                                "analysis": dict(analysis),
                            }
                        )

                    resultado_final = {
                        "matched_positions": positions_result,
                        "processed_candidate": processed_candidate,
                        "timestamp": datetime.now(),
                    }

                    logger.info("Devolviendo %d posiciones matcheadas", len(positions_result))
                    return resultado_final
            # Candidadate with
    except psycopg2.Error as db_error:
        logger.error("Error en la base de datos: %s", db_error, exc_info=True)
        raise HTTPException(status_code=500, detail=f"execute_match. Database error occurred: {str(db_error)}")
    except HTTPException as http_exc:
        logger.error("HTTP Exception en el endpoint /match: %s", http_exc.detail, exc_info=True)
        raise http_exc
    except Exception as e:
        logger.error("Error en el endpoint /match: %s", e, exc_info=True)
        raise HTTPException(status_code=500, detail="An error occurred while processing the match")


# Helper function for parallel processing
def process_candidate_parallel(result_row, processed_position):
    candidate_id_db = result_row[0]
    proj_id_db = result_row[1]
    candidate_info_db = result_row[2]
    similarity_score = result_row[3]
    candidate_text = result_row[4]

    # Process this candidate
    analysis = match_functions.evaluate_candidate(
        candidate_text, 
        processed_position
    )

    return {
        "id": candidate_id_db,
        "proj_id": proj_id_db,
        "candidate_info": candidate_info_db,
        "cosine_similarity": similarity_score,
        "analysis": dict(analysis),
    }


# Custom prompt evaluation
# This endpoint evaluates a candidate's CV against a job description using a custom prompt.
@router.post("/evaluate_candidate_custom_prompt")
def evaluate_candidate_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    """
    Evaluates a candidate's CV against a job description using a custom prompt.
    Returns a structured analysis including compatibility percentage, recommendation, matches found, and missing requirements.
    """
    logger.info("Evaluating candidate with custom prompt")
    if not candidate_text or not processed_position:
        logger.error("Candidate text or processed position is empty")
        raise HTTPException(status_code=400, detail="Candidate text and processed position are required")
    analysis = match_functions.evaluate_candidate_custom_prompt(
        candidate_text=candidate_text,
        processed_position=processed_position
    )
    if not analysis:
        return None
    return analysis


# Helper function to get the candidate query based on feedback status
# This function returns a SQL query string based on the hasFeedback parameter.
# If hasFeedback is 0, it retrieves candidates without feedback.
def get_candidate_query(hasFeedback):
    if hasFeedback == 0:
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id AND i.position_id = %s
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed
            HAVING 
                COUNT(i.id) = 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    elif hasFeedback == 1:
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true  AND i.position_id = %s
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed
            HAVING 
                COUNT(i.id) > 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    else:
        return """
            SELECT 
                id, 
                proj_id, 
                candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                to_be_embebbed
            FROM 
                candidates_smarthr 
            Where is_deleted = false and is_active = true
            ORDER BY cosine_similarity DESC
            LIMIT %s
        """
