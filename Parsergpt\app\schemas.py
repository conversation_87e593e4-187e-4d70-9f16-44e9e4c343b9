"""Pydantic schemas for ParserGPT POC."""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime


class FieldSpec(BaseModel):
    """Field specification for data extraction."""
    name: str = Field(..., description="Field name")
    dtype: str = Field(..., description="Data type (string, int, float, string[], etc.)")
    description: Optional[str] = Field(None, description="Field description")
    required: bool = Field(True, description="Whether field is required")
    
    @validator('dtype')
    def validate_dtype(cls, v):
        valid_types = ['string', 'int', 'float', 'bool', 'string[]', 'int[]', 'float[]']
        if v not in valid_types:
            raise ValueError(f"Invalid dtype: {v}. Must be one of {valid_types}")
        return v


class SelectorSpec(BaseModel):
    """Selector specification for a field."""
    css: str = Field(default="", description="CSS selector")
    xpath: str = Field(default="", description="XPath selector")
    regex: str = Field(default="", description="Regex pattern")
    
    @validator('css', 'xpath', 'regex')
    def at_least_one_selector(cls, v, values):
        # At least one selector must be provided
        if not any([v, values.get('css', ''), values.get('xpath', ''), values.get('regex', '')]):
            raise ValueError("At least one selector (css, xpath, or regex) must be provided")
        return v


class TestCase(BaseModel):
    """Test case for adapter validation."""
    url: str = Field(..., description="Test URL")
    expects: Dict[str, Any] = Field(..., description="Expected extraction results")
    page_type: Optional[str] = Field(None, description="Page type (list, detail, other)")


class AdapterData(BaseModel):
    """Complete adapter data structure."""
    domain: str = Field(..., description="Domain this adapter is for")
    version: int = Field(default=1, description="Adapter version")
    url_patterns: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="URL patterns for different page types"
    )
    selectors: Dict[str, SelectorSpec] = Field(
        default_factory=dict,
        description="Selectors for each field"
    )
    tests: List[TestCase] = Field(
        default_factory=list,
        description="Test cases for validation"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    
    @validator('url_patterns')
    def validate_url_patterns(cls, v):
        valid_types = ['list', 'detail', 'other']
        for page_type in v.keys():
            if page_type not in valid_types:
                raise ValueError(f"Invalid page type: {page_type}. Must be one of {valid_types}")
        return v


class AdapterDraft(BaseModel):
    """Draft adapter structure for LLM generation."""
    url_patterns: Dict[str, List[str]] = Field(default_factory=dict)
    selectors: Dict[str, Dict[str, str]] = Field(default_factory=dict)
    
    def to_adapter_data(self, domain: str) -> AdapterData:
        """Convert draft to full adapter data."""
        selector_specs = {}
        for field_name, selector_dict in self.selectors.items():
            selector_specs[field_name] = SelectorSpec(**selector_dict)
        
        return AdapterData(
            domain=domain,
            url_patterns=self.url_patterns,
            selectors=selector_specs
        )


class JobRequest(BaseModel):
    """Request model for creating a new job."""
    start_url: str = Field(..., description="Starting URL for scraping")
    allowed_domains: Optional[List[str]] = Field(
        default_factory=list,
        description="Allowed domains for scraping"
    )
    max_depth: int = Field(default=1, ge=1, le=5, description="Maximum crawl depth")
    max_pages: int = Field(default=10, ge=1, le=1000, description="Maximum pages to scrape")
    field_spec: List[FieldSpec] = Field(..., description="Fields to extract")
    
    @validator('start_url')
    def validate_start_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("start_url must be a valid HTTP/HTTPS URL")
        return v


class JobResponse(BaseModel):
    """Response model for job operations."""
    job_id: int = Field(..., description="Job ID")
    status: str = Field(..., description="Job status")
    message: Optional[str] = Field(None, description="Status message")


class JobStatus(BaseModel):
    """Job status response model."""
    job_id: int = Field(..., description="Job ID")
    status: str = Field(..., description="Current status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")
    
    # Progress information
    pages_discovered: int = Field(default=0, description="Pages discovered")
    pages_processed: int = Field(default=0, description="Pages processed")
    pages_extracted: int = Field(default=0, description="Pages with successful extraction")
    
    # Error information
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    # Configuration
    start_url: str = Field(..., description="Starting URL")
    max_pages: int = Field(..., description="Maximum pages to scrape")
    field_spec: List[FieldSpec] = Field(..., description="Fields being extracted")


class ExtractionResult(BaseModel):
    """Result of data extraction from a page."""
    url: str = Field(..., description="Page URL")
    data: Dict[str, Any] = Field(..., description="Extracted data")
    method: str = Field(..., description="Extraction method used")
    confidence: Optional[float] = Field(None, description="Confidence score")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")


class ValidationResult(BaseModel):
    """Result of adapter validation."""
    field_name: str = Field(..., description="Field being validated")
    success: bool = Field(..., description="Whether validation passed")
    expected: Any = Field(..., description="Expected value")
    actual: Any = Field(..., description="Actual extracted value")
    confidence: Optional[float] = Field(None, description="Confidence score")


class AdapterValidationReport(BaseModel):
    """Complete adapter validation report."""
    domain: str = Field(..., description="Domain being validated")
    overall_success: bool = Field(..., description="Overall validation success")
    success_rate: float = Field(..., description="Success rate (0.0 to 1.0)")
    field_results: List[ValidationResult] = Field(..., description="Per-field results")
    coverage: Dict[str, float] = Field(..., description="Coverage per field")
    recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")


class LLMExtractionRequest(BaseModel):
    """Request for LLM-based extraction fallback."""
    url: str = Field(..., description="Page URL")
    html: str = Field(..., description="HTML content")
    field_spec: List[FieldSpec] = Field(..., description="Fields to extract")
    missing_fields: List[str] = Field(..., description="Fields that need extraction")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
