#!/bin/bash

# Financial Analysis Project Deployment Script
# This script helps deploy the application using Docker Compose

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Dock<PERSON> and <PERSON>er Compose are installed"
}

# Function to check environment variables
check_env() {
    if [ ! -f "backend/.env" ]; then
        print_warning ".env file not found. Creating from template..."
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_warning "Please edit backend/.env and add your GROQ_API_KEY"
            return 1
        else
            print_error ".env.example file not found"
            return 1
        fi
    fi
    
    # Check if GROQ_API_KEY is set
    if ! grep -q "GROQ_API_KEY=gsk_" backend/.env; then
        print_warning "GROQ_API_KEY not properly set in backend/.env"
        print_warning "Please add your GROQ API key to backend/.env"
        return 1
    fi
    
    print_success "Environment variables configured"
    return 0
}

# Function to build and start services
deploy_production() {
    print_status "Deploying Financial Analysis Project in production mode..."
    
    # Stop any running containers
    print_status "Stopping existing containers..."
    docker-compose down --remove-orphans || true
    
    # Build and start services
    print_status "Building and starting services..."
    docker-compose up --build -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    if docker-compose ps | grep -q "healthy"; then
        print_success "Services are running and healthy!"
        print_status "Frontend: http://localhost:8501"
        print_status "Backend API: http://localhost:8000"
        print_status "API Documentation: http://localhost:8000/docs"
    else
        print_error "Some services may not be healthy. Check logs with: docker-compose logs"
    fi
}

# Function to deploy in development mode
deploy_development() {
    print_status "Deploying Financial Analysis Project in development mode..."
    
    # Stop any running containers
    print_status "Stopping existing containers..."
    docker-compose -f docker-compose.dev.yml down --remove-orphans || true
    
    # Build and start services
    print_status "Building and starting services in development mode..."
    docker-compose -f docker-compose.dev.yml up --build -d
    
    print_success "Development environment started!"
    print_status "Frontend: http://localhost:8501"
    print_status "Backend API: http://localhost:8000"
    print_status "Services will auto-reload on code changes"
}

# Function to show logs
show_logs() {
    if [ "$1" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose logs -f
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    docker-compose down --remove-orphans
    docker-compose -f docker-compose.dev.yml down --remove-orphans || true
    print_success "Services stopped"
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down --remove-orphans --volumes
    docker system prune -f
    print_success "Cleanup completed"
}

# Function to show status
show_status() {
    print_status "Service Status:"
    docker-compose ps
}

# Main script logic
case "$1" in
    "prod"|"production")
        check_docker
        if check_env; then
            deploy_production
        else
            print_error "Please configure environment variables first"
            exit 1
        fi
        ;;
    "dev"|"development")
        check_docker
        if check_env; then
            deploy_development
        else
            print_error "Please configure environment variables first"
            exit 1
        fi
        ;;
    "logs")
        show_logs "$2"
        ;;
    "stop")
        stop_services
        ;;
    "status")
        show_status
        ;;
    "clean")
        cleanup
        ;;
    *)
        echo "Financial Analysis Project Deployment Script"
        echo ""
        echo "Usage: $0 {prod|dev|logs|stop|status|clean}"
        echo ""
        echo "Commands:"
        echo "  prod        Deploy in production mode"
        echo "  dev         Deploy in development mode"
        echo "  logs        Show service logs (add 'dev' for development logs)"
        echo "  stop        Stop all services"
        echo "  status      Show service status"
        echo "  clean       Clean up Docker resources"
        echo ""
        echo "Examples:"
        echo "  $0 prod              # Deploy in production"
        echo "  $0 dev               # Deploy in development"
        echo "  $0 logs              # Show production logs"
        echo "  $0 logs dev          # Show development logs"
        echo "  $0 stop              # Stop services"
        echo ""
        exit 1
        ;;
esac
