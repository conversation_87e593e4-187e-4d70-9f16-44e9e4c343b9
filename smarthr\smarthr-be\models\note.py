from datetime import datetime, timezone
from typing import Optional, Dict
from pydantic import BaseModel, Field


class NoteBase(BaseModel):
    candidate_id: str
    notes: dict
    created_by: str
    updated_by: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: Optional[datetime] = None


class Note(NoteBase):
    id: str

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class NoteCreate(BaseModel):
    candidate_id: str
    notes: dict
    created_by: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class NoteUpdate(BaseModel):
    notes: dict
    updated_by: str
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
