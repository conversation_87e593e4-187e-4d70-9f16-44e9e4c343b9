"""Tests for the extraction engine."""

import pytest
from app.extractor import ExtractionEngine


class TestExtractionEngine:
    """Test cases for the ExtractionEngine class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = ExtractionEngine()
        self.sample_html = """
        <html>
            <head><title>Test Page</title></head>
            <body>
                <h1 class="main-title">Sample Article</h1>
                <div class="content">
                    <p class="description">This is a test description.</p>
                    <ul class="tags">
                        <li>tag1</li>
                        <li>tag2</li>
                        <li>tag3</li>
                    </ul>
                    <span class="price">$29.99</span>
                    <div class="meta">
                        <span>Published: 2024-01-01</span>
                        <span>Author: <PERSON></span>
                    </div>
                </div>
            </body>
        </html>
        """
    
    def test_css_selector_extraction(self):
        """Test CSS selector extraction."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.sample_html, 'lxml')
        
        # Test single element
        values = self.engine._extract_with_css(soup, "h1.main-title")
        assert values == ["Sample Article"]
        
        # Test multiple elements
        values = self.engine._extract_with_css(soup, ".tags li")
        assert values == ["tag1", "tag2", "tag3"]
        
        # Test non-existent selector
        values = self.engine._extract_with_css(soup, ".non-existent")
        assert values == []
    
    def test_xpath_extraction(self):
        """Test XPath selector extraction."""
        # Test text extraction
        values = self.engine._extract_with_xpath(self.sample_html, "//h1/text()")
        assert values == ["Sample Article"]
        
        # Test multiple elements
        values = self.engine._extract_with_xpath(self.sample_html, "//li/text()")
        assert values == ["tag1", "tag2", "tag3"]
        
        # Test non-existent xpath
        values = self.engine._extract_with_xpath(self.sample_html, "//nonexistent/text()")
        assert values == []
    
    def test_regex_extraction(self):
        """Test regex pattern extraction."""
        text = "Price: $29.99, Discount: $5.00, Total: $24.99"
        
        # Test price extraction
        values = self.engine._extract_with_regex(text, r'\$(\d+\.\d+)')
        assert values == ["29.99", "5.00", "24.99"]
        
        # Test non-matching pattern
        values = self.engine._extract_with_regex(text, r'€(\d+\.\d+)')
        assert values == []
    
    def test_field_value_normalization(self):
        """Test field value normalization."""
        # Test string field
        field_spec = {"name": "title", "dtype": "string"}
        result = self.engine._normalize_field_value(["Test Title"], field_spec)
        assert result == "Test Title"
        
        # Test string array field
        field_spec = {"name": "tags", "dtype": "string[]"}
        result = self.engine._normalize_field_value(["tag1", "tag2", "tag1"], field_spec)
        assert result == ["tag1", "tag2"]  # Duplicates removed
        
        # Test integer field
        field_spec = {"name": "count", "dtype": "int"}
        result = self.engine._normalize_field_value(["42"], field_spec)
        assert result == 42
        
        # Test empty values
        field_spec = {"name": "empty", "dtype": "string"}
        result = self.engine._normalize_field_value([], field_spec)
        assert result == ""
    
    @pytest.mark.asyncio
    async def test_extract_with_adapter(self):
        """Test complete extraction with adapter."""
        adapter = {
            "selectors": {
                "title": {"css": "h1.main-title", "xpath": "", "regex": ""},
                "description": {"css": ".description", "xpath": "", "regex": ""},
                "tags": {"css": ".tags li", "xpath": "", "regex": ""},
                "price": {"css": "", "xpath": "", "regex": r'\$(\d+\.\d+)'}
            }
        }
        
        field_specs = [
            {"name": "title", "dtype": "string"},
            {"name": "description", "dtype": "string"},
            {"name": "tags", "dtype": "string[]"},
            {"name": "price", "dtype": "string"}
        ]
        
        result = await self.engine.extract_with_adapter(
            self.sample_html, adapter, field_specs
        )
        
        assert result["title"] == "Sample Article"
        assert result["description"] == "This is a test description."
        assert result["tags"] == ["tag1", "tag2", "tag3"]
        assert result["price"] == "29.99"
    
    def test_validate_selectors(self):
        """Test selector validation."""
        # Valid adapter
        valid_adapter = {
            "selectors": {
                "title": {"css": "h1", "xpath": "//h1", "regex": r"Title: (.+)"}
            }
        }
        errors = self.engine.validate_selectors(valid_adapter)
        assert errors == []
        
        # Invalid CSS selector
        invalid_adapter = {
            "selectors": {
                "title": {"css": "h1[invalid", "xpath": "", "regex": ""}
            }
        }
        errors = self.engine.validate_selectors(invalid_adapter)
        assert len(errors) > 0
        assert "Invalid CSS selector" in errors[0]
