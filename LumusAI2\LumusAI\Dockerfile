FROM python:3.12.7-alpine

WORKDIR /app

# Instalar dependencias necesarias para compilar psutil y otras bibliotecas
RUN apk update && apk add --no-cache --virtual .build-deps \
    gcc \
    musl-dev \
    linux-headers \
    python3-dev \
    libffi-dev \
    && apk add --no-cache \
    libstdc++ \
    && rm -rf /var/cache/apk/*

# Copiar el archivo de dependencias de Python
COPY requirements.txt .
COPY requirements_webhook.txt .

# Instalar las dependencias de Python
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r requirements_webhook.txt

# Remover dependencias de compilación para reducir tamaño
RUN apk del .build-deps

# Copiar el resto del código de la aplicación
COPY . .

# Exponer el puerto de la aplicación
EXPOSE 8000

# Comando para ejecutar la aplicación
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
