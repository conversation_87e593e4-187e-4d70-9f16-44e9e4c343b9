# FastAPI Semantic Matching Service

This project provides a FastAPI service to manage Projects, Candidates, and Positions, 
and perform semantic matching using embeddings stored in a Postgres database with pgvector.

## Setup

1. Copy `.env.example` to `.env` and fill in credentials.
2. Run `docker-compose up --build` to start the service.
3. Access the service at `http://localhost:8080/docs`.

## Endpoints

- POST /projects: Create Project
- GET /projects/{proj_id}: Get Project by ID
- POST /candidates: Ingest Candidate
- GET /candidates/{candidate_id}: Get Candidate by ID
- GET /candidates: Fetch all candidates
- POST /positions: Ingest Position
- GET /positions/{position_id}: Get Position by ID
- GET /positions: Fetch all positions
- POST /match: Execute match
