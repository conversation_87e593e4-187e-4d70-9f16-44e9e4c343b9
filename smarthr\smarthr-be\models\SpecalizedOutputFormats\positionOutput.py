#Option 2
from pydantic import BaseModel, Field
from typing import List, Optional

class Skill(BaseModel):
    skillName: Optional[str] = Field(..., description="Name of the skill (e.g., Azure Data Factory).")
    skillCategory: Optional[str] = Field(..., description="Category of the skill (e.g., professionalSkills, niceToHave).")
    skillLeveName: Optional[str] = Field(..., description="Skill level name (e.g., Intermediate, Advanced).")
    skillScore: Optional[int] = Field(..., description="Numerical score representing the skill proficiency (e.g., 1-5).")

class PositionAllocation(BaseModel):
    Name: Optional[str] = Field(..., description="Name of the country or region associated with the position.")
    isoCode: Optional[str] = Field(None, description="ISO code of the country or region (e.g., US, IN).")

class ReasonStatus(BaseModel):
    reason: Optional[str] = Field(None, description="Reason why the position was closed or updated.")

class Seniority(BaseModel):
    name: Optional[str] = Field(..., description="Seniority level of the position (e.g., Junior, Senior).")

class Position(BaseModel):
    positionName: Optional[str] = Field(..., description="Name of the position (e.g., Azure Data Engineer).")
    clientName: Optional[str] = Field("", description="Name of the client offering the position.")
    jobDescription: Optional[str] = Field("", description="Detailed job description in plain text.")
    mainResponsabilities: Optional[str] = Field("", description="Main responsibilities of the position.")
    seniority: Seniority = Field(..., description="Seniority level of the position.")
    roleName: Optional[str] = Field("", description="Role associated with the position (e.g., Data Engineer).")
    projectName: Optional[str] = Field("", description="Project name the position is associated with.")
    positionTypeName: Optional[str] = Field("", description="Type of position (e.g., Full-time, Contract).")
    positionCreateDate: Optional[str] = Field("", description="Date when the position was created.")
    positionStartDate: Optional[str] = Field("", description="Start date of the position.")
    positionCloseDate: Optional[str] = Field("", description="Close date of the position, if applicable.")
    createdBy: Optional[str] = Field("", description="Name or identifier of the person who created the position.")
    reasonStatus: Optional[ReasonStatus] = Field(None, description="Reason for the current status of the position.")
    openPositionSkills: Optional[List[Skill]] = Field(
        None, description="List of skills required for the position. Return this as a JSON array of objects."
    )
    positionAllocations: Optional[List[PositionAllocation]] = Field(
        None, description="List of geographic allocations for the position. Return this as a JSON array of objects."
    )