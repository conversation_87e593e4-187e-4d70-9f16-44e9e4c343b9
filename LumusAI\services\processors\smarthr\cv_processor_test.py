from services.processors.base_processor import DocumentProcessor
from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient
from fastapi import UploadFile, HTTPException
from typing import Optional, List
from pydantic import BaseModel
import os
import tempfile
import fitz
import base64
import requests
import json
from domain.models.smarthr.cv_model import CV
import asyncio  # Used for CancelledError handling


def merge_cv_data(existing_data: dict, new_data: dict) -> dict:
    """Merge new CV data into existing data, preserving existing values when new values are null.

    Args:
        existing_data: The accumulated CV data from previous pages
        new_data: The new CV data extracted from the current page

    Returns:
        The merged CV data
    """
    if not existing_data:
        return new_data
    if not new_data:
        return existing_data

    result = existing_data.copy()

    # Process each top-level field
    for key, new_value in new_data.items():
        # Skip if the new value is None/null
        if new_value is None:
            continue

        # If the field doesn't exist in the result yet, add it
        if key not in result or result[key] is None:
            result[key] = new_value
            continue

        # Handle different types of fields
        if isinstance(new_value, dict) and isinstance(result[key], dict):
            # Recursively merge nested dictionaries (like personal_info)
            result[key] = merge_cv_data(result[key], new_value)

        elif isinstance(new_value, list) and isinstance(result[key], list):
            # For lists (like work_experience, skills, etc.), append new items
            # We need to be careful not to duplicate items

            # If the list contains dictionaries (like work_experience)
            if new_value and isinstance(new_value[0], dict):
                # Special handling for work_experience to merge responsibilities
                if key == 'work_experience':
                    # Create a map of existing work experiences by job title and company name
                    existing_work_exp_map = {}
                    for i, item in enumerate(result[key]):
                        if 'job_title' in item and 'company_name' in item:
                            job_key = f"{item['job_title']}_{item['company_name']}"
                            existing_work_exp_map[job_key] = i

                    # Process each new work experience
                    for new_item in new_value:
                        if 'job_title' in new_item and 'company_name' in new_item:
                            job_key = f"{new_item['job_title']}_{new_item['company_name']}"

                            # If this job already exists, merge the responsibilities
                            if job_key in existing_work_exp_map:
                                existing_index = existing_work_exp_map[job_key]
                                existing_item = result[key][existing_index]

                                # Merge responsibilities if they exist in both
                                if 'responsibilities' in new_item and new_item['responsibilities'] and 'responsibilities' in existing_item:
                                    # Add only new responsibilities that don't already exist
                                    existing_resp_set = set(existing_item['responsibilities'])
                                    for resp in new_item['responsibilities']:
                                        if resp not in existing_resp_set:
                                            existing_item['responsibilities'].append(resp)
                                            existing_resp_set.add(resp)
                                # If responsibilities only exist in the new item, add them
                                elif 'responsibilities' in new_item and new_item['responsibilities']:
                                    existing_item['responsibilities'] = new_item['responsibilities']

                                # Merge skills if they exist in both
                                if 'skills' in new_item and new_item['skills'] and 'skills' in existing_item:
                                    existing_skill_names = {skill['name'] for skill in existing_item['skills'] if 'name' in skill}
                                    for skill in new_item['skills']:
                                        if 'name' in skill and skill['name'] not in existing_skill_names:
                                            existing_item['skills'].append(skill)
                                            existing_skill_names.add(skill['name'])
                                # If skills only exist in the new item, add them
                                elif 'skills' in new_item and new_item['skills']:
                                    existing_item['skills'] = new_item['skills']
                            else:
                                # This is a new job, add it to the result
                                result[key].append(new_item)
                                # Update the map with the new job
                                existing_work_exp_map[job_key] = len(result[key]) - 1
                        else:
                            # If job_title or company_name is missing, just add it
                            result[key].append(new_item)
                else:
                    # For other types of lists with dictionaries (like skills, education)
                    # Create a set of existing items for comparison
                    existing_items = set()
                    for item in result[key]:
                        if 'name' in item:
                            existing_items.add(item['name'])
                        elif 'institution_name' in item:
                            existing_items.add(item['institution_name'])
                        else:
                            existing_items.add(str(item))

                    # Add new items that don't exist yet
                    for item in new_value:
                        if 'name' in item and item['name'] in existing_items:
                            continue
                        elif 'institution_name' in item and item['institution_name'] in existing_items:
                            continue
                        elif str(item) in existing_items:
                            continue
                        result[key].append(item)
            else:
                # For simple lists (like strings), just add new items
                for item in new_value:
                    if item not in result[key]:
                        result[key].append(item)

        # For other types, prefer non-null values
        elif new_value is not None:
            result[key] = new_value

    return result


def cleanup_file(file_path: str, temp_files_list: Optional[List[str]] = None) -> bool:
    """Safely remove a file and optionally remove it from tracking list.

    Args:
        file_path: Path to the file to remove
        temp_files_list: Optional list of tracked files to update

    Returns:
        True if the file was successfully removed, False otherwise
    """
    if not file_path:
        return False

    if temp_files_list is not None and file_path in temp_files_list:
        temp_files_list.remove(file_path)

    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            return True
        except Exception as e:
            print(f"Warning: Failed to remove {file_path}: {e}")
    return False


class TempFileTracker:
    """Context manager for tracking and cleaning up temporary files.

    Ensures that all tracked files are properly cleaned up when the context exits,
    even if an exception occurs.
    """
    def __init__(self):
        self.files: List[str] = []

    def add(self, file_path: str) -> str:
        """Add a file to be tracked for cleanup.

        Args:
            file_path: Path to the file to track

        Returns:
            The same file path for convenience
        """
        self.files.append(file_path)
        return file_path

    def remove(self, file_path: str) -> bool:
        """Remove a file from tracking and delete it if it exists.

        Args:
            file_path: Path to the file to remove

        Returns:
            True if the file was successfully removed, False otherwise
        """
        return cleanup_file(file_path, self.files)

    def __enter__(self):
        return self

    def __exit__(self, *_):
        """Clean up all tracked files when exiting the context.

        The underscore parameter name indicates that we're deliberately ignoring the arguments.
        """
        for file_path in list(self.files):  # Create a copy of the list to avoid modification during iteration
            cleanup_file(file_path, self.files)


# Markdown conversion prompt
markdown_prompt = """
Extract ALL text content from this CV page image and convert it to clean Markdown format.

CRITICAL INSTRUCTIONS:
- Extract EVERY piece of text visible in the image - do not miss anything
- Preserve the EXACT structure and hierarchy as shown in the image
- Do NOT add headers, sections, or structure that is not explicitly visible in the image
- Do NOT infer or add content like "### Work Experience" if it's not clearly written in the image
- Use bullet points (•) exactly as they appear in the image
- Preserve all formatting: bold (**text**), italic (*text*), dates, contact info
- If text appears to continue from previous page or is cut off, include it exactly as visible
- Maintain the original spacing and line breaks
- Do NOT reorganize or restructure the content
- Do NOT add explanatory text or section headers that aren't in the original

WHAT TO EXTRACT:
- Every word, number, symbol, and punctuation mark visible
- All bullet points and list items
- All dates, names, companies, job titles
- All contact information
- All skills, technologies, and tools mentioned
- All responsibilities and achievements

Return ONLY the extracted text in Markdown format, nothing else.
"""

# Structured data extraction prompt
structured_prompt = """
Extract ALL relevant information from the provided CV markdown text and present it in the following standardized format,
without adding any notes or additional information:

If the information is missing DO NOT make up new info, leave it blank.

**Personal Information:**
- Full Name: [Value]
- Country: [Value]
- City: [Value]
- Address: [Value]
- Phone Number: [Value]
- Email: [Value] (If multiple emails are found, separate them with semicolons)
- LinkedIn Profile: [Value]
- Website: [Value]

**Summary:**
[Value]

**Education:**
1. Institution Name: [Value]
- Degree: [Value]
- Field of Study: [Value]
- Start Date: [Value]
- End Date: [Value]
- Location: [Value]
- Description: [Value]
(Continue numbering for each educational experience.)

**Work Experience:**
1. Job Title: [Value]
- Company Name: [Value]
- Start Date: [Value]
- End Date: [Value]
- Location: [Value]
- Responsibilities:
    - [Responsibility 1]
    - [Responsibility 2]
    - (Continue listing responsibilities.)
- Technical skills used:
    - [Technology 1]
    - Months of Experience: [Value]
    - [Technology 2]
    - Months of Experience: [Value]
    - (Continue listing Technical skills.)
(Continue numbering for each work experience. Without omitting any work experience.)

**Skills:**
1. Skill type: [Value]
    1. Skill name: [Value]
    - Proficiency Level: [Value]
    - Years of Experience: [Value]
    (Continue numbering for each skill name.)
(Continue numbering for each skill type.)

**Soft Skills:**
1. Skill type: [Value]
    - Skill name: [Value]
    - Description: [Value]
    (Continue numbering for each soft skill.)

**Certifications:**
1. Name: [Value]
- Issuing Organization: [Value]
- Issue Date: [Value]
- Expiration Date: [Value]
- Credential ID: [Value]
- Credential URL: [Value]
(Continue numbering for each certification.)

**Languages:**
1. Language: [Value]
- Proficiency Level: [Value]
(Continue numbering for each language.)

**Projects:**
1. Name: [Value]
- Description: [Value]
- Role: [Value]
- Technical skills used:
    - [Technology 1]
        - Months of Experience: [Value]
    - [Technology 2]
        - Months of Experience: [Value]
    - (Continue listing technologies.)
- Start Date: [Value]
- End Date: [Value]
- URL: [Value]
(Continue numbering for each project.)

**Roles:**
(List the top 3 most relevant positions or roles that the candidate can perform based on their experience, order them in order from most relevant to least)
- Role 1: [Value]
- Role 2: [Value]
- Role 3: [Value]

**References:**
1. Name: [Value]
- Relationship: [Value]
- Phone Number: [Value]
- Email: [Value] (If multiple emails are found, separate them with semicolons)
(Continue numbering for each reference.)

**Hobbies and Interests:**
- [Hobby or Interest 1]
- [Hobby or Interest 2]
(Continue listing hobbies and interests.)

**Instructions:**
+ Extract ALL relevant information from the CV, including personal details, education, work experience, skills, certifications, languages, projects, references, and hobbies/interests.
+ IMPORTANT: Include ALL work experiences found in the CV. Do not omit or summarize any work experience entries.
+ Include in **Work Experience:** jobs as teacher or researcher in universities.
+ If **Education:** items present just one year, repeat the same year in "Start date" and in the "End date".
+ DO NOT CONFUSE THE "SKILL TYPE" WITH THE "SKILL".  "SKILL TYPE" IS THE GENERAL CLASSIFICATION OF THE SKILL.
+ For the best calculus of the months of experience, "Today", "Present" or "Actual" makes reference of the today's date.
+ IMPORTANT: If multiple email addresses are found in the CV, extract ALL of them and separate them with semicolons (;). For example: "<EMAIL>;<EMAIL>".
+ For each **Work Experience:** in the document, DEDUCE the "Technical skills used" found in the description of EACH project and DEDUCE their "Months of Experience" according to the time extension of the project.
+ Separate each individual skill.
+ **Skills:** section must be extracted from the skills found in "Technical skills used" in **Work Experience:** section.
+ SUM the months extracted for EACH skill found in EACH **Work Experience:**, sum and display for each skill in **Skills:**.  Display the amount in years.
+ IF you find other technical skills with "Years of Experience", INCLUDE THESE SKILLS at the end of the **Skills:** section.
+ IF you find other technical skills without "Years of Experience", INCLUDE THESE SKILLS at the end of the **Skills:** section and declare in "Years of Experience" as "NOT DEFINED".
+ Include every section and sub-section as shown above, even if some sections are empty.
+ Do not add any notes or additional comments; only include the information extracted from the CV.
+ Present all information clearly and in the order shown above.
+ For lists (like responsibilities and technologies used), list each item on a separate line with a bullet point.
+ Use the provided format exactly, including the labels and the order.
+ DO NOT use phrases like "Other work experiences omitted for brevity" or similar - include ALL experiences.

TRANSLATE ALL THE INFORMATION EXTRACTED FROM THE TEXT TO ENGLISH.
"""


class CVProcessorTest(DocumentProcessor):
    """
    Test processor for extracting structured information from CV/resume documents.

    This version processes PDFs only and follows a specific workflow:
    1. Convert each page to an image
    2. Extract markdown from each page image
    3. Concatenate with first paragraph of next page (if available)
    4. Send markdown to structured data extraction with initial values
    5. Use merging function to preserve all previously extracted data

    Key Features:
    - PDF-only processing with page-by-page image conversion
    - Markdown extraction from page images
    - Intelligent page concatenation with lookahead
    - Comprehensive information extraction with data preservation
    - Multi-page document handling with content merging

    Attributes:
        openai_client (OpenAIClient): Client for OpenAI API interactions
        langchain_client (LangChainClient): Client for LangChain operations
    """
    def __init__(self, openai_client: OpenAIClient, langchain_client: LangChainClient):
        """
        Initializes the CVProcessorTest with the required OpenAI and
        LangChain clients.
        """
        self.openai_client = openai_client
        self.langchain_client = langchain_client

    async def process(self, file: Optional[UploadFile], data: Optional[str] = None) -> dict:
        """
        Main entry point to process a CV. Either a file or a URL must be
        provided. Only PDF files are supported.

        :param file: The uploaded PDF file to be processed.
        :param data: A URL from which the PDF file can be downloaded, if no
                     file is provided.
        :return: Dictionary containing the extracted CV data.
        """
        if not file and not data:
            raise HTTPException(status_code=400, detail="The CV action requires a file or URL")
        result = await self.process_cv(file, data)
        return result

    async def process_cv(self, file: UploadFile, data: Optional[str] = None) -> dict:
        """
        Process a CV document using the new workflow:
        1. Convert PDF pages to images
        2. Extract markdown from each page
        3. Concatenate with first paragraph of next page
        4. Extract structured data with initial values
        5. Merge data preserving all information

        Args:
            file: The uploaded PDF file
            data: Optional URL to download the file from

        Returns:
            Dictionary containing the extracted CV data
        """
        # Use our TempFileTracker context manager for better cleanup
        with tempfile.TemporaryDirectory() as temp_dir, TempFileTracker() as file_tracker:
            try:
                # ------------------------------------------------------------
                # 1. Download or read the file
                # ------------------------------------------------------------
                if data:
                    url = data.strip()
                    response = requests.get(url)
                    if response.status_code != 200:
                        raise HTTPException(status_code=400, detail="Could not download file from provided URL.")
                    filename = url.split("/")[-1].split("?")[0]
                    file_extension = filename.split(".")[-1].lower()

                    file_path = os.path.join(temp_dir, filename)
                    with open(file_path, "wb") as f:
                        f.write(response.content)
                    # Track the temporary file
                    file_tracker.add(file_path)
                else:
                    filename = file.filename
                    file_extension = filename.split(".")[-1].lower()

                    file_path = os.path.join(temp_dir, filename)
                    file_content = await file.read()
                    with open(file_path, "wb") as f:
                        f.write(file_content)
                    # Track the temporary file
                    file_tracker.add(file_path)

                # ------------------------------------------------------------
                # 2. Validate file type - only PDF supported
                # ------------------------------------------------------------
                if file_extension != 'pdf':
                    raise HTTPException(status_code=400, detail="Only PDF files are supported in this processor.")

                # ------------------------------------------------------------
                # 3. Process PDF pages as images with markdown extraction
                # ------------------------------------------------------------
                print("Processing CV pages as images with markdown extraction.")

                # Initialize accumulated CV data and token usage
                accumulated_cv_data = None
                page_markdowns = []  # Store markdown for each page
                total_extraction_cost = {
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0,
                    "cost": 0.0
                }

                with fitz.open(file_path) as doc:
                    # Log the number of pages
                    page_count = len(doc)
                    print(f"Document with {page_count} pages detected.")

                    # First pass: Extract markdown from all pages
                    for i, page in enumerate(doc):
                        # Render page to image with dpi=300
                        pix = page.get_pixmap(dpi=300)
                        image_filename = f"page-{page.number}.png"
                        image_path = os.path.join(temp_dir, image_filename)
                        pix.save(image_path)
                        file_tracker.add(image_path)  # Track for cleanup

                        # Encode the image to base64
                        base64_image_data = self.encode_image(image_path)

                        try:
                            print(f"\n--- Extracting markdown from page {i+1} ---")

                            # Extract markdown from the page image
                            markdown_result = await self.langchain_client.extract_data(
                                markdown_prompt,
                                base64_image_data
                            )

                            page_markdown = markdown_result['response']

                            # Accumulate token usage from markdown extraction
                            if 'token_usage' in markdown_result:
                                total_extraction_cost["prompt_tokens"] += markdown_result["token_usage"]["prompt_tokens"]
                                total_extraction_cost["completion_tokens"] += markdown_result["token_usage"]["completion_tokens"]
                                total_extraction_cost["total_tokens"] += markdown_result["token_usage"]["total_tokens"]
                                total_extraction_cost["cost"] += markdown_result["token_usage"]["cost"]

                            # Clean the markdown by removing code block markers
                            cleaned_markdown = self.clean_markdown_response(page_markdown)
                            page_markdowns.append(cleaned_markdown)
                            # Clean up the image file
                            file_tracker.remove(image_path)

                        except Exception as e:
                            print(f"Error extracting markdown from page {i+1}: {str(e)}")
                            page_markdowns.append("")  # Add empty markdown to maintain index alignment

                    # Second pass: Move first paragraphs and process markdown with structured extraction
                    # First, move first paragraphs from next pages to current pages
                    modified_markdowns = []
                    for i, current_markdown in enumerate(page_markdowns):
                        if not current_markdown:  # Skip pages where markdown extraction failed
                            modified_markdowns.append("")
                            continue

                        markdown_content = current_markdown

                        # Move first block of text from next page if available
                        if i + 1 < len(page_markdowns) and page_markdowns[i + 1]:
                            next_page_markdown = page_markdowns[i + 1]

                            # Extract first block of text (all consecutive bullet points or first paragraph)
                            first_block = self.extract_first_content_block(next_page_markdown)

                            # Only move if there's actually content to move
                            if first_block.strip():
                                # Add to current page
                                markdown_content += "\n" + first_block
                                print(f"Added to page {i+1}: '{first_block}'")

                                # Remove first block from next page
                                remaining_content = self.remove_first_content_block(next_page_markdown, first_block)
                                page_markdowns[i + 1] = remaining_content

                                print(f"Page {i+2} now starts with: '{remaining_content.split(chr(10))[0] if remaining_content else '(empty)'}'")
                            else:
                                print(f"First block is empty, not moving anything")
                            print("=== END CONTENT MOVE ===\n")

                        modified_markdowns.append(markdown_content)
                        print("-=========--------------=============---------------")
                        print(modified_markdowns[0])

                    # Now process each modified page
                    for i, markdown_content in enumerate(modified_markdowns):
                        if not markdown_content:  # Skip pages where markdown extraction failed
                            continue

                        print(f"\n=== FINAL CONTENT FOR PAGE {i+1} ===")
                        print(f"Content length: {len(markdown_content)} characters")
                        print(f"First 200 chars: {markdown_content[:200]}...")
                        print("=== END FINAL CONTENT ===\n")

                        # Create a prompt for structured data extraction
                        if i == 0:
                            page_prompt = """Extract all structured information from this CV markdown content.
                            This is the first page of the CV, so it likely contains personal information,
                            summary, and possibly the start of education or work experience sections.

                            Pay special attention to any content marked as continuation from the next page,
                            as this may provide additional context for incomplete sections."""
                        else:
                            page_prompt = f"""Extract all structured information from this CV markdown content.
                            This is page {i+1} of the CV, which likely continues from previous pages.

                            IMPORTANT: If you see information that appears to be a continuation of content
                            from previous pages, make sure to include it appropriately. Look for job titles,
                            company names, or other identifiers that match previous entries.

                            Pay special attention to any content marked as continuation from the next page,
                            as this may provide additional context for incomplete sections."""

                        try:
                            print(f"\n--- Processing structured data for page {i+1} ---")

                            # If we have accumulated data, print it
                            if accumulated_cv_data:
                                print(f"Existing accumulated data keys: {list(accumulated_cv_data.keys())}")

                            # Extract structured data from markdown
                            page_result = await self.langchain_client.get_structured_data_test(
                                CV,  # The Pydantic model
                                markdown_content,
                                f"{page_prompt}\n\n{structured_prompt}",
                                initial_data=accumulated_cv_data  # Pass accumulated data for subsequent pages
                            )

                            print(f"Structured data extracted from page {i+1}")
                            print(f"Response keys: {list(page_result['response'].keys())}")

                            # Accumulate token usage from structured data extraction
                            if 'token_usage' in page_result:
                                total_extraction_cost["prompt_tokens"] += page_result["token_usage"]["prompt_tokens"]
                                total_extraction_cost["completion_tokens"] += page_result["token_usage"]["completion_tokens"]
                                total_extraction_cost["total_tokens"] += page_result["token_usage"]["total_tokens"]
                                total_extraction_cost["cost"] += page_result["token_usage"]["cost"]

                            # Update accumulated data with the result from this page
                            if accumulated_cv_data is None:
                                # First page, just use the result
                                accumulated_cv_data = page_result["response"]
                                print(f"Initial data from page {i+1}")
                            else:
                                # Merge the new data with accumulated data
                                new_data = page_result["response"]
                                print(f"\nMerging data from page {i+1}")

                                accumulated_cv_data = merge_cv_data(accumulated_cv_data, new_data)

                                print(f"Data merged successfully")

                        except Exception as e:
                            print(f"Error processing structured data for page {i+1}: {str(e)}")
                            # Continue with next page even if this one fails

                # Clean up the original file
                file_tracker.remove(file_path)

                # Return the final accumulated data with token usage added to the original response
                if accumulated_cv_data:
                    print("\n=====================================================================")
                    print("FINAL RESULT AFTER PROCESSING ALL PAGES:")
                    print(json.dumps(accumulated_cv_data, indent=2, ensure_ascii=False))
                    print(f"TOTAL TOKEN USAGE: {total_extraction_cost}")
                    print("=====================================================================\n")

                    # Add extraction_cost to the original response structure
                    response_with_extraction_cost = {"response": accumulated_cv_data}
                    response_with_extraction_cost["extraction_cost"] = total_extraction_cost

                    return response_with_extraction_cost
                else:
                    raise HTTPException(status_code=500, detail="Failed to extract any data from the CV")

            except asyncio.CancelledError:
                # The TempFileTracker will automatically clean up files in its __exit__ method
                # We just need to re-raise to propagate the cancellation
                raise

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error processing CV: {str(e)}")

    def clean_markdown_response(self, markdown_text: str) -> str:
        """
        Clean markdown response by removing code block markers and extra formatting.

        Args:
            markdown_text (str): Raw markdown response from the model

        Returns:
            str: Cleaned markdown content
        """
        if not markdown_text:
            return ""

        # Remove markdown code block markers
        cleaned = markdown_text.strip()

        # Remove opening ```markdown or ``` markers
        if cleaned.startswith('```markdown'):
            cleaned = cleaned[11:].strip()
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:].strip()

        # Remove closing ``` markers
        if cleaned.endswith('```'):
            cleaned = cleaned[:-3].strip()

        return cleaned

    def extract_first_content_block(self, markdown_text: str) -> str:
        """
        Extract the first content block from markdown text.
        Moves everything until it finds an empty line, which indicates a new block.

        Args:
            markdown_text (str): Markdown content to extract from

        Returns:
            str: First content block (everything until first empty line)
        """
        if not markdown_text.strip():
            return ""

        lines = markdown_text.split('\n')
        if not lines:
            return ""

        # Collect all lines until we hit an empty line
        content_lines = []
        for line in lines:
            if not line.strip() and content_lines:  # Empty line after we've collected some content
                break
            content_lines.append(line)

        return '\n'.join(content_lines)

    def remove_first_content_block(self, markdown_text: str, block_to_remove: str) -> str:
        """
        Remove the first content block from markdown text.

        Args:
            markdown_text (str): Original markdown content
            block_to_remove (str): The block that was extracted and should be removed

        Returns:
            str: Remaining content after removing the first block
        """
        if not block_to_remove.strip():
            return markdown_text

        # Simple approach: remove the block from the beginning
        if markdown_text.startswith(block_to_remove):
            remaining = markdown_text[len(block_to_remove):].lstrip('\n')
            return remaining
        else:
            # Fallback: try to find and remove the block
            try:
                idx = markdown_text.index(block_to_remove)
                if idx == 0 or markdown_text[:idx].strip() == "":
                    remaining = markdown_text[idx + len(block_to_remove):].lstrip('\n')
                    return remaining
            except ValueError:
                pass

        # If we can't find exact match, return original (safety fallback)
        return markdown_text

    def encode_image(self, image_path: str) -> str:
        """
        Encode an image file to base64 format for API transmission.

        Args:
            image_path (str): Path to the image file

        Returns:
            str: Base64-encoded image data with appropriate data URL prefix
        """
        extension = image_path.split('.')[-1].lower()
        mime_types = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg'
        }
        mime_type = mime_types.get(extension, 'application/octet-stream')
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
        return f"data:{mime_type};base64,{base64_image}"
