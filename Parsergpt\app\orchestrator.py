"""Main orchestrator for ParserGPT POC workflow."""

import asyncio
import json
import logging
import os
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from .models import Job, Page, Extraction, Adapter
from .schemas import FieldSpec, AdapterData
from .sampling import sample_pages, PageSample
from .learning import learn_adapter
from .extractor import get_extractor
from .fallback import get_fallback_extractor, merge_extraction_results
from .fetching import fetch_with_fallback
from .database import get_session
from .config import get_settings

logger = logging.getLogger(__name__)


class AdapterCache:
    """In-memory cache for adapters with file system persistence."""

    def __init__(self):
        self.settings = get_settings()
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.adapters_dir = self.settings.adapters_dir
        os.makedirs(self.adapters_dir, exist_ok=True)

    def _get_adapter_file_path(self, domain: str) -> str:
        """Get file path for adapter JSON."""
        safe_domain = domain.replace('/', '_').replace(':', '_')
        return os.path.join(self.adapters_dir, f"{safe_domain}.json")

    async def get_adapter(self, domain: str) -> Optional[Dict[str, Any]]:
        """Get adapter from cache or file system."""
        # Check memory cache first
        if domain in self.cache:
            logger.debug(f"Adapter for {domain} found in memory cache")
            return self.cache[domain]

        # Check file system
        file_path = self._get_adapter_file_path(domain)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    adapter_data = json.load(f)

                # Cache in memory
                self.cache[domain] = adapter_data
                logger.debug(f"Adapter for {domain} loaded from file and cached")
                return adapter_data

            except Exception as e:
                logger.error(f"Failed to load adapter file for {domain}: {e}")

        return None

    async def save_adapter(self, domain: str, adapter_data: Dict[str, Any]) -> None:
        """Save adapter to cache and file system."""
        # Save to memory cache
        self.cache[domain] = adapter_data

        # Save to file system
        file_path = self._get_adapter_file_path(domain)
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(adapter_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Adapter for {domain} saved to {file_path}")

        except Exception as e:
            logger.error(f"Failed to save adapter file for {domain}: {e}")

    def invalidate_adapter(self, domain: str) -> None:
        """Remove adapter from cache and optionally from file system."""
        # Remove from memory cache
        if domain in self.cache:
            del self.cache[domain]
            logger.info(f"Adapter for {domain} removed from cache")

        # Optionally remove file (for now, we keep files for persistence)
        # file_path = self._get_adapter_file_path(domain)
        # if os.path.exists(file_path):
        #     os.remove(file_path)

    def should_regenerate_adapter(
        self,
        domain: str,
        adapter_data: Dict[str, Any],
        success_rate: Optional[float] = None
    ) -> bool:
        """Determine if adapter should be regenerated."""
        # Check adapter age (regenerate if older than 30 days)
        metadata = adapter_data.get("metadata", {})

        # Check success rate (regenerate if below threshold)
        if success_rate is not None and success_rate < 0.6:
            logger.info(f"Adapter for {domain} has low success rate ({success_rate:.2%}), should regenerate")
            return True

        # Check version (regenerate if very old version)
        version = adapter_data.get("version", 1)
        if version < 1:  # Placeholder for version-based logic
            return True

        return False


class JobOrchestrator:
    """Main orchestrator that coordinates the entire ParserGPT workflow."""

    def __init__(self):
        self.settings = get_settings()
        self.extractor = get_extractor()
        self.fallback_extractor = get_fallback_extractor()
        self.adapter_cache = AdapterCache()
    
    async def run_job(self, job_id: int) -> None:
        """
        Run a complete scraping job.
        
        Args:
            job_id: ID of the job to run
        """
        logger.info(f"Starting job {job_id}")
        
        async with get_session() as session:
            try:
                # Load job
                job = await session.get(Job, job_id)
                if not job:
                    raise ValueError(f"Job {job_id} not found")
                
                # Update job status
                await self._update_job_status(session, job, "running")
                
                # Get domain from start URL
                domain = urlparse(job.start_url).netloc
                
                # Step 1: Check if we have an adapter for this domain
                adapter_data = await self._get_or_create_adapter(session, domain, job)
                
                # Step 2: Discover and fetch pages
                pages = await self._discover_and_fetch_pages(session, job)
                
                # Step 3: Extract data from all pages
                await self._extract_from_pages(session, job, pages, adapter_data)
                
                # Step 4: Generate CSV output
                await self._generate_csv_output(session, job)
                
                # Mark job as completed
                await self._update_job_status(session, job, "completed")
                logger.info(f"Job {job_id} completed successfully")
                
            except Exception as e:
                logger.error(f"Job {job_id} failed: {e}")
                await self._update_job_status(session, job, "failed", str(e))
                raise
    
    async def _get_or_create_adapter(
        self,
        session: AsyncSession,
        domain: str,
        job: Job
    ) -> Dict[str, Any]:
        """Get existing adapter or create a new one with caching."""
        logger.info(f"Getting adapter for domain: {domain}")

        # First check cache
        cached_adapter = await self.adapter_cache.get_adapter(domain)
        if cached_adapter:
            # Check if we should regenerate based on performance
            if not self.adapter_cache.should_regenerate_adapter(domain, cached_adapter):
                logger.info(f"Using cached adapter for {domain}")
                return cached_adapter
            else:
                logger.info(f"Cached adapter for {domain} needs regeneration")

        # Check database
        result = await session.execute(
            select(Adapter).where(Adapter.domain == domain)
        )
        existing_adapter = result.scalar_one_or_none()

        if existing_adapter and not cached_adapter:
            # Load from database to cache
            adapter_data = existing_adapter.adapter_dict
            await self.adapter_cache.save_adapter(domain, adapter_data)

            # Check if we should regenerate
            if not self.adapter_cache.should_regenerate_adapter(domain, adapter_data, existing_adapter.success_rate):
                logger.info(f"Using database adapter for {domain} (version {existing_adapter.version})")
                return adapter_data

        # Need to learn a new adapter
        logger.info(f"Learning new adapter for {domain}")

        # Sample pages for learning
        field_specs = [FieldSpec(**spec) for spec in job.field_spec]
        samples = await sample_pages(
            job.start_url,
            job.allowed_domains_list or [domain],
            max_samples=6,
            max_depth=2
        )

        if not samples:
            raise ValueError(f"No samples collected for domain {domain}")

        # Learn adapter
        adapter_data_obj = await learn_adapter(domain, field_specs, samples)
        adapter_data = adapter_data_obj.dict()

        # Save to cache
        await self.adapter_cache.save_adapter(domain, adapter_data)

        # Save or update in database
        if existing_adapter:
            # Update existing adapter
            existing_adapter.version += 1
            existing_adapter.adapter_data = adapter_data
            existing_adapter.sample_urls = [s.url for s in samples[:3]]
            existing_adapter.success_rate = None  # Will be updated after extraction
        else:
            # Create new adapter
            new_adapter = Adapter(
                domain=domain,
                version=1,
                adapter_data=adapter_data,
                sample_urls=[s.url for s in samples[:3]]
            )
            session.add(new_adapter)

        await session.commit()

        logger.info(f"New adapter learned and saved for {domain}")
        return adapter_data
    
    async def _discover_and_fetch_pages(
        self,
        session: AsyncSession,
        job: Job
    ) -> List[Page]:
        """Discover and fetch pages for the job."""
        logger.info(f"Discovering pages for job {job.id}")
        
        # For POC, we'll use simple sampling approach
        # In production, this would be more sophisticated crawling
        domain = urlparse(job.start_url).netloc
        samples = await sample_pages(
            job.start_url,
            job.allowed_domains_list or [domain],
            max_samples=min(job.max_pages, 20),
            max_depth=job.max_depth
        )
        
        pages = []
        for sample in samples:
            # Create page record
            page = Page(
                job_id=job.id,
                url=sample.url,
                domain=sample.domain,
                html_content=sample.html,
                status_code=200,
                content_type="text/html",
                content_length=len(sample.html),
                fetch_method="httpx"
            )
            session.add(page)
            pages.append(page)
        
        await session.commit()
        
        # Update job progress
        job.pages_discovered = len(pages)
        job.pages_processed = len(pages)
        await session.commit()
        
        logger.info(f"Fetched {len(pages)} pages for job {job.id}")
        return pages
    
    async def _extract_from_pages(
        self,
        session: AsyncSession,
        job: Job,
        pages: List[Page],
        adapter_data: Dict[str, Any]
    ) -> None:
        """Extract data from all pages."""
        logger.info(f"Extracting data from {len(pages)} pages")
        
        field_specs = [FieldSpec(**spec) for spec in job.field_spec]
        successful_extractions = 0
        
        for page in pages:
            try:
                # Deterministic extraction
                deterministic_data = await self.extractor.extract_with_adapter(
                    page.html_content,
                    adapter_data,
                    [spec.dict() for spec in field_specs]
                )
                
                # Check for missing fields
                missing_fields = []
                for spec in field_specs:
                    value = deterministic_data.get(spec.name)
                    if not self._has_meaningful_value(value, spec.dtype):
                        missing_fields.append(spec.name)
                
                # LLM fallback for missing fields
                final_data = deterministic_data
                extraction_method = "deterministic"
                
                if missing_fields:
                    logger.info(f"Using LLM fallback for {len(missing_fields)} missing fields on {page.url}")
                    llm_data = await self.fallback_extractor.extract_missing_fields(
                        page.url,
                        page.html_content,
                        field_specs,
                        missing_fields,
                        deterministic_data
                    )
                    
                    final_data = merge_extraction_results(deterministic_data, llm_data)
                    extraction_method = "hybrid"
                
                # Calculate confidence score
                confidence = self._calculate_confidence(final_data, field_specs)
                
                # Save extraction
                extraction = Extraction(
                    job_id=job.id,
                    page_id=page.id,
                    extracted_data=final_data,
                    extraction_method=extraction_method,
                    confidence_score=confidence
                )
                session.add(extraction)
                
                if confidence > 0.5:  # Consider successful if > 50% confidence
                    successful_extractions += 1
                
            except Exception as e:
                logger.error(f"Extraction failed for {page.url}: {e}")
                # Save failed extraction
                extraction = Extraction(
                    job_id=job.id,
                    page_id=page.id,
                    extracted_data={},
                    extraction_method="failed",
                    confidence_score=0.0
                )
                session.add(extraction)
        
        await session.commit()
        
        # Update job progress
        job.pages_extracted = successful_extractions
        await session.commit()
        
        logger.info(f"Extracted data from {successful_extractions}/{len(pages)} pages successfully")
    
    async def _generate_csv_output(self, session: AsyncSession, job: Job) -> None:
        """Generate CSV output file for the job."""
        import csv
        import os
        
        logger.info(f"Generating CSV output for job {job.id}")
        
        # Get all extractions for this job
        result = await session.execute(
            select(Extraction).where(Extraction.job_id == job.id)
        )
        extractions = result.scalars().all()
        
        if not extractions:
            logger.warning(f"No extractions found for job {job.id}")
            return
        
        # Prepare CSV data
        field_names = [spec["name"] for spec in job.field_spec]
        csv_filename = f"job_{job.id}.csv"
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=field_names)
            writer.writeheader()
            
            for extraction in extractions:
                row = {}
                for field_name in field_names:
                    value = extraction.extracted_data.get(field_name, "")
                    # Convert lists to JSON strings for CSV
                    if isinstance(value, list):
                        import json
                        row[field_name] = json.dumps(value)
                    else:
                        row[field_name] = str(value) if value is not None else ""
                writer.writerow(row)
        
        logger.info(f"CSV output saved to {csv_filename}")
    
    def _has_meaningful_value(self, value: Any, dtype: str) -> bool:
        """Check if a value is meaningful for the given data type."""
        if value is None:
            return False
        
        if dtype.endswith("[]"):
            return isinstance(value, list) and len(value) > 0
        
        if isinstance(value, str):
            return bool(value.strip())
        
        if isinstance(value, (int, float)):
            return value != 0
        
        return bool(value)
    
    def _calculate_confidence(self, data: Dict[str, Any], field_specs: List[FieldSpec]) -> float:
        """Calculate confidence score for extracted data."""
        if not field_specs:
            return 0.0
        
        successful_fields = 0
        for spec in field_specs:
            value = data.get(spec.name)
            if self._has_meaningful_value(value, spec.dtype):
                successful_fields += 1
        
        return successful_fields / len(field_specs)
    
    async def _update_job_status(
        self,
        session: AsyncSession,
        job: Job,
        status: str,
        error_message: Optional[str] = None
    ) -> None:
        """Update job status."""
        job.status = status
        if error_message:
            job.error_message = error_message
        
        if status == "completed":
            from datetime import datetime
            job.completed_at = datetime.utcnow()
        
        await session.commit()
        logger.info(f"Job {job.id} status updated to: {status}")


# Global orchestrator instance
_orchestrator = None


def get_orchestrator() -> JobOrchestrator:
    """Get the global orchestrator instance."""
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = JobOrchestrator()
    return _orchestrator


async def run_job(job_id: int) -> None:
    """Convenience function to run a job."""
    orchestrator = get_orchestrator()
    await orchestrator.run_job(job_id)
