"""
<PERSON><PERSON><PERSON> to create sample financial data for testing the analysis system.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os

# Set random seed for reproducibility
np.random.seed(42)
random.seed(42)

def create_sample_financial_data():
    """Create comprehensive sample financial data."""
    
    # Sample suppliers
    suppliers = [
        "ABC Corporation", "XYZ Industries", "Global Tech Solutions", 
        "Local Manufacturing Co", "Premium Services Ltd", "Budget Supplies Inc",
        "Elite Consulting Group", "Standard Materials Corp", "Quality Products LLC",
        "Innovative Systems Inc", "Reliable Partners Ltd", "Express Logistics Co"
    ]
    
    # Generate date range (last 12 months)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    # Generate sample data
    records = []
    voucher_counter = 1000
    
    for _ in range(500):  # Generate 500 transactions
        # Random date within the range
        random_days = random.randint(0, 365)
        transaction_date = start_date + timedelta(days=random_days)
        
        # Random supplier
        supplier = random.choice(suppliers)
        
        # Generate voucher ID
        voucher_id = f"INV-{voucher_counter:04d}"
        voucher_counter += 1
        
        # Generate gross amount (with some variation by supplier)
        base_amount = random.uniform(1000, 50000)
        
        # Some suppliers have consistently higher/lower amounts
        if "Premium" in supplier or "Elite" in supplier:
            base_amount *= random.uniform(1.5, 3.0)
        elif "Budget" in supplier or "Standard" in supplier:
            base_amount *= random.uniform(0.3, 0.8)
        
        gross_amount = round(base_amount, 2)
        
        # Generate cost (with some missing values to test assumptions)
        if random.random() < 0.8:  # 80% of records have cost data
            # Cost typically 60-85% of gross amount
            cost_percentage = random.uniform(0.60, 0.85)
            
            # Some suppliers have better/worse margins
            if "Premium" in supplier or "Elite" in supplier:
                cost_percentage *= random.uniform(0.7, 0.9)  # Better margins
            elif "Budget" in supplier:
                cost_percentage *= random.uniform(1.1, 1.3)  # Worse margins (some negative)
            
            cost_amount = round(gross_amount * cost_percentage, 2)
        else:
            cost_amount = None  # Missing cost data
        
        records.append({
            'supplier': supplier,
            'voucher': voucher_id,
            'gross_amount': gross_amount,
            'cost': cost_amount,
            'date': transaction_date
        })
    
    return pd.DataFrame(records)


def create_problematic_data():
    """Create data with specific issues for testing edge cases."""
    
    records = [
        # Negative margin transactions
        {
            'supplier': 'Problem Supplier A',
            'voucher': 'PROB-001',
            'gross_amount': 1000.00,
            'cost': 1200.00,  # Cost > Revenue = Negative margin
            'date': datetime(2024, 1, 15)
        },
        {
            'supplier': 'Problem Supplier B',
            'voucher': 'PROB-002',
            'gross_amount': 5000.00,
            'cost': 5100.00,  # Slightly negative margin
            'date': datetime(2024, 2, 10)
        },
        
        # Very low margin transactions
        {
            'supplier': 'Low Margin Corp',
            'voucher': 'LOW-001',
            'gross_amount': 10000.00,
            'cost': 9950.00,  # 0.5% margin
            'date': datetime(2024, 3, 5)
        },
        {
            'supplier': 'Low Margin Corp',
            'voucher': 'LOW-002',
            'gross_amount': 8000.00,
            'cost': 7920.00,  # 1% margin
            'date': datetime(2024, 3, 20)
        },
        
        # Missing cost data
        {
            'supplier': 'No Cost Data Inc',
            'voucher': 'NOCOST-001',
            'gross_amount': 15000.00,
            'cost': None,
            'date': datetime(2024, 4, 1)
        },
        
        # Duplicate voucher IDs (should be flagged)
        {
            'supplier': 'Duplicate Corp',
            'voucher': 'DUP-001',
            'gross_amount': 3000.00,
            'cost': 2100.00,
            'date': datetime(2024, 5, 1)
        },
        {
            'supplier': 'Duplicate Corp',
            'voucher': 'DUP-001',  # Same voucher ID
            'gross_amount': 3500.00,
            'cost': 2450.00,
            'date': datetime(2024, 5, 2)
        }
    ]
    
    return pd.DataFrame(records)


def create_multilingual_data():
    """Create sample data with Spanish column names for testing flexibility."""
    
    suppliers_es = [
        "Corporación ABC", "Industrias XYZ", "Soluciones Tecnológicas Globales",
        "Manufactura Local SA", "Servicios Premium Ltda", "Suministros Económicos Inc"
    ]
    
    records = []
    for i in range(50):
        records.append({
            'proveedor': random.choice(suppliers_es),
            'comprobante': f"FACT-{i+1:03d}",
            'importe': round(random.uniform(500, 25000), 2),
            'costo': round(random.uniform(300, 20000), 2),
            'fecha': datetime(2024, random.randint(1, 12), random.randint(1, 28))
        })
    
    return pd.DataFrame(records)


def save_sample_files():
    """Save all sample data files."""
    
    # Create directory if it doesn't exist
    os.makedirs('sample_data', exist_ok=True)
    
    # Main sample data
    print("Creating main sample data...")
    main_data = create_sample_financial_data()
    main_data.to_excel('sample_data/sample_financial_data.xlsx', index=False)
    main_data.to_csv('sample_data/sample_financial_data.csv', index=False)
    
    # Problematic data for testing edge cases
    print("Creating problematic data...")
    problem_data = create_problematic_data()
    problem_data.to_excel('sample_data/problematic_financial_data.xlsx', index=False)
    
    # Multilingual data
    print("Creating multilingual data...")
    multilingual_data = create_multilingual_data()
    multilingual_data.to_excel('sample_data/datos_financieros_es.xlsx', index=False)
    
    # Combined dataset
    print("Creating combined dataset...")
    combined_data = pd.concat([main_data, problem_data], ignore_index=True)
    combined_data.to_excel('sample_data/comprehensive_financial_data.xlsx', index=False)
    
    # Create data summary
    summary = {
        'main_data': {
            'records': len(main_data),
            'suppliers': main_data['supplier'].nunique(),
            'date_range': f"{main_data['date'].min()} to {main_data['date'].max()}",
            'total_amount': main_data['gross_amount'].sum(),
            'missing_cost_records': main_data['cost'].isnull().sum()
        },
        'problem_data': {
            'records': len(problem_data),
            'negative_margins': len(problem_data[problem_data['cost'] > problem_data['gross_amount']]),
            'duplicate_vouchers': problem_data['voucher'].duplicated().sum()
        },
        'multilingual_data': {
            'records': len(multilingual_data),
            'column_language': 'Spanish'
        }
    }
    
    print("\n=== Sample Data Summary ===")
    for dataset, info in summary.items():
        print(f"\n{dataset.upper()}:")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    print(f"\nFiles created in 'sample_data' directory:")
    print("- sample_financial_data.xlsx (main dataset)")
    print("- sample_financial_data.csv (CSV version)")
    print("- problematic_financial_data.xlsx (edge cases)")
    print("- datos_financieros_es.xlsx (Spanish columns)")
    print("- comprehensive_financial_data.xlsx (combined)")


if __name__ == "__main__":
    save_sample_files()
