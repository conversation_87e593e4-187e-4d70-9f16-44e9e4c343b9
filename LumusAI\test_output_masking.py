#!/usr/bin/env python3
"""
Test script to verify that output masking is working correctly.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_output_masking():
    """Test that sensitive data in outputs is masked in Langfuse traces."""
    
    print("🔒 Testing Output Masking")
    print("=" * 60)
    
    # Simple CV data that should produce structured output with sensitive info
    cv_data = """
    <PERSON>
    Senior Software Developer
    
    Contact Information:
    Email: <EMAIL>
    Phone: (57) 3157536767
    LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
    Location: Cali, Valle Del Cauca, Colombia
    
    Professional Experience:
    - Senior Developer at TechCorp SA (2022-2024)
      * Led development team
      * Built scalable applications
      * Contact: <EMAIL>
    
    - Software Engineer at StartupXYZ LTDA (2020-2022)
      * Full-stack development
      * DevOps implementation
    
    Education:
    - Master's in Computer Science
      Universidad Nacional de Colombia (2018-2020)
      Student Email: <EMAIL>
    
    Skills: Python, JavaScript, React, Django
    """
    
    print("📞 Testing /process endpoint for output masking...")
    print("🔍 This test focuses on checking that:")
    print("   - API response contains real data (for user functionality)")
    print("   - Langfuse traces contain masked data (for privacy)")
    
    try:
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "cv",
                "data": cv_data
            },
            timeout=60
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Analyze the API response
            if isinstance(result, dict):
                print("\n📊 API Response Analysis:")
                
                # Check personal information in response
                if 'personal_information' in result or 'personal_info' in result:
                    personal = result.get('personal_information') or result.get('personal_info', {})
                    
                    print("📋 Personal Information in API Response:")
                    print(f"   - Name: {personal.get('full_name', 'N/A')}")
                    print(f"   - Email: {personal.get('email', 'N/A')}")
                    print(f"   - Phone: {personal.get('phone_number', 'N/A')}")
                    print(f"   - LinkedIn: {personal.get('linkedin_profile', 'N/A')}")
                    print(f"   - City: {personal.get('city', 'N/A')}")
                    
                    # This is expected - API should return real data
                    print("\n✅ API Response Behavior: CORRECT")
                    print("   - Contains actual personal data for user functionality")
                    print("   - This is the expected behavior for API responses")
                
                # Check work experience
                if 'work_experience' in result:
                    work_exp = result['work_experience']
                    if work_exp and len(work_exp) > 0:
                        print(f"\n📋 Work Experience: {len(work_exp)} entries found")
                        for i, job in enumerate(work_exp[:2]):  # Show first 2
                            print(f"   Job {i+1}: {job.get('job_title', 'N/A')} at {job.get('company_name', 'N/A')}")
                
                # Check education
                if 'education' in result:
                    education = result['education']
                    if education and len(education) > 0:
                        print(f"\n📋 Education: {len(education)} entries found")
                        for i, edu in enumerate(education[:2]):  # Show first 2
                            print(f"   Education {i+1}: {edu.get('degree', 'N/A')} at {edu.get('institution_name', 'N/A')}")
                
                # Token usage
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"\n💰 Token Usage: {tokens.get('total_tokens', 'N/A')} tokens, ${tokens.get('cost', 0):.4f}")
            
            print("\n" + "=" * 60)
            print("🔍 CRITICAL: Check Langfuse Dashboard NOW!")
            print("=" * 60)
            print("URL: http://157.230.167.30:3000")
            print("\n📋 What to look for in Langfuse traces:")
            print("✅ INPUT traces should show:")
            print("   - Names: [REDACTED_NAME] instead of 'Santiago García Martínez'")
            print("   - Emails: [REDACTED_EMAIL] instead of '<EMAIL>'")
            print("   - Phones: [REDACTED_PHONE] instead of '(57) 3157536767'")
            
            print("\n✅ OUTPUT traces should show:")
            print("   - personal_info.full_name: '[REDACTED_NAME]'")
            print("   - personal_info.email: '[REDACTED_EMAIL]'")
            print("   - personal_info.phone_number: '[REDACTED_PHONE]'")
            print("   - personal_info.linkedin_profile: '[REDACTED_LINKEDIN]'")
            print("   - education[].institution_name: '[REDACTED_COMPANY]' or '[REDACTED_NAME]'")
            print("   - work_experience[].company_name: '[REDACTED_COMPANY]'")
            
            print("\n❌ If you still see in Langfuse OUTPUT traces:")
            print("   - 'Santiago García Martínez' (should be [REDACTED_NAME])")
            print("   - '<EMAIL>' (should be [REDACTED_EMAIL])")
            print("   - '(57) 3157536767' (should be [REDACTED_PHONE])")
            print("   - 'TechCorp SA' (should be [REDACTED_COMPANY])")
            print("   - Then OUTPUT masking is still not working")
            
            print("\n🎯 Expected Result:")
            print("   - API responses: Real data (for functionality)")
            print("   - Langfuse traces: Masked data (for privacy)")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🔒 Output Masking Verification Test")
    print("=" * 50)
    
    # Check service health
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Run the test
    success = test_output_masking()
    
    print("\n" + "=" * 60)
    print("🎯 Output Masking Test Summary")
    print("=" * 60)
    
    if success:
        print("✅ Test completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Check application logs for:")
        print("   - '🔍 MaskingCallbackHandler.on_llm_end called'")
        print("   - '🔍 MaskingCallbackHandler.on_chain_end called'")
        print("   - '🔒 Applied masking to chain outputs'")
        print("   - '🔒 Applied masking to LLM response'")
        
        print("\n2. Check Langfuse dashboard:")
        print("   - Look for the most recent trace")
        print("   - Verify OUTPUT data is masked")
        print("   - INPUT masking should already be working")
        
        print("\n3. If output masking is working:")
        print("   - You should see [REDACTED_*] in Langfuse traces")
        print("   - But real data in API responses")
        print("   - This provides privacy protection with full functionality")
        
    else:
        print("❌ Test failed - check application configuration")
    
    print("\n🔍 Remember:")
    print("- The goal is to mask sensitive data in Langfuse traces")
    print("- While keeping real data in API responses for users")
    print("- This protects privacy without breaking functionality")

if __name__ == "__main__":
    main()
