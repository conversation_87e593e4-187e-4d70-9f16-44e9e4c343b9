#!/usr/bin/env python3
"""
Excel Analyzer - Advanced analysis capabilities for Excel data using the enhanced Pandas agent.
"""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ExcelAnalyzer:
    """Advanced Excel data analyzer with visualization capabilities."""
    
    def __init__(self):
        self.data = None
        self.analysis_history = []
    
    def load_data(self, data_source: Union[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Load data from file path or DataFrame.
        
        Args:
            data_source: File path (str) or pandas DataFrame
            
        Returns:
            Dictionary with loading results
        """
        try:
            if isinstance(data_source, str):
                # Load from file
                if data_source.endswith('.csv'):
                    self.data = pd.read_csv(data_source)
                elif data_source.endswith(('.xlsx', '.xls')):
                    self.data = pd.read_excel(data_source)
                else:
                    return {"success": False, "error": "Unsupported file format"}
            elif isinstance(data_source, pd.DataFrame):
                self.data = data_source.copy()
            else:
                return {"success": False, "error": "Invalid data source type"}
            
            return {
                "success": True,
                "shape": self.data.shape,
                "columns": list(self.data.columns),
                "data_types": self.data.dtypes.to_dict(),
                "memory_usage": self.data.memory_usage(deep=True).sum()
            }
            
        except Exception as e:
            return {"success": False, "error": f"Failed to load data: {str(e)}"}
    
    def analyze_data(self, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Perform comprehensive data analysis.
        
        Args:
            analysis_type: Type of analysis ('comprehensive', 'financial', 'statistical', 'trend')
            
        Returns:
            Dictionary with analysis results
        """
        if self.data is None:
            return {"success": False, "error": "No data loaded"}
        
        try:
            analysis_result = {
                "success": True,
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
                "data_overview": self._get_data_overview(),
                "statistical_summary": self._get_statistical_summary(),
                "data_quality": self._assess_data_quality(),
                "visualizations": []
            }
            
            # Add specific analysis based on type
            if analysis_type == "financial":
                analysis_result.update(self._financial_analysis())
            elif analysis_type == "trend":
                analysis_result.update(self._trend_analysis())
            elif analysis_type == "statistical":
                analysis_result.update(self._statistical_analysis())
            else:  # comprehensive
                analysis_result.update(self._comprehensive_analysis())
            
            # Generate visualizations
            analysis_result["visualizations"] = self._create_visualizations(analysis_type)
            
            # Store in history
            self.analysis_history.append(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            return {"success": False, "error": f"Analysis failed: {str(e)}"}
    
    def _get_data_overview(self) -> Dict[str, Any]:
        """Get basic data overview."""
        return {
            "shape": self.data.shape,
            "columns": list(self.data.columns),
            "data_types": self.data.dtypes.astype(str).to_dict(),
            "memory_usage_mb": round(self.data.memory_usage(deep=True).sum() / 1024 / 1024, 2),
            "sample_data": self.data.head(3).to_dict('records')
        }
    
    def _get_statistical_summary(self) -> Dict[str, Any]:
        """Get statistical summary of numeric columns."""
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) == 0:
            return {"message": "No numeric columns found"}
        
        summary = self.data[numeric_cols].describe()
        
        return {
            "numeric_columns": list(numeric_cols),
            "summary_statistics": summary.to_dict(),
            "correlation_matrix": self.data[numeric_cols].corr().to_dict() if len(numeric_cols) > 1 else {}
        }
    
    def _assess_data_quality(self) -> Dict[str, Any]:
        """Assess data quality issues."""
        quality_report = {
            "missing_values": self.data.isnull().sum().to_dict(),
            "missing_percentage": (self.data.isnull().sum() / len(self.data) * 100).to_dict(),
            "duplicate_rows": self.data.duplicated().sum(),
            "unique_values_per_column": self.data.nunique().to_dict()
        }
        
        # Identify potential issues
        issues = []
        for col, missing_pct in quality_report["missing_percentage"].items():
            if missing_pct > 50:
                issues.append(f"Column '{col}' has {missing_pct:.1f}% missing values")
        
        if quality_report["duplicate_rows"] > 0:
            issues.append(f"Found {quality_report['duplicate_rows']} duplicate rows")
        
        quality_report["issues"] = issues
        quality_report["overall_quality"] = "Good" if len(issues) == 0 else "Needs attention"
        
        return quality_report
    
    def _financial_analysis(self) -> Dict[str, Any]:
        """Perform financial-specific analysis."""
        financial_insights = {"analysis_type": "financial"}
        
        # Look for common financial columns
        price_cols = [col for col in self.data.columns if any(keyword in col.lower() 
                     for keyword in ['price', 'open', 'high', 'low', 'close', 'value', 'amount'])]
        
        volume_cols = [col for col in self.data.columns if any(keyword in col.lower() 
                      for keyword in ['volume', 'quantity', 'shares'])]
        
        date_cols = [col for col in self.data.columns if any(keyword in col.lower() 
                    for keyword in ['date', 'time', 'timestamp'])]
        
        financial_insights.update({
            "identified_price_columns": price_cols,
            "identified_volume_columns": volume_cols,
            "identified_date_columns": date_cols
        })
        
        # Calculate financial metrics if price columns exist
        if price_cols:
            for col in price_cols:
                if pd.api.types.is_numeric_dtype(self.data[col]):
                    financial_insights[f"{col}_metrics"] = {
                        "volatility": self.data[col].std(),
                        "range": self.data[col].max() - self.data[col].min(),
                        "trend": "increasing" if self.data[col].iloc[-1] > self.data[col].iloc[0] else "decreasing"
                    }
        
        return financial_insights
    
    def _trend_analysis(self) -> Dict[str, Any]:
        """Perform trend analysis."""
        trend_insights = {"analysis_type": "trend"}
        
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            # Simple trend calculation
            values = self.data[col].dropna()
            if len(values) > 1:
                trend_slope = np.polyfit(range(len(values)), values, 1)[0]
                trend_insights[f"{col}_trend"] = {
                    "slope": trend_slope,
                    "direction": "increasing" if trend_slope > 0 else "decreasing",
                    "strength": abs(trend_slope)
                }
        
        return trend_insights
    
    def _statistical_analysis(self) -> Dict[str, Any]:
        """Perform advanced statistical analysis."""
        stats_insights = {"analysis_type": "statistical"}
        
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            values = self.data[col].dropna()
            if len(values) > 0:
                stats_insights[f"{col}_statistics"] = {
                    "skewness": values.skew(),
                    "kurtosis": values.kurtosis(),
                    "outliers_count": len(values[(values < values.quantile(0.25) - 1.5 * (values.quantile(0.75) - values.quantile(0.25))) | 
                                                (values > values.quantile(0.75) + 1.5 * (values.quantile(0.75) - values.quantile(0.25)))])
                }
        
        return stats_insights
    
    def _comprehensive_analysis(self) -> Dict[str, Any]:
        """Perform comprehensive analysis combining all types."""
        comprehensive = {"analysis_type": "comprehensive"}
        
        # Combine insights from all analysis types
        comprehensive.update(self._financial_analysis())
        comprehensive.update(self._trend_analysis())
        comprehensive.update(self._statistical_analysis())
        
        return comprehensive
    
    def _create_visualizations(self, analysis_type: str) -> List[Dict[str, Any]]:
        """Create visualizations based on analysis type."""
        visualizations = []
        
        try:
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 0:
                # Distribution plots
                for col in numeric_cols[:3]:  # Limit to first 3 numeric columns
                    fig = px.histogram(self.data, x=col, title=f"Distribution of {col}")
                    visualizations.append({
                        "type": "histogram",
                        "title": f"Distribution of {col}",
                        "column": col,
                        "html": fig.to_html(include_plotlyjs='cdn')
                    })
                
                # Correlation heatmap if multiple numeric columns
                if len(numeric_cols) > 1:
                    corr_matrix = self.data[numeric_cols].corr()
                    fig = px.imshow(corr_matrix, text_auto=True, aspect="auto", 
                                   title="Correlation Matrix")
                    visualizations.append({
                        "type": "correlation_heatmap",
                        "title": "Correlation Matrix",
                        "html": fig.to_html(include_plotlyjs='cdn')
                    })
                
                # Time series plot if date column exists
                date_cols = [col for col in self.data.columns if 'date' in col.lower() or 'time' in col.lower()]
                if date_cols and len(numeric_cols) > 0:
                    date_col = date_cols[0]
                    value_col = numeric_cols[0]
                    
                    try:
                        # Try to convert to datetime
                        self.data[date_col] = pd.to_datetime(self.data[date_col])
                        fig = px.line(self.data, x=date_col, y=value_col, 
                                     title=f"{value_col} over Time")
                        visualizations.append({
                            "type": "time_series",
                            "title": f"{value_col} over Time",
                            "html": fig.to_html(include_plotlyjs='cdn')
                        })
                    except:
                        pass  # Skip if date conversion fails
        
        except Exception as e:
            visualizations.append({
                "type": "error",
                "message": f"Visualization error: {str(e)}"
            })
        
        return visualizations
    
    def generate_insights(self) -> List[str]:
        """Generate human-readable insights from the analysis."""
        if not self.analysis_history:
            return ["No analysis performed yet. Run analyze_data() first."]
        
        latest_analysis = self.analysis_history[-1]
        insights = []
        
        # Data overview insights
        shape = latest_analysis["data_overview"]["shape"]
        insights.append(f"Dataset contains {shape[0]:,} rows and {shape[1]} columns")
        
        # Data quality insights
        quality = latest_analysis["data_quality"]
        if quality["overall_quality"] == "Good":
            insights.append("✅ Data quality is good with no major issues detected")
        else:
            insights.append(f"⚠️ Data quality needs attention: {len(quality['issues'])} issues found")
        
        # Statistical insights
        if "summary_statistics" in latest_analysis["statistical_summary"]:
            numeric_cols = latest_analysis["statistical_summary"]["numeric_columns"]
            insights.append(f"Found {len(numeric_cols)} numeric columns for analysis")
        
        # Financial insights (if applicable)
        if "identified_price_columns" in latest_analysis:
            price_cols = latest_analysis["identified_price_columns"]
            if price_cols:
                insights.append(f"Identified {len(price_cols)} potential price/value columns: {', '.join(price_cols)}")
        
        return insights

def main():
    """Example usage of the ExcelAnalyzer."""
    print("📊 Excel Analyzer - Example Usage")
    print("=" * 40)
    
    # This would be used with actual data
    print("To use this analyzer:")
    print("1. analyzer = ExcelAnalyzer()")
    print("2. result = analyzer.load_data('your_file.xlsx')")
    print("3. analysis = analyzer.analyze_data('comprehensive')")
    print("4. insights = analyzer.generate_insights()")

if __name__ == "__main__":
    main()
