# utils/formatting_utils.py

from models.arroyo_models import ArroyoCandidate
from models.llm import models_pool
from utils.extract_clasess import read_py_file_extract_class_to_string



def format_cv_from_images(images):
    prompt = f"""
    Format the following CV following the JSON format:
    {read_py_file_extract_class_to_string()}
    The output should be a JSON.
    """
    messages =[ 
            {"role": "system", "content": prompt},
            {"role": "user", "content": [
                {"type": "image_url", "image_url": {
                    "url": f"data:image/png;base64,{image}"}}    for image in images
                ]
            }
    ]
    
    structured_model = models_pool["gpt-4o"].with_structured_output(ArroyoCandidate,method="json_mode")
    result = structured_model.invoke(messages)
    return result


def format_cv_from_readed_pdf(cv_text,links,model):
    if not links:
        links = "No links provided"
    if len(links) == 0:
        links = "No links provided"
    links = "\n".join(links)
    prompt = f"""
    Format the following raw CV text and links following the JSON format:
    {read_py_file_extract_class_to_string()}
    The output should be a JSON.
    """
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": "CV TEXT:"+ cv_text},
        {"role": "user", "content": "LINKS:"+ links}
    ]
    try:
        structured_model = models_pool[model].with_structured_output(ArroyoCandidate,method="json_mode")
        return structured_model.invoke(messages)
    except Exception as e:
        print(e)
        try:
            structured_model = models_pool["llama32-90b"].with_structured_output(ArroyoCandidate,method="json_mode")
            return structured_model.invoke(messages)
        except:
            structured_model = models_pool["gpt-4o"].with_structured_output(ArroyoCandidate,method="json_mode")
            return structured_model.invoke(messages)
        
def format_cv_from_readed_docx(cv_text, model="gpt-4o"):
    prompt = f"""
    Format the following raw CV text following the JSON format:
    {read_py_file_extract_class_to_string()}
    The output should be a JSON.
    """
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": "CV TEXT:"+ cv_text},
    ]
    try:
        structured_model = models_pool[model].with_structured_output(ArroyoCandidate,method="json_mode")
        return structured_model.invoke(messages)
    except Exception as e:
        print(e)
        try:
            structured_model = models_pool["llama32-90b"].with_structured_output(ArroyoCandidate,method="json_mode")
            return structured_model.invoke(messages)
        except:
            structured_model = models_pool["gpt-4o"].with_structured_output(ArroyoCandidate,method="json_mode")
            return structured_model.invoke(messages)   


def extract_text_and_links(pdf_path):
    from PyPDF2 import PdfReader

    pdf_reader = PdfReader(open(pdf_path, "rb"))
    text = ""
    links = []

    for page_num in range(len(pdf_reader.pages)):
        page = pdf_reader.pages[page_num]
        text += page.extract_text()

        if '/Annots' in page:
            annots = page['/Annots']
            for annot in annots:
                uri_obj = annot.get_object()
                if '/A' in uri_obj and '/URI' in uri_obj['/A']:
                    uri = uri_obj['/A']['/URI']
                    links.append(uri)
    
    return text, links


# from ironpdf import PdfDocument
import os
import tempfile
import base64
# def convert_pdf_to_images(pdf_path):
#     try:
#         # Load PDF
#         pdf_document = PdfDocument.FromFile(pdf_path)
#         # Define output path for images
#         output_path = os.path.join(tempfile.gettempdir(), "page_*.png")
#         # Rasterize PDF to images
#         pdf_document.RasterizeToImageFiles(output_path, DPI=600)
        
#         # Collect all generated images and encode them in Base64
#         image_files = sorted([f for f in os.listdir(tempfile.gettempdir()) if f.startswith("page_") and f.endswith(".png")])
#         encoded_images = []
#         for img_file in image_files:
#             img_path = os.path.join(tempfile.gettempdir(), img_file)
#             print(img_path)
#             with open(img_path, "rb") as image_file:
#                 encoded_images.append(base64.b64encode(image_file.read()).decode("utf-8"))
#         print("len images", len(encoded_images))
#         return encoded_images, None
#     except Exception as e:
#         return None, str(e)
from pdf2image import convert_from_path
import base64
import os
import tempfile

import fitz  # PyMuPDF
import os
import tempfile

def convert_pdf_to_images(pdf_path):
    """
    Converts each page of the PDF into images and returns their file paths.

    Args:
        pdf_path (str): The path to the PDF file.

    Returns:
        Tuple[List[str], Union[None, str]]: A tuple containing the list of image file paths and an error message if any.
    """
    try:
        # Open the PDF file
        pdf_document = fitz.open(pdf_path)
        # Create a temporary directory to store images
        temp_dir = tempfile.mkdtemp()
        image_paths = []

        # Iterate over each page
        for page_number in range(len(pdf_document)):
            # Select the page
            page = pdf_document.load_page(page_number)
            # Define the transformation matrix for scaling (2x zoom)
            zoom = 2
            matrix = fitz.Matrix(zoom, zoom)
            # Render the page to a pixmap
            pix = page.get_pixmap(matrix=matrix)
            # Define the output image path
            image_path = os.path.join(temp_dir, f'page_{page_number + 1}.png')
            # Save the image
            pix.save(image_path)
            image_paths.append(image_path)

        # Close the PDF document
        pdf_document.close()
        return image_paths, None
    except Exception as e:
        return None, str(e)

    
from PIL import Image
from typing import Tuple, Union, List
import io

import base64

def encode_images_to_base64(image_paths):
    """
    Encodes images from file paths into Base64 strings.

    Args:
        image_paths (List[str]): The list of image file paths.

    Returns:
        List[str]: A list of Base64-encoded strings representing the images.
    """
    encoded_images = []
    for image_path in image_paths:
        with open(image_path, "rb") as image_file:
            img_str = base64.b64encode(image_file.read()).decode("utf-8")
            encoded_images.append(img_str)
        #delete the image file
        os.remove(image_path)
    return encoded_images
    
def format_model_candidate_embedding(candidate: ArroyoCandidate) -> str:
    """
    Formats the ArroyoCandidate model into a single string for embedding generation.
    Excludes personal information and links.

    Args:
        candidate (ArroyoCandidate): The candidate data model.

    Returns:
        str: A formatted string suitable for embedding.
    """
    sections = []

    # Summary
    if candidate.summary:
        sections.append(f"Summary:\n{candidate.summary}\n")

    # Experience
    if candidate.experience:
        experience_section = "Experience:\n"
        for exp in candidate.experience:
            experience_section += (
                f"Company: {exp.company or 'N/A'}\n"
                f"Position: {exp.position or 'N/A'}\n"
                f"Start Date: {exp.start_date or 'N/A'}\n"
                f"End Date: {exp.end_date or 'Present'}\n"
                f"Description: {exp.description or 'N/A'}\n\n"
            )
        sections.append(experience_section)

    # Undergraduate Education
    if candidate.undergraduate_education:
        edu = candidate.undergraduate_education
        sections.append(
            f"Undergraduate Education:\n"
            f"Degree: {edu.degree or 'N/A'}\n"
            f"Institution: {edu.institution or 'N/A'}\n"
            f"Year: {edu.year or 'N/A'}\n"
        )

    # Graduate Education
    if candidate.graduate_education:
        grad_section = "Graduate Education:\n"
        for grad in candidate.graduate_education:
            grad_section += (
                f"Degree: {grad.degree or 'N/A'}\n"
                f"Institution: {grad.institution or 'N/A'}\n"
                f"Year: {grad.year or 'N/A'}\n"
                f"Skills: {', '.join(grad.skills) if grad.skills else 'N/A'}\n\n"
            )
        sections.append(grad_section)

    # Extra Courses
    if candidate.extra_courses:
        courses_section = "Extra Courses:\n"
        for course in candidate.extra_courses:
            courses_section += (
                f"Certification: {course.certification_name or 'N/A'}\n"
                f"Skills: {', '.join(course.skills) if course.skills else 'N/A'}\n\n"
            )
        sections.append(courses_section)

    # Technical Skills
    if candidate.technical_skills:
        sections.append(f"Technical Skills:\n{', '.join(candidate.technical_skills)}\n")

    # Soft Skills
    if candidate.soft_skills:
        sections.append(f"Soft Skills:\n{', '.join(candidate.soft_skills)}\n")

    # Combine all sections into a single string
    full_text = "\n".join(sections)
    return full_text.strip()


def format_model_candidate_text(candidate: ArroyoCandidate) -> str:
    """
    Formats the ArroyoCandidate model into a single string for full-text representation.
    Includes all information, including personal details.

    Args:
        candidate (ArroyoCandidate): The candidate data model.

    Returns:
        str: A comprehensive formatted string.
    """
    sections = []

    # Personal Information
    if candidate.personal_info:
        pi = candidate.personal_info
        sections.append(
            f"Personal Information:\n"
            f"Full Name: {pi.full_name or 'N/A'}\n"
            f"Email: {pi.email or 'N/A'}\n"
            f"Telephone: {pi.telephone or 'N/A'}\n"
            f"Address: {pi.address or 'N/A'}\n"
            f"Country: {pi.country or 'N/A'}\n"
            f"City: {pi.city or 'N/A'}\n"
            f"GitHub: {pi.github or 'N/A'}\n"
            f"LinkedIn: {pi.linkedin or 'N/A'}\n"
            f"Website: {pi.website or 'N/A'}\n"
        )

    # Summary
    if candidate.summary:
        sections.append(f"Summary:\n{candidate.summary}\n")

    # Experience
    if candidate.experience:
        experience_section = "Experience:\n"
        for exp in candidate.experience:
            experience_section += (
                f"Company: {exp.company or 'N/A'}\n"
                f"Position: {exp.position or 'N/A'}\n"
                f"Start Date: {exp.start_date or 'N/A'}\n"
                f"End Date: {exp.end_date or 'Present'}\n"
                f"Description: {exp.description or 'N/A'}\n\n"
            )
        sections.append(experience_section)

    # Undergraduate Education
    if candidate.undergraduate_education:
        edu = candidate.undergraduate_education
        sections.append(
            f"Undergraduate Education:\n"
            f"Degree: {edu.degree or 'N/A'}\n"
            f"Institution: {edu.institution or 'N/A'}\n"
            f"Year: {edu.year or 'N/A'}\n"
        )

    # Graduate Education
    if candidate.graduate_education:
        grad_section = "Graduate Education:\n"
        for grad in candidate.graduate_education:
            grad_section += (
                f"Degree: {grad.degree or 'N/A'}\n"
                f"Institution: {grad.institution or 'N/A'}\n"
                f"Year: {grad.year or 'N/A'}\n"
                f"Skills: {', '.join(grad.skills) if grad.skills else 'N/A'}\n\n"
            )
        sections.append(grad_section)

    # Extra Courses
    if candidate.extra_courses:
        courses_section = "Extra Courses:\n"
        for course in candidate.extra_courses:
            courses_section += (
                f"Certification: {course.certification_name or 'N/A'}\n"
                f"Skills: {', '.join(course.skills) if course.skills else 'N/A'}\n\n"
            )
        sections.append(courses_section)

    # Technical Skills
    if candidate.technical_skills:
        sections.append(f"Technical Skills:\n{', '.join(candidate.technical_skills)}\n")

    # Soft Skills
    if candidate.soft_skills:
        sections.append(f"Soft Skills:\n{', '.join(candidate.soft_skills)}\n")

    # Combine all sections into a single string
    full_text = "\n".join(sections)
    return full_text.strip()
