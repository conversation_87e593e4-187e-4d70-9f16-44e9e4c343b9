version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financial-analysis-backend
    ports:
      - "8000:8000"
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
      - DEBUG=False
      - MAX_FILE_SIZE_MB=50
      - BATCH_PROCESSING_THRESHOLD=500
      - BATCH_SIZE=200
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - backend_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - financial-analysis-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: financial-analysis-frontend
    ports:
      - "8501:8501"
    environment:
      - BACKEND_URL=http://backend:8000
    volumes:
      - ./frontend:/app
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - financial-analysis-network

volumes:
  backend_data:
    driver: local

networks:
  financial-analysis-network:
    driver: bridge
