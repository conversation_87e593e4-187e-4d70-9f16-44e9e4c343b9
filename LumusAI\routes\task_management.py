# task_management.py

import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request

router = APIRouter()

# In-memory task storage for process-test tasks
# In a production environment, you'd use Redis or a database
task_storage: Dict[str, Dict[str, Any]] = {}

def store_task(task_id: str, action: str, status: str = "processing", **kwargs):
    """Store task information in memory."""
    task_storage[task_id] = {
        "task_id": task_id,
        "action": action,
        "status": status,
        "created_at": time.time(),
        "timestamp": datetime.now(timezone.utc).isoformat(),
        **kwargs
    }

def update_task_status(task_id: str, status: str, **kwargs):
    """Update task status and additional information."""
    if task_id in task_storage:
        task_storage[task_id].update({
            "status": status,
            "updated_at": time.time(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            **kwargs
        })

def get_task(task_id: str) -> Optional[Dict[str, Any]]:
    """Get task information by ID."""
    return task_storage.get(task_id)

@router.get("/task/status/{task_id}", summary="Get task status")
async def get_task_status(task_id: str, request: Request):
    """
    Get the current status of a processing task.
    
    Args:
        task_id: The unique task identifier
        
    Returns:
        dict: Task status information
        
    Example response:
    ```json
    {
        "task_id": "cv-1703123456-abc12345",
        "action": "cv",
        "status": "processing",
        "created_at": 1703123456.789,
        "timestamp": "2024-01-15T10:30:00.123456+00:00",
        "file_name": "resume.pdf"
    }
    ```
    
    Status values:
    - "processing": Task is currently being processed
    - "completed": Task completed successfully
    - "failed": Task failed with an error
    """
    logger = request.app.state.logger
    logger.info(f"Status check requested for task: {task_id}")
    
    task = get_task(task_id)
    if not task:
        logger.warning(f"Task not found: {task_id}")
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
    
    return task

@router.get("/task/result/{task_id}", summary="Get task result")
async def get_task_result(task_id: str, request: Request):
    """
    Get the result of a completed processing task.
    
    Args:
        task_id: The unique task identifier
        
    Returns:
        dict: Task result or status information
        
    Example responses:
    
    **Completed:**
    ```json
    {
        "task_id": "cv-1703123456-abc12345",
        "status": "completed",
        "result": {
            "personal_info": {...},
            "skills": [...],
            "work_experience": [...]
        },
        "processing_time": 245.6
    }
    ```
    
    **Still Processing:**
    ```json
    {
        "task_id": "cv-1703123456-abc12345",
        "status": "processing",
        "message": "Task is still being processed"
    }
    ```
    
    **Failed:**
    ```json
    {
        "task_id": "cv-1703123456-abc12345",
        "status": "failed",
        "error": "Error processing document: Invalid file format",
        "processing_time": 12.3
    }
    ```
    """
    logger = request.app.state.logger
    logger.info(f"Result requested for task: {task_id}")
    
    task = get_task(task_id)
    if not task:
        logger.warning(f"Task not found: {task_id}")
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
    
    status = task.get("status")
    
    if status == "processing":
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "Task is still being processed",
            "created_at": task.get("created_at"),
            "timestamp": task.get("timestamp")
        }
    elif status == "completed":
        return {
            "task_id": task_id,
            "status": "completed",
            "result": task.get("result", {}),
            "processing_time": task.get("processing_time"),
            "completed_at": task.get("completed_at"),
            "timestamp": task.get("timestamp")
        }
    elif status == "failed":
        return {
            "task_id": task_id,
            "status": "failed",
            "error": task.get("error", "Unknown error"),
            "processing_time": task.get("processing_time"),
            "failed_at": task.get("failed_at"),
            "timestamp": task.get("timestamp")
        }
    else:
        return {
            "task_id": task_id,
            "status": status,
            "message": f"Task status: {status}",
            "timestamp": task.get("timestamp")
        }

@router.get("/tasks", summary="List all tasks")
async def list_all_tasks(request: Request):
    """
    List all tasks for monitoring purposes.
    
    Returns:
        dict: Summary of all tasks
        
    Example response:
    ```json
    {
        "total_tasks": 5,
        "processing": 2,
        "completed": 2,
        "failed": 1,
        "tasks": [
            {
                "task_id": "cv-1703123456-abc12345",
                "action": "cv",
                "status": "completed",
                "created_at": 1703123456.789
            }
        ]
    }
    ```
    """
    logger = request.app.state.logger
    logger.info("Task list requested")
    
    # Count tasks by status
    status_counts = {"processing": 0, "completed": 0, "failed": 0, "other": 0}
    
    for task in task_storage.values():
        status = task.get("status", "other")
        if status in status_counts:
            status_counts[status] += 1
        else:
            status_counts["other"] += 1
    
    # Get task summaries (without full results to keep response manageable)
    task_summaries = []
    for task in task_storage.values():
        summary = {
            "task_id": task["task_id"],
            "action": task["action"],
            "status": task["status"],
            "created_at": task.get("created_at"),
            "timestamp": task.get("timestamp")
        }
        
        # Add processing time if available
        if "processing_time" in task:
            summary["processing_time"] = task["processing_time"]
            
        # Add file name if available
        if "file_name" in task:
            summary["file_name"] = task["file_name"]
            
        task_summaries.append(summary)
    
    # Sort by creation time (newest first)
    task_summaries.sort(key=lambda x: x.get("created_at", 0), reverse=True)
    
    return {
        "total_tasks": len(task_storage),
        "processing": status_counts["processing"],
        "completed": status_counts["completed"],
        "failed": status_counts["failed"],
        "other": status_counts["other"],
        "tasks": task_summaries
    }
