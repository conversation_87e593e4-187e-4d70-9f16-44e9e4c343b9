def format_job_description(description: str) -> str:
    return description.strip() if description else ""


def format_main_responsibilities(responsibilities: str) -> str:
    return responsibilities.strip() if responsibilities else ""


def format_skills(skills_list: list) -> str:
    if not skills_list:
        return ""
    skills_str_list = []
    for skill in skills_list:
        name = skill.get("skillName", "Unknown Skill")
        category = skill.get("skillCategory", "Unknown Category")
        level = skill.get("skillLeveName", "Unknown Level")
        score = skill.get("skillScore", "Unknown Score")
        skills_str_list.append(
            f"{name} (Category: {category}, Level: {level}, Score: {score})"
        )
    return ", ".join(skills_str_list)


def format_position_allocations(allocations_list: list) -> str:
    if not allocations_list:
        return ""
    allocation_str_list = []
    for allocation in allocations_list:
        country_name = allocation.get("Name", "Unknown Country")
        iso_code = allocation.get("isoCode", "Unknown ISO Code")
        allocation_str_list.append(f"{country_name} (ISO: {iso_code})")
    return ", ".join(allocation_str_list)


def format_position(position_info: dict) -> str:
    position_name = position_info.get("positionName", "Unknown Position")
    job_description = format_job_description(position_info.get("jobDescription", ""))
    main_responsibilities = format_main_responsibilities(
        position_info.get("mainResponsabilities", "")
    )
    seniority = position_info.get("seniority", {}).get("name", "Unknown Seniority")
    role_name = position_info.get("roleName", "Unknown Role")
    project_name = position_info.get("projectName", "Unknown Project")
    client_name = position_info.get("clientName", "Unknown Client")
    position_type = position_info.get("positionTypeName", "Unknown Position Type")
    position_start_date = position_info.get("positionStartDate", "Unknown Start Date")
    created_by = position_info.get("createdBy", "Unknown Creator")
    reason_status = position_info.get("reasonStatus", {}).get(
        "reason", "Unknown Reason"
    )

    skills = format_skills(position_info.get("openPositionSkills", []))
    allocations = format_position_allocations(
        position_info.get("positionAllocations", [])
    )

    formatted_text = f"""
Position Name: {position_name}
Role: {role_name}
Project: {project_name}
Client: {client_name}
Seniority: {seniority}
Position Type: {position_type}
Start Date: {position_start_date}
Created By: {created_by}
Reason Status: {reason_status}

Job Description:
{job_description}

Main Responsibilities:
{main_responsibilities}

Required Skills:
{skills}

Position Allocations:
{allocations}
"""
    return formatted_text.strip()


def prepare_position_for_embedding(proj_id: str, position_info: dict) -> str:
    """
    Extracts relevant professional information from position_info
    and returns a formatted text for embedding.
    """
    return format_position(position_info)
