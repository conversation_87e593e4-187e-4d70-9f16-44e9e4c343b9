# 🛠️ Installation Guide

This guide will walk you through setting up the Financial Analysis Project on your local machine or server.

## 📋 Prerequisites

### System Requirements
- **Python 3.8 or higher**
- **pip** (Python package installer)
- **Git** (for cloning the repository)
- **4GB RAM minimum** (8GB recommended)
- **2GB free disk space**

### API Keys Required
- **GROQ API Key** (free tier available)
  - Sign up at: https://console.groq.com/
  - Used for LangChain AI agents

## 🚀 Quick Installation (Recommended)

### Option 1: Automated Setup Script
```bash
# Navigate to project directory
cd financial_analysis_project

# Run the automated setup script
python setup_and_run.py
```

The script will:
- ✅ Check Python version compatibility
- ✅ Install all dependencies automatically
- ✅ Set up environment variables
- ✅ Create sample data files
- ✅ Run tests (optional)
- ✅ Start both backend and frontend

## 🔧 Manual Installation

### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd financial_analysis_project
```

### Step 2: Set Up Python Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv financial_analysis_env

# Activate virtual environment
# On Windows:
financial_analysis_env\Scripts\activate
# On macOS/Linux:
source financial_analysis_env/bin/activate
```

### Step 3: Install Backend Dependencies
```bash
cd backend
pip install -r requirements.txt
```

**Backend Dependencies Include:**
- FastAPI (web framework)
- LangChain (AI agents)
- Pandas (data processing)
- OpenPyXL (Excel handling)
- Uvicorn (ASGI server)

### Step 4: Install Frontend Dependencies
```bash
cd ../frontend
pip install -r requirements.txt
```

**Frontend Dependencies Include:**
- Streamlit (web interface)
- Plotly (data visualization)
- Requests (API communication)

### Step 5: Configure Environment Variables
```bash
cd ../backend

# Copy environment template
cp .env.example .env

# Edit the .env file
nano .env  # or use your preferred editor
```

**Required Environment Variables:**
```env
# GROQ API Key (required)
GROQ_API_KEY=your_actual_groq_api_key_here

# Optional: Model configurations
GROQ_MODEL_FAST=llama-3.1-8b-instant
GROQ_MODEL_ANALYSIS=llama3-70b-8192
GROQ_MODEL_REPORT=gemma2-9b-it

# Application settings
DEBUG=True
MAX_FILE_SIZE_MB=50
```

### Step 6: Create Sample Data
```bash
cd ../sample_data
python create_sample_data.py
```

This creates:
- `sample_financial_data.xlsx` - Main dataset (500+ transactions)
- `problematic_financial_data.xlsx` - Edge cases for testing
- `datos_financieros_es.xlsx` - Spanish column names
- `comprehensive_financial_data.xlsx` - Combined dataset

### Step 7: Run Tests (Optional but Recommended)
```bash
cd ../
pip install pytest
pytest tests/ -v
```

## 🚀 Starting the Application

### Option 1: Start Both Services Automatically
```bash
python setup_and_run.py
```

### Option 2: Start Services Manually

#### Terminal 1 - Backend (FastAPI)
```bash
cd backend
uvicorn app.main:app --reload --port 8000
```

#### Terminal 2 - Frontend (Streamlit)
```bash
cd frontend
streamlit run streamlit_app.py --server.port 8501
```

## 🌐 Access Points

Once running, access the application at:

- **Frontend (Streamlit UI)**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🔍 Verification

### 1. Check Backend Status
```bash
curl http://localhost:8000/health
```
Expected response:
```json
{"status": "healthy", "service": "financial-analysis-api"}
```

### 2. Check Frontend
- Open http://localhost:8501
- You should see the Financial Analysis Tool interface

### 3. Test File Upload
- Upload `sample_data/sample_financial_data.xlsx`
- Configure analysis parameters
- Click "Analyze Financial Data"
- Verify results are displayed

## 🐛 Troubleshooting Installation

### Common Issues

#### Python Version Error
```
Error: Python 3.8 or higher is required
```
**Solution:** Update Python or use pyenv to manage versions

#### Missing GROQ API Key
```
Error: GROQ_API_KEY not found in environment
```
**Solution:** 
1. Get API key from https://console.groq.com/
2. Add to `.env` file: `GROQ_API_KEY=your_key_here`

#### Port Already in Use
```
Error: Port 8000 is already in use
```
**Solution:** 
- Kill existing process: `lsof -ti:8000 | xargs kill -9`
- Or use different port: `uvicorn app.main:app --port 8001`

#### Module Import Errors
```
ModuleNotFoundError: No module named 'langchain'
```
**Solution:** 
- Ensure virtual environment is activated
- Reinstall dependencies: `pip install -r requirements.txt`

#### File Permission Errors
```
PermissionError: [Errno 13] Permission denied
```
**Solution:** 
- Check file permissions
- Run with appropriate user privileges
- Ensure write access to project directory

### Getting Help

If you encounter issues:
1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Review [Common Issues](troubleshooting.md#common-issues)
3. Check the [FAQ](faq.md)
4. Create an issue in the project repository

## 📦 Alternative Installation Methods

### Using Docker (Coming Soon)
```bash
docker-compose up -d
```

### Using Conda
```bash
conda env create -f environment.yml
conda activate financial-analysis
```

## 🔄 Updating the Installation

To update to a newer version:
```bash
git pull origin main
pip install -r backend/requirements.txt --upgrade
pip install -r frontend/requirements.txt --upgrade
```

---

**Next Steps:** Once installed, check out the [Quick Start Guide](quick-start.md) to begin using the application.
