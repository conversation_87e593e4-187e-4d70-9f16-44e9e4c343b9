# Troubleshooting Guide

This guide helps you resolve common issues with the Financial Analysis and Stock Analysis systems.

## Quick Diagnostics

### System Health Check
```bash
# Check Python version
python --version  # Should be 3.8+

# Check virtual environment
which python  # Should point to venv/Scripts/python (Windows) or venv/bin/python (Unix)

# Check key dependencies
python -c "import langchain, langgraph, pandas, streamlit, fastapi; print('All dependencies OK')"

# Check API connectivity
python -c "from langchain_groq import ChatGroq; print('Groq connection OK')"
```

### Environment Variables Check
```bash
# Check if .env file exists
ls financial_analysis_project/backend/.env

# Verify GROQ_API_KEY is set
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('API Key:', 'SET' if os.getenv('GROQ_API_KEY') else 'NOT SET')"
```

## Common Issues

### 1. Installation and Setup Issues

#### Issue: "ModuleNotFoundError: No module named 'langchain'"
**Symptoms**: Import errors when running scripts
**Cause**: Dependencies not installed or wrong Python environment
**Solution**:
```bash
# Ensure virtual environment is activated
source venv/bin/activate  # Unix
# or
venv\Scripts\activate     # Windows

# Reinstall dependencies
pip install -r financial_analysis_project/backend/requirements.txt
pip install -r financial_analysis_project/frontend/requirements.txt

# For stock analysis
pip install yfinance langchain-community duckduckgo-search
```

#### Issue: "GROQ_API_KEY not found"
**Symptoms**: API authentication errors
**Cause**: Missing or incorrect API key configuration
**Solution**:
```bash
# Create .env file in backend directory
cd financial_analysis_project/backend
echo "GROQ_API_KEY=your_actual_api_key_here" > .env

# Verify the file was created
cat .env
```

#### Issue: "Permission denied" on script execution
**Symptoms**: Cannot run Python scripts
**Cause**: File permissions or execution policy
**Solution**:
```bash
# Unix/Linux/macOS
chmod +x ejemplo.py

# Windows PowerShell (if execution policy blocks)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. Financial Analysis Issues

#### Issue: "Column not found" error
**Symptoms**: Excel processing fails with column errors
**Cause**: Excel file doesn't have expected column structure
**Solution**:
```python
# Check your Excel file has these column types:
required_columns = [
    'supplier',    # or 'vendor', 'company'
    'voucher',     # or 'transaction_id', 'invoice'
    'amount',      # or 'value', 'gross_amount'
    'date'         # or 'transaction_date'
]

# Use sample data to test:
# financial_analysis_project/sample_data/sample_financial_data.xlsx
```

#### Issue: "Invalid date format" error
**Symptoms**: Date parsing fails during analysis
**Cause**: Unrecognized date formats in Excel
**Solution**:
```python
# Acceptable date formats:
# YYYY-MM-DD (2024-01-15)
# MM/DD/YYYY (01/15/2024)
# DD/MM/YYYY (15/01/2024)
# DD-MM-YYYY (15-01-2024)

# Fix in Excel:
# 1. Select date column
# 2. Format Cells > Date > Choose standard format
# 3. Save file
```

#### Issue: "Analysis timeout" error
**Symptoms**: Processing stops with timeout
**Cause**: Large files or complex analysis taking too long
**Solution**:
```python
# Reduce file size:
# - Remove unnecessary columns
# - Filter to specific date ranges
# - Split large files into smaller chunks

# Or increase timeout in code:
# In pandas_agents.py, modify agent creation:
agent = create_pandas_dataframe_agent(
    llm=self.llm,
    df=df,
    verbose=True,
    allow_dangerous_code=True,
    agent_executor_kwargs={
        "handle_parsing_errors": True,
        "max_execution_time": 300  # Increase from default
    }
)
```

### 3. Stock Analysis Issues

#### Issue: "Ticker not found" error
**Symptoms**: Yahoo Finance returns no data
**Cause**: Invalid ticker symbol or delisted stock
**Solution**:
```python
# Verify ticker symbol:
import yfinance as yf

# Test ticker manually
ticker = "AAPL"  # Replace with your ticker
stock = yf.Ticker(ticker)
info = stock.info

if info:
    print(f"Found: {info.get('longName', 'Unknown')}")
else:
    print("Ticker not found - check symbol")

# Common ticker corrections:
# Google: GOOGL (not GOOG for Class A shares)
# Berkshire: BRK-A or BRK-B (include hyphen)
# Meta: META (not FB after rebrand)
```

#### Issue: "No news found" error
**Symptoms**: DuckDuckGo search returns empty results
**Cause**: Network issues or search API limitations
**Solution**:
```python
# Test news search manually:
from langchain_community.utilities import DuckDuckGoSearchAPIWrapper

wrapper = DuckDuckGoSearchAPIWrapper(
    region="us-en", 
    time="7d",  # Try longer time period
    max_results=10
)

# Test search
results = wrapper.run("AAPL stock news")
print(results)

# If still failing, check internet connection:
import requests
response = requests.get("https://duckduckgo.com")
print(f"DuckDuckGo accessible: {response.status_code == 200}")
```

#### Issue: "Analysis produces poor results"
**Symptoms**: Generic or unhelpful analysis output
**Cause**: Data quality issues or model limitations
**Solution**:
```python
# Check data quality:
import yfinance as yf
df = yf.download("AAPL", period="1y")

print(f"Data shape: {df.shape}")
print(f"Date range: {df.index.min()} to {df.index.max()}")
print(f"Missing values: {df.isnull().sum().sum()}")

# If data looks good, try different model:
# In ejemplo.py, change model in AgenteAnalizarDatos:
self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")  # More powerful model
```

### 4. API and Server Issues

#### Issue: "Connection refused" on API calls
**Symptoms**: Cannot connect to FastAPI server
**Cause**: Server not running or wrong port
**Solution**:
```bash
# Check if server is running
curl http://localhost:8000/health

# If not running, start server:
cd financial_analysis_project/backend
uvicorn app.main:app --reload --port 8000

# Check for port conflicts:
netstat -an | grep 8000  # Unix
netstat -an | findstr 8000  # Windows

# Use different port if needed:
uvicorn app.main:app --reload --port 8001
```

#### Issue: "CORS error" in browser
**Symptoms**: Browser blocks API requests
**Cause**: Cross-origin request restrictions
**Solution**:
```python
# CORS is already configured in main.py, but if issues persist:
# In financial_analysis_project/backend/app/main.py:

from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

#### Issue: "File upload fails"
**Symptoms**: API returns error on file upload
**Cause**: File size limits or format issues
**Solution**:
```python
# Check file size (default limit is usually 16MB):
import os
file_size = os.path.getsize("your_file.xlsx")
print(f"File size: {file_size / (1024*1024):.2f} MB")

# If too large, increase limit in main.py:
from fastapi import FastAPI, File, UploadFile

app = FastAPI()

# Add this configuration:
app.add_middleware(
    # ... other middleware
)

# Or split large files into smaller chunks
```

### 5. Performance Issues

#### Issue: "Slow analysis performance"
**Symptoms**: Analysis takes very long to complete
**Cause**: Large datasets, complex queries, or API rate limits
**Solution**:
```python
# Optimize data size:
# 1. Filter date ranges
df_filtered = df[df.index >= '2024-01-01']

# 2. Sample large datasets
df_sample = df.sample(n=1000) if len(df) > 1000 else df

# 3. Use faster models for initial analysis:
# llama-3.1-8b-instant (fastest)
# llama3-70b-8192 (balanced)
# gemma2-9b-it (creative)

# 4. Enable caching:
import functools

@functools.lru_cache(maxsize=100)
def cached_analysis(ticker, period):
    return yf.download(ticker, period=period)
```

#### Issue: "Memory errors with large files"
**Symptoms**: Out of memory errors during processing
**Cause**: Large Excel files or DataFrames
**Solution**:
```python
# Process in chunks:
def process_large_file(file_path, chunk_size=1000):
    results = []
    
    # Read file in chunks
    for chunk in pd.read_excel(file_path, chunksize=chunk_size):
        # Process each chunk
        chunk_result = process_chunk(chunk)
        results.append(chunk_result)
    
    # Combine results
    return combine_results(results)

# Or use more memory-efficient formats:
# Save as Parquet instead of Excel for large datasets
df.to_parquet('data.parquet')
df = pd.read_parquet('data.parquet')
```

## Debugging Techniques

### Enable Verbose Logging
```python
import logging

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# For LangChain agents:
agent = create_pandas_dataframe_agent(
    llm=self.llm,
    df=df,
    verbose=True,  # Enable verbose output
    # ... other parameters
)
```

### Test Individual Components
```python
# Test ticker extraction:
from ejemplo import AgenteProcesadorConsulta
processor = AgenteProcesadorConsulta()
ticker = processor.extraer_ticker("análisis de Apple")
print(f"Extracted ticker: {ticker}")

# Test data retrieval:
from ejemplo import ObtenerDatosFinancieros
data = ObtenerDatosFinancieros("AAPL")
print(f"Data shape: {data.shape}")

# Test news retrieval:
from ejemplo import ObtenerNoticias
news = ObtenerNoticias("AAPL")
print(f"News length: {len(news)}")
```

### Monitor API Usage
```python
import time
from datetime import datetime

def monitor_api_calls(func):
    """Decorator to monitor API call performance."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"[{datetime.now()}] Starting {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"[{datetime.now()}] {func.__name__} completed in {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"[{datetime.now()}] {func.__name__} failed after {duration:.2f}s: {e}")
            raise
    
    return wrapper

# Apply to functions:
@monitor_api_calls
def monitored_analysis(query):
    return correr_modelo(query)
```

## Getting Help

### Log Collection
When reporting issues, collect these logs:

```bash
# System information
python --version
pip list | grep -E "(langchain|langgraph|streamlit|fastapi)"

# Error logs
python ejemplo.py > output.log 2>&1

# API logs
uvicorn app.main:app --log-level debug > api.log 2>&1
```

### Minimal Reproduction
Create a minimal example that reproduces the issue:

```python
# minimal_test.py
from ejemplo import correr_modelo

try:
    resultado = correr_modelo("test query")
    print("SUCCESS")
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
```

### Community Resources
- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check latest documentation updates
- **Stack Overflow**: Search for similar issues
- **LangChain Community**: LangChain-specific questions

## Prevention Tips

1. **Regular Updates**: Keep dependencies updated
2. **Environment Management**: Use virtual environments
3. **Data Validation**: Validate input data before processing
4. **Error Handling**: Implement comprehensive error handling
5. **Monitoring**: Set up logging and monitoring
6. **Testing**: Test with sample data before production use
7. **Backups**: Keep backups of working configurations

---

**Still having issues?** Check our [FAQ](./faq.md) or create a detailed issue report with logs and reproduction steps.
