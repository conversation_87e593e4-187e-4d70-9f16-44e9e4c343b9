#!/usr/bin/env python3
"""
Example test payloads for LinkedIn Integration API
Shows correct JSON structure with only database-accessible fields
"""

import json
from typing import Dict, Any, List

class LinkedInTestExamples:
    """Collection of test examples for LinkedIn integration."""
    
    @staticmethod
    def basic_search_example() -> Dict[str, Any]:
        """Basic search example with minimal fields."""
        return {
            "keywords": ["software engineer"],
            "location": "San Francisco, CA",
            "limit": 10,
            "transform_profiles": True,
            "include_raw_profiles": False
        }
    
    @staticmethod
    def detailed_search_example() -> Dict[str, Any]:
        """Detailed search example with all available fields."""
        return {
            "keywords": [
                "senior software engineer",
                "python developer",
                "backend engineer"
            ],
            "location": "San Francisco Bay Area",
            "experience_level": "senior",
            "skills": [
                "Python",
                "Django",
                "FastAPI",
                "PostgreSQL",
                "Docker",
                "Kubernetes",
                "AWS",
                "REST APIs",
                "GraphQL"
            ],
            "school": "Stanford University",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "full_name": "<PERSON>",
            "country": "United States",
            "city": "San Francisco",
            "degree": "Computer Science",
            "years_of_experience": "5-10",
            "languages": ["English", "Spanish"],
            "past_title": "Software Developer",
            "limit": 25,
            "transform_profiles": True,
            "include_raw_profiles": False
        }
    
    @staticmethod
    def batch_search_example() -> Dict[str, Any]:
        """Batch search example with multiple search requests."""
        return {
            "search_requests": [
                {
                    "keywords": ["data scientist", "machine learning engineer"],
                    "location": "New York, NY",
                    "skills": ["Python", "TensorFlow", "PyTorch", "SQL", "Pandas"],
                    "experience_level": "mid-level",
                    "limit": 15
                },
                {
                    "keywords": ["frontend developer", "react developer"],
                    "location": "Austin, TX",
                    "skills": ["React", "JavaScript", "TypeScript", "CSS", "HTML"],
                    "experience_level": "junior",
                    "limit": 10
                },
                {
                    "keywords": ["devops engineer", "site reliability engineer"],
                    "location": "Seattle, WA",
                    "skills": ["Docker", "Kubernetes", "AWS", "Terraform", "Jenkins"],
                    "experience_level": "senior",
                    "limit": 20
                }
            ],
            "transform_profiles": True,
            "include_raw_profiles": False
        }
    
    @staticmethod
    def skills_focused_search() -> Dict[str, Any]:
        """Search focused on specific skills."""
        return {
            "keywords": ["full stack developer"],
            "skills": [
                "React",
                "Node.js",
                "Python",
                "PostgreSQL",
                "MongoDB",
                "Docker",
                "AWS"
            ],
            "location": "Remote",
            "experience_level": "mid-level",
            "limit": 30,
            "transform_profiles": True
        }
    
    @staticmethod
    def education_focused_search() -> Dict[str, Any]:
        """Search focused on educational background."""
        return {
            "keywords": ["software engineer", "computer scientist"],
            "school": "MIT",
            "degree": "Computer Science",
            "location": "Boston, MA",
            "limit": 20,
            "transform_profiles": True
        }
    
    @staticmethod
    def location_specific_search() -> Dict[str, Any]:
        """Search for specific geographic locations."""
        return {
            "keywords": ["product manager"],
            "location": "San Francisco, CA",
            "country": "United States",
            "city": "San Francisco",
            "limit": 15,
            "transform_profiles": True
        }
    
    @staticmethod
    def experience_level_search() -> Dict[str, Any]:
        """Search based on experience level."""
        return {
            "keywords": ["senior engineer", "tech lead"],
            "experience_level": "senior",
            "years_of_experience": "8-15",
            "skills": ["Python", "Java", "System Design", "Leadership"],
            "limit": 25,
            "transform_profiles": True
        }
    
    @staticmethod
    def multilingual_search() -> Dict[str, Any]:
        """Search for multilingual candidates."""
        return {
            "keywords": ["international business", "global sales"],
            "languages": ["English", "Spanish", "French"],
            "location": "Miami, FL",
            "limit": 20,
            "transform_profiles": True
        }
    
    @staticmethod
    def get_removed_fields_example() -> Dict[str, Any]:
        """Example showing fields that were REMOVED (DO NOT USE)."""
        return {
            "❌ REMOVED FIELDS - DO NOT USE": {
                "current_title": "This field was removed - not accessible from database",
                "current_company": "This field was removed - not accessible from database", 
                "past_company": "This field was removed - not accessible from database"
            },
            "✅ USE THESE INSTEAD": {
                "keywords": ["Use keywords to search for titles"],
                "skills": ["Use skills to find relevant candidates"],
                "past_title": "This field is still available for past job titles"
            }
        }
    
    @staticmethod
    def expected_response_structure() -> Dict[str, Any]:
        """Example of expected response structure."""
        return {
            "profiles": [
                {
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": "not_provided",
                    "phone": "not_provided",
                    "location": "San Francisco, CA",
                    "current_position": "Senior Software Engineer",
                    "professional_summary": "Experienced software engineer with 8+ years...",
                    "experience": [
                        {
                            "title": "Senior Software Engineer",
                            "company": "Tech Corp",
                            "duration": "2020-Present",
                            "description": "Led development of...",
                            "is_current": True
                        }
                    ],
                    "education": [
                        {
                            "school": "Stanford University",
                            "degree": "BS Computer Science",
                            "field_of_study": "Computer Science",
                            "start_year": 2010,
                            "end_year": 2014
                        }
                    ],
                    "skills": [
                        "Python",
                        "Django",
                        "PostgreSQL",
                        "AWS"
                    ],
                    "linkedin_url": "https://www.linkedin.com/in/johndoe"
                }
            ],
            "search_metadata": {
                "total_results": 1,
                "search_parameters": "...",
                "execution_time_ms": 1500
            },
            "transformation_metadata": {
                "profiles_transformed": 1,
                "transformation_success_rate": 100.0,
                "llm_model_used": "gpt-4o"
            }
        }
    
    @staticmethod
    def print_all_examples():
        """Print all examples in a formatted way."""
        examples = {
            "Basic Search": LinkedInTestExamples.basic_search_example(),
            "Detailed Search": LinkedInTestExamples.detailed_search_example(),
            "Batch Search": LinkedInTestExamples.batch_search_example(),
            "Skills Focused": LinkedInTestExamples.skills_focused_search(),
            "Education Focused": LinkedInTestExamples.education_focused_search(),
            "Location Specific": LinkedInTestExamples.location_specific_search(),
            "Experience Level": LinkedInTestExamples.experience_level_search(),
            "Multilingual": LinkedInTestExamples.multilingual_search(),
            "Removed Fields Info": LinkedInTestExamples.get_removed_fields_example(),
            "Expected Response": LinkedInTestExamples.expected_response_structure()
        }
        
        print("LinkedIn Integration API - Test Examples")
        print("=" * 60)
        print("Updated JSON structure with only database-accessible fields")
        print("=" * 60)
        
        for name, example in examples.items():
            print(f"\n📋 {name.upper()}:")
            print("-" * 40)
            print(json.dumps(example, indent=2))
            print()
    
    @staticmethod
    def save_examples_to_file(filename: str = "linkedin_test_examples.json"):
        """Save all examples to a JSON file."""
        examples = {
            "basic_search": LinkedInTestExamples.basic_search_example(),
            "detailed_search": LinkedInTestExamples.detailed_search_example(),
            "batch_search": LinkedInTestExamples.batch_search_example(),
            "skills_focused": LinkedInTestExamples.skills_focused_search(),
            "education_focused": LinkedInTestExamples.education_focused_search(),
            "location_specific": LinkedInTestExamples.location_specific_search(),
            "experience_level": LinkedInTestExamples.experience_level_search(),
            "multilingual": LinkedInTestExamples.multilingual_search(),
            "removed_fields_info": LinkedInTestExamples.get_removed_fields_example(),
            "expected_response": LinkedInTestExamples.expected_response_structure()
        }
        
        with open(filename, 'w') as f:
            json.dump(examples, f, indent=2)
        
        print(f"💾 Examples saved to: {filename}")


if __name__ == "__main__":
    LinkedInTestExamples.print_all_examples()
    LinkedInTestExamples.save_examples_to_file()
