# Deployment Guide - FlowHR MCP Server

This guide provides detailed instructions for deploying the FlowHR MCP Server to Azure.

## Prerequisites

Before deploying, ensure you have:

- Azure CLI installed and configured
- Azure Functions Core Tools (>= 4.0.7030)
- An active Azure subscription
- PowerShell (for Windows deployment script)

## Deployment Options

### Option 1: Automated Deployment with PowerShell Script

The easiest way to deploy is using the provided PowerShell script:

```powershell
# Deploy to existing resources
.\deploy.ps1 -FunctionAppName "your-mcp-server-name"

# Create new resources and deploy
.\deploy.ps1 -FunctionAppName "your-mcp-server-name" -CreateResources
```

**Parameters:**
- `FunctionAppName` (required): Name for your Azure Function App
- `ResourceGroup` (optional): Resource group name (default: "rg-flowhr-mcp")
- `Location` (optional): Azure region (default: "East US")
- `StorageAccount` (optional): Storage account name (auto-generated if not provided)
- `CreateResources` (switch): Create Azure resources if they don't exist

### Option 2: Manual Deployment

#### Step 1: Create Azure Resources

```bash
# Create resource group
az group create --name rg-flowhr-mcp --location "East US"

# Create storage account
az storage account create --name stflowhr1234 --resource-group rg-flowhr-mcp --location "East US" --sku Standard_LRS

# Create function app
az functionapp create \
  --resource-group rg-flowhr-mcp \
  --consumption-plan-location "East US" \
  --runtime python \
  --runtime-version 3.9 \
  --functions-version 4 \
  --name your-mcp-server-name \
  --storage-account stflowhr1234
```

#### Step 2: Configure App Settings

```bash
az functionapp config appsettings set \
  --name your-mcp-server-name \
  --resource-group rg-flowhr-mcp \
  --settings "FUNCTIONS_EXTENSION_VERSION=~4" "WEBSITE_PYTHON_DEFAULT_VERSION=3.9"
```

#### Step 3: Deploy Function Code

```bash
func azure functionapp publish your-mcp-server-name --python
```

### Option 3: Azure Developer CLI (azd)

If you prefer using Azure Developer CLI:

```bash
# Initialize (first time only)
azd init

# Deploy
azd up
```

## Post-Deployment Configuration

### Get MCP Endpoint

After deployment, retrieve your MCP Server-Sent Events endpoint:

```bash
# Get function app URL
az functionapp show --name your-mcp-server-name --resource-group rg-flowhr-mcp --query "defaultHostName" -o tsv

# Get system key
az functionapp keys list --name your-mcp-server-name --resource-group rg-flowhr-mcp --query "systemKeys.default" -o tsv
```

Your MCP SSE endpoint will be:
```
https://your-function-app.azurewebsites.net/runtime/webhooks/mcp/sse?code=<system_key>
```

### Configure MCP Clients

#### VS Code Copilot
Add the SSE endpoint to your VS Code Copilot MCP configuration.

#### MCP Inspector
Use the endpoint URL to connect and test your MCP tools.

#### Custom MCP Clients
Configure your client to connect to the SSE endpoint for tool discovery and invocation.

## Verification

### Test Tool Availability

1. Connect to your MCP endpoint using MCP Inspector or compatible client
2. Verify that both tools are discovered:
   - `getTime` - Get current time in various formats
   - `calculate` - Perform mathematical calculations

### Test Tool Functionality

**Test getTime:**
```json
{
  "tool": "getTime",
  "arguments": {
    "format": "iso"
  }
}
```

**Test calculate:**
```json
{
  "tool": "calculate",
  "arguments": {
    "operation": "multiply",
    "operand1": 15,
    "operand2": 3.5
  }
}
```

## Monitoring and Troubleshooting

### View Logs

```bash
# Stream live logs
az functionapp log tail --name your-mcp-server-name --resource-group rg-flowhr-mcp

# View logs in Azure portal
az functionapp browse --name your-mcp-server-name --resource-group rg-flowhr-mcp
```

### Common Issues

1. **Function not starting**: Check extension bundle configuration in host.json
2. **Tools not discovered**: Verify MCP configuration and system key
3. **Storage errors**: Ensure storage account is properly configured

### Health Check

Test basic function health:
```bash
curl https://your-function-app.azurewebsites.net/api/health
```

## Security Considerations

- System keys provide access to your MCP server
- Store keys securely and rotate them regularly
- Consider using managed identity for production deployments
- Monitor access logs for unusual activity

## Scaling and Performance

- Consumption plan automatically scales based on demand
- Monitor function execution times and memory usage
- Consider Premium plan for consistent performance requirements

## Cost Management

- Consumption plan charges based on execution time and memory
- Monitor costs through Azure Cost Management
- Set up billing alerts for unexpected usage

## Updates and Maintenance

### Update Function Code
```bash
func azure functionapp publish your-mcp-server-name --python
```

### Update Configuration
```bash
az functionapp config appsettings set --name your-mcp-server-name --resource-group rg-flowhr-mcp --settings "KEY=VALUE"
```

### Monitor Extension Updates
Keep track of MCP extension updates as it's currently in preview.

## Cleanup

To remove all resources:
```bash
az group delete --name rg-flowhr-mcp --yes --no-wait
```

## Support

For issues related to:
- Azure Functions: Check Azure documentation and support
- MCP Extension: Monitor Microsoft Learn for updates (preview feature)
- This implementation: Review logs and error messages for troubleshooting
