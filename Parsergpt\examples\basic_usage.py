#!/usr/bin/env python3
"""
Basic usage example for ParserGPT POC.

This example demonstrates how to:
1. Create a scraping job
2. Monitor job progress
3. Download results as CSV

Run this script after starting the ParserGPT server:
    python -m app.main

Then run this example:
    python examples/basic_usage.py
"""

import asyncio
import httpx
import time
from typing import Dict, Any


async def create_scraping_job(client: httpx.AsyncClient, job_config: Dict[str, Any]) -> int:
    """Create a new scraping job and return the job ID."""
    print("Creating scraping job...")
    
    response = await client.post("http://localhost:8000/jobs", json=job_config)
    response.raise_for_status()
    
    result = response.json()
    job_id = result["job_id"]
    
    print(f"✅ Job created successfully!")
    print(f"   Job ID: {job_id}")
    print(f"   Status: {result['status']}")
    print(f"   Message: {result['message']}")
    
    return job_id


async def monitor_job_progress(client: httpx.AsyncClient, job_id: int) -> Dict[str, Any]:
    """Monitor job progress until completion."""
    print(f"\n📊 Monitoring job {job_id} progress...")
    
    while True:
        response = await client.get(f"http://localhost:8000/jobs/{job_id}")
        response.raise_for_status()
        
        status = response.json()
        
        # Print progress update
        print(f"   Status: {status['status']}")
        if status.get('pages_discovered'):
            print(f"   Pages discovered: {status['pages_discovered']}")
        if status.get('pages_processed'):
            print(f"   Pages processed: {status['pages_processed']}")
        if status.get('pages_extracted'):
            print(f"   Pages extracted: {status['pages_extracted']}")
        
        # Check if job is complete
        if status["status"] in ["completed", "failed"]:
            break
        
        # Wait before checking again
        print("   Waiting 5 seconds...")
        await asyncio.sleep(5)
    
    return status


async def download_results(client: httpx.AsyncClient, job_id: int, filename: str = None) -> str:
    """Download job results as CSV."""
    if filename is None:
        filename = f"results_job_{job_id}.csv"
    
    print(f"\n📥 Downloading results to {filename}...")
    
    response = await client.get(f"http://localhost:8000/jobs/{job_id}/csv")
    response.raise_for_status()
    
    with open(filename, "wb") as f:
        f.write(response.content)
    
    print(f"✅ Results saved to {filename}")
    return filename


async def main():
    """Main example function."""
    print("🚀 ParserGPT Basic Usage Example")
    print("=" * 40)
    
    # Example job configuration for a hypothetical e-commerce site
    job_config = {
        "start_url": "https://books.toscrape.com",  # Public scraping test site
        "allowed_domains": ["books.toscrape.com"],
        "max_depth": 2,
        "max_pages": 20,
        "field_spec": [
            {
                "name": "title",
                "dtype": "string",
                "description": "Book title",
                "required": True
            },
            {
                "name": "price",
                "dtype": "string", 
                "description": "Book price",
                "required": True
            },
            {
                "name": "rating",
                "dtype": "string",
                "description": "Book rating (stars)",
                "required": False
            },
            {
                "name": "availability",
                "dtype": "string",
                "description": "Stock availability",
                "required": False
            }
        ]
    }
    
    print("📋 Job Configuration:")
    print(f"   Start URL: {job_config['start_url']}")
    print(f"   Max pages: {job_config['max_pages']}")
    print(f"   Max depth: {job_config['max_depth']}")
    print(f"   Fields to extract: {len(job_config['field_spec'])}")
    for field in job_config['field_spec']:
        print(f"     - {field['name']} ({field['dtype']}): {field['description']}")
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            # Step 1: Create the job
            job_id = await create_scraping_job(client, job_config)
            
            # Step 2: Monitor progress
            final_status = await monitor_job_progress(client, job_id)
            
            # Step 3: Handle results
            if final_status["status"] == "completed":
                print(f"\n🎉 Job completed successfully!")
                print(f"   Pages extracted: {final_status.get('pages_extracted', 0)}")
                
                # Download results
                csv_file = await download_results(client, job_id)
                
                print(f"\n📊 Summary:")
                print(f"   Job ID: {job_id}")
                print(f"   Status: {final_status['status']}")
                print(f"   Results file: {csv_file}")
                print(f"   Total pages: {final_status.get('pages_discovered', 0)}")
                print(f"   Successful extractions: {final_status.get('pages_extracted', 0)}")
                
            elif final_status["status"] == "failed":
                print(f"\n❌ Job failed!")
                if final_status.get("error_message"):
                    print(f"   Error: {final_status['error_message']}")
                
            else:
                print(f"\n⚠️  Job ended with unexpected status: {final_status['status']}")
        
        except httpx.HTTPStatusError as e:
            print(f"\n❌ HTTP Error: {e.response.status_code}")
            print(f"   Response: {e.response.text}")
        
        except httpx.RequestError as e:
            print(f"\n❌ Request Error: {e}")
            print("   Make sure the ParserGPT server is running on http://localhost:8000")
        
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    print("Starting ParserGPT basic usage example...")
    print("Make sure the server is running: python -m app.main")
    print()
    
    asyncio.run(main())
