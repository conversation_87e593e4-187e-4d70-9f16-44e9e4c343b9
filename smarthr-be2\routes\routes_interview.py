"""
API Router for processing interview transcripts using LLMs.
"""
import logging
from typing import List, Dict, Any
from fastapi import APIRouter, Body, HTTPException, Path, Query
import psycopg2
from controllers.interview_controller import (
    generate_and_persist_qa,
    run_and_persist_interview,
    create_interview,
    fetch_all_interviews_by_position_id,
    update_interview_tec,
    update_interview_hr,
    fetch_interview_by_position_id_candidate_id,
    fetch_questions_by_position_id,
    delete_interview,
    fetch_interviews_by_candidate_id,
    re_evalute_interview,
    fetch_interview_by_interview_id
)
from models.interview import InterviewCreate, ProcessType, Interview, InterviewTec, InterviewHr
from opentelemetry import trace

from models.models import SingleQuestions

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)
router = APIRouter()


# Generate questions for a position
@router.post("/{position_id}/questions", response_model=Dict[str, Any])
def generate_questions(
    position_id: str = Path(..., description="UUID of the position"),
    n_questions: int = Query(10, description="Number of questions to generate", ge=1, le=20),
    include: str = Query("", description="Topics to include in the questions, separated by commas")
):
    try:
        logger.info(f"Generating questions for position_id: {position_id}. Number of questions: {n_questions}")
        qa = generate_and_persist_qa(position_id, n_questions, include)
        if not qa:
            raise HTTPException(status_code=404, detail="Failed to generate questions")
        return qa.model_dump()
    except psycopg2.Error as e:
        logger.error(f"Database error during question generation: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"QA generation failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"QA generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"QA generation failed: {str(e)}")


# Extract answers from an interview transcript
# @router.post("/{interview_id}/answers")
def extract_answers(
    interview_id: str = Path(...),
    process_type: ProcessType = ProcessType.EXTRACT,
):
    try:
        result = run_and_persist_interview(interview_id, process_type)
        return result.model_dump()
    except psycopg2.Error as e:
        logger.error(f"Database error during answer extraction: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Answer extraction failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Answer extraction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Answer extraction failed: {str(e)}")


# Evaluate an interview
@router.post("/{interview_id}/evaluate", response_model=Interview)
def evaluate(interview_id: str = Path(...)):
    try:
        evaluation = re_evalute_interview(interview_id)
        if not evaluation:
            raise HTTPException(status_code=404, detail="Interview not found (re_evalute_interview)")
        return evaluation
    except psycopg2.Error as e:
        logger.error(f"Database error during evaluation: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Evaluation failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Evaluation failed: {str(e)}")


# Create an interview for a position with multiple candidates
@router.post("/{position_id}/create", response_model=List[Interview])
def interview_create(
    position_id: str = Path(..., description="UUID of the position"),
    analysis_data: list[InterviewCreate] = Body(..., description="Analysis data for the interview")
):
    try:
        candidates_id = [data.candidate_id for data in analysis_data]
        logger.info(f"Creating interview for position_id: {position_id} and candidates_id: {candidates_id}")
        if not candidates_id:
            raise HTTPException(status_code=400, detail="At least one candidate id is required")
        if not position_id:
            raise HTTPException(status_code=400, detail="Position ID is required")
        response = create_interview(position_id, analysis_data)
        if not response:
            raise HTTPException(status_code=404, detail="Failed to create interview")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during interview creation: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview creation failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Interview creation failed: {str(e)}")


# Interview list by position ID
@router.get("/{position_id}", response_model=List[Interview])
def interview_list(
    position_id: str = Path(..., description="UUID of the position")
):
    try:
        logger.info(f"Fetching interviews for position_id: {position_id}")
        response = fetch_all_interviews_by_position_id(position_id)
        return response or []
    except psycopg2.Error as e:
        logger.error(f"Database error during interview list fetch: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview list fetch failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview list fetch failed: {e}")
        raise HTTPException(status_code=500, detail=f"Fetching interview list failed: {str(e)}")


# Update interview details for technical interview
@router.put("/tec", response_model=Interview)
def interview_update_tec(interview_data: InterviewTec):
    try:
        logger.info(f"Interview data: {interview_data}")
        # interview_tec = InterviewTec(**interview_data)
        response = update_interview_tec(interview_data)
        if not response:
            raise HTTPException(status_code=404, detail="Interview not found (tec update)")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during interview tec update: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview tec update failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview update failed: {e}")
        raise HTTPException(status_code=500, detail=f"Interview update failed: {str(e)}")


# Update interview details for HR interview 
@router.put("/hr", response_model=Interview)
def interview_update_hr(interview_data: InterviewHr):
    try:
        logger.info(f"Interview data: {interview_data}")
        # interview_hr = InterviewHr(**interview_data)
        response = update_interview_hr(interview_data)
        if not response:
            raise HTTPException(status_code=404, detail="Interview not found (hr update)")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during interview hr update: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview hr update failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview update failed: {e}")
        raise HTTPException(status_code=500, detail=f"Interview update failed: {str(e)}")


# Get a single interview by position ID and candidate ID 
@router.get("/{position_id}/{candidate_id}", response_model=Interview)
def interview_single_candidate(
    position_id: str = Path(..., description="UUID of the position"),
    candidate_id: str = Path(..., description="UUID of the candidate")
):
    try:
        logger.info(f"Fetching single interview for position_id: {position_id} and candidate_id: {candidate_id}")
        response = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not response:
            raise HTTPException(status_code=404, detail="Interview not found")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during single interview fetch: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Single interview fetch failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Single interview fetch failed: {e}")
        raise HTTPException(status_code=500, detail=f"Fetching single interview failed: {str(e)}")


# Get interview questions by position ID
@router.get("/questions/", response_model=SingleQuestions)
def interview_questions(
        position_id: str = Query(..., description="UUID of the position")
):
    try:
        logger.info(f"Fetching questions for position_id: {position_id}")
        if not position_id:
            raise HTTPException(status_code=400, detail="Position ID is required")

        response = fetch_questions_by_position_id(position_id)
        if not response:
            raise HTTPException(status_code=404, detail="Questions not found")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during interview questions fetch: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview questions fetch failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview questions fetch failed: {e}")
        raise HTTPException(status_code=500, detail=f"Fetching interview questions failed: {str(e)}")


# Delete an interview by position ID and candidate ID
@router.delete("/{position_id}/{candidate_id}")
def interview_delete(
    position_id: str = Path(..., description="UUID of the position"),
    candidate_id: str = Path(..., description="UUID of the candidate")
):
    try:
        logger.info(f"Deleting interview for position_id: {position_id} and candidate_id: {candidate_id}")
        response = delete_interview(position_id, candidate_id)
        if not response:
            raise HTTPException(status_code=404, detail="Interview not found")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during interview deletion: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview deletion failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview deletion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Interview deletion failed: {str(e)}")


# Interviews by candidate ID
@router.get("/", response_model=List[Interview])
def interviews_by_candidate(
    candidate_id: str = Query(..., description="UUID of the candidate")
):
    try:
        logger.info(f"Fetching interviews for candidate_id: {candidate_id}")
        response = fetch_interviews_by_candidate_id(candidate_id)
        return response or []
    except psycopg2.Error as e:
        logger.error(f"Database error during interviews by candidate fetch: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interviews by candidate fetch failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interviews by candidate fetch failed: {e}")
        raise HTTPException(status_code=500, detail=f"Fetching interviews by candidate failed: {str(e)}")


# Get interview by interview ID
@router.get("", response_model=Interview)
def interview_get(
    interview_id: str = Query(..., description="UUID of the interview")
):
    try:
        logger.info(f"Fetching interview for interview_id: {interview_id}")
        response = fetch_interview_by_interview_id(interview_id)
        if not response:
            raise HTTPException(status_code=404, detail="Interview not found (interview_get)")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error during interview fetch: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {e}")
    except HTTPException as e:
        logger.error(f"Interview fetch failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Interview fetch failed: {e}")
        raise HTTPException(status_code=500, detail=f"Fetching interview failed: {str(e)}")
