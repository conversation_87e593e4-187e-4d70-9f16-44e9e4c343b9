# Standard library imports
import json
import traceback
from contextlib import contextmanager
from typing import List, Optional
import logging

# Third-party imports
from datetime import datetime
import psycopg2
from psycopg2.extras import Json
from fastapi import HTTPException
from langchain_core.messages import HumanMessage
from typing import Union

# Internal imports
from config.config import MODELS_CONFIG
from core.config import settings
from models.enums import StatusInterview
from models.llm import inference_with_fallback, get_related_class_definitions
from models.interview import (
    InterviewCreate,
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ProcessType,
    QA_model,
    Interview,
    InterviewHr,
    InterviewTec,
    TranscriptQuestions,
    TranscriptQuestion,
    # 4-Agent System Models
    TranscriptQAPair,
    ParsedTranscript,
    ExpectedResponseSet,
    ExpectedResponses,
    IndividualQuestionEvaluation,
    FourAgentEvaluationResult,
    Agent1Request,
    Agent1Response,
    Agent2Request,
    Agent2Response,
    Agent3Request,
    Agent3Response,
    Agent4Request,
    Agent4Response,
    FourAgentEvaluationRequest,
    Seniority,
    QuestionEvaluation,
)
from typing import List, Optional
from models.models import SingleQuestions
from controllers.positions_controller import get_position_by_id
from controllers.candidates_controller import get_candidate_by_id

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)





# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Helper function to build text for LLM prompts
# This function constructs a text prompt based on the provided items.
def get_topics(include: str) -> str:
    """
    This function constructs a text prompt based on the provided items.

    Args:
        include (str): A comma-separated string of items to include in the prompt.

    Returns:
        str: A formatted string based on the provided items.
    """
    base = ""
    desired_order = ['Technical Skills', 'Methodologies', 'Soft Skills', 'Language - Tools']

    # Map lowercase to original case
    lower_to_original = {item.lower(): item for item in desired_order}

    # Normalize input items to lowercase
    input_items = [item.strip().lower() for item in include.split(",") if item.strip()]

    if not input_items:
        return f"{base}{', '.join(desired_order)}."

    # Keep the order from desired_order and match only those present
    ordered = [lower_to_original[item.lower()] for item in desired_order if item.lower() in input_items]

    return f"{base}{', '.join(ordered)}."


def _attempt_fallback_extraction(request: InterviewProcessingRequest) -> Optional[ExtractedAnswers]:
    """
    Attempt alternative extraction methods when primary extraction fails.

    Args:
        request: The original interview processing request

    Returns:
        ExtractedAnswers if successful, None if all fallback methods fail
    """
    logger.info("FALLBACK EXTRACTION: Starting alternative extraction approaches...")

    # Fallback 1: More aggressive pattern matching
    fallback_prompt_1 = (
        "EMERGENCY EXTRACTION MODE: The primary extraction failed. You must be more aggressive in finding responses.\n\n"
        "INSTRUCTIONS:\n"
        "- Look for ANY text that could be a candidate response, even if it's not clearly marked\n"
        "- Include responses that are:\n"
        "  * After question numbers (1., 2., etc.)\n"
        "  * Following question marks (?)\n"
        "  * Any text that seems like an answer to a technical question\n"
        "  * Responses in conversational format (Q: ... A: ...)\n"
        "  * Any admission of knowledge or lack thereof\n"
        "- If you find ANYTHING that looks like a response, extract it\n"
        "- Only use 'Invalid transcript' if you absolutely cannot find any response pattern\n"
        "- Be liberal in what you consider a response - err on the side of inclusion\n\n"
        f"Questions to find responses for:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + f"\n\nTranscript to search:\n{request.transcript}"
    )

    try:
        user_msg = HumanMessage(content=fallback_prompt_1)
        schema_text = get_related_class_definitions(ExtractedAnswers)

        result = inference_with_fallback(
            task_prompt="Extract responses using aggressive pattern matching.",
            model_schema=ExtractedAnswers,
            user_messages=[user_msg],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if result and hasattr(result, 'answers'):
            valid_answers = [ans for ans in result.answers if ans != "Invalid transcript"]
            if len(valid_answers) > 0:
                logger.info(f"FALLBACK EXTRACTION: Method 1 succeeded - found {len(valid_answers)} valid responses")
                return result

    except Exception as e:
        logger.error(f"FALLBACK EXTRACTION: Method 1 failed with error: {str(e)}")

    # Fallback 2: Question-by-question extraction
    logger.info("FALLBACK EXTRACTION: Trying question-by-question approach...")
    try:
        extracted_answers = []

        for question in request.questions:
            # Simple string-based extraction for individual questions
            response_text = _extract_single_response(question, request.transcript)
            extracted_answers.append(response_text if response_text else "Invalid transcript")

        if extracted_answers:
            valid_count = len([ans for ans in extracted_answers if ans != "Invalid transcript"])
            if valid_count > 0:
                logger.info(f"FALLBACK EXTRACTION: Method 2 succeeded - found {valid_count} valid responses")
                return ExtractedAnswers(answers=extracted_answers)

    except Exception as e:
        logger.error(f"FALLBACK EXTRACTION: Method 2 failed with error: {str(e)}")

    logger.error("FALLBACK EXTRACTION: All fallback methods failed")
    return None


def _extract_single_response(question: str, transcript: str) -> Optional[str]:
    """
    Simple pattern-based extraction for a single question-response pair.

    Args:
        question: The question to find a response for
        transcript: The full transcript

    Returns:
        The extracted response or None if not found
    """
    # Simple heuristic-based extraction
    lines = transcript.split('\n')

    # Look for the question in the transcript
    question_words = question.lower().split()[:5]  # First 5 words of question

    for i, line in enumerate(lines):
        line_lower = line.lower()

        # If this line contains part of the question
        if any(word in line_lower for word in question_words if len(word) > 3):
            # Look for response in next few lines
            for j in range(i + 1, min(i + 5, len(lines))):
                response_line = lines[j].strip()
                if response_line and len(response_line) > 5:
                    # Check if this looks like a response
                    if (response_line.startswith(('Expected Response:', 'CANDIDATE:', 'A:', 'Answer:')) or
                        any(indicator in response_line.lower() for indicator in ['i think', 'i know', 'i don\'t', 'yes', 'no', 'maybe'])):
                        return response_line

    return None


# Core business logic for processing interviews
def process_interview(request: InterviewProcessingRequest):
    """
    This function processes an interview transcript based on the specified request type.

    Args:
        request (InterviewProcessingRequest): The request object containing questions, transcript, and process type.

    Returns:
        The processed result based on the request type.
    """
    if request.process_type == ProcessType.EXTRACT:
        schema = ExtractedAnswers
        task_prompt = (
            "Extract the candidate's actual responses from the interview transcript for each question. "
            "You MUST find and extract responses even if they are brief, incomplete, or indicate lack of knowledge. "
            "\n\n**TRANSCRIPT FORMAT RECOGNITION (try these patterns in order):**\n"
            "1. **'Expected Response:' format** - Text following 'Expected Response:' labels contains the candidate's ACTUAL responses\n"
            "2. **'CANDIDATE:' format** - Direct candidate responses marked with 'CANDIDATE:' prefix\n"
            "3. **Conversational format** - Look for interviewer questions followed by candidate responses\n"
            "4. **Mixed format** - Combination of the above patterns\n"
            "\n**RESPONSE EXTRACTION RULES:**\n"
            "- Extract ANY verbal response from the candidate, including:\n"
            "  * Complete technical explanations\n"
            "  * Brief answers like 'Yes', 'No', 'I think so'\n"
            "  * Admissions of lack of knowledge: 'I don't know', 'I'm not familiar with that'\n"
            "  * Partial responses: 'I have some experience with...'\n"
            "  * Wrong answers (still count as responses)\n"
            "- If a candidate provided ANY verbal response to a question, extract it fully\n"
            "- Only use 'Invalid transcript' for a specific question if:\n"
            "  * The question was never asked in the transcript, OR\n"
            "  * The candidate remained completely silent (no verbal response at all)\n"
            "- Do NOT use 'Invalid transcript' just because the answer is brief, wrong, or shows lack of knowledge\n"
            "\n**CRITICAL SUCCESS CRITERIA:**\n"
            "- If the transcript shows a candidate who answered most questions (even briefly), you should extract responses for most questions\n"
            "- A senior candidate who 'only didn't know one question' should have responses extracted for 11 out of 12 questions\n"
            "- Look beyond perfect answers - extract honest admissions, partial knowledge, and learning-oriented responses\n"
            "- Return responses in the same order as questions appear in the questions list"
        )

        # Enhanced debug logging for answer extraction
        transcript_length = len(request.transcript) if request.transcript else 0
        logger.info(f"EXTRACT DEBUG: transcript length = {transcript_length}")
        logger.info(f"EXTRACT DEBUG: transcript preview = {request.transcript[:200] if request.transcript else 'None'}...")
        logger.info(f"EXTRACT DEBUG: number of questions = {len(request.questions)}")

        # Log transcript format indicators
        if request.transcript:
            expected_response_count = request.transcript.count("Expected Response:")
            candidate_count = request.transcript.count("CANDIDATE:")
            logger.info(f"EXTRACT DEBUG: 'Expected Response:' patterns found = {expected_response_count}")
            logger.info(f"EXTRACT DEBUG: 'CANDIDATE:' patterns found = {candidate_count}")

            # Log first few questions for context
            for i, question in enumerate(request.questions[:3]):
                logger.info(f"EXTRACT DEBUG: Question {i+1}: {question[:100]}...")
        else:
            logger.warning("EXTRACT DEBUG: No transcript provided!")
    else:
        schema = ParaphrasedAnswers
        task_prompt = (
            "Paraphrase the candidate's answers using the full context of the transcript, ensuring that:\n"
            "- The paraphrased answer remains faithful to what was actually said.\n"
            "- If relevant details appear in other parts of the transcript, include a 'complement_from' field.\n"
            "- Do NOT introduce new information or modify qualifications.\n"
            "- Return JSON following the provided schema."
        )

    user_msg = HumanMessage(
        content="Questions:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + "\n\nTranscript:\n"
        + request.transcript
    )

    schema_text = get_related_class_definitions(schema)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=schema,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("All LLM providers failed")

    # Enhanced debug logging for AI response
    if request.process_type == ProcessType.EXTRACT:
        logger.info(f"EXTRACT DEBUG: AI result type = {type(result)}")
        logger.info(f"EXTRACT DEBUG: AI result = {str(result)[:500]}...")

        # Analyze extraction results
        if hasattr(result, 'answers') and result.answers:
            valid_answers = [ans for ans in result.answers if ans != "Invalid transcript"]
            invalid_count = len([ans for ans in result.answers if ans == "Invalid transcript"])

            logger.info(f"EXTRACT DEBUG: Total answers extracted = {len(result.answers)}")
            logger.info(f"EXTRACT DEBUG: Valid answers = {len(valid_answers)}")
            logger.info(f"EXTRACT DEBUG: Invalid transcript count = {invalid_count}")
            logger.info(f"EXTRACT DEBUG: Extraction success rate = {len(valid_answers)/len(result.answers)*100:.1f}%")

            # Log first few extracted answers for analysis
            for i, answer in enumerate(result.answers[:3]):
                logger.info(f"EXTRACT DEBUG: Answer {i+1}: {answer[:100]}...")

            # Critical warning if all answers are invalid
            if invalid_count == len(result.answers):
                logger.error("EXTRACT CRITICAL: ALL ANSWERS MARKED AS 'Invalid transcript' - This indicates extraction failure!")
                logger.error(f"EXTRACT CRITICAL: Questions count = {len(request.questions)}, Expected responses = {len(request.questions)}")

                # Attempt fallback extraction with alternative approach
                logger.info("EXTRACT FALLBACK: Attempting alternative extraction method...")
                fallback_result = _attempt_fallback_extraction(request)
                if fallback_result:
                    logger.info("EXTRACT FALLBACK: Alternative extraction succeeded!")
                    return fallback_result
                else:
                    logger.error("EXTRACT FALLBACK: Alternative extraction also failed")
        else:
            logger.error("EXTRACT DEBUG: No answers found in result or result is malformed")

    return result


def extract_questions_from_transcript(transcript: str) -> TranscriptQuestions:
    """
    Extract questions from an interview transcript using LLM analysis.

    Args:
        transcript (str): The interview transcript to analyze.

    Returns:
        TranscriptQuestions: The extracted questions with their categories.
    """
    task_prompt = """
        Analyze the interview transcript and extract ONLY the questions that were ACTUALLY ASKED AND ANSWERED during the interview.

        CRITICAL INSTRUCTIONS:
        1. Look for questions that have BOTH a question AND a candidate response
        2. Only include questions where you can find the candidate's actual verbal response in the transcript
        3. Do NOT include questions that appear to be templates or examples but weren't actually asked
        4. Do NOT include questions that were asked but the candidate didn't respond to
        5. Number the questions sequentially starting from 1 based on the order they appear in the interview

        WHAT TO EXTRACT:
        - Question text as it appears in the transcript
        - Only questions that have a corresponding candidate response
        - Category/topic if mentioned (e.g., "Technical Skills", "Soft Skills")

        WHAT TO IGNORE:
        - Template questions that weren't actually asked
        - Questions without candidate responses
        - Rhetorical questions or greetings
        - Questions that appear in lists but weren't part of the actual conversation

        EXAMPLE PATTERNS TO LOOK FOR:
        - Interviewer: "Can you explain your experience with Python?"
          Candidate: "I have 3 years of experience..."
        - Q1: "Describe your approach to data modeling"
          A1: "I typically start by..."

        IMPORTANT: If the transcript contains a list of 15-20 questions but only 3-6 have actual candidate responses,
        extract ONLY the 3-6 questions that have responses. Do not extract template questions.

        TYPICAL INTERVIEW LENGTH: Most interviews have 3-8 questions, not 15-20.

        Return a JSON object with ONLY the questions that were actually asked and answered.
    """

    logger.info(f"QUESTION EXTRACTION DEBUG: Starting extraction from transcript (length: {len(transcript)})")
    logger.info(f"QUESTION EXTRACTION DEBUG: Transcript preview: {transcript[:300]}...")

    user_msg = HumanMessage(content=f"Transcript:\n{transcript}")

    schema_text = get_related_class_definitions(TranscriptQuestions)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=TranscriptQuestions,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )

    if not result:
        logger.error(f"QUESTION EXTRACTION DEBUG: LLM inference failed - returning empty questions")
        # Return empty questions if extraction fails
        return TranscriptQuestions(questions=[])

    logger.info(f"QUESTION EXTRACTION DEBUG: Successfully extracted {len(result.questions)} questions")
    for i, q in enumerate(result.questions, 1):
        logger.info(f"QUESTION EXTRACTION DEBUG: Q{i}: {q.question_text[:100]}...")
        logger.info(f"QUESTION EXTRACTION DEBUG: Category{i}: {q.category if q.category else 'None'}")

    return result


# Helper function to run and persist interview processing
def run_and_persist_interview(interview_id: str, process_type: ProcessType):
    """
    This function runs the interview processing and persists the results in the database.

    Args:
        interview_id (str): The ID of the interview to process.
        process_type (ProcessType): The type of processing to perform.

    Returns:
        The processed result based on the request type.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        # raise HTTPException(status_code=404, detail="Interview or questionnaire not found")
        return None

    questionnaire, transcript = row
    questions = [q["question"] for q in questionnaire["questions"]]

    req = InterviewProcessingRequest(
        questions=questions, transcript=transcript, process_type=process_type
    )
    result = process_interview(req)

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET anwers_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result













# Generate and persist interview questions
# This function generates interview questions based on the position ID and persists them in the database.
def generate_and_persist_qa(position_id: str, n_questions: int, include: str, current_user: str) -> QA_model:
    """
    This function generates interview questions based on the position ID and persists them in the database.

    Args:
        position_id (str): The ID of the position for which to generate questions.
        n_questions (int): The number of questions to generate.
        include (str): A comma-separated string of topics to include in the questions.
        current_user (str): The current user generating the questions.

    Returns:
        QA_model: The generated questions and related data.
    """
    # 1) Fetch position
    position = get_position_by_id(position_id)
    if not position:
        raise HTTPException(status_code=404, detail="Position not found")

    # Check if questions already exist for this position
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT data, allow_regeneration FROM interview_questions WHERE position_id = %s;
            """,
            (position_id,)
        )
        result = cur.fetchone()  # fetchone can return None
        allow_regeneration = True if not result or result[1] is None else result[1]
        if not allow_regeneration:
            # If questions exist and regeneration is not allowed, raise an error
            raise HTTPException(
                status_code=400,
                detail=f"Interview questions already exist for position {position_id} and regeneration is not allowed"
            )

    # Adjust the field names if your JSON differs.
    info: dict = position.position_info or {}
    full_description = json.dumps(info, ensure_ascii=False)

    topics_text = get_topics(include)
    # print("topics_text", topics_text)
    task_prompt = f"""
        You are tasked with creating a structured interview questionnaire designed to evaluate **technical and methodological skills** while clearly differentiating levels of seniority among candidates for a specific role.

        Role Description:
        {full_description}

        **Please generate exactly {n_questions} questions based on the following topics: {topics_text}. For each question, ensure the output includes:**

        1. A sequential question_number ranging from 1 to {n_questions}.
        2. A single tag indicating the specific topic addressed, selected exclusively from: {topics_text}.
        3. Three distinct answers that reflect different seniority levels:
        - junior_answer
        - mid_answer
        - senior_answer

        **Guidelines for Answer Construction (Chain of Thought per level):**

        - senior_answer: Highlight advanced concepts, frameworks, and strategies. Emphasize decision-making, scalability, efficiency, and alignment with business value. Conclude with measurable outcomes or impact on organizational objectives.  
        - mid_answer: Describe practical execution, tools, and methodologies in detail. Show structured problem-solving and collaboration. Conclude with how these practices improve workflows or contribute to project/team success.  
        - junior_answer: Cover foundational concepts, learning in practice, and hands-on skills. Emphasize adaptability, eagerness to learn, and contribution to immediate team objectives.  

        **Formatting Rules:**
        - Deliver the output strictly in JSON format with valid syntax.  
        - Each topic from {topics_text} must appear in at least one question.  
        - Each question must have exactly one tag.  
        - Do not combine tags (e.g., "SOFT SKILLS METHODOLOGIES" is prohibited).  
        - Ensure clear differentiation between junior, mid, and senior answers — avoid repetition or generic filler.  
        - Avoid referencing seniority explicitly (e.g., "As a junior…" or "With X years of experience").  
        - Keep answers professional, substantive, and business-relevant.

        **Example (Agile Methodologies — Sprint Planning):**  
        - Junior: Basic understanding of Agile/Scrum, learning task organization, showing how participation supports team collaboration.  
        - Mid: Refining backlog, coordinating with stakeholders, ensuring adaptability and efficiency in delivery.  
        - Senior: Driving strategic alignment, leading planning sessions, ensuring measurable improvements in delivery and business outcomes.  
        """
    
    schema_text = get_related_class_definitions(QA_model)

    qa = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=QA_model,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not qa:
        raise RuntimeError("LLM failed to generate questionnaire")

    # 3) Persist
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO interview_questions 
                (position_id, data, created_by, created_at, updated_by, updated_at) 
            VALUES 
                (%s, %s, %s, NOW(), %s, NOW())
            ON CONFLICT (position_id) DO UPDATE
            SET 
                data = EXCLUDED.data,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW();
            """,
            (position_id, Json(qa.model_dump()), current_user, current_user)
        )
    return qa


# Create interviews for the given position and candidates
# This function creates interviews for the given position and candidates.
def create_interviews_for_position(position_id, analysis_data: list[InterviewCreate]) -> List[Interview]:
    """
    Create interviews for the given position and candidates.

    Args:
        position_id (str): The ID of the position for which to create interviews.
        analysis_data (list[InterviewCreate]): List of interview data for candidates.

    Returns:
        List[Interview]: List of created interview objects.
    """
    try:
        for data in analysis_data:
            candidate_id = data.candidate_id

            if not candidate_id:
                continue
            # Validate if candidate_id is a valid UUID
            if not isinstance(candidate_id, str) or len(candidate_id) != 36:
                continue
            # validate if candidate_id exists in candidates_smarthr table
            exist = get_candidate_by_id(candidate_id)
            if not exist:
                continue
            # Check if interview already exists for this candidate and position
            exist = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
            if exist:
                continue
            # Insert new interview
            with get_cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO interviews 
                    (position_id, candidate_id, analysis_data, status_hr, status_tec, created_at, updated_at)
                    VALUES (%s, %s, %s, 'not_scheduled', 'not_scheduled', NOW(), NOW())
                    RETURNING id, position_id, candidate_id
                    """,
                    (
                        position_id,
                        candidate_id,
                        Json(data.analysis_data if data.analysis_data else {}),
                    ),
                )

        return fetch_all_interviews_by_position_id(position_id)
    except psycopg2.Error as e:
        print(f"Database error occurred while creating interview: {str(e)}")
        logger.error(f"Database error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"create_interview.Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while creating interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while creating interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by position ID
# This function fetches all interviews for a given position ID.
def fetch_all_interviews_by_position_id(position_id: str) -> List[Interview]:
    """
    Fetch all interviews for a given position ID.

    Args:
        position_id (str): The ID of the position for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id,),
            )
            rows = cur.fetchall()
        interviews = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_all_interviews_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Update interview feedback for HR
# This function updates the interview feedback for HR.
def update_interview_hr(interviewData: InterviewHr) -> Interview:
    """
    Update the interview feedback for HR.

    Args:
        interviewData (InterviewHr): The interview data to update.

    Returns:
        Interview: The updated interview object.
    """
    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found")

    if feedback.status_hr == StatusInterview.COMPLETED.value or feedback.status_hr == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET
            feedback_hr = %s,
            recruiter_hr_id = %s,
            scheduled_hr_id = %s,
            interview_date_hr = %s,
            feedback_date_hr = %s,
            status_hr = %s,
            recommendation_hr = %s,
            transcript_hr = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_hr),
        interviewData.recruiter_hr_id,
        interviewData.scheduled_hr_id,
        interviewData.interview_date_hr,
        interviewData.feedback_date_hr,
        interviewData.status_hr,
        interviewData.recommendation_hr,
        interviewData.transcript_hr,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    return Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )


# Update interview feedback for the technical team
# This function updates the interview feedback for the technical team.
def update_interview_tec(interviewData: InterviewTec) -> Interview:
    """
     Update the interview feedback for the technical team.

     Args:
         interviewData (InterviewTec): The interview data to update.

     Returns:
         Interview: The updated interview object.
    """
    # Debug logging
    logger.info(f"UPDATE_INTERVIEW_TEC: Received data for position_id={interviewData.position_id}, candidate_id={interviewData.candidate_id}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Status received: '{interviewData.status_tec}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: Transcript length: {len(interviewData.transcript_tec) if interviewData.transcript_tec else 0}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Transcript preview: {interviewData.transcript_tec[:200] if interviewData.transcript_tec else 'None'}...")
    logger.info(f"UPDATE_INTERVIEW_TEC: Feedback keys: {list(interviewData.feedback_tec.keys()) if interviewData.feedback_tec else 'None'}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Feedback content: {interviewData.feedback_tec}")

    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found (update_interview_tec)")

    logger.info(f"UPDATE_INTERVIEW_TEC: Current status in DB: '{feedback.status_tec}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: StatusInterview.COMPLETED.value = '{StatusInterview.COMPLETED.value}'")

    if feedback.status_tec == StatusInterview.COMPLETED.value or feedback.status_tec == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET            
            feedback_tec = %s,
            recruiter_tec_id = %s,
            scheduled_tec_id = %s,
            interview_date_tec = %s,
            feedback_date_tec = %s,
            status_tec = %s,
            recommendation_tec = %s,
            transcript_tec = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_tec),
        interviewData.recruiter_tec_id,
        interviewData.scheduled_tec_id,
        interviewData.interview_date_tec,
        interviewData.feedback_date_tec,
        interviewData.status_tec,
        interviewData.recommendation_tec,
        interviewData.transcript_tec,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    response = Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )

    # fill anwers_data from transcript_tec after is completed
    logger.info(f"UPDATE_INTERVIEW_TEC: Checking if evaluation should run. Status: '{response.status_tec}', Expected: '{StatusInterview.COMPLETED.value}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: Status comparison result: {response.status_tec == StatusInterview.COMPLETED.value}")

    if response.status_tec == StatusInterview.COMPLETED.value:
        logger.info(f"UPDATE_INTERVIEW_TEC: Status is COMPLETED, starting evaluation process for interview {response.id}")

        # Check if we have transcript data or feedback data to evaluate
        has_transcript = response.transcript_tec and response.transcript_tec.strip()
        transcript_length = len(response.transcript_tec) if response.transcript_tec else 0
        has_feedback_data = response.feedback_tec and isinstance(response.feedback_tec, dict)

        logger.info(f"UPDATE_INTERVIEW_TEC: Has transcript: {has_transcript}")
        logger.info(f"UPDATE_INTERVIEW_TEC: Transcript length: {transcript_length}")
        logger.info(f"UPDATE_INTERVIEW_TEC: Has feedback data: {has_feedback_data}")

        # Check if transcript is too short to be meaningful
        if has_transcript and transcript_length < 100:
            logger.warning(f"UPDATE_INTERVIEW_TEC: Transcript is very short ({transcript_length} chars), might be truncated")
            logger.warning(f"UPDATE_INTERVIEW_TEC: Transcript content: '{response.transcript_tec}'")

        if has_transcript or has_feedback_data:
            try:
                # Extract answers from transcript and persist to database
                extraction_result = run_and_persist_interview(response.id, ProcessType.EXTRACT)
                logger.info(f"UPDATE_INTERVIEW_TEC: Extraction result: {extraction_result}")

                # Only proceed with evaluation if extraction was successful
                if extraction_result:
                    # Use the new 4-agent evaluation system with pre-extracted answers
                    logger.info(f"Starting enhanced 4-agent evaluation for interview {response.id}")
                    logger.info(f"UPDATE_INTERVIEW_TEC: Using pre-extracted answers (skipping Agent 1)")

                    # Extract the answers list from the extraction result
                    extracted_answers = None
                    if hasattr(extraction_result, 'answers') and extraction_result.answers:
                        extracted_answers = extraction_result.answers
                        logger.info(f"UPDATE_INTERVIEW_TEC: Found {len(extracted_answers)} pre-extracted answers")

                    evaluate_interview_with_four_agents(response.id, extracted_answers)
                    logger.info(f"Enhanced 4-agent evaluation completed for interview {response.id}")
                else:
                    logger.warning(f"UPDATE_INTERVIEW_TEC: Extraction failed for interview {response.id}")
                    # Try direct evaluation if we have feedback data
                    if has_feedback_data:
                        logger.info(f"UPDATE_INTERVIEW_TEC: Attempting direct enhanced evaluation using feedback data")
                        evaluate_interview_with_four_agents(response.id, None)
            except Exception as e:
                logger.error(f"Error during interview processing for {response.id}: {str(e)}")
                logger.error(f"Full traceback: {traceback.format_exc()}")
                # Don't re-raise the exception to avoid breaking the update operation
                # The interview update should still succeed even if evaluation fails
        else:
            logger.warning(f"UPDATE_INTERVIEW_TEC: No transcript or feedback data to evaluate for interview {response.id}")
            # Create a basic evaluation indicating no data was provided
            try:
                from models.interview import EvaluateInterviewNoQA, Seniority
                basic_evaluation = EvaluateInterviewNoQA(
                    overall_seniority=Seniority.NA,
                    percentage_of_match=0.0,
                    explanation="No technical interview data was provided. The transcript field appears to be empty or incomplete. Please ensure the full interview transcript with questions and candidate responses is included when submitting technical feedback."
                )

                # Persist this basic evaluation
                with get_cursor() as cur:
                    cur.execute(
                        """
                        UPDATE interviews
                        SET interview_data = %s,
                            updated_at = NOW()
                        WHERE id = %s;
                        """,
                        (Json(basic_evaluation.model_dump()), response.id)
                    )
                logger.info(f"UPDATE_INTERVIEW_TEC: Created basic evaluation for incomplete data in interview {response.id}")
            except Exception as eval_error:
                logger.error(f"UPDATE_INTERVIEW_TEC: Failed to create basic evaluation: {str(eval_error)}")
    else:
        logger.info(f"UPDATE_INTERVIEW_TEC: Status is not COMPLETED, skipping evaluation. Status: '{response.status_tec}'")

    return response


# Get a single interview by position ID and candidate ID
# This function fetches a single interview for the given position and candidate IDs.
def fetch_interview_by_position_id_candidate_id(position_id: str, candidate_id: str) -> Optional[Interview]:
    """
     Fetch a single interview for the given position and candidate IDs.

     Args:
         position_id (str): The ID of the position.
         candidate_id (str): The ID of the candidate.

     Returns:
         Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info, 
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id, candidate_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_position_id_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get questions by position ID
# This function fetches the questions for the given position ID.
def fetch_questions_by_position_id(position_id: str) -> SingleQuestions:
    """
    Fetch the questions for the given position ID.

    Args:
        position_id (str): The ID of the position.

    Returns:
        SingleQuestions: The questions object for the specified position ID.
    """
    try:
        with get_cursor() as cur:
            sqlQuery = """
                SELECT id, position_id, data, created_at, updated_at, allow_regeneration, created_by, updated_by
                FROM interview_questions WHERE position_id::text=%s
            """
            params = (position_id,)
            cur.execute(sqlQuery, params)
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Questions not found")
            return SingleQuestions(
                id=str(row[0]),
                position_id=str(row[1]),
                data=row[2],
                created_at=row[3],
                updated_at=row[4],
                allow_regeneration=row[5],
                created_by=row[6],
                updated_by=row[7]
            )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_questions_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Delete interview by position ID and candidate ID
# This function deletes an interview for the given position and candidate IDs.
def delete_interview(position_id: str, candidate_id: str) -> bool:
    """
    Delete an interview for the given position and candidate IDs.

    Args:
        position_id (str): The ID of the position.
        candidate_id (str): The ID of the candidate.

    Returns:
        bool: True if the interview was deleted successfully, False otherwise.
    """
    try:
        # Fetch the interview to check its status
        feedback = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not feedback:
            raise HTTPException(status_code=404, detail="Interview not found")
        # You may add status checks here if needed

        with get_cursor() as cur:
            cur.execute(
                """
                DELETE FROM interviews WHERE id = %s
                """,
                (feedback.id,),
            )
        return True
    except psycopg2.Error as e:
        print(f"Database error occurred while deleting interview: {str(e)}")
        logger.error(f"Database error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_interview. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while deleting interview: {str(e)}")
        logger.error(f"Error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by candidate ID
# This function fetches all interviews for the given candidate ID.
def fetch_interviews_by_candidate_id(candidate_id: str) -> List[Interview]:
    """
    Fetch all interviews for the given candidate ID.

    Args:
        candidate_id (str): The ID of the candidate for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (candidate_id,),
            )
            rows = cur.fetchall()

        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interviews_by_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch interview by interview ID
# This function fetches an interview by its ID.
def fetch_interview_by_interview_id(interview_id: str) -> Optional[Interview]:
    """
    Fetch an interview by its ID.

    Args:
        interview_id (str): The ID of the interview to fetch.

    Returns:
        Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (interview_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_interview_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Re-evaluate an interview by its ID
# This function re-evaluates an interview by its ID.
# It fetches the interview data, extract answers data if needed, evaluates it, and then fetches the updated interview data.
def re_evalute_interview(interview_id: str) -> Interview:
    """
    Re-evaluate an interview by its ID.

    Args:       
        interview_id (str): The ID of the interview to re-evaluate.

    Returns:
        Interview: The updated interview object.
    """
    # 1. Fetch the interview data
    interview = fetch_interview_by_interview_id(interview_id)
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (re_evalute_interview)")

    if not interview.transcript_tec:
        raise HTTPException(status_code=400, detail="Technical Transcript not found")

    if not interview.anwers_data:
        # 2. Run and persist the interview
        run_and_persist_interview(interview.id, ProcessType.EXTRACT)

    # 3. Evaluate the interview using 4-agent system
    evaluate_interview_with_four_agents(interview.id)

    # 4. Fetch the updated interview data
    interview = fetch_interview_by_interview_id(interview.id)
    return interview


# Change Questions status
def update_question_regeneration_status(position_id: str, question_id: str, allow_regeneration: bool) -> bool:
    """
    Change the status of a question in the interview_questions table.
    :param position_id: The ID of the position to which the question belongs.
    :param question_id: The ID of the question to update.
    :param allow_regeneration: Boolean indicating whether regeneration is allowed.
    :return: True if the update was successful, False otherwise.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interview_questions
            SET allow_regeneration = %s,
                updated_at = NOW()
            WHERE id = %s AND position_id = %s;
            """,
            (allow_regeneration, question_id, position_id)
        )
        if cur.rowcount == 0:
            raise HTTPException(status_code=404, detail="Question not found")
        return True
    return False


# ═══════════════════════════════════════════════════════════════════════════════
# 4-AGENT EVALUATION SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════

def agent1_parse_transcript(request: Agent1Request) -> Agent1Response:
    """
    Agent 1: Parse transcript into structured Q&A pairs.

    This agent analyzes the interview transcript and extracts questions and answers,
    properly identifying valid responses vs invalid/inadequate responses like "mmmmmm".

    Args:
        request: Agent1Request containing transcript and interview_id

    Returns:
        Agent1Response with parsed transcript data
    """
    try:
        logger.info(f"AGENT1: Starting transcript parsing for interview {request.interview_id}")
        logger.info(f"AGENT1: Transcript length: {len(request.transcript)} characters")

        task_prompt = """
        You are Agent 1 in a 4-agent interview evaluation system. Your job is to parse the interview transcript
        and extract structured Question-Answer pairs.

        TRANSCRIPT FORMAT: The transcript uses this format:
        - Questions appear after numbers (e.g., "1. Can you describe...")
        - Answers appear after "Expected Response:" or "Actual Response:"
        - Look for both the question text and the candidate's actual response

        CRITICAL INSTRUCTIONS:
        1. Extract EVERY question that was asked, regardless of response quality
        2. For each Q&A pair, assess the ACTUAL candidate response quality objectively:
           - 'valid': Meaningful response that attempts to answer the question (detailed, technical, shows knowledge)
           - 'inadequate': Responses like "I don't know", "I can't remember", "Not sure" (honest but insufficient)
           - 'invalid': Inappropriate, nonsensical, or completely off-topic responses
           - 'missing': No response found at all

        3. Set has_valid_response to True ONLY for 'valid' responses
        4. Be strict but fair about response quality - don't give credit for non-answers
        5. IMPORTANT: Even if most responses are poor, still extract ALL Q&A pairs found

        RESPONSE QUALITY EXAMPLES FOR THIS TRANSCRIPT TYPE:
        - VALID: "I have extensive experience with ServiceNow development, having worked on multiple modules..." → has_valid_response: true, response_quality: "valid"
        - INADEQUATE: "I may know" → has_valid_response: false, response_quality: "inadequate"
        - INADEQUATE: "I cant remember right now" → has_valid_response: false, response_quality: "inadequate"
        - INADEQUATE: "I dont really know you know?" → has_valid_response: false, response_quality: "inadequate"
        - INVALID: "I was told this interview was gonna be aeasy" → has_valid_response: false, response_quality: "invalid"

        IMPORTANT: Your job is to parse and categorize, not to reject the transcript.
        Extract all Q&A pairs you can find, even if many responses are inadequate or invalid.
        This is essential for accurate percentage calculations later.

        Focus on finding the ACTUAL candidate responses, not the "Expected Response" examples.
        """

        user_msg = HumanMessage(content=f"Transcript to parse:\n{request.transcript}")

        schema_text = get_related_class_definitions(ParsedTranscript)
        result = inference_with_fallback(
            task_prompt=task_prompt,
            model_schema=ParsedTranscript,
            user_messages=[user_msg],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            logger.error(f"AGENT1: LLM parsing failed for interview {request.interview_id}")
            logger.warning(f"AGENT1: Attempting basic fallback parsing")

            # Basic fallback parsing - try to extract at least some Q&A pairs
            try:
                fallback_qa_pairs = []
                lines = request.transcript.split('\n')
                current_question = None
                current_answer = None
                question_num = 0
                in_expected_response = False

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Check for question patterns (numbered questions)
                    if line and line[0].isdigit() and '.' in line:
                        # Save previous Q&A if exists
                        if current_question and current_answer:
                            question_num += 1
                            # Assess response quality
                            answer_lower = current_answer.lower()
                            if any(phrase in answer_lower for phrase in ['i may know', 'i cant remember', 'i dont really know', 'i don\'t know']):
                                quality = "inadequate"
                                valid = False
                            elif 'interview was gonna be' in answer_lower or len(current_answer.strip()) < 5:
                                quality = "invalid"
                                valid = False
                            elif len(current_answer.strip()) > 20 and not any(invalid in answer_lower for invalid in ['mmm', 'uhh']):
                                quality = "valid"
                                valid = True
                            else:
                                quality = "inadequate"
                                valid = False

                            fallback_qa_pairs.append(TranscriptQAPair(
                                question_number=question_num,
                                question_text=current_question,
                                answer_text=current_answer,
                                has_valid_response=valid,
                                response_quality=quality
                            ))

                        current_question = line
                        current_answer = ""
                        in_expected_response = False

                    elif line.startswith('Expected Response:'):
                        # In QA testing, "Expected Response:" contains the actual candidate's answer
                        continue  # Skip the "Expected Response:" label, capture the content that follows

                    elif current_question and line and not line.startswith('TECHNICAL SKILLS') and not line.startswith('SOFT SKILLS'):
                        # This is the actual candidate response (could be after "Expected Response:" label)
                        if not current_answer:  # First response line
                            current_answer = line
                        else:  # Additional response lines
                            current_answer += " " + line

                # Save last Q&A
                if current_question and current_answer:
                    question_num += 1
                    # Assess response quality
                    answer_lower = current_answer.lower()
                    if any(phrase in answer_lower for phrase in ['i may know', 'i cant remember', 'i dont really know', 'i don\'t know']):
                        quality = "inadequate"
                        valid = False
                    elif 'interview was gonna be' in answer_lower or len(current_answer.strip()) < 5:
                        quality = "invalid"
                        valid = False
                    elif len(current_answer.strip()) > 20 and not any(invalid in answer_lower for invalid in ['mmm', 'uhh']):
                        quality = "valid"
                        valid = True
                    else:
                        quality = "inadequate"
                        valid = False

                    fallback_qa_pairs.append(TranscriptQAPair(
                        question_number=question_num,
                        question_text=current_question,
                        answer_text=current_answer,
                        has_valid_response=valid,
                        response_quality=quality
                    ))

                if fallback_qa_pairs:
                    logger.info(f"AGENT1: Fallback parsing extracted {len(fallback_qa_pairs)} Q&A pairs")
                    return Agent1Response(
                        parsed_transcript=ParsedTranscript(
                            qa_pairs=fallback_qa_pairs,
                            total_questions_found=len(fallback_qa_pairs),
                            parsing_notes="Used fallback parsing due to LLM failure"
                        ),
                        success=True
                    )

            except Exception as fallback_error:
                logger.error(f"AGENT1: Fallback parsing also failed: {str(fallback_error)}")

            return Agent1Response(
                parsed_transcript=ParsedTranscript(
                    qa_pairs=[],
                    total_questions_found=0,
                    parsing_notes="Both LLM and fallback parsing failed"
                ),
                success=False,
                error_message="LLM parsing failed and fallback unsuccessful"
            )

        logger.info(f"AGENT1: Successfully parsed {len(result.qa_pairs)} Q&A pairs")
        logger.info(f"AGENT1: Valid responses: {sum(1 for qa in result.qa_pairs if qa.has_valid_response)}")

        return Agent1Response(
            parsed_transcript=result,
            success=True
        )

    except Exception as e:
        logger.error(f"AGENT1: Error parsing transcript for interview {request.interview_id}: {str(e)}")
        return Agent1Response(
            parsed_transcript=ParsedTranscript(
                qa_pairs=[],
                total_questions_found=0,
                parsing_notes=f"Error during parsing: {str(e)}"
            ),
            success=False,
            error_message=str(e)
        )


def agent2_extract_expected_responses(request: Agent2Request) -> Agent2Response:
    """
    Agent 2: Extract expected responses for each seniority level from database.

    This agent matches transcript questions to database questions and extracts
    the corresponding expected answers for junior, mid, and senior levels.

    Args:
        request: Agent2Request containing parsed transcript and position_id

    Returns:
        Agent2Response with expected responses for each question
    """
    try:
        logger.info(f"AGENT2: Starting expected response extraction for interview {request.interview_id}")
        logger.info(f"AGENT2: Processing {len(request.parsed_transcript.qa_pairs)} Q&A pairs")

        # Filter out questions that weren't actually asked (marked as "Invalid transcript")
        valid_qa_pairs = [
            qa for qa in request.parsed_transcript.qa_pairs
            if qa.answer_text.strip().lower() != "invalid transcript"
        ]

        logger.info(f"AGENT2: Filtered to {len(valid_qa_pairs)} questions that were actually asked (removed {len(request.parsed_transcript.qa_pairs) - len(valid_qa_pairs)} 'Invalid transcript' entries)")

        if not valid_qa_pairs:
            logger.warning(f"AGENT2: No valid questions found - all responses were 'Invalid transcript'")
            return Agent2Response(
                expected_responses=ExpectedResponses(
                    responses=[],
                    matching_notes="No valid questions found - all responses were 'Invalid transcript'"
                ),
                success=True  # This is success - it means no questions were asked
            )

        # Get expected questions from database
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT data FROM interview_questions WHERE position_id = %s
                """,
                (request.position_id,)
            )
            row = cur.fetchone()

        if not row or not row[0]:
            logger.warning(f"AGENT2: No expected questions found for position {request.position_id}")
            return Agent2Response(
                expected_responses=ExpectedResponses(
                    responses=[],
                    matching_notes="No expected questions found in database for this position"
                ),
                success=False,
                error_message="No expected questions found in database"
            )

        expected_questions_data = row[0]
        expected_questions = expected_questions_data.get('questions', [])

        logger.info(f"AGENT2: Found {len(expected_questions)} expected questions in database")

        # Use LLM to match transcript questions to expected questions
        task_prompt = """
        You are Agent 2 in a 4-agent interview evaluation system. Your job is to match transcript questions
        to database questions and extract expected responses.

        INSTRUCTIONS:
        1. For each transcript question, find the best matching expected question from the database
        2. If a good match is found, extract the expected answers (junior, mid, senior)
        3. If no good match is found, create a note about it
        4. Be flexible with matching - questions may be phrased differently but cover the same topic

        MATCHING CRITERIA:
        - Same technical concept or skill being tested
        - Similar question intent even if wording differs
        - Related topics (e.g., "React hooks" matches "React state management")

        For unmatched questions, still create an ExpectedResponseSet but note the matching issue.
        """

        context_data = {
            "transcript_questions": [
                {
                    "question_number": qa.question_number,
                    "question_text": qa.question_text
                }
                for qa in valid_qa_pairs  # Use filtered qa_pairs instead of all qa_pairs
            ],
            "expected_questions": expected_questions
        }

        user_msg = HumanMessage(content=json.dumps(context_data, ensure_ascii=False))

        schema_text = get_related_class_definitions(ExpectedResponses)
        result = inference_with_fallback(
            task_prompt=task_prompt,
            model_schema=ExpectedResponses,
            user_messages=[user_msg],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            logger.error(f"AGENT2: LLM matching failed for interview {request.interview_id}")
            return Agent2Response(
                expected_responses=ExpectedResponses(
                    responses=[],
                    matching_notes="LLM matching failed - no response from language model"
                ),
                success=False,
                error_message="LLM matching failed"
            )

        logger.info(f"AGENT2: Successfully matched {len(result.responses)} questions")

        return Agent2Response(
            expected_responses=result,
            success=True
        )

    except Exception as e:
        logger.error(f"AGENT2: Error extracting expected responses for interview {request.interview_id}: {str(e)}")
        return Agent2Response(
            expected_responses=ExpectedResponses(
                responses=[],
                matching_notes=f"Error during matching: {str(e)}"
            ),
            success=False,
            error_message=str(e)
        )


def agent3_evaluate_individual_question(request: Agent3Request) -> Agent3Response:
    """
    Agent 3: Evaluate individual question response against expected answers.

    This agent compares the actual candidate response to expected responses
    and properly handles invalid/inadequate responses like "mmmmmm".

    Args:
        request: Agent3Request containing Q&A pair and expected responses

    Returns:
        Agent3Response with individual question evaluation
    """
    try:
        logger.info(f"AGENT3: Evaluating question {request.qa_pair.question_number} for interview {request.interview_id}")

        # EXACT MATCH DETECTION - Check for high similarity with expected responses
        from difflib import SequenceMatcher

        def calculate_similarity(text1: str, text2: str) -> float:
            """Calculate similarity between two texts (0.0 to 1.0)"""
            if not text1 or not text2:
                return 0.0
            return SequenceMatcher(None, text1.lower().strip(), text2.lower().strip()).ratio()

        actual_answer = request.qa_pair.answer_text.strip()
        junior_answer = request.expected_response.junior_answer.strip()
        mid_answer = request.expected_response.mid_answer.strip()
        senior_answer = request.expected_response.senior_answer.strip()

        # Pre-check for obviously nonsensical responses before exact match detection
        # Note: "I don't know" responses are handled by Agent 3 LLM, not pre-filtered
        nonsensical_responses = [
            "mmmmmm", "uhhhh", "ummm", "hmmm", "errr", "ahhh",
            "...", "???", "???", "n/a", "na", "null", "none",
            "test", "testing", "hello", "hi"
        ]

        actual_lower = actual_answer.lower().strip()
        if actual_lower in nonsensical_responses or len(actual_answer.strip()) < 3:
            logger.info(f"AGENT3: Pre-check detected invalid response - Q{request.qa_pair.question_number}: '{actual_answer}'")

            invalid_evaluation = IndividualQuestionEvaluation(
                question_number=request.qa_pair.question_number,
                question_text=request.qa_pair.question_text,
                actual_answer=actual_answer,
                expected_answers=request.expected_response,
                is_valid_response=False,
                is_dont_know_response=False,  # These are nonsensical, not "I don't know"
                detected_seniority=Seniority.NA,
                confidence_score=0.0,
                evaluation_reasoning=f"Response '{actual_answer}' is clearly invalid/inadequate and indicates lack of knowledge.",
                similarity_to_junior=0.0,
                similarity_to_mid=0.0,
                similarity_to_senior=0.0
            )

            return Agent3Response(
                evaluation=invalid_evaluation,
                success=True
            )

        # Calculate similarities
        junior_similarity = calculate_similarity(actual_answer, junior_answer)
        mid_similarity = calculate_similarity(actual_answer, mid_answer)
        senior_similarity = calculate_similarity(actual_answer, senior_answer)

        # High similarity threshold for exact matches (95%+)
        EXACT_MATCH_THRESHOLD = 0.95
        max_similarity = max(junior_similarity, mid_similarity, senior_similarity)

        if max_similarity >= EXACT_MATCH_THRESHOLD:
            # Determine which expected response it matches
            if senior_similarity >= EXACT_MATCH_THRESHOLD:
                detected_seniority = Seniority.SENIOR
                match_type = "senior"
                logger.info(f"AGENT3: EXACT MATCH detected - Q{request.qa_pair.question_number} matches SENIOR response ({senior_similarity:.3f} similarity)")
            elif mid_similarity >= EXACT_MATCH_THRESHOLD:
                detected_seniority = Seniority.MID
                match_type = "mid"
                logger.info(f"AGENT3: EXACT MATCH detected - Q{request.qa_pair.question_number} matches MID response ({mid_similarity:.3f} similarity)")
            else:
                detected_seniority = Seniority.JUNIOR
                match_type = "junior"
                logger.info(f"AGENT3: EXACT MATCH detected - Q{request.qa_pair.question_number} matches JUNIOR response ({junior_similarity:.3f} similarity)")

            # Create immediate evaluation result for exact match
            exact_match_evaluation = IndividualQuestionEvaluation(
                question_number=request.qa_pair.question_number,
                question_text=request.qa_pair.question_text,
                actual_answer=actual_answer,
                expected_answers=request.expected_response,
                is_valid_response=True,
                is_dont_know_response=False,  # Exact matches are not "I don't know" responses
                detected_seniority=detected_seniority,
                confidence_score=1.0,  # Maximum confidence for exact matches
                evaluation_reasoning=f"Exact match detected with {match_type} expected response (similarity: {max_similarity:.3f}). No LLM evaluation needed.",
                similarity_to_junior=junior_similarity,
                similarity_to_mid=mid_similarity,
                similarity_to_senior=senior_similarity
            )

            return Agent3Response(
                evaluation=exact_match_evaluation,
                success=True
            )

        # If no exact match, proceed with LLM evaluation
        logger.info(f"AGENT3: No exact match found (max similarity: {max_similarity:.3f}), proceeding with LLM evaluation")

        task_prompt = """
        You are Agent 3 in a 4-agent interview evaluation system. Your job is to evaluate a single question
        response by comparing it against expected answers for different seniority levels (junior, mid, senior).

        EVALUATION PROCESS:
        1. FIRST, compare the actual answer with each expected answer (junior, mid, senior)
        2. THEN, determine which seniority level the actual answer most closely matches
        3. FINALLY, classify the response type and assign appropriate scores

        RESPONSE CLASSIFICATION RULES:
        1. COMPLETELY INVALID (nonsensical responses):
           - Examples: "mmmmmm", "uhhhh", random text, gibberish
           - Set: is_valid_response=false, detected_seniority="n/a", confidence_score=0.0

        2. INADEQUATE BUT HONEST (admission of not knowing):
           - Examples: "I don't know", "Not sure", "No idea", "Never heard of it"
           - Set: is_valid_response=false, is_dont_know_response=true, detected_seniority="n/a", confidence_score=0.0

        3. VAGUE/META RESPONSES (describes what someone would say instead of demonstrating knowledge):
           - Examples: "A mid-level developer would describe...", "I have worked with X", "I have used Y"
           - These don't demonstrate actual knowledge, just awareness of concepts
           - Set: is_valid_response=false, detected_seniority="n/a", confidence_score=0.0

        4. WRONG/INCOMPLETE ATTEMPTS (tries to answer but incorrect/inadequate):
           - Examples: Partially correct, outdated information, misunderstood concepts
           - Set: is_valid_response=true, detected_seniority="junior" (or appropriate), confidence_score=low

        5. VALID RESPONSES (genuine attempts with specific, detailed information):
           - Must contain specific technical details, examples, or concrete experience
           - Compare content, depth, and accuracy against expected answers
           - Assign seniority based on which expected answer it most closely matches
           - Set appropriate confidence score and similarity scores

        COMPARISON GUIDELINES:
        - JUNIOR match: Basic understanding, simple concepts, minimal detail
        - MID match: Good understanding, some depth, practical knowledge
        - SENIOR match: Deep expertise, comprehensive knowledge, advanced concepts

        EXAMPLES OF RESPONSE TYPES:
        - VAGUE: "I have worked with React", "A senior developer would use advanced patterns"
        - INADEQUATE: "I don't know", "Not sure about that"
        - VALID JUNIOR: "React is a JavaScript library for building user interfaces"
        - VALID MID: "React uses components and state management. I've built several apps using hooks like useState"
        - VALID SENIOR: "React's virtual DOM optimizes rendering through reconciliation. I've implemented custom hooks, context patterns, and performance optimizations using React.memo and useMemo"

        SIMILARITY SCORING (0.0 to 1.0):
        - Compare actual answer content with each expected answer
        - Consider technical accuracy, depth of knowledge, and completeness
        - Higher similarity = closer match to that seniority level

        Be thorough in your comparison and strict but fair in your evaluation.
        Vague responses that don't demonstrate actual knowledge should be marked as invalid.
        """

        evaluation_data = {
            "question": request.qa_pair.question_text,
            "actual_answer": request.qa_pair.answer_text,
            "response_quality": request.qa_pair.response_quality,
            "has_valid_response": request.qa_pair.has_valid_response,
            "expected_answers": {
                "junior": request.expected_response.junior_answer,
                "mid": request.expected_response.mid_answer,
                "senior": request.expected_response.senior_answer
            },
            "question_category": request.expected_response.question_category
        }

        user_msg = HumanMessage(content=json.dumps(evaluation_data, ensure_ascii=False))

        schema_text = get_related_class_definitions(IndividualQuestionEvaluation)
        result = inference_with_fallback(
            task_prompt=task_prompt,
            model_schema=IndividualQuestionEvaluation,
            user_messages=[user_msg],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            logger.error(f"AGENT3: LLM evaluation failed for question {request.qa_pair.question_number}")
            # Create a fallback evaluation
            fallback_evaluation = IndividualQuestionEvaluation(
                question_number=request.qa_pair.question_number,
                question_text=request.qa_pair.question_text,
                actual_answer=request.qa_pair.answer_text,
                expected_answers=request.expected_response,
                is_valid_response=False,
                detected_seniority=Seniority.NA,
                confidence_score=0.0,
                evaluation_reasoning="LLM evaluation failed - no response from language model",
                similarity_to_junior=0.0,
                similarity_to_mid=0.0,
                similarity_to_senior=0.0
            )
            return Agent3Response(
                evaluation=fallback_evaluation,
                success=False,
                error_message="LLM evaluation failed"
            )

        logger.info(f"AGENT3: Question {request.qa_pair.question_number} evaluated as {result.detected_seniority} (confidence: {result.confidence_score})")

        return Agent3Response(
            evaluation=result,
            success=True
        )

    except Exception as e:
        logger.error(f"AGENT3: Error evaluating question {request.qa_pair.question_number}: {str(e)}")
        fallback_evaluation = IndividualQuestionEvaluation(
            question_number=request.qa_pair.question_number,
            question_text=request.qa_pair.question_text,
            actual_answer=request.qa_pair.answer_text,
            expected_answers=request.expected_response,
            is_valid_response=False,
            detected_seniority=Seniority.NA,
            confidence_score=0.0,
            evaluation_reasoning=f"Error during evaluation: {str(e)}",
            similarity_to_junior=0.0,
            similarity_to_mid=0.0,
            similarity_to_senior=0.0
        )
        return Agent3Response(
            evaluation=fallback_evaluation,
            success=False,
            error_message=str(e)
        )


def agent4_create_overall_evaluation(request: Agent4Request) -> Agent4Response:
    """
    Agent 4: Create overall evaluation summary from individual question evaluations.

    This agent aggregates individual evaluations and creates the final assessment
    with accurate percentage calculations and seniority assignment.

    Args:
        request: Agent4Request containing individual evaluations and context

    Returns:
        Agent4Response with final evaluation result
    """
    try:
        logger.info(f"AGENT4: Creating overall evaluation for interview {request.interview_id}")
        logger.info(f"AGENT4: Processing {len(request.individual_evaluations)} individual evaluations")

        # Calculate statistics for LLM context
        total_questions = len(request.individual_evaluations)
        valid_responses = sum(1 for eval in request.individual_evaluations if eval.is_valid_response)
        dont_know_responses = sum(1 for eval in request.individual_evaluations if eval.is_dont_know_response)
        invalid_responses = total_questions - valid_responses

        # Count seniority distribution (only for valid responses)
        seniority_counts = {"senior": 0, "mid": 0, "junior": 0, "n/a": 0}
        for eval in request.individual_evaluations:
            if eval.is_valid_response:
                seniority_counts[eval.detected_seniority.value] += 1
            else:
                seniority_counts["n/a"] += 1

        # Calculate percentage based on all responses
        # Invalid responses count as 0 points and are included in denominator
        evaluable_responses = valid_responses + dont_know_responses
        if total_questions > 0:
            percentage_score = (
                seniority_counts["senior"] * 100 +
                seniority_counts["mid"] * 75 +
                seniority_counts["junior"] * 50
            ) / total_questions  # Divide by all responses (including invalid ones)
        else:
            percentage_score = 0.0

        logger.info(f"AGENT4: Calculated percentage: {percentage_score:.1f}%")
        logger.info(f"AGENT4: Valid responses: {valid_responses}/{total_questions}")
        logger.info(f"AGENT4: 'I don't know' responses: {dont_know_responses}")
        logger.info(f"AGENT4: Evaluable responses (valid + 'I don't know'): {evaluable_responses}")
        logger.info(f"AGENT4: Seniority distribution: {seniority_counts}")

        # Determine base seniority from individual evaluations (most common valid seniority)
        if valid_responses == 0:
            base_seniority = Seniority.NA
        else:
            # Find the most common seniority among valid responses
            valid_seniorities = [eval.detected_seniority for eval in request.individual_evaluations if eval.is_valid_response]
            if not valid_seniorities:
                base_seniority = Seniority.NA
            else:
                # Count occurrences of each seniority
                seniority_freq = {}
                for seniority in valid_seniorities:
                    seniority_freq[seniority] = seniority_freq.get(seniority, 0) + 1

                # Get the most frequent seniority
                base_seniority = max(seniority_freq, key=seniority_freq.get)

        # Apply threshold downgrade logic (70% threshold)
        final_seniority = base_seniority
        threshold_applied = False

        if percentage_score < 70.0 and base_seniority != Seniority.NA:
            threshold_applied = True
            if base_seniority == Seniority.SENIOR:
                final_seniority = Seniority.MID
                logger.info(f"AGENT4: THRESHOLD APPLIED: {percentage_score:.1f}% < 70% → Downgraded SENIOR → MID")
            elif base_seniority == Seniority.MID:
                final_seniority = Seniority.JUNIOR
                logger.info(f"AGENT4: THRESHOLD APPLIED: {percentage_score:.1f}% < 70% → Downgraded MID → JUNIOR")
            else:  # JUNIOR
                final_seniority = Seniority.JUNIOR
                logger.info(f"AGENT4: THRESHOLD APPLIED: {percentage_score:.1f}% < 70% → JUNIOR stays JUNIOR")
        else:
            logger.info(f"AGENT4: NO DOWNGRADE: {percentage_score:.1f}% ≥ 70% → Seniority remains {base_seniority.value}")

        logger.info(f"AGENT4: Final seniority determination: {base_seniority.value} → {final_seniority.value}")

        # Generate concise justification summary using LLM
        task_prompt = """
        You are Agent 4 in a 4-agent interview evaluation system. Create a CONCISE professional justification
        for the candidate's seniority level based on their interview performance.

        INSTRUCTIONS:
        1. Keep the response SHORT (maximum 3-4 sentences)
        2. Explain WHY the candidate received this seniority level
        3. Mention key strengths and any significant weaknesses
        4. Be professional and direct

        IMPORTANT CONTEXT:
        - Final seniority: {final_seniority.value}
        - Performance score: {percentage_score:.1f}% (based on {valid_responses} valid responses)
        - Seniority distribution: {seniority_counts}
        - Threshold applied: {"Yes" if percentage_score < 70.0 and base_seniority != final_seniority else "No"}

        Create a BRIEF justification (3-4 sentences maximum) that explains the seniority assignment.
        """

        summary_data = {
            "individual_evaluations": [eval.model_dump() for eval in request.individual_evaluations],
            "final_seniority": final_seniority.value,
            "percentage_score": percentage_score,
            "valid_responses": valid_responses,
            "dont_know_responses": dont_know_responses,
            "seniority_distribution": seniority_counts,
            "base_seniority": base_seniority.value,
            "threshold_applied": percentage_score < 70.0 and base_seniority != final_seniority
        }

        user_msg = HumanMessage(content=json.dumps(summary_data, ensure_ascii=False))

        # Get LLM-generated justification
        try:
            from pydantic import BaseModel

            class EvaluationSummary(BaseModel):
                justification: str

            schema_text = get_related_class_definitions(EvaluationSummary)

            llm_result = inference_with_fallback(
                task_prompt=task_prompt,
                model_schema=EvaluationSummary,
                user_messages=[user_msg],
                model_schema_text=schema_text,
                models_order=MODELS_CONFIG["default_models_order"],
            )

            if llm_result and llm_result.justification:
                evaluation_summary = llm_result.justification
            else:
                # Fallback summary if LLM fails
                evaluation_summary = f"""
                The candidate demonstrated {final_seniority.value}-level competency based on their interview performance.

                Performance Analysis:
                - Evaluated {valid_responses} valid responses out of {total_questions} total questions
                - Achieved {percentage_score:.1f}% performance score
                - Seniority distribution: {seniority_counts['senior']} senior, {seniority_counts['mid']} mid, {seniority_counts['junior']} junior responses

                {"The seniority was downgraded from " + base_seniority.value + " to " + final_seniority.value + " due to the 70% threshold rule." if percentage_score < 70.0 and base_seniority != final_seniority else "The performance met the requirements for " + final_seniority.value + " level competency."}
                """
        except Exception as e:
            logger.warning(f"AGENT4: LLM summary generation failed: {e}")
            evaluation_summary = f"""
            The candidate demonstrated {final_seniority.value}-level competency based on their interview performance.

            Performance Analysis:
            - Evaluated {valid_responses} valid responses out of {total_questions} total questions
            - Achieved {percentage_score:.1f}% performance score
            - Seniority distribution: {seniority_counts['senior']} senior, {seniority_counts['mid']} mid, {seniority_counts['junior']} junior responses

            {"The seniority was downgraded from " + base_seniority.value + " to " + final_seniority.value + " due to the 70% threshold rule." if percentage_score < 70.0 and base_seniority != final_seniority else "The performance met the requirements for " + final_seniority.value + " level competency."}
            """

        # Create the final evaluation result using calculated values
        final_evaluation = FourAgentEvaluationResult(
            overall_seniority=final_seniority,
            individual_evaluations=request.individual_evaluations,
            total_questions=total_questions,
            valid_responses=valid_responses,
            invalid_responses=invalid_responses,
            percentage_of_correct_answers=round(percentage_score, 1),
            seniority_distribution=seniority_counts,
            evaluation_summary=evaluation_summary.strip(),
            agent_processing_notes=f"Agent 4 completed evaluation with {valid_responses}/{total_questions} valid responses"
        )

        logger.info(f"AGENT4: Created final evaluation - {final_seniority.value} ({percentage_score:.1f}%)")
        return Agent4Response(
            final_evaluation=final_evaluation,
            success=True
        )

    except Exception as e:
        logger.error(f"AGENT4: Error during evaluation: {str(e)}")
        # Create error fallback
        error_evaluation = FourAgentEvaluationResult(
            overall_seniority=Seniority.NA,
            individual_evaluations=request.individual_evaluations if request.individual_evaluations else [],
            total_questions=len(request.individual_evaluations) if request.individual_evaluations else 0,
            valid_responses=0,
            invalid_responses=len(request.individual_evaluations) if request.individual_evaluations else 0,
            percentage_of_correct_answers=0.0,
            seniority_distribution={"senior": 0, "mid": 0, "junior": 0, "n/a": len(request.individual_evaluations) if request.individual_evaluations else 0},
            evaluation_summary=f"Error during evaluation: {str(e)}",
            agent_processing_notes=f"Agent 4 failed with error: {str(e)}"
        )
        return Agent4Response(
            final_evaluation=error_evaluation,
            success=False,
            error_message=str(e)
        )

    except Exception as e:
        logger.error(f"AGENT4: Error creating overall evaluation for interview {request.interview_id}: {str(e)}")
        # Calculate basic statistics for fallback
        total_questions = len(request.individual_evaluations)
        valid_responses = sum(1 for eval in request.individual_evaluations if eval.is_valid_response)
        invalid_responses = total_questions - valid_responses

        fallback_evaluation = FourAgentEvaluationResult(
            overall_seniority=Seniority.NA,
            individual_evaluations=request.individual_evaluations,
            total_questions=total_questions,
            valid_responses=valid_responses,
            invalid_responses=invalid_responses,
            percentage_of_correct_answers=0.0,
            seniority_distribution={"senior": 0, "mid": 0, "junior": 0, "n/a": total_questions},
            evaluation_summary=f"Error during evaluation: {str(e)}",
            agent_processing_notes=f"Agent 4 failed with error: {str(e)}"
        )
        return Agent4Response(
            final_evaluation=fallback_evaluation,
            success=False,
            error_message=str(e)
        )


def four_agent_evaluate_interview(request: FourAgentEvaluationRequest, extracted_answers: list = None) -> FourAgentEvaluationResult:
    """
    Main orchestrator for the 4-agent interview evaluation system.

    This function coordinates all 4 agents to provide a comprehensive evaluation
    that properly handles invalid responses like "mmmmmm".

    Args:
        request: FourAgentEvaluationRequest containing interview_id and options
        extracted_answers: Optional list of pre-extracted answers (skips Agent 1 if provided)

    Returns:
        FourAgentEvaluationResult with complete evaluation
    """
    try:
        logger.info(f"4-AGENT ORCHESTRATOR: Starting evaluation for interview {request.interview_id}")

        # Get interview data
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.transcript_tec, i.position_id, i.candidate_id,
                       c.candidate_info, p.position_info
                FROM interviews i
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                JOIN positions_smarthr p ON i.position_id = p.id
                WHERE i.id = %s
                """,
                (request.interview_id,)
            )
            row = cur.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail="Interview not found")

        transcript, position_id, candidate_id, candidate_info, position_info = row

        if not transcript or not transcript.strip():
            raise HTTPException(status_code=400, detail="No transcript available for evaluation")

        logger.info(f"4-AGENT ORCHESTRATOR: Retrieved interview data - transcript length: {len(transcript)}")

        # AGENT 1: Parse transcript (or use pre-extracted answers)
        if extracted_answers:
            logger.info(f"4-AGENT ORCHESTRATOR: SKIPPING Agent 1 - using {len(extracted_answers)} pre-extracted answers")

            # Convert extracted answers to Q&A pairs format
            qa_pairs = []
            for i, answer in enumerate(extracted_answers, 1):
                # Determine if the answer is valid
                is_valid = answer not in ["Invalid transcript", ""] and not (
                    answer.lower().strip() in ["i don't know", "i dont know", "mmmmmm", "uhhhh"]
                )

                qa_pairs.append(TranscriptQAPair(
                    question_number=i,
                    question_text=f"Question {i}",  # We don't have the actual question text from extraction
                    answer_text=answer,
                    response_quality="valid" if is_valid else "invalid",
                    has_valid_response=is_valid
                ))

            # Calculate valid count
            valid_count = sum(1 for qa in qa_pairs if qa.has_valid_response)

            # Create a mock Agent 1 response
            parsed_transcript = ParsedTranscript(
                qa_pairs=qa_pairs,
                total_questions_found=len(qa_pairs),
                parsing_notes=f"Pre-extracted answers converted to Q&A pairs. {valid_count} valid responses out of {len(qa_pairs)} total."
            )
            agent1_response = Agent1Response(
                parsed_transcript=parsed_transcript,
                success=True
            )

            logger.info(f"4-AGENT ORCHESTRATOR: Pre-extracted conversion - {len(qa_pairs)} Q&A pairs, {valid_count} valid responses")

        else:
            logger.info(f"4-AGENT ORCHESTRATOR: Running Agent 1 - parsing transcript")
            agent1_request = Agent1Request(
                transcript=transcript,
                interview_id=request.interview_id
            )
            agent1_response = agent1_parse_transcript(agent1_request)

            if not agent1_response.success:
                logger.error(f"4-AGENT ORCHESTRATOR: Agent 1 failed for interview {request.interview_id}")
                logger.error(f"4-AGENT ORCHESTRATOR: Agent 1 error: {agent1_response.error_message}")
                logger.error(f"4-AGENT ORCHESTRATOR: Transcript preview: {transcript[:500]}...")
                raise RuntimeError(f"Agent 1 failed: {agent1_response.error_message}")

            # Log Agent 1 success details
            qa_pairs = agent1_response.parsed_transcript.qa_pairs
            valid_count = sum(1 for qa in qa_pairs if qa.has_valid_response)
            logger.info(f"4-AGENT ORCHESTRATOR: Agent 1 success - {len(qa_pairs)} Q&A pairs, {valid_count} valid responses")

        # AGENT 2: Extract expected responses
        agent2_request = Agent2Request(
            parsed_transcript=agent1_response.parsed_transcript,
            position_id=position_id,
            interview_id=request.interview_id
        )
        agent2_response = agent2_extract_expected_responses(agent2_request)

        if not agent2_response.success:
            raise RuntimeError(f"Agent 2 failed: {agent2_response.error_message}")

        # Check if Agent 2 found any valid questions to evaluate
        if not agent2_response.expected_responses.responses:
            logger.warning(f"4-AGENT ORCHESTRATOR: No valid questions to evaluate - Agent 2 found no matching questions")

            # Create a "no data" evaluation
            no_data_evaluation = {
                "overall_seniority": "n/a",
                "percentage_of_match": 0.0,
                "explanation": "No valid interview questions found to evaluate. All questions in the transcript were either not asked or contained invalid responses."
            }

            # Store the no-data evaluation
            with get_cursor() as cur:
                cur.execute(
                    """
                    UPDATE interviews
                    SET interview_data = %s,
                        updated_at = NOW()
                    WHERE id = %s
                    """,
                    (Json(no_data_evaluation), request.interview_id)
                )

            logger.info(f"4-AGENT ORCHESTRATOR: Created no-data evaluation for interview {request.interview_id}")

            # Return a basic result structure
            return FourAgentEvaluationResult(
                overall_seniority=Seniority.NA,
                individual_evaluations=[],
                total_questions=len(agent1_response.parsed_transcript.qa_pairs),
                valid_responses=0,
                invalid_responses=len(agent1_response.parsed_transcript.qa_pairs),
                percentage_of_correct_answers=0.0,
                seniority_distribution={"senior": 0, "mid": 0, "junior": 0, "n/a": len(agent1_response.parsed_transcript.qa_pairs)},
                evaluation_summary=no_data_evaluation["explanation"],
                agent_processing_notes="No valid questions found to evaluate"
            )

        # AGENT 3: Evaluate individual questions (only those that Agent 2 found valid)
        individual_evaluations = []

        # Only process questions that have expected responses (i.e., were actually asked)
        for expected_response in agent2_response.expected_responses.responses:
            # Find the corresponding qa_pair
            qa_pair = None
            for qa in agent1_response.parsed_transcript.qa_pairs:
                if qa.question_number == expected_response.question_number:
                    qa_pair = qa
                    break

            if not qa_pair:
                logger.warning(f"4-AGENT ORCHESTRATOR: No qa_pair found for expected response {expected_response.question_number}")
                continue
            agent3_request = Agent3Request(
                qa_pair=qa_pair,
                expected_response=expected_response,
                interview_id=request.interview_id
            )
            agent3_response = agent3_evaluate_individual_question(agent3_request)

            if agent3_response.success:
                individual_evaluations.append(agent3_response.evaluation)
                logger.info(f"4-AGENT ORCHESTRATOR: Agent 3 success for Q{qa_pair.question_number}: {agent3_response.evaluation.detected_seniority.value if agent3_response.evaluation.is_valid_response else 'invalid'}")
            else:
                logger.warning(f"4-AGENT ORCHESTRATOR: Agent 3 failed for question {qa_pair.question_number}: {agent3_response.error_message}")
                # Add the fallback evaluation from the response
                if agent3_response.evaluation:
                    individual_evaluations.append(agent3_response.evaluation)
                    logger.info(f"4-AGENT ORCHESTRATOR: Using fallback evaluation for Q{qa_pair.question_number}")
                else:
                    logger.error(f"4-AGENT ORCHESTRATOR: No evaluation available for Q{qa_pair.question_number}")

        logger.info(f"4-AGENT ORCHESTRATOR: Collected {len(individual_evaluations)} individual evaluations")

        # AGENT 4: Create overall evaluation
        agent4_request = Agent4Request(
            individual_evaluations=individual_evaluations,
            parsed_transcript=agent1_response.parsed_transcript,
            interview_id=request.interview_id,
            position_info=position_info,
            candidate_info=candidate_info
        )
        agent4_response = agent4_create_overall_evaluation(agent4_request)

        if not agent4_response.success:
            raise RuntimeError(f"Agent 4 failed: {agent4_response.error_message}")

        # Convert 4-agent result to old format for frontend compatibility
        legacy_format = {
            "overall_seniority": agent4_response.final_evaluation.overall_seniority.value,
            "percentage_of_match": agent4_response.final_evaluation.percentage_of_correct_answers,
            "explanation": agent4_response.final_evaluation.evaluation_summary
        }

        # Store results in database using legacy format
        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interviews
                SET interview_data = %s,
                    updated_at = NOW()
                WHERE id = %s
                """,
                (Json(legacy_format), request.interview_id)
            )

        logger.info(f"4-AGENT ORCHESTRATOR: Evaluation completed successfully for interview {request.interview_id}")
        logger.info(f"4-AGENT ORCHESTRATOR: Final result - {agent4_response.final_evaluation.overall_seniority} ({agent4_response.final_evaluation.percentage_of_correct_answers}%)")

        return agent4_response.final_evaluation

    except Exception as e:
        logger.error(f"4-AGENT ORCHESTRATOR: Error evaluating interview {request.interview_id}: {str(e)}")
        logger.error(f"4-AGENT ORCHESTRATOR: Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"4-agent evaluation failed: {str(e)}")


def evaluate_interview_with_four_agents(interview_id: str, extracted_answers: list = None) -> FourAgentEvaluationResult:
    """
    Enhanced evaluation function that uses the new 4-agent system.

    This function provides a clean, direct path to the 4-agent evaluation system
    without fallback to legacy evaluation methods.

    Args:
        interview_id: The ID of the interview to evaluate
        extracted_answers: Optional list of pre-extracted answers (skips Agent 1 if provided)

    Returns:
        FourAgentEvaluationResult: Complete 4-agent evaluation result
    """
    logger.info(f"4-AGENT EVALUATION: Starting evaluation for interview {interview_id}")

    if extracted_answers:
        logger.info(f"4-AGENT EVALUATION: Using {len(extracted_answers)} pre-extracted answers (Agent 1 will be skipped)")
    else:
        logger.info(f"4-AGENT EVALUATION: No pre-extracted answers provided, Agent 1 will parse transcript")

    # Direct 4-agent evaluation - no fallback
    request = FourAgentEvaluationRequest(interview_id=interview_id)
    result = four_agent_evaluate_interview(request, extracted_answers)

    logger.info(f"4-AGENT EVALUATION: Evaluation successful - {result.overall_seniority} ({result.percentage_of_correct_answers}%)")
    return result
