import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from models.linkedin import (
    LinkedInSearchRequest,
    LinkedInSearchFilters,
    SchemaTransformationRequest
)
from models.linkedin_config import LinkedInIntegrationConfig, load_linkedin_config
from controllers.linkedin_workflow_orchestrator import (
    LinkedInWorkflowOrchestrator,
    execute_linkedin_candidate_search
)
from utils.linkedin_agent_communication import LinkedInAgentCommunicationProtocol

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/external_source", tags=["External Source Integration"])

# Load configuration
try:
    external_source_config = load_linkedin_config()
    logger.info("External source configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load external source configuration: {str(e)}")
    external_source_config = None


# Request/Response Models for API
class ExternalSourceSearchAPIRequest(BaseModel):
    """API request model for external source people search."""
    keywords: List[str] = Field(..., description="Keywords to search for people")
    location: Optional[str] = Field(None, description="Location filter")
    experience_level: Optional[str] = Field(None, description="Experience level filter")
    skills: Optional[List[str]] = Field(None, description="Skills filter")
    school: Optional[str] = Field(None, description="School/university filter")
    limit: int = Field(default=25, ge=1, le=100, description="Maximum number of results")
    transform_profiles: bool = Field(default=True, description="Whether to transform profiles to smartHR format")
    include_raw_profiles: bool = Field(default=False, description="Whether to include raw LinkedIn profiles in response")


class ExternalSourceSearchOnlyRequest(BaseModel):
    """API request model for search-only operations (no transformation)."""
    keywords: List[str] = Field(..., description="Keywords to search for people")
    location: Optional[str] = Field(None, description="Location filter")
    experience_level: Optional[str] = Field(None, description="Experience level filter")
    skills: Optional[List[str]] = Field(None, description="Skills filter")
    school: Optional[str] = Field(None, description="School/university filter")
    limit: int = Field(default=25, ge=1, le=100, description="Maximum number of results")


class ExternalSourceTransformRequest(BaseModel):
    """API request model for transforming LinkedIn profiles to standard candidate format."""
    profiles: List[Dict[str, Any]] = Field(..., description="LinkedIn profiles to transform to standard format")
    include_quality_assessment: bool = Field(default=True, description="Whether to include quality assessment")


class ExternalSourceWorkflowStatusResponse(BaseModel):
    """Response model for workflow status."""
    status: str
    orchestrator_stats: Dict[str, Any]
    agent_stats: Dict[str, Any]
    timestamp: str


@router.get("/status", response_model=ExternalSourceWorkflowStatusResponse)
async def get_external_source_integration_status():
    """
    Get the status of external source integration system.

    Returns:
        Current status of external source agents and orchestrator
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        orchestrator = LinkedInWorkflowOrchestrator(external_source_config)
        stats = orchestrator.get_workflow_stats()
        
        return ExternalSourceWorkflowStatusResponse(
            status="operational",
            orchestrator_stats=stats["orchestrator_stats"],
            agent_stats=stats["agent_stats"],
            timestamp=stats["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Status check failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Status check failed: {str(e)}"
        )


@router.post("/search")
async def search_external_source_people(request: ExternalSourceSearchAPIRequest):
    """
    Search for external source people/candidates and optionally transform to smartHR format.

    This endpoint orchestrates the complete external source people sourcing workflow:
    1. Search external source for people matching criteria (skills, location, experience, etc.)
    2. Transform profiles to smartHR candidate format (if requested)
    3. Validate and assess quality of results
    4. Return comprehensive results with recommendations

    Args:
        request: External source people search parameters and options

    Returns:
        Complete workflow results including search results, transformations, and quality assessment
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        logger.info(f"External source people search request: {len(request.keywords)} keywords, limit: {request.limit}")

        # Execute the workflow
        workflow_result = await execute_linkedin_candidate_search(
            config=external_source_config,
            keywords=request.keywords,
            location=request.location,
            experience_level=request.experience_level,
            skills=request.skills,
            school=request.school,
            limit=request.limit
        )

        # Customize response based on request options
        if not request.include_raw_profiles and workflow_result.get("search_results"):
            workflow_result["search_results"]["profiles"] = None

        if not request.transform_profiles:
            workflow_result["transformation_results"] = None

        logger.info(
            f"LinkedIn search completed: {workflow_result['workflow_summary']['profiles_found']} profiles found, "
            f"{workflow_result['workflow_summary']['candidates_transformed']} transformed"
        )

        return workflow_result

    except Exception as e:
        logger.error(f"LinkedIn search failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"LinkedIn search failed: {str(e)}"
        )


@router.post("/search-only")
async def search_only_external_source_people(request: ExternalSourceSearchOnlyRequest):
    """
    Search for external source people/candidates WITHOUT transformation.

    This endpoint performs ONLY the search operation:
    1. Search external source for people matching criteria (skills, location, experience, etc.)
    2. Return raw LinkedIn profile data without any transformation
    3. No schema transformation or quality assessment

    Use this endpoint when you only need raw profile data or want to handle transformation separately.

    Args:
        request: External source people search parameters (no transformation options)

    Returns:
        Raw search results with LinkedIn profile data
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        logger.info(f"External source search-only request: {len(request.keywords)} keywords, limit: {request.limit}")

        # Create orchestrator and execute search only
        orchestrator = LinkedInWorkflowOrchestrator(external_source_config)

        # Create search request
        search_request = LinkedInSearchRequest(
            filters=LinkedInSearchFilters(
                keywords=request.keywords,
                location=request.location,
                experience_level=request.experience_level,
                skills=request.skills,
                school=request.school,
                limit=request.limit
            )
        )

        # Execute only the search step
        search_result = await orchestrator.execute_search_step(search_request)

        logger.info(f"Search-only completed: {search_result.total_results} profiles found")

        return {
            "success": True,
            "total_results": search_result.total_results,
            "profiles_returned": search_result.returned_results,
            "profiles": search_result.profiles,
            "search_filters_used": search_result.search_filters_used.model_dump(),
            "search_id": search_result.search_id,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Search-only failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Search-only failed: {str(e)}"
        )


def transform_linkedin_to_standard_format(linkedin_profile: dict) -> dict:
    """
    Transform LinkedIn profile to the standard candidate format shown in interactive feedback.

    Args:
        linkedin_profile: LinkedIn profile data as dict

    Returns:
        Transformed candidate data in the expected format
    """
    from datetime import datetime

    # Helper function to safely get nested values
    def safe_get(data, *keys, default=None):
        for key in keys:
            if isinstance(data, dict) and key in data:
                data = data[key]
            else:
                return default
        return data if data is not None else default

    # Extract location information
    location_data = safe_get(linkedin_profile, 'location', default={})
    city = safe_get(location_data, 'name', default=None)
    country = safe_get(location_data, 'country', default=None)

    # Build personal_info section
    personal_info = {
        "full_name": safe_get(linkedin_profile, 'full_name') or f"{safe_get(linkedin_profile, 'first_name', default='')} {safe_get(linkedin_profile, 'last_name', default='')}".strip() or None,
        "country": country,
        "city": city,
        "address": None,  # LinkedIn doesn't provide detailed addresses
        "phone_number": None,  # LinkedIn doesn't provide phone numbers
        "email": None,  # LinkedIn doesn't provide email addresses
        "linkedin_profile": safe_get(linkedin_profile, 'profile_url'),
        "website": None  # Could be extracted from additional info if available
    }

    # Build education section
    education = []
    linkedin_education = safe_get(linkedin_profile, 'education', default=[])
    for edu in linkedin_education:
        education_entry = {
            "institution_name": safe_get(edu, 'school'),
            "degree": safe_get(edu, 'degree'),
            "field_of_study": safe_get(edu, 'field_of_study'),
            "start_date": str(safe_get(edu, 'start_year')) if safe_get(edu, 'start_year') else None,
            "end_date": str(safe_get(edu, 'end_year')) if safe_get(edu, 'end_year') else None,
            "location": None,  # LinkedIn education doesn't always have location
            "description": safe_get(edu, 'description')
        }
        education.append(education_entry)

    # Build work_experience section
    work_experience = []
    linkedin_experience = safe_get(linkedin_profile, 'experience', default=[])
    for exp in linkedin_experience:
        # Extract company name
        company_data = safe_get(exp, 'company', default={})
        company_name = safe_get(company_data, 'name') if isinstance(company_data, dict) else str(company_data) if company_data else None

        # Format dates
        start_date = safe_get(exp, 'start_date')
        end_date = safe_get(exp, 'end_date')
        if safe_get(exp, 'is_current'):
            end_date = "Present"

        # Extract location
        exp_location_data = safe_get(exp, 'location', default={})
        exp_location = safe_get(exp_location_data, 'name') if isinstance(exp_location_data, dict) else str(exp_location_data) if exp_location_data else None

        # Build responsibilities list
        responsibilities = []
        description = safe_get(exp, 'description')
        if description:
            # Split description into bullet points if it contains multiple sentences
            if '.' in description and len(description) > 100:
                sentences = [s.strip() for s in description.split('.') if s.strip()]
                responsibilities = [s + '.' for s in sentences if len(s) > 10]
            else:
                responsibilities = [description]

        work_entry = {
            "job_title": safe_get(exp, 'title'),
            "company_name": company_name,
            "start_date": start_date,
            "end_date": end_date,
            "location": exp_location,
            "responsibilities": responsibilities,
            "skills": None  # LinkedIn experience doesn't typically have separate skills
        }
        work_experience.append(work_entry)

    # Build skills section
    skills = []
    linkedin_skills = safe_get(linkedin_profile, 'skills', default=[])
    for skill in linkedin_skills:
        skill_name = safe_get(skill, 'name') if isinstance(skill, dict) else str(skill) if skill else None
        if skill_name:
            skill_entry = {
                "name": skill_name,
                "proficiency_level": None,  # LinkedIn doesn't provide proficiency levels
                "years_of_experience": None  # LinkedIn doesn't provide years of experience
            }
            skills.append(skill_entry)

    # Build languages section
    languages = []
    linkedin_languages = safe_get(linkedin_profile, 'languages', default=[])
    for lang in linkedin_languages:
        if isinstance(lang, str):
            language_entry = {
                "language": lang,
                "proficiency_level": None  # LinkedIn doesn't always provide proficiency levels
            }
            languages.append(language_entry)
        elif isinstance(lang, dict):
            language_entry = {
                "language": safe_get(lang, 'name') or safe_get(lang, 'language'),
                "proficiency_level": safe_get(lang, 'proficiency_level')
            }
            languages.append(language_entry)

    # Build certifications section
    certifications = []
    linkedin_certifications = safe_get(linkedin_profile, 'certifications', default=[])
    for cert in linkedin_certifications:
        if isinstance(cert, str):
            cert_entry = {
                "name": cert,
                "issuing_organization": None,
                "issue_date": None,
                "expiration_date": None,
                "credential_id": None,
                "credential_url": None
            }
            certifications.append(cert_entry)
        elif isinstance(cert, dict):
            cert_entry = {
                "name": safe_get(cert, 'name'),
                "issuing_organization": safe_get(cert, 'issuing_organization') or safe_get(cert, 'authority'),
                "issue_date": safe_get(cert, 'issue_date'),
                "expiration_date": safe_get(cert, 'expiration_date'),
                "credential_id": safe_get(cert, 'credential_id'),
                "credential_url": safe_get(cert, 'credential_url')
            }
            certifications.append(cert_entry)

    # Build the complete response
    response = {
        "personal_info": personal_info,
        "summary": safe_get(linkedin_profile, 'summary'),
        "education": education,
        "work_experience": work_experience,
        "roles": None,  # Not typically available in LinkedIn data
        "skills": skills,
        "soft_skills": None,  # Not typically available in LinkedIn data
        "certifications": certifications,
        "languages": languages,
        "projects": [],  # Could be extracted from additional sections if available
        "references": None  # Not available in LinkedIn data
    }

    return response


@router.post("/transform")
async def transform_external_source_profiles(request: ExternalSourceTransformRequest):
    """
    Transform LinkedIn profiles to the standard candidate format WITHOUT searching.

    This endpoint performs ONLY the transformation operation:
    1. Takes existing LinkedIn profile data as input (LinkedIn schema)
    2. Transforms profiles to the standard candidate format shown in interactive feedback
    3. Returns data in the exact format expected by the system
    4. No search operation performed

    Use this endpoint when you already have LinkedIn profile data and only need transformation.

    Args:
        request: LinkedIn profiles to transform and transformation options

    Returns:
        Transformed candidate data in the standard format with extraction cost information
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        logger.info(f"Transform-only request: {len(request.profiles)} profiles to transform to standard format")

        # Import LinkedIn profile validation
        from models.linkedin import LinkedInProfile
        from datetime import datetime

        transformed_responses = []
        failed_transformations = []
        total_tokens_used = 0

        for i, profile_data in enumerate(request.profiles):
            try:
                # Convert dict to LinkedInProfile object for proper validation
                linkedin_profile = LinkedInProfile(**profile_data)

                # Transform to standard format
                transformed_data = transform_linkedin_to_standard_format(profile_data)

                # Mock extraction cost (since we're not using LLM for transformation)
                extraction_cost = {
                    "prompt_tokens": 1000,  # Mock values
                    "completion_tokens": 500,
                    "total_tokens": 1500,
                    "cost": 0.0075  # Mock cost
                }
                total_tokens_used += extraction_cost["total_tokens"]

                # Build response in the expected format
                response_data = {
                    "response": transformed_data,
                    "extraction_cost": extraction_cost
                }

                transformed_responses.append(response_data)

                logger.debug(f"Successfully transformed profile {i+1}: {linkedin_profile.full_name or 'Unknown'}")

            except Exception as e:
                error_info = {
                    "profile_index": i,
                    "error": str(e),
                    "profile_id": profile_data.get("id", "unknown")
                }
                failed_transformations.append(error_info)
                logger.error(f"Failed to transform profile {i+1}: {str(e)}")
                continue

        logger.info(f"Transform-only completed: {len(transformed_responses)} profiles transformed to standard format")

        # Return the transformed data
        if len(transformed_responses) == 1:
            # Single profile - return the response directly
            return transformed_responses[0]
        else:
            # Multiple profiles - return array with summary
            return {
                "success": True,
                "profiles_input": len(request.profiles),
                "profiles_transformed": len(transformed_responses),
                "failed_transformations": len(failed_transformations),
                "transformed_profiles": transformed_responses,
                "failed_transformation_details": failed_transformations,
                "total_extraction_cost": {
                    "total_tokens": total_tokens_used,
                    "total_cost": sum(r["extraction_cost"]["cost"] for r in transformed_responses)
                },
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Transform-only failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Transform-only failed: {str(e)}"
        )


@router.post("/search/batch")
async def batch_search_external_source_people(
    requests: List[ExternalSourceSearchAPIRequest] = Body(...),
    max_concurrent: int = Query(default=3, ge=1, le=10, description="Maximum concurrent searches")
):
    """
    Execute multiple external source people searches concurrently.

    Args:
        requests: List of external source people search requests
        max_concurrent: Maximum number of concurrent operations

    Returns:
        List of workflow results for each search request
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        if len(requests) > 20:
            raise HTTPException(
                status_code=400,
                detail="Maximum 20 batch requests allowed"
            )

        logger.info(f"Batch external source search: {len(requests)} requests, max concurrent: {max_concurrent}")

        orchestrator = LinkedInWorkflowOrchestrator(external_source_config)
        
        # Convert API requests to internal search requests
        search_requests = []
        for req in requests:
            search_request = LinkedInAgentCommunicationProtocol.create_search_request(
                keywords=req.keywords,
                location=req.location,
                experience_level=req.experience_level,
                skills=req.skills,
                school=req.school,
                limit=req.limit
            )
            search_requests.append(search_request)
        
        # Execute batch processing
        batch_results = await orchestrator.batch_search_and_transform(
            search_requests, max_concurrent=max_concurrent
        )
        
        # Convert results to API format
        api_results = []
        for i, (search_response, transformation_response) in enumerate(batch_results):
            result = {
                "request_index": i,
                "search_results": {
                    "success": search_response.success,
                    "total_results": search_response.total_results,
                    "profiles_returned": len(search_response.profiles),
                    "error_message": search_response.error_message if not search_response.success else None
                },
                "transformation_results": {
                    "success": transformation_response.success if transformation_response else False,
                    "candidates_transformed": len(transformation_response.transformed_candidates) if transformation_response else 0,
                    "transformed_candidates": transformation_response.transformed_candidates if transformation_response else []
                } if transformation_response else None
            }
            api_results.append(result)
        
        successful_searches = sum(1 for result in api_results if result["search_results"]["success"])
        logger.info(f"Batch search completed: {successful_searches}/{len(requests)} successful")
        
        return {
            "batch_id": f"batch_{int(datetime.now().timestamp())}",
            "total_requests": len(requests),
            "successful_requests": successful_searches,
            "results": api_results
        }
        
    except Exception as e:
        logger.error(f"Batch LinkedIn search failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Batch LinkedIn search failed: {str(e)}"
        )


@router.get("/config")
async def get_external_source_configuration():
    """
    Get current external source integration configuration (sanitized).

    Returns:
        Sanitized configuration information
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        # Return sanitized configuration
        config_info = {
            "api_config": {
                "provider": external_source_config.api_config.provider.value,
                "rate_limit_per_minute": external_source_config.api_config.rate_limits.requests_per_minute,
                "timeout_seconds": external_source_config.api_config.rate_limits.timeout_seconds
            },
            "transformation_config": {
                "llm_models_order": external_source_config.transformation_config.llm_models_order,
                "use_fallback_transformation": external_source_config.transformation_config.use_fallback_transformation,
                "confidence_threshold": external_source_config.transformation_config.confidence_threshold
            },
            "agent_config": {
                "agent_timeout_seconds": external_source_config.agent_config.agent_timeout_seconds,
                "enable_parallel_processing": external_source_config.agent_config.enable_parallel_processing,
                "max_concurrent_requests": external_source_config.agent_config.max_concurrent_requests
            }
        }
        
        return config_info
        
    except Exception as e:
        logger.error(f"Configuration retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Configuration retrieval failed: {str(e)}"
        )


@router.post("/test")
async def test_linkedin_integration(
    keywords: List[str] = Body(..., description="Test keywords for people search"),
    limit: int = Body(default=5, ge=1, le=10, description="Test result limit")
):
    """
    Test LinkedIn people search integration with minimal search.

    Args:
        keywords: Keywords to test people search with
        limit: Maximum results for test

    Returns:
        Test results and system status
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        logger.info(f"Testing LinkedIn integration with keywords: {keywords}")

        # Create minimal test request
        test_request = ExternalSourceSearchAPIRequest(
            keywords=keywords,
            limit=limit,
            transform_profiles=True,
            include_raw_profiles=False
        )

        # Execute test search
        test_result = await execute_linkedin_candidate_search(
            config=external_source_config,
            keywords=test_request.keywords,
            limit=test_request.limit
        )
        
        # Create test summary
        test_summary = {
            "test_status": "success" if test_result["success"] else "failed",
            "profiles_found": test_result["workflow_summary"]["profiles_found"],
            "candidates_transformed": test_result["workflow_summary"]["candidates_transformed"],
            "quality_score": test_result["workflow_summary"]["overall_quality_score"],
            "processing_time_ms": test_result["processing_time_ms"],
            "recommendations": test_result["workflow_summary"]["recommendations"]
        }
        
        logger.info(f"LinkedIn integration test completed: {test_summary['test_status']}")
        
        return {
            "test_summary": test_summary,
            "full_results": test_result if test_result["success"] else None,
            "error_details": test_result.get("error_message") if not test_result["success"] else None
        }
        
    except Exception as e:
        logger.error(f"LinkedIn integration test failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"LinkedIn integration test failed: {str(e)}"
        )


# External source API method endpoints
@router.get("/profile/{profile_id}")
async def get_external_source_profile(profile_id: str):
    """
    Get a single external source profile by ID using GET method.

    Uses: GET https://api.linkedin.com/v2/people/{profileId}

    Args:
        profile_id: External source profile ID

    Returns:
        External source profile data or error message
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        from utils.linkedin_client import LinkedInAPIClient

        client = LinkedInAPIClient(external_source_config)
        profile = await client.get_person_by_id(profile_id)

        if profile:
            return {
                "success": True,
                "profile": profile.model_dump(),
                "method": "GET",
                "api_endpoint": f"/v2/people/{profile_id}"
            }
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Profile {profile_id} not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving profile {profile_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve profile: {str(e)}"
        )


@router.post("/profiles/batch")
async def get_external_source_profiles_batch(profile_ids: List[str] = Body(..., description="List of external source profile IDs")):
    """
    Get multiple external source profiles by IDs using BATCH_GET method.

    Uses: GET https://api.linkedin.com/v2/people?ids=List(id1,id2,id3)

    Args:
        profile_ids: List of external source profile IDs (max 50)

    Returns:
        List of external source profiles or error message
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="External source integration not configured"
            )

        if len(profile_ids) > 50:
            raise HTTPException(
                status_code=400,
                detail="Maximum 50 profile IDs allowed per batch request"
            )

        from utils.linkedin_client import LinkedInAPIClient

        client = LinkedInAPIClient(external_source_config)
        profiles = await client.get_people_by_ids(profile_ids)

        return {
            "success": True,
            "profiles": [profile.model_dump() for profile in profiles],
            "requested_count": len(profile_ids),
            "returned_count": len(profiles),
            "method": "BATCH_GET",
            "api_endpoint": "/v2/people?ids=List(...)"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch profile retrieval: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve profiles: {str(e)}"
        )


# Health check endpoint
@router.get("/health")
async def external_source_health_check():
    """
    Health check for external source integration.

    Returns:
        Health status of external source integration components
    """
    try:
        health_status = {
            "status": "healthy",
            "components": {
                "configuration": "loaded" if external_source_config else "missing",
                "search_agent": "ready",
                "transformation_agent": "ready",
                "orchestrator": "ready"
            },
            "timestamp": datetime.now().isoformat()
        }

        if not external_source_config:
            health_status["status"] = "unhealthy"
            health_status["components"]["configuration"] = "missing"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
