#!/usr/bin/env python3
"""
Excel Upload Helper - Simplified interface for uploading and analyzing Excel files.
Provides both API-based and direct processing options.
"""

import os
import sys
import pandas as pd
import requests
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
import time

class ExcelUploadHelper:
    """Helper class for Excel file upload and analysis."""
    
    def __init__(self, api_base_url: str = "http://127.0.0.1:8000"):
        self.api_base_url = api_base_url
        self.api_available = self._check_api_availability()
        
    def _check_api_availability(self) -> bool:
        """Check if the API server is available."""
        try:
            response = requests.get(f"{self.api_base_url}/health/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def upload_excel_file(self, file_path: str, table_name: Optional[str] = None, 
                         description: Optional[str] = None) -> Dict[str, Any]:
        """
        Upload an Excel file for analysis.
        
        Args:
            file_path: Path to the Excel file
            table_name: Optional table name for storage
            description: Optional description of the data
            
        Returns:
            Dictionary with upload results and analysis data
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {"success": False, "error": f"File not found: {file_path}"}
        
        if not file_path.suffix.lower() in ['.xlsx', '.xls', '.csv']:
            return {"success": False, "error": "File must be Excel (.xlsx, .xls) or CSV (.csv)"}
        
        print(f"📁 Processing file: {file_path.name}")
        print(f"📊 API Available: {'Yes' if self.api_available else 'No'}")
        
        if self.api_available:
            return self._upload_via_api(file_path, table_name, description)
        else:
            return self._process_directly(file_path, table_name, description)
    
    def _upload_via_api(self, file_path: Path, table_name: Optional[str], 
                       description: Optional[str]) -> Dict[str, Any]:
        """Upload file via API server."""
        print("🚀 Uploading via API server...")
        
        try:
            # Read file
            if file_path.suffix.lower() == '.csv':
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                content_type = 'text/csv'
            else:
                # Convert Excel to CSV for API upload
                df = pd.read_excel(file_path)
                file_content = df.to_csv(index=False)
                content_type = 'text/csv'
                file_path = file_path.with_suffix('.csv')
            
            # Prepare upload
            files = {
                'file': (file_path.name, file_content, content_type)
            }
            
            data = {
                'data_type': 'financial',
                'table_name': table_name or f"excel_data_{int(time.time())}",
                'description': description or f"Excel file upload: {file_path.name}",
                'overwrite_existing': True,
                'validate_data': True
            }
            
            # Upload
            response = requests.post(f"{self.api_base_url}/upload/", files=files, data=data, timeout=60)
            
            if response.status_code == 201:
                result = response.json()
                upload_id = result.get('upload_id')
                
                print(f"✅ Upload successful! ID: {upload_id}")
                
                # Monitor processing
                if upload_id:
                    status_result = self._monitor_upload_status(upload_id)
                    result.update(status_result)
                
                return result
            else:
                return {
                    "success": False, 
                    "error": f"Upload failed: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            return {"success": False, "error": f"API upload failed: {str(e)}"}
    
    def _monitor_upload_status(self, upload_id: str, max_attempts: int = 30) -> Dict[str, Any]:
        """Monitor upload processing status."""
        print("📋 Monitoring upload status...")
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{self.api_base_url}/upload/{upload_id}/status", timeout=10)
                
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data.get('status')
                    progress = status_data.get('progress', 0)
                    
                    print(f"   Status: {status} ({progress:.1f}%)")
                    
                    if status in ['completed', 'failed', 'cancelled']:
                        return {
                            "processing_status": status,
                            "processing_complete": status == 'completed',
                            "status_details": status_data
                        }
                
                time.sleep(2)
                
            except Exception as e:
                print(f"   Status check error: {e}")
                break
        
        return {
            "processing_status": "timeout",
            "processing_complete": False,
            "status_details": {"message": "Status monitoring timed out"}
        }
    
    def _process_directly(self, file_path: Path, table_name: Optional[str], 
                         description: Optional[str]) -> Dict[str, Any]:
        """Process file directly without API server."""
        print("🔧 Processing file directly...")
        
        try:
            # Read the file
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            print(f"✅ File loaded successfully!")
            print(f"   Rows: {len(df)}")
            print(f"   Columns: {len(df.columns)}")
            print(f"   Columns: {list(df.columns)}")
            
            # Basic data analysis
            analysis_results = self._perform_basic_analysis(df)
            
            # Save processed data
            output_path = self._save_processed_data(df, file_path, table_name)
            
            return {
                "success": True,
                "method": "direct_processing",
                "data_shape": df.shape,
                "columns": list(df.columns),
                "data_types": df.dtypes.to_dict(),
                "analysis": analysis_results,
                "output_file": str(output_path),
                "dataframe": df  # Include the actual dataframe for further analysis
            }
            
        except Exception as e:
            return {"success": False, "error": f"Direct processing failed: {str(e)}"}
    
    def _perform_basic_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform basic analysis on the dataframe."""
        analysis = {
            "summary_stats": {},
            "missing_values": {},
            "data_types": {},
            "sample_data": {}
        }
        
        try:
            # Summary statistics for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                analysis["summary_stats"] = df[numeric_cols].describe().to_dict()
            
            # Missing values
            analysis["missing_values"] = df.isnull().sum().to_dict()
            
            # Data types
            analysis["data_types"] = df.dtypes.astype(str).to_dict()
            
            # Sample data (first 5 rows)
            analysis["sample_data"] = df.head().to_dict('records')
            
        except Exception as e:
            analysis["error"] = f"Analysis error: {str(e)}"
        
        return analysis
    
    def _save_processed_data(self, df: pd.DataFrame, original_path: Path, 
                           table_name: Optional[str]) -> Path:
        """Save processed data to a new file."""
        # Create output filename
        if table_name:
            output_name = f"{table_name}_processed.csv"
        else:
            output_name = f"{original_path.stem}_processed.csv"
        
        output_path = original_path.parent / output_name
        
        # Save as CSV
        df.to_csv(output_path, index=False)
        
        return output_path

def main():
    """Main function for command-line usage."""
    if len(sys.argv) < 2:
        print("Usage: python excel_upload_helper.py <excel_file_path> [table_name] [description]")
        print("Example: python excel_upload_helper.py data.xlsx my_data 'Financial data for analysis'")
        return
    
    file_path = sys.argv[1]
    table_name = sys.argv[2] if len(sys.argv) > 2 else None
    description = sys.argv[3] if len(sys.argv) > 3 else None
    
    helper = ExcelUploadHelper()
    result = helper.upload_excel_file(file_path, table_name, description)
    
    print("\n" + "="*50)
    print("📊 UPLOAD RESULTS")
    print("="*50)
    
    if result.get("success"):
        print("✅ Upload/Processing successful!")
        print(f"   Method: {result.get('method', 'API')}")
        if 'data_shape' in result:
            print(f"   Data shape: {result['data_shape']}")
            print(f"   Columns: {result['columns']}")
        if 'output_file' in result:
            print(f"   Output file: {result['output_file']}")
    else:
        print("❌ Upload/Processing failed!")
        print(f"   Error: {result.get('error')}")

if __name__ == "__main__":
    main()
