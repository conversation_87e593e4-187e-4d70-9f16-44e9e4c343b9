{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OpenAI API Connection Test\n", "\n", "This notebook tests the connection to the OpenAI API using the credentials from your .env file."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Environment Variables\n", "\n", "First, we'll load the environment variables from the .env file using python-dotenv."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: python-dotenv in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (1.0.1)\n", "Requirement already satisfied: langchain-openai in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (0.3.6)\n", "Requirement already satisfied: langchain in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (0.3.19)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.35 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain-openai) (0.3.37)\n", "Requirement already satisfied: openai<2.0.0,>=1.58.1 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain-openai) (1.58.1)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain-openai) (0.8.0)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.6 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (0.3.6)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (0.2.4)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (2.9.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (2.0.36)\n", "Requirement already satisfied: requests<3,>=2 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (3.10.10)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (9.0.0)\n", "Requirement already satisfied: numpy<3,>=1.26.2 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain) (2.1.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.3)\n", "Requirement already satisfied: aiosignal>=1.1.2 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.12.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.15.5)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langsmith<0.4,>=0.1.17->langchain) (0.27.2)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.12)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (4.6.2.post1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (0.5.0)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (4.66.5)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.4 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.23.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from requests<3,>=2->langchain) (3.4.0)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from requests<3,>=2->langchain) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from requests<3,>=2->langchain) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from SQLAlchemy<3,>=1.4->langchain) (3.1.1)\n", "Requirement already satisfied: regex>=2022.1.18 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from tiktoken<1,>=0.7->langchain-openai) (2024.11.6)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.0.6)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.35->langchain-openai) (3.0.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from tqdm>4->openai<2.0.0,>=1.58.1->langchain-openai) (0.4.6)\n", "Requirement already satisfied: propcache>=0.2.0 in c:\\users\\<USER>\\desktop\\vsc\\job\\lumusazure\\lumusai\\venv\\lib\\site-packages (from yarl<2.0,>=1.12.0->aiohttp<4.0.0,>=3.8.3->langchain) (0.2.0)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.3.1 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["# Install required packages if not already installed\n", "!pip install python-dotenv langchain-openai langchain"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API_KEY: ****************************3182\n", "API_VERSION: 2025-01-01-preview\n", "AZURE_ENDPOINT: https://oailumusnpdeu.openai.azure.com/\n", "MODEL: lumus-npd-gpt-4o\n"]}], "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "\n", "# Get API credentials\n", "api_key = os.getenv(\"API_KEY\")\n", "api_version = os.getenv(\"API_VERSION\")\n", "azure_endpoint = os.getenv(\"AZURE_ENDPOINT\")\n", "model = os.getenv(\"MODEL\")\n", "\n", "# Print the variables (with partial masking for security)\n", "print(f\"API_KEY: {'*' * (len(api_key) - 4) + api_key[-4:] if api_key else 'Not found'}\")\n", "print(f\"API_VERSION: {api_version if api_version else 'Not found'}\")\n", "print(f\"AZURE_ENDPOINT: {azure_endpoint if azure_endpoint else 'Not found'}\")\n", "print(f\"MODEL: {model if model else 'Not found'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Test Connection with <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Now we'll test the connection using LangChain, which is what your application uses."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from langchain_openai import AzureChatOpenAI\n", "from langchain_core.messages import HumanMessage\n", "\n", "# Create the Lang<PERSON><PERSON><PERSON> client\n", "llm = AzureChatOpenAI(\n", "    api_key=api_key,\n", "    api_version=api_version,\n", "    azure_endpoint=azure_endpoint,\n", "    model=model\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response from OpenAI:\n", "Hello! Generative AI refers to a type of artificial intelligence that is designed to create or generate new content, such as text, images, audio, video, code, or other forms of data. Unlike traditional AI systems that focus primarily on recognizing patterns or classifying information, generative AI is capable of producing original outputs based on the patterns it has learned during training. The key idea is that it generates outputs that feel human-like and creative.\n", "\n", "### How It Works:\n", "Generative AI typically uses deep learning models such as **neural networks**, particularly **Transformer architectures**, which are highly effective at understanding and producing sequential or structured data. These models are trained on vast datasets to learn the relationships between elements of data—for example, words in sentences, pixels in images, or notes in music.\n", "\n", "Popular frameworks often employ **unsupervised learning** or **reinforcement learning**. Here's an overview of key concepts involved:\n", "\n", "1. **Training**: During training, the AI model learns from large datasets. For text-based generative models (like ChatGPT), it analyzes patterns in language and the context of words to predict the next word or sequence of words.\n", "\n", "2. **Generative Process**: Once trained, the AI can create new data by sampling from the probabilities it has learned during training. For example:\n", "   - In text generation, the model predicts and writes coherent sentences.\n", "   - In image generation, the model may create visuals by assembling shapes, colors, and patterns to resemble realistic or stylized imagery.\n", "\n", "### Types of Generative AI Techniques:\n", "1. **Generative Adversarial Networks (GANs)**:\n", "   - GANs consist of two networks, a **generator** and a **discriminator**, that compete against each other. The generator creates data, while the discriminator evaluates how realistic it looks, helping the generator improve.\n", "   - GANs are often used for generating highly realistic images (e.g., deepfakes).\n", "\n", "2. **Variational Autoencoders (VAEs)**:\n", "   - These models learn to compress input data into a smaller representation (latent space) and then reconstruct it, enabling the generation of similar but new data.\n", "\n", "3. **Transformer-Based Models**:\n", "   - Transformer models, such as **GPT (Generative Pre-trained Transformer)** and **BERT**, are hugely successful for text creation and other related tasks.\n", "   - GPT models, for example, predict the next token in a sequence and are trained on trillions of parameters, enabling them to produce human-like text.\n", "\n", "4. **Diffusion Models**:\n", "   - These models are used in image generation, working by gradually adding noise to sample data and then reversing the process to construct new images from scratch.\n", "\n", "### Applications of Generative AI:\n", "Generative AI has seen widespread adoption across a variety of industries, thanks to its ability to create surprisingly realistic or useful content. Common applications include:\n", "- **Text generation and conversation**: AI chatbots (like ChatGPT), email drafts, creative writing tools, translation, and summarization.\n", "- **Image generation**: AI art platforms (e.g., DALL·E, MidJourney), digital asset creation for gaming or film, and medical imaging.\n", "- **Code generation**: Tools that assist developers by generating code snippets or debugging software.\n", "- **Music and audio creation**: Composing original music, sound effects, or voice synthesis.\n", "- **Video and 3D modeling**: Generating animations, visual effects, or virtual environments.\n", "- **Healthcare**: Drug discovery, medical report generation, and diagnosis support.\n", "- **Marketing and personalization**: Designing targeted ad campaigns, product mockups, or dynamic customer experiences.\n", "\n", "### The Benefits:\n", "- **Creativity Boost**: Generative AI accelerates workflows by proposing ideas or prototypes.\n", "- **Efficiency**: Automates repetitive tasks, saving time and costs.\n", "- **Accessibility**: Makes artistic tools available to anyone, regardless of artistic skills.\n", "- **Scalability**: Can produce vast amounts of content quickly.\n", "\n", "### Challenges and Concerns:\n", "While generative AI offers transformative capabilities, it also raises ethical and practical concerns:\n", "1. **Bias in Outputs**: If trained on biased data, the generated outputs may reinforce stereotypes or misinformation.\n", "2. **Misinformation**: The ability to generate realistic text or media raises concerns about the spread of fake news or impersonation (e.g., deepfakes).\n", "3. **Copyright Issues**: Generative AI models might inadvertently create content too similar to copyrighted works, sparking legal debates.\n", "4. **Usage and Regulation**: Ensuring responsible deployment of generative AI is a key challenge for governments and organizations globally.\n", "\n", "### Key Tools and Platforms:\n", "Some widely recognized generative AI platforms/tools include:\n", "- **OpenAI GPT (e.g., ChatGPT)** for text generation and conversation.\n", "- **DALL-E**, **MidJourney**, and **Stable Diffusion** for image generation.\n", "- **GitHub Copilot** for code generation.\n", "- **AudioLM** and **Jukebox** for music creation.\n", "\n", "In summary, generative AI represents a leap forward in machines’ ability to produce creative outputs, significantly enhancing automation, creativity, and problem-solving capabilities across industries.\n"]}], "source": ["# Test a simple query\n", "response = llm.invoke(\n", "    input=[\n", "        HumanMessage(\n", "            content=[\n", "                {\"type\": \"text\", \"text\": \"Hello! Please explain generative AI.\"}\n", "            ]\n", "        )\n", "    ]\n", ")\n", "\n", "# Print the response\n", "print(\"Response from OpenAI:\")\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Test Structured Output\n", "\n", "Let's also test structured output functionality, which is used in your application."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structured Output:\n", "{\n", "  \"name\": \"<PERSON>\",\n", "  \"age\": 35,\n", "  \"hobbies\": [\n", "    \"hiking\",\n", "    \"reading\",\n", "    \"cooking\"\n", "  ],\n", "  \"address\": \"New York\"\n", "}\n"]}], "source": ["from pydantic import BaseModel, Field\n", "from typing import List, Optional\n", "\n", "# Define a simple Pydantic model\n", "class Person(BaseModel):\n", "    name: str = <PERSON>(..., description=\"The person's full name\")\n", "    age: int = Field(..., description=\"The person's age\")\n", "    hobbies: List[str] = Field(..., description=\"List of the person's hobbies\")\n", "    address: Optional[str] = Field(None, description=\"The person's address if available\")\n", "\n", "# Create a structured output model\n", "structured_llm = llm.with_structured_output(Person, method=\"function_calling\")\n", "\n", "# Test structured output\n", "prompt = \"Extract information about a person named <PERSON> who is 35 years old, lives in New York, and enjoys hiking, reading, and cooking.\"\n", "\n", "result = structured_llm.invoke(prompt)\n", "print(\"Structured Output:\")\n", "print(result.model_dump_json(indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Test Image Processing\n", "\n", "Finally, let's test image processing with a sample base64 image."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using sample base64 image: data:image/png;base64,iVBORw0K...\n"]}], "source": ["# This is a simple function to create a base64 image URL for a small test image\n", "# In a real scenario, you would load an actual image file\n", "def get_sample_base64_image():\n", "    # This is a tiny 1x1 pixel transparent PNG image encoded as base64\n", "    return \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==\"\n", "\n", "# Get sample base64 image\n", "base64_image = get_sample_base64_image()\n", "print(f\"Using sample base64 image: {base64_image[:30]}...\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response from image processing:\n", "This image appears to be entirely red, with no discernible details or objects.\n"]}], "source": ["# Test image processing\n", "try:\n", "    response = llm.invoke(\n", "        [\n", "            HumanMessage(\n", "                content=[\n", "                    {\"type\": \"text\", \"text\": \"What's in this image?\"},\n", "                    {\n", "                        \"type\": \"image_url\",\n", "                        \"image_url\": {\n", "                            \"url\": base64_image,\n", "                            \"detail\": \"auto\",\n", "                        },\n", "                    },\n", "                ]\n", "            )\n", "        ]\n", "    )\n", "    \n", "    print(\"Response from image processing:\")\n", "    print(response.content)\n", "except Exception as e:\n", "    print(f\"Error processing image: {str(e)}\")\n", "    print(\"Note: This might fail if your model doesn't support image processing.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test the get_structured_data_image Function\n", "\n", "Let's implement a simplified version of the get_structured_data_image function to test it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.callbacks import get_openai_callback\n", "\n", "async def get_structured_data_image(structure, base64_image_data, prompt):\n", "    \"\"\"Simplified version of the get_structured_data_image function\"\"\"\n", "    try:\n", "        with get_openai_callback() as callback:\n", "            # Create a model with structured output based on the provided Pydantic model\n", "            structured_model = llm.with_structured_output(structure, method=\"function_calling\")\n", "            \n", "            # Invoke the model with both text prompt and image\n", "            response = await structured_model.ainvoke(\n", "                [\n", "                    HumanMessage(\n", "                        content=[\n", "                            {\"type\": \"text\", \"text\": prompt},\n", "                            {\n", "                                \"type\": \"image_url\",\n", "                                \"image_url\": {\n", "                                    \"url\": base64_image_data,\n", "                                    \"detail\": \"auto\",\n", "                                },\n", "                            },\n", "                        ]\n", "                    )\n", "                ]\n", "            )\n", "            \n", "        return {\n", "            \"response\": response.model_dump(),\n", "            \"token_usage\": {\n", "                \"prompt_tokens\": callback.prompt_tokens,\n", "                \"completion_tokens\": callback.completion_tokens,\n", "                \"total_tokens\": callback.total_tokens,\n", "                \"cost\": callback.total_cost\n", "            }\n", "        }\n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "        return {\"error\": str(e)}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a simple test model\n", "class ImageContent(BaseModel):\n", "    description: str = Field(..., description=\"A description of what's in the image\")\n", "    objects: List[str] = Field(..., description=\"List of objects detected in the image\")\n", "    colors: List[str] = Field(..., description=\"List of dominant colors in the image\")\n", "\n", "# Test prompt\n", "test_prompt = \"Analyze this image and extract structured information about its content.\"\n", "\n", "# Import asyncio to run the async function\n", "import asyncio\n", "\n", "# Run the test\n", "print(\"Testing get_structured_data_image function...\")\n", "try:\n", "    result = await get_structured_data_image(ImageContent, base64_image, test_prompt)\n", "    print(\"Result:\")\n", "    print(result)\n", "except Exception as e:\n", "    print(f\"Error: {str(e)}\")\n", "    print(\"Note: This might fail if your model doesn't support image processing or structured output.\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}