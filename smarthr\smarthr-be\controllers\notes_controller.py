from contextlib import contextmanager
import json
from typing import List, Optional
import psycopg2
from psycopg2.extras import <PERSON><PERSON>, RealDictCursor
from psycopg2 import sql
from core.config import settings
from models.note import Note, NoteCreate, NoteUpdate
from fastapi import HTTPException


# DB helper to get cursor
@contextmanager
def get_cursor():
    print("Connecting to the database..." + settings.DATABASE_URL)
    # Establish a connection to the database
    conn = psycopg2.connect(settings.DATABASE_URL)
    try:
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                yield cur
    finally:
        conn.close()


# Notes controller class
class NotesController:
    def __init__(self):
        self.table_name = "candidate_notes"

    # Create a new note
    def create_note(self, note: NoteCreate) -> Note:
        with get_cursor() as cur:
            query = sql.SQL(
                "INSERT INTO {table} (candidate_id, notes, created_by, created_at) "
                "VALUES (%s, %s, %s, %s) RETURNING id, candidate_id, notes, created_by, created_at;"
            ).format(table=sql.Identifier(self.table_name))
            cur.execute(query, (
                note.candidate_id,
                Json(note.notes),
                note.created_by,
                note.created_at
            ))
            created_note = cur.fetchone()
            return Note(**created_note)

    # Update an existing note
    def update_note(self, note_id: str, note_update: NoteUpdate) -> Note:
        with get_cursor() as cur:
            query = sql.SQL(
                "UPDATE {table} SET notes = %s, updated_by = %s, updated_at = %s "
                "WHERE id = %s RETURNING *;"
            ).format(table=sql.Identifier(self.table_name))
            cur.execute(query, (
                Json(note_update.notes),
                note_update.updated_by,
                note_update.updated_at,
                note_id
            ))
            updated_note = cur.fetchone()
            if updated_note:
                return Note(**updated_note)
            else:
                raise HTTPException(status_code=404, detail="Note not found")

    # Get a note by candidate ID
    def get_notes_by_candidate(self, candidate_id: str) -> List[Note]:
        with get_cursor() as cur:
            query = sql.SQL(
                "SELECT id, candidate_id, notes, created_by, created_at, updated_by, updated_at FROM {table} WHERE candidate_id = %s;"
            ).format(table=sql.Identifier(self.table_name))
            cur.execute(query, (candidate_id,))
            notes = cur.fetchall()
            return [Note(**note) for note in notes] if notes else []

    # Get a note by ID
    def get_note_by_id(self, note_id: str) -> Optional[Note]:
        with get_cursor() as cur:
            query = sql.SQL(
                "SELECT id, candidate_id, notes, created_by, created_at, updated_by, updated_at FROM {table} WHERE id = %s;"
            ).format(table=sql.Identifier(self.table_name))
            cur.execute(query, (note_id,))
            note = cur.fetchone()
            return Note(**note) if note else None

    # Delete a note by ID
    def delete_note(self, note_id: str) -> bool:
        with get_cursor() as cur:
            query = sql.SQL(
                "DELETE FROM {table} WHERE id = %s;"
            ).format(table=sql.Identifier(self.table_name))
            cur.execute(query, (note_id,))
            return cur.rowcount > 0
