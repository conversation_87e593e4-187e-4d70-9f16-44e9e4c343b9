# Software Requirements Specification (SRS)
## LumusAI - Intelligent Document Processing Service

**Document Version:** 1.0  
**Date:** December 2024  
**Prepared by:** Development Team  
**Project:** LumusAI  

---

## Table of Contents

1. [Introduction](#1-introduction)
2. [Overall Description](#2-overall-description)
3. [Specific Requirements](#3-specific-requirements)
4. [Supporting Information](#4-supporting-information)

---

## 1. Introduction

### 1.1 Purpose

This Software Requirements Specification (SRS) document describes the functional and non-functional requirements for LumusAI, an intelligent document processing service that leverages artificial intelligence to extract, analyze, and structure information from various document types including CVs/resumes, legal documents, and invoices.

The intended audience for this document includes:
- Development team members
- Project stakeholders
- Quality assurance engineers
- System administrators
- End users and integrators

### 1.2 Scope

LumusAI is a cloud-based document processing service that provides:

**Product Name:** LumusAI  
**Product Components:**
- **SmartHR Module:** CV/Resume processing and analysis
- **Papirus Module:** Legal document processing (Tutela documents)
- **Facturius Module:** Invoice and billing document processing

**Key Benefits:**
- Automated extraction of structured data from unstructured documents
- Multi-format document support (PDF, DOCX, Excel, images)
- RESTful API for easy integration
- Real-time processing with concurrent task management
- Comprehensive health monitoring and maintenance capabilities

**Scope Limitations:**
- Document processing is limited to supported formats and types
- AI model accuracy depends on document quality and format
- Processing capacity is governed by configured concurrency limits

### 1.3 Definitions, Acronyms, and Abbreviations

| Term | Definition |
|------|------------|
| API | Application Programming Interface |
| AI | Artificial Intelligence |
| CV | Curriculum Vitae (Resume) |
| DOCX | Microsoft Word Document Format |
| FastAPI | Modern web framework for building APIs with Python |
| HTTP | Hypertext Transfer Protocol |
| JSON | JavaScript Object Notation |
| LLM | Large Language Model |
| OCR | Optical Character Recognition |
| PDF | Portable Document Format |
| REST | Representational State Transfer |
| SRS | Software Requirements Specification |
| Tutela | Colombian legal protection mechanism |
| UUID | Universally Unique Identifier |

### 1.4 References

- IEEE Std 830-1998: IEEE Recommended Practice for Software Requirements Specifications
- FastAPI Documentation: https://fastapi.tiangolo.com/
- OpenAI API Documentation
- Azure OpenAI Service Documentation
- LangChain Documentation

### 1.5 Overview

This SRS document is organized into four main sections:

1. **Introduction:** Provides context and scope
2. **Overall Description:** Describes the product from a high-level perspective
3. **Specific Requirements:** Details functional and non-functional requirements
4. **Supporting Information:** Contains appendices and additional resources

---

## 2. Overall Description

### 2.1 Product Perspective

LumusAI is a standalone microservice designed to integrate with existing business systems through RESTful APIs. The system architecture consists of:

**System Components:**
- **Web API Layer:** FastAPI-based REST endpoints
- **Processing Engine:** Document-specific processors for different document types
- **AI Integration Layer:** OpenAI/Azure OpenAI integration via LangChain
- **Utility Services:** Helper functions for document conversion and text extraction
- **Monitoring System:** Health checks and task management

**External Interfaces:**
- OpenAI/Azure OpenAI API for language model processing
- Client applications via HTTP REST API
- File storage systems for document input
- Logging and monitoring systems

### 2.2 Product Functions

#### 2.2.1 SmartHR Module Functions
- Extract personal information from CVs/resumes
- Parse education history and qualifications
- Analyze work experience and calculate durations
- Identify and categorize technical and soft skills
- Generate professional summaries
- Support multiple document formats (PDF, DOCX)

#### 2.2.2 Papirus Module Functions
- Process Colombian Tutela legal documents
- Extract case information and legal details
- Handle different Tutela document types:
  - Tutela Contestación (Response)
  - Tutela Fallo (Ruling)
  - Tutela Desacato (Contempt)
  - Tutela Correo (Email communications)
- Structure legal document content for analysis

#### 2.2.3 Facturius Module Functions
- Process various invoice types:
  - Purchase invoices
  - Utility bills (electricity, water, gas)
  - Delivery tickets
  - Credit notes
- Extract billing information and line items
- Calculate totals and validate amounts
- Handle multi-page invoice documents
- Support multiple formats (PDF, Excel, images)

#### 2.2.4 System Management Functions
- Concurrent task processing with configurable limits
- Real-time health monitoring
- Task queue management
- Error handling and retry mechanisms
- Logging and audit trails
- Maintenance and cleanup operations

### 2.3 User Characteristics

**Primary Users:**
- **System Integrators:** Technical users implementing LumusAI in business systems
- **API Consumers:** Applications and services that send documents for processing
- **System Administrators:** Personnel responsible for deployment and monitoring

**User Expertise Levels:**
- **Technical Users:** Familiar with REST APIs and JSON data formats
- **Business Users:** End users of integrated systems (indirect interaction)
- **Administrative Users:** System administration and monitoring capabilities

### 2.4 Constraints

#### 2.4.1 Technical Constraints
- Python 3.12.7 runtime environment required
- Docker containerization for deployment
- OpenAI/Azure OpenAI API dependency
- Memory and processing limitations for large documents
- Network connectivity requirements for AI model access

#### 2.4.2 Regulatory Constraints
- Data privacy compliance (GDPR, local regulations)
- Document retention and processing policies
- Security requirements for sensitive document handling

#### 2.4.3 Operational Constraints
- Concurrent processing limits (configurable, default: 4 tasks)
- API rate limits imposed by OpenAI services
- Document size and format limitations
- Processing time constraints for real-time applications

### 2.5 Assumptions and Dependencies

#### 2.5.1 Assumptions
- Users have valid OpenAI/Azure OpenAI API credentials
- Input documents are in supported formats and readable quality
- Network connectivity is stable for AI model API calls
- Sufficient system resources for concurrent processing

#### 2.5.2 Dependencies
- **OpenAI/Azure OpenAI API:** Core dependency for AI processing
- **LangChain Framework:** AI model integration and management
- **FastAPI Framework:** Web API implementation
- **PyMuPDF/fitz:** PDF processing capabilities
- **python-docx:** DOCX document processing
- **Pandas/OpenPyXL:** Excel file processing

---

## 3. Specific Requirements

### 3.1 Functional Requirements

#### 3.1.1 Document Processing Requirements

**FR-1: Document Upload and Processing**
- **FR-1.1:** The system SHALL accept document uploads via HTTP POST requests
- **FR-1.2:** The system SHALL support file uploads and URL-based document references
- **FR-1.3:** The system SHALL validate document formats before processing
- **FR-1.4:** The system SHALL return structured JSON responses for all processed documents

**FR-2: CV/Resume Processing (SmartHR)**
- **FR-2.1:** The system SHALL extract personal information including name, contact details, and location
- **FR-2.2:** The system SHALL parse education history with institutions, degrees, and dates
- **FR-2.3:** The system SHALL analyze work experience and calculate employment durations
- **FR-2.4:** The system SHALL identify technical skills and soft skills
- **FR-2.5:** The system SHALL generate professional summaries
- **FR-2.6:** The system SHALL support multi-page CV processing with data continuation

**FR-3: Legal Document Processing (Papirus)**
- **FR-3.1:** The system SHALL process Tutela Contestación documents
- **FR-3.2:** The system SHALL process Tutela Fallo documents
- **FR-3.3:** The system SHALL process Tutela Desacato documents
- **FR-3.4:** The system SHALL process Tutela email communications
- **FR-3.5:** The system SHALL extract case numbers, dates, and legal entities
- **FR-3.6:** The system SHALL structure legal content for analysis

**FR-4: Invoice Processing (Facturius)**
- **FR-4.1:** The system SHALL process purchase invoices with line items
- **FR-4.2:** The system SHALL process utility bills (electricity, water, gas)
- **FR-4.3:** The system SHALL extract invoice numbers, dates, and amounts
- **FR-4.4:** The system SHALL calculate totals and validate billing amounts
- **FR-4.5:** The system SHALL handle multi-page invoice documents
- **FR-4.6:** The system SHALL support delivery tickets and credit notes

#### 3.1.2 System Management Requirements

**FR-5: Task Management**
- **FR-5.1:** The system SHALL implement concurrent processing with configurable limits
- **FR-5.2:** The system SHALL maintain a queue for pending document processing tasks
- **FR-5.3:** The system SHALL provide task status tracking and monitoring
- **FR-5.4:** The system SHALL implement automatic cleanup of completed tasks

**FR-6: Health Monitoring**
- **FR-6.1:** The system SHALL provide health check endpoints
- **FR-6.2:** The system SHALL report system metrics (CPU, memory usage)
- **FR-6.3:** The system SHALL display active and queued task information
- **FR-6.4:** The system SHALL track processing times and performance metrics

**FR-7: Error Handling and Recovery**
- **FR-7.1:** The system SHALL implement retry mechanisms for failed AI API calls
- **FR-7.2:** The system SHALL provide detailed error messages for processing failures
- **FR-7.3:** The system SHALL handle document format validation errors gracefully
- **FR-7.4:** The system SHALL log all errors for debugging and monitoring

### 3.2 External Interface Requirements

#### 3.2.1 User Interface Requirements
- **UI-1:** The system SHALL provide a RESTful API interface (no graphical user interface required)
- **UI-2:** API documentation SHALL be available via interactive Swagger/OpenAPI interface
- **UI-3:** All API responses SHALL be in JSON format with consistent structure

#### 3.2.2 Hardware Interface Requirements
- **HI-1:** The system SHALL run on standard x86-64 server hardware
- **HI-2:** The system SHALL support containerized deployment via Docker
- **HI-3:** Minimum hardware requirements: 2GB RAM, 1GB storage, network connectivity

#### 3.2.3 Software Interface Requirements
- **SI-1:** The system SHALL integrate with OpenAI/Azure OpenAI API services
- **SI-2:** The system SHALL support Python 3.12.7 runtime environment
- **SI-3:** The system SHALL use FastAPI framework for web service implementation
- **SI-4:** The system SHALL integrate with LangChain for AI model management

#### 3.2.4 Communication Interface Requirements
- **CI-1:** The system SHALL communicate via HTTP/HTTPS protocols
- **CI-2:** The system SHALL support CORS for cross-origin requests
- **CI-3:** The system SHALL implement proper HTTP status codes and error responses
- **CI-4:** The system SHALL support multipart/form-data for file uploads

### 3.3 Performance Requirements

#### 3.3.1 Response Time Requirements
- **PR-1:** Document processing SHALL complete within 60 seconds for standard documents
- **PR-2:** Health check endpoints SHALL respond within 2 seconds
- **PR-3:** API endpoints SHALL acknowledge requests within 5 seconds

#### 3.3.2 Throughput Requirements
- **PR-4:** The system SHALL support configurable concurrent processing (default: 4 tasks)
- **PR-5:** The system SHALL process at least 100 documents per hour under normal load
- **PR-6:** The system SHALL handle multiple simultaneous API requests

#### 3.3.3 Capacity Requirements
- **PR-7:** The system SHALL support documents up to 50MB in size
- **PR-8:** The system SHALL handle multi-page documents (up to 100 pages)
- **PR-9:** The system SHALL maintain performance with up to 50 queued tasks

### 3.4 Design Constraints

#### 3.4.1 Standards Compliance
- **DC-1:** The system SHALL follow REST API design principles
- **DC-2:** The system SHALL use JSON for data exchange
- **DC-3:** The system SHALL implement proper HTTP status codes
- **DC-4:** The system SHALL follow OpenAPI 3.0 specification for documentation

#### 3.4.2 Implementation Constraints
- **DC-5:** The system SHALL be implemented in Python 3.12.7
- **DC-6:** The system SHALL use FastAPI framework for web services
- **DC-7:** The system SHALL use Pydantic for data validation and serialization
- **DC-8:** The system SHALL support Docker containerization

### 3.5 Software System Attributes

#### 3.5.1 Reliability
- **SA-1:** The system SHALL have 99.5% uptime availability
- **SA-2:** The system SHALL implement automatic error recovery mechanisms
- **SA-3:** The system SHALL provide graceful degradation during high load

#### 3.5.2 Security
- **SA-4:** The system SHALL validate all input data and file uploads
- **SA-5:** The system SHALL implement proper error handling without exposing sensitive information
- **SA-6:** The system SHALL support secure API key management for OpenAI integration
- **SA-7:** The system SHALL log security-relevant events

#### 3.5.3 Maintainability
- **SA-8:** The system SHALL provide comprehensive logging for debugging
- **SA-9:** The system SHALL implement modular architecture for easy updates
- **SA-10:** The system SHALL include health monitoring and diagnostic capabilities
- **SA-11:** The system SHALL support configuration via environment variables

#### 3.5.4 Portability
- **SA-12:** The system SHALL run on Linux, Windows, and macOS platforms
- **SA-13:** The system SHALL support Docker deployment across different environments
- **SA-14:** The system SHALL minimize platform-specific dependencies

---

## 4. Supporting Information

### 4.1 Appendices

#### Appendix A: API Endpoint Reference

**Primary Endpoints:**
- `POST /process` - Main document processing endpoint
- `GET /health` - System health and status monitoring
- `GET /maintenance/tasks` - Task maintenance and cleanup

**Supported Actions:**
- `cv` - CV/Resume processing
- `invoice` - Invoice processing
- `tutela_contestacion` - Legal document processing (Contestación)
- `tutela_fallo` - Legal document processing (Fallo)
- `tutela_desacato` - Legal document processing (Desacato)
- `tutela_correo` - Legal email processing

#### Appendix B: Data Models

**CV Model Components:**
- PersonalInfo, Education, WorkExperience, Skills, SoftSkills

**Invoice Model Components:**
- InvoiceBase, TotalBill, ProductItem, ConsumptionDetail, AccountSummary

**Legal Document Models:**
- TutelaContestacionDocument, TutelaFalloDocument, CorreoTutela

#### Appendix C: Environment Configuration

**Required Environment Variables:**
- `API_KEY` - OpenAI/Azure API key
- `API_VERSION` - API version
- `AZURE_ENDPOINT` - Azure endpoint URL
- `MODEL` - AI model name

**Optional Configuration:**
- `MAX_CONCURRENT_TASKS` - Concurrency limit (default: 4)
- `ROOT_PATH` - Application root path

### 4.2 Index

This document serves as the complete requirements specification for LumusAI version 1.0. For technical implementation details, refer to the project documentation and source code repository.

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Quarterly or upon major system changes
