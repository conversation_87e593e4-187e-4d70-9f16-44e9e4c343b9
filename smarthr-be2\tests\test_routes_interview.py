import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from main import app
from models.interview import (
    ProcessType,
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ParaphrasedAnswerDetail,
    Seniority,
    EvaluationResult,
    QuestionEvaluation,
    QA_model,
    QuestionAnswer
)
from models.models import SingleQuestions, Position
from models.interview import Interview, InterviewTec, InterviewHr, InterviewStatus
from uuid import uuid4
from datetime import datetime

client = TestClient(app)

# Test data
test_position_id = str(uuid4())
test_candidate_id = str(uuid4())
test_interview_id = str(uuid4())
test_datetime = datetime(2024, 3, 20, 10, 0, 0)


@pytest.fixture
def mock_db():
    """Mock database cursor"""
    with patch('controllers.interview_controller.get_cursor') as mock_get_cursor:
        mock_cur = MagicMock()
        mock_get_cursor.return_value.__enter__.return_value = mock_cur
        yield mock_cur


@pytest.fixture
def mock_position():
    """Mock position data"""
    return Position(
        id=test_position_id,
        proj_id=str(uuid4()),
        position_info={"title": "Senior Python Developer", "requirements": ["Python", "FastAPI"]},
        top_candidates=[],
        created_at=test_datetime,
        updated_at=test_datetime,
        last_matching=test_datetime
    )


@pytest.fixture
def mock_interview_row():
    """Mock interview database row"""
    return {
        "id": test_interview_id,
        "position_id": test_position_id,
        "candidate_id": test_candidate_id,
        "feedback_hr": {"communication": "Excellent"},
        "interview_date_hr": test_datetime,
        "feedback_date_hr": test_datetime,
        "status_hr": InterviewStatus.COMPLETED,
        "recommendation_hr": True,
        "transcript_hr": "HR interview transcript",
        "feedback_tec": {"technical": "Strong"},
        "interview_date_tec": test_datetime,
        "feedback_date_tec": test_datetime,
        "status_tec": InterviewStatus.COMPLETED,
        "recommendation_tec": True,
        "transcript_tec": "Technical interview transcript",
        "anwers_data": {"answers": ["Answer 1"]},
        "interview_data": {"evaluation": "Good"},
        "created_at": test_datetime,
        "updated_at": test_datetime,
        "candidate_info": {"name": "Test Candidate"},
        "position_info": {"title": "Senior Python Developer"},
        "recruiter_hr_id": str(uuid4()),
        "scheduled_hr_id": str(uuid4()),
        "recruiter_tec_id": str(uuid4()),
        "scheduled_tec_id": str(uuid4())
    }


@pytest.fixture
def mock_questions():
    """Mock interview questions"""
    return SingleQuestions(
        id=str(uuid4()),
        position_id=test_position_id,
        data={
            "questions": [
                {
                    "question_number": 1,
                    "question": "What is Python?",
                    "senior_answer": "Expert answer",
                    "mid_answer": "Intermediate answer",
                    "junior_answer": "Basic answer"
                }
            ]
        },
        created_at=test_datetime,
        updated_at=test_datetime
    )


def test_create_interview_success(mock_db, mock_interview_row, mock_position):
    """Test successful interview creation"""
    with patch('routes.routes_interview.create_interview', return_value=[mock_interview_row]), \
            patch('routes.routes_interview.fetch_interview_by_position_id_candidate_id', return_value=mock_interview_row), \
            patch('routes.routes_interview.fetch_all_interviews_by_position_id', return_value=[mock_interview_row]), \
            patch('routes.routes_interview.fetch_questions_by_position_id', return_value={}), \
            patch('routes.routes_interview.fetch_interviews_by_candidate_id', return_value=[]):
        response = client.post(f"/interview/{test_position_id}?candidates_id={test_candidate_id}")

        assert response.status_code == 200
        assert isinstance(response.json(), list)
        assert len(response.json()) > 0
        assert response.json()[0]["position_id"] == test_position_id


def test_generate_questions_success(mock_db, mock_position, mock_questions):
    """Test successful question generation"""
    with patch('routes.routes_interview.generate_and_persist_qa', return_value=mock_questions):
        response = client.post(f"/interview/{test_position_id}/questions")

        assert response.status_code == 200
        assert "questions" in response.json()["data"]


def test_evaluate_interview_success(mock_db, mock_interview_row):
    """Test successful interview evaluation"""
    mock_db.fetchone.return_value = mock_interview_row

    with patch('routes.routes_interview.re_evalute_interview', return_value={**mock_interview_row, "interview_data": mock_interview_row["interview_data"]}):
        response = client.post(f"/interview/{test_interview_id}/evaluate")

        assert response.status_code == 200
        # assert response.json()["interview_data"]["overall_seniority"] == "mid"
        # assert response.json()["interview_data"]["percentage_of_match"] == 0.8


def test_update_interview_hr_success(mock_db, mock_interview_row):
    """Test successful HR interview update"""
    interview_data = {
        "position_id": test_position_id,
        "candidate_id": test_candidate_id,
        "recruiter_hr_id": str(uuid4()),
        "scheduled_hr_id": str(uuid4()),
        "feedback_hr": {"communication": "Excellent"},
        "interview_date_hr": test_datetime.isoformat(),
        "feedback_date_hr": test_datetime.isoformat(),
        "status_hr": InterviewStatus.COMPLETED,
        "recommendation_hr": True,
        "transcript_hr": "..."
    }

    mock_db.fetchone.side_effect = [mock_interview_row, mock_interview_row]

    with patch('routes.routes_interview.update_interview_hr', return_value=mock_interview_row):
        response = client.put("/interview/hr", json=interview_data)

        assert response.status_code == 200
        assert response.json()["status_hr"] == InterviewStatus.COMPLETED
        assert response.json()["feedback_hr"]["communication"] == "Excellent"


def test_update_interview_tec_success(mock_db, mock_interview_row):
    """Test successful technical interview update"""
    interview_data = {
        "position_id": test_position_id,
        "candidate_id": test_candidate_id,
        "recruiter_tec_id": str(uuid4()),
        "scheduled_tec_id": str(uuid4()),
        "feedback_tec": {"technical": "Strong"},
        "interview_date_tec": test_datetime.isoformat(),
        "feedback_date_tec": test_datetime.isoformat(),
        "status_tec": InterviewStatus.COMPLETED,
        "recommendation_tec": True,
        "transcript_tec": "..."
    }

    mock_db.fetchone.side_effect = [mock_interview_row, mock_interview_row]

    with patch('routes.routes_interview.update_interview_tec', return_value=mock_interview_row):
        response = client.put("/interview/tec", json=interview_data)

        assert response.status_code == 200
        assert response.json()["status_tec"] == InterviewStatus.COMPLETED
        assert response.json()["feedback_tec"]["technical"] == "Strong"


def test_fetch_interviews_by_candidate_success(mock_db, mock_interview_row):
    """Test successful fetch of all interviews for a candidate"""
    mock_db.fetchall.return_value = [mock_interview_row, mock_interview_row]

    response = client.get(f"/interview/?candidate_id={test_candidate_id}")
    # print(response.json())
    # Check the response status and data structure
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    # assert len(response.json()) > 0
    # Check that all interviews belong to the test candidate
    assert all(interview["candidate_id"] == test_candidate_id for interview in response.json())


def test_fetch_interview_by_position_candidate_success(mock_db, mock_interview_row):
    """Test successful fetch of a specific interview"""
    with patch('routes.routes_interview.fetch_interview_by_position_id_candidate_id', return_value=mock_interview_row):
        response = client.get(f"/interview/{test_position_id}/{test_candidate_id}")

        assert response.status_code == 200
        assert response.json()["position_id"] == test_position_id
        assert response.json()["candidate_id"] == test_candidate_id


def test_fetch_questions_success(mock_db, mock_questions):
    """Test successful fetch of interview questions"""
    with patch('routes.routes_interview.fetch_questions_by_position_id', return_value=mock_questions):
        response = client.get(f"/interview/questions/?position_id={test_position_id}")

        assert response.status_code == 200
        assert "questions" in response.json()["data"]


def test_delete_interview_success(mock_db):
    """Test successful interview deletion"""
    with patch('routes.routes_interview.delete_interview', return_value=True):
        response = client.delete(f"/interview/{test_position_id}/{test_candidate_id}")

        assert response.status_code == 200
        assert response.json() is True
