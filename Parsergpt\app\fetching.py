"""Web fetching module for ParserGPT POC."""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
import httpx
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, Page
from .config import get_settings

logger = logging.getLogger(__name__)


class FetchResult:
    """Result of a web fetch operation."""
    
    def __init__(
        self,
        url: str,
        html: str = "",
        status_code: int = 0,
        content_type: str = "",
        content_length: int = 0,
        fetch_method: str = "httpx",
        processing_time: float = 0.0,
        error: Optional[str] = None
    ):
        self.url = url
        self.html = html
        self.status_code = status_code
        self.content_type = content_type
        self.content_length = content_length
        self.fetch_method = fetch_method
        self.processing_time = processing_time
        self.error = error
    
    @property
    def success(self) -> bool:
        """Check if fetch was successful."""
        return self.status_code == 200 and self.html and not self.error
    
    @property
    def domain(self) -> str:
        """Get domain from URL."""
        return urlparse(self.url).netloc
    
    def __repr__(self):
        return f"<FetchResult(url={self.url}, status={self.status_code}, success={self.success})>"


class HttpxFetcher:
    """HTTP client for fetching web pages using httpx."""
    
    def __init__(self):
        self.settings = get_settings()
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0"
        ]
        self.current_ua_index = 0
    
    def _get_headers(self) -> Dict[str, str]:
        """Get HTTP headers with rotating user agent."""
        user_agent = self.user_agents[self.current_ua_index]
        self.current_ua_index = (self.current_ua_index + 1) % len(self.user_agents)
        
        return {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "max-age=0"
        }
    
    async def fetch(self, url: str) -> FetchResult:
        """
        Fetch a single URL using httpx.
        
        Args:
            url: The URL to fetch
            
        Returns:
            FetchResult object with the response data
        """
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient(
                follow_redirects=True,
                timeout=self.settings.request_timeout,
                http2=True,
                limits=httpx.Limits(max_keepalive_connections=10, max_connections=20)
            ) as client:
                
                response = await client.get(url, headers=self._get_headers())
                processing_time = time.time() - start_time
                
                # Check if response is HTML
                content_type = response.headers.get("content-type", "").lower()
                if "text/html" not in content_type and "application/xhtml" not in content_type:
                    return FetchResult(
                        url=url,
                        status_code=response.status_code,
                        content_type=content_type,
                        processing_time=processing_time,
                        error=f"Non-HTML content type: {content_type}"
                    )
                
                # Get HTML content
                html = response.text
                content_length = len(html.encode('utf-8'))
                
                logger.info(f"Successfully fetched {url} ({content_length} bytes, {processing_time:.2f}s)")
                
                return FetchResult(
                    url=url,
                    html=html,
                    status_code=response.status_code,
                    content_type=content_type,
                    content_length=content_length,
                    fetch_method="httpx",
                    processing_time=processing_time
                )
                
        except httpx.TimeoutException:
            processing_time = time.time() - start_time
            error = f"Request timeout after {self.settings.request_timeout}s"
            logger.warning(f"Timeout fetching {url}: {error}")
            return FetchResult(url=url, processing_time=processing_time, error=error)
            
        except httpx.HTTPStatusError as e:
            processing_time = time.time() - start_time
            error = f"HTTP error {e.response.status_code}: {e.response.reason_phrase}"
            logger.warning(f"HTTP error fetching {url}: {error}")
            return FetchResult(
                url=url,
                status_code=e.response.status_code,
                processing_time=processing_time,
                error=error
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error = f"Fetch error: {str(e)}"
            logger.error(f"Error fetching {url}: {error}")
            return FetchResult(url=url, processing_time=processing_time, error=error)
    
    async def fetch_multiple(self, urls: List[str], max_concurrent: int = 5) -> List[FetchResult]:
        """
        Fetch multiple URLs concurrently.
        
        Args:
            urls: List of URLs to fetch
            max_concurrent: Maximum number of concurrent requests
            
        Returns:
            List of FetchResult objects
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_with_semaphore(url: str) -> FetchResult:
            async with semaphore:
                return await self.fetch(url)
        
        tasks = [fetch_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        fetch_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                fetch_results.append(FetchResult(
                    url=urls[i],
                    error=f"Exception during fetch: {str(result)}"
                ))
            else:
                fetch_results.append(result)
        
        return fetch_results


# Global fetcher instance
_httpx_fetcher = None


def get_httpx_fetcher() -> HttpxFetcher:
    """Get the global httpx fetcher instance."""
    global _httpx_fetcher
    if _httpx_fetcher is None:
        _httpx_fetcher = HttpxFetcher()
    return _httpx_fetcher


async def fetch_url(url: str) -> FetchResult:
    """Convenience function to fetch a single URL."""
    fetcher = get_httpx_fetcher()
    return await fetcher.fetch(url)


async def fetch_urls(urls: List[str], max_concurrent: int = 5) -> List[FetchResult]:
    """Convenience function to fetch multiple URLs."""
    fetcher = get_httpx_fetcher()
    return await fetcher.fetch_multiple(urls, max_concurrent)


class PlaywrightFetcher:
    """Browser-based fetcher using Playwright for bot-protected sites."""

    def __init__(self):
        self.settings = get_settings()
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None

    async def _ensure_browser(self):
        """Ensure browser and context are initialized."""
        if self.browser is None or self.context is None:
            playwright = await async_playwright().start()

            # Launch browser with anti-detection measures
            self.browser = await playwright.chromium.launch_persistent_context(
                user_data_dir=self.settings.playwright_user_data_dir,
                headless=self.settings.playwright_headless,
                args=[
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-gpu",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding"
                ],
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

            # Set up anti-detection script
            await self.browser.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                window.chrome = {
                    runtime: {},
                };
            """)

            logger.info("Playwright browser initialized")

    async def fetch(self, url: str) -> FetchResult:
        """
        Fetch a single URL using Playwright.

        Args:
            url: The URL to fetch

        Returns:
            FetchResult object with the response data
        """
        start_time = time.time()

        try:
            await self._ensure_browser()

            page = await self.browser.new_page()

            try:
                # Navigate to the page
                response = await page.goto(
                    url,
                    wait_until="load",
                    timeout=self.settings.request_timeout * 1000
                )

                # Wait a bit for dynamic content
                await page.wait_for_timeout(2500)

                # Get the HTML content
                html = await page.content()
                processing_time = time.time() - start_time

                # Get response details
                status_code = response.status if response else 0
                content_type = "text/html"
                content_length = len(html.encode('utf-8'))

                logger.info(f"Successfully fetched {url} with Playwright ({content_length} bytes, {processing_time:.2f}s)")

                return FetchResult(
                    url=url,
                    html=html,
                    status_code=status_code,
                    content_type=content_type,
                    content_length=content_length,
                    fetch_method="playwright",
                    processing_time=processing_time
                )

            finally:
                await page.close()

        except Exception as e:
            processing_time = time.time() - start_time
            error = f"Playwright fetch error: {str(e)}"
            logger.error(f"Error fetching {url} with Playwright: {error}")
            return FetchResult(url=url, processing_time=processing_time, error=error)

    async def close(self):
        """Close the browser and clean up resources."""
        if self.browser:
            await self.browser.close()
            self.browser = None
            self.context = None
            logger.info("Playwright browser closed")


# Global playwright fetcher instance
_playwright_fetcher = None


def get_playwright_fetcher() -> PlaywrightFetcher:
    """Get the global playwright fetcher instance."""
    global _playwright_fetcher
    if _playwright_fetcher is None:
        _playwright_fetcher = PlaywrightFetcher()
    return _playwright_fetcher


async def fetch_with_fallback(url: str) -> FetchResult:
    """
    Fetch URL with httpx first, fallback to Playwright if needed.

    Args:
        url: The URL to fetch

    Returns:
        FetchResult object with the response data
    """
    # Try httpx first
    result = await fetch_url(url)

    # If httpx failed or returned empty content, try Playwright
    if not result.success or not result.html.strip():
        logger.info(f"Falling back to Playwright for {url}")
        playwright_fetcher = get_playwright_fetcher()
        result = await playwright_fetcher.fetch(url)

    return result


async def cleanup_fetchers():
    """Clean up all fetcher resources."""
    global _playwright_fetcher
    if _playwright_fetcher:
        await _playwright_fetcher.close()
        _playwright_fetcher = None
