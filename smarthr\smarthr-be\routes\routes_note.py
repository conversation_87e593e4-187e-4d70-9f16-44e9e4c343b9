from datetime import datetime, timezone
import psycopg2
from controllers.notes_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List
from fastapi import APIRouter, HTTPException, Path
from models.note import Note, NoteCreate, NoteUpdate
# Telemetry Section (Keep sections logically grouped)
from opentelemetry import trace
import logging

from routes.routes_candidate import is_valid_email
# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)
router = APIRouter()

# Notes Controller
notes_controller = NotesController()


# Create a new note
@router.post("/", response_model=Note)
def create_note(note: NoteCreate):
    try:
        if not note.created_by:
            logger.error("created_by is required and must be a valid email")
            raise HTTPException(status_code=400, detail="created_by is required")
        if not is_valid_email(note.created_by):
            logger.error(f"Invalid email format for created_by: {note.created_by}")
            raise HTTPException(status_code=400, detail="created_by must be a valid email")
        note.created_at = datetime.now(timezone.utc)
        note.created_by = note.created_by.lower()  # Normalize email to lowercase
        created_note = notes_controller.create_note(note)
        return created_note
    except psycopg2.Error as e:
        logger.error(f"Database error during note creation: {e}")
        print(f"Database error during note creation: {e}")
        raise HTTPException(500, f"Database error during note creation: {e}")
    except HTTPException as e:
        logger.error(f"Note creation failed: {e.detail}")
        print(f"Note creation failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Note creation failed: {e}")
        print(f"Note creation failed: {e}")
        raise HTTPException(500, f"Note creation failed: {e}")


# Update an existing note
@router.put("/{note_id}", response_model=Note)
def update_note(
    note_update: NoteUpdate,
    note_id: str = Path(..., description="UUID of the note")
):
    try:
        if not note_update.updated_by:
            logger.error("updated_by is required and must be a valid email")
            raise HTTPException(status_code=400, detail="updated_by is required")
        if not is_valid_email(note_update.updated_by):
            logger.error(f"Invalid email format for updated_by: {note_update.updated_by}")
            raise HTTPException(status_code=400, detail="updated_by must be a valid email")
        note_update.updated_at = datetime.now(timezone.utc)
        note_update.updated_by = note_update.updated_by.lower()  # Normalize email to lowercase
        updated_note = notes_controller.update_note(note_id, note_update)
        return updated_note
    except psycopg2.Error as e:
        logger.error(f"Database error during note update: {e}")
        raise HTTPException(500, f"Database error during note update: {e}")
    except HTTPException as e:
        logger.error(f"Note update failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Note update failed: {e}")
        raise HTTPException(500, f"Note update failed: {e}")


# Get notes by candidate ID
@router.get("/candidate/{candidate_id}", response_model=List[Note])
def get_notes_by_candidate(
    candidate_id: str = Path(..., description="UUID of the candidate")
):
    try:
        notes = notes_controller.get_notes_by_candidate(candidate_id)
        return notes
    except psycopg2.Error as e:
        logger.error(f"Database error fetching notes by candidate: {e}")
        raise HTTPException(500, f"Database error fetching notes by candidate: {e}")
    except HTTPException as e:
        logger.error(f"Fetching notes by candidate failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Fetching notes by candidate failed: {e}")
        raise HTTPException(500, f"Fetching notes by candidate failed: {e}")


# Get a note by ID
@router.get("/{note_id}", response_model=Note)
def get_note_by_id(
    note_id: str = Path(..., description="UUID of the note")
):
    try:
        note = notes_controller.get_note_by_id(note_id)
        if not note:
            raise HTTPException(status_code=404, detail="Note not found")
        return note
    except psycopg2.Error as e:
        logger.error(f"Database error fetching note by ID: {e}")
        raise HTTPException(500, f"Database error fetching note by ID: {e}")
    except HTTPException as e:
        logger.error(f"Fetching note by ID failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Fetching note by ID failed: {e}")
        raise HTTPException(500, f"Fetching note by ID failed: {e}")


# Delete a note by ID
@router.delete("/{note_id}", response_model=bool)
def delete_note(
    note_id: str = Path(..., description="UUID of the note")
):
    try:
        deleted = notes_controller.delete_note(note_id)
        if not deleted:
            raise HTTPException(status_code=404, detail="Note not found")
        return True
    except psycopg2.Error as e:
        logger.error(f"Database error during note deletion: {e}")
        raise HTTPException(500, f"Database error during note deletion: {e}")
    except HTTPException as e:
        logger.error(f"Note deletion failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Note deletion failed: {e}")
        raise HTTPException(500, f"Note deletion failed: {e}")
