# match_evaluation.py
from datetime import datetime
from typing import Any, Dict, List, Optional, Literal
from pydantic import BaseModel, Field
from config.config import MODELS_CONFIG
from models.llm import inference_with_fallback, get_related_class_definitions
from langchain_core.messages import HumanMessage
from opentelemetry.trace import Status, StatusCode
from models.llm import models_pool
from models.match_analysis_models import (
    CompatibilityEvaluation,
    PositionCandidateAnalysis,
    BatchMatchAnalysis,
    ExperienceAnalysis,
    ExperienceRelevanceScore,
    CandidateExperienceRanking,
    BatchExperienceReranking
)
from models.SpecalizedOutputFormats.postionSkills import PositionSkillAnalysis
from contextlib import contextmanager
import time
# Note: LangSmith-based prompts replaced with enhanced prompts that include proper technical skills weighting
# from templates.candidates_templates.candidate_analysis import (
#     get_candidate_analysis_prompt,
#     get_batch_candidate_analysis_batch_prompt,
# )
from templates.positions_templates.position_analysis import get_position_analysis_prompt
import logging
from opentelemetry import trace
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)


class RoleCompatibilityAssessment(BaseModel):
    """Model for role compatibility assessment using LLM intelligence."""
    candidate_primary_role: str = Field(..., description="The candidate's primary role type (e.g., 'DevOps Engineer', 'QA Engineer', 'Business Analyst')")
    position_target_role: str = Field(..., description="The position's target role type")
    compatibility_level: Literal["same", "related", "major_mismatch"] = Field(..., description="Level of role compatibility")
    confidence: float = Field(..., description="Confidence level in the assessment (0.0 to 1.0)")
    reasoning: str = Field(..., description="Brief explanation of the role compatibility assessment")


def classify_role_compatibility_with_llm(candidate_text: str, position_text: str, models_order: list = MODELS_CONFIG["default_models_order"]) -> RoleCompatibilityAssessment:
    """
    Use LLM intelligence to classify role compatibility instead of brittle keyword matching.

    Args:
        candidate_text (str): Candidate's CV/resume text
        position_text (str): Position description text
        models_order (list): Order of LLM models to try

    Returns:
        RoleCompatibilityAssessment: Structured assessment of role compatibility
    """
    logger.info("🤖 ROLE CLASSIFICATION AGENT: Analyzing role compatibility with LLM")

    prompt = f"""
    [ROLE CLASSIFICATION AGENT]
    You are an expert HR analyst specializing in role classification and compatibility assessment.

    **TASK**: Analyze the candidate's background and the position requirements to determine role compatibility based on **CORE JOB FUNCTIONS**, not technologies.

    **CRITICAL MATHEMATICAL CONSTRAINT**: When job functions are fundamentally different, you MUST classify as "major_mismatch" which triggers a 35% compatibility cap. This is NON-NEGOTIABLE.

    **THRESHOLD ENFORCEMENT**: Your classification directly controls compatibility scoring:
    - "major_mismatch" → Maximum 35% compatibility (ENFORCED BY SYSTEM)

    **CRITICAL**: Focus on the **PRIMARY JOB FUNCTION**, not the technology stack. For example:
    - Salesforce Business Analyst vs Salesforce QA Engineer = MAJOR MISMATCH (different job functions despite same technology)
    - Frontend Developer vs Backend Developer = RELATED (same job function, different specialization)
    - DevOps Engineer vs Software Developer = RELATED (related technical roles)

    **INSTRUCTIONS**:
    1. **Identify the candidate's primary job function** (ignore technologies, focus on what they DO):
       - Quality Assurance/Testing (QA Engineer, Test Engineer, SDET, QA Tester, Manual Tester, Automation Tester)
       - Business Analysis (Business Analyst, Consultant, Requirements Analyst, Systems Analyst)
       - Software Development (Developer, Software Engineer, Programmer, .Net Developer, Java Developer, Frontend Developer, Backend Developer)
       - DevOps/Infrastructure (DevOps Engineer, SRE, Infrastructure Engineer)
       - Data Analysis (Data Analyst, Data Scientist, BI Developer)
       - Design (UI/UX Designer, Product Designer)
       - Project Management (Product Manager, Project Manager)

    2. **Identify the position's target job function** (ignore technologies, focus on what the role DOES)

    3. **Assess compatibility level based on JOB FUNCTIONS**:
       - **"same"**: Same core job function (QA → QA, Developer → Developer, BA → BA)
       - **"related"**: Related job functions with transferable skills (Developer → DevOps, QA → Test Automation Engineer)
       - **"major_mismatch"**: Fundamentally different job functions - TRIGGERS 35% CAP

    **MANDATORY MAJOR_MISMATCH CLASSIFICATIONS** (these MUST be classified as major_mismatch):
    - QA/Tester → Developer (any type): MAJOR_MISMATCH
    - Developer (any type) → QA/Tester: MAJOR_MISMATCH
    - Business Analyst → QA/Tester: MAJOR_MISMATCH
    - Business Analyst → Developer: MAJOR_MISMATCH
    - Consultant → QA/Tester: MAJOR_MISMATCH
    - Consultant → Developer: MAJOR_MISMATCH

    **EXAMPLES**:
    - "QA Tester" → ".Net Developer II" = MAJOR_MISMATCH (QA vs Developer functions)
    - "Manual QA Engineer" → "Java Developer" = MAJOR_MISMATCH (QA vs Developer functions)
    - "Salesforce Business Analyst" → "Salesforce QA Engineer" = MAJOR_MISMATCH (BA vs QA functions)
    - "Java Developer" → "Python Developer" = SAME (both are developers)
    - "Manual QA Tester" → "Automation QA Engineer" = SAME (both are QA)
    - "Technical Consultant" → "QA Engineer" = MAJOR_MISMATCH (Consultant vs QA functions)
    - "Software Developer" → "QA Engineer" = MAJOR_MISMATCH (Developer vs QA functions)

    **IMPORTANT**:
    - Pay attention to context and negation
    - Don't be fooled by shared technologies - focus on job functions
    - QA/Testing and Software Development are fundamentally different job functions
    - Consultants and Business Analysts have fundamentally different job functions than QA Engineers and Developers

    **MATHEMATICAL ENFORCEMENT RULES**:
    - IF candidate_primary_role ∈ {{QA, Tester, Test Engineer}} AND position_target_role ∈ {{Developer, Software Engineer, Programmer}} → compatibility_level = "major_mismatch"
    - IF candidate_primary_role ∈ {{Developer, Software Engineer, Programmer}} AND position_target_role ∈ {{QA, Tester, Test Engineer}} → compatibility_level = "major_mismatch"
    - IF candidate_primary_role ∈ {{Business Analyst, Consultant}} AND position_target_role ∈ {{QA, Developer}} → compatibility_level = "major_mismatch"
    - major_mismatch classification is MANDATORY for these combinations - no exceptions

    **CANDIDATE BACKGROUND**:
    {candidate_text}

    **POSITION REQUIREMENTS**:
    {position_text}

    **FINAL VALIDATION**: Before returning your response, verify that if the candidate and position represent fundamentally different job functions (QA vs Developer, BA vs QA, etc.), you have classified it as "major_mismatch". This is a hard requirement that cannot be overridden.

    Return your analysis in strict JSON format according to the RoleCompatibilityAssessment model.
    """

    try:
        # Create user message and schema text
        user_msg = HumanMessage(content="")  # Empty content since prompt contains everything
        schema_text = get_related_class_definitions(RoleCompatibilityAssessment)

        response = inference_with_fallback(
            task_prompt=prompt,
            model_schema=RoleCompatibilityAssessment,
            user_messages=[user_msg],
            model_schema_text=schema_text,
            models_order=models_order
        )

        if not response:
            raise RuntimeError("All LLM providers failed")

        logger.info(f"🤖 ROLE CLASSIFICATION: {response.candidate_primary_role} → {response.position_target_role} = {response.compatibility_level}")
        logger.info(f"🤖 REASONING: {response.reasoning}")

        return response

    except Exception as e:
        logger.error(f"Role classification failed: {e}")

        # Improved fallback logic - use keyword detection as backup
        candidate_lower = candidate_text.lower()
        position_lower = position_text.lower()

        # Detect Business Analyst candidates
        ba_keywords = ["business analyst", "consultant", "business consultant", "technical consultant", "solution consultant"]
        is_ba_candidate = any(keyword in candidate_lower for keyword in ba_keywords)

        # Detect QA positions
        qa_keywords = ["qa", "quality assurance", "test", "testing", "tester"]
        is_qa_position = any(keyword in position_lower for keyword in qa_keywords)

        # If BA candidate applying for QA position, default to major_mismatch
        if is_ba_candidate and is_qa_position:
            logger.warning(f"🚨 FALLBACK LOGIC: Business Analyst → QA detected, defaulting to major_mismatch")
            return RoleCompatibilityAssessment(
                candidate_primary_role="Business Analyst",
                position_target_role="Quality Assurance",
                compatibility_level="major_mismatch",  # Force major mismatch for BA → QA
                confidence=0.5,
                reasoning=f"Fallback classification: Business Analyst → QA = major role mismatch. Original error: {e}"
            )

        # Default fallback to conservative assessment
        return RoleCompatibilityAssessment(
            candidate_primary_role="Unknown",
            position_target_role="Unknown",
            compatibility_level="related",  # Conservative default for other cases
            confidence=0.0,
            reasoning=f"Classification failed: {e}"
        )


@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


def get_enhanced_candidate_analysis_prompt() -> str:
    """
    Enhanced prompt template that incorporates proper technical skills weighting
    while maintaining PositionCandidateAnalysis output format.
    """
    return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach with proper technical skills weighting.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR - APPLY CAPS STRICTLY**
**MATHEMATICAL CONSTRAINT: Major role mismatches CANNOT exceed 35% compatibility - this is ENFORCED and NON-NEGOTIABLE**

**MANDATORY FIRST STEP - ROLE TYPE IDENTIFICATION:**
Before any scoring, identify the PRIMARY role type for both position and candidate:
- Position role types: QA Engineer, Developer, DevOps Engineer, Data Scientist, Business Analyst, etc.
- Candidate role types: Look at actual job titles and work experience, NOT just skills listed
- **CRITICAL**: Having "Testing Methodologies" as a skill does NOT make someone a QA Engineer
- **CRITICAL**: "Analytical Skills" does NOT qualify someone for a QA Engineer role
- **CRITICAL**: QA/Testing and Software Development are fundamentally different job functions

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 85% weight)**: First, identify the core job function/role type from both the position and candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential (can score 70-100%)
   - **Different role types**: Low compatibility (MAXIMUM 35% - ENFORCED MATHEMATICALLY)

2. **Technology Skills Alignment (SECONDARY - 15% weight)**: Assess overlap in required technologies, frameworks, and tools.

**MATHEMATICAL CONSTRAINTS - THESE ARE ABSOLUTE LIMITS:**
- IF candidate_role ∈ {QA, Tester} AND position_role ∈ {Developer, Software Engineer} → Score ≤ 35
- IF candidate_role ∈ {Developer, Software Engineer} AND position_role ∈ {QA, Tester} → Score ≤ 35
- IF candidate_role ∈ {Business Analyst, Consultant} AND position_role ∈ {QA, Developer} → Score ≤ 35
- These constraints CANNOT be overridden by technology skills or experience

**COMPATIBILITY CALCULATION RULES:**
- Final Score = (Role Function Score × 0.85) + (Technology Skills Score × 0.15)
- **ROLE TYPE ASSESSMENT - APPLY THESE STRICTLY:**
  * **Major role mismatch** (e.g., QA → Developer, Developer → QA, Consultant/Analyst → QA/Developer): MAXIMUM 35% - NO EXCEPTIONS
  * **Moderate role mismatch** (e.g., Frontend vs Backend): Moderate compatibility based on transferable skills
  * **Minor role mismatch** (e.g., Junior vs Senior same role): High compatibility with experience considerations
  * **Same role type**: Full compatibility potential based on technology and experience alignment

**CRITICAL ROLE TYPE ASSESSMENT:**
- Role type alignment is the PRIMARY factor in compatibility assessment
- Technology expertise CANNOT override fundamental role type mismatches
- "Analytical skills" or "testing methodologies" knowledge does NOT make someone a QA Engineer

**STRICT SKILL MATCHING RULES:**
- ONLY match skills that are LITERALLY mentioned in the candidate's CV
- DO NOT make technical inferences or assumptions (e.g., C# ≠ .NET, Java ≠ Spring, Python ≠ Django)
- DO NOT assume related technologies even if commonly used together
- If position requires ".NET" but candidate only mentions "C#", this is NOT a match
- If position requires "Spring Framework" but candidate only mentions "Java", this is NOT a match

**CRITICAL EXAMPLES - APPLY THESE CAPS STRICTLY:**
- **QA Tester → .Net Developer II Position: MAX 35%** (QA vs Developer - fundamentally different job functions)
- **Manual QA Engineer → Java Developer Position: MAX 35%** (QA vs Developer - fundamentally different job functions)
- **Software Developer → QA Engineer Position: MAX 35%** (Developer vs QA - fundamentally different job functions)
- **Salesforce Consultant/Analyst → Salesforce QA Position: MAX 35%** (same technology, completely different job functions)
- **Business Analyst → QA Engineer Position: MAX 35%** (different role type, some analytical overlap)
- **Salesforce Developer → Salesforce QA Position: MAX 35%** (same technology, completely different job functions)
- **React Developer → Angular Developer Position: MAX 75%** (same role type, different but related technology)
- **Python Developer → Python Developer Position: Up to 100%** (perfect alignment)
- **Manual QA → Automation QA Position: MAX 75%** (same domain, different approach)
- **QA Engineer → QA Engineer Position: Up to 100%** (perfect role match)

**OUTPUT REQUIREMENTS:**
Return a JSON response with EXACTLY this structure (do not nest extra_questions, highlights, or Score inside LLM_Analysis):

{
  "LLM_Analysis": {
    "reason": "Detailed explanation of the compatibility assessment including role type analysis and weighting applied",
    "skill_match_analysis": {"skill_name": "explanation for matched skills only"},
    "skill_not_matched": ["list of skills not matched"]
  },
  "extra_questions": ["list of additional questions for further evaluation"],
  "highlights": ["list of key points about the candidate's suitability"],
  "Score": 85.5
}

**CRITICAL:**
- The Score field must be at the TOP LEVEL, not inside LLM_Analysis
- The extra_questions field must be at the TOP LEVEL, not inside LLM_Analysis
- The highlights field must be at the TOP LEVEL, not inside LLM_Analysis
- The Score must reflect the proper weighting (85% role function, 15% technology skills) and accurate role type assessment
- **FINAL CHECK**: Ensure role type alignment is properly weighted as the primary compatibility factor
- In the reason field, explain the role type assessment and overall compatibility evaluation"""


def evaluate_candidate(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> dict:
    with tracer.start_as_current_span("evaluate_candidate_llm") as span:
        span.set_attribute("eval.candidate_length", len(candidate))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando candidato con LLM usando prompt mejorado con ponderación de habilidades técnicas")
        system_prompt = get_enhanced_candidate_analysis_prompt()

        messages = [
            {"role": "user", "content": f"Candidate description: {candidate}"},
            {"role": "user", "content": f"Position description: {position}"},
        ]
        analysis_response = {}

        with log_time_block("Invoke LLM on evaluate candidate"):
            analysis_response = inference_with_fallback(
                task_prompt=system_prompt,
                model_schema=PositionCandidateAnalysis,
                user_messages=messages,
                models_order=models_order
            )
        if not analysis_response:
            logger.error("LLM analysis failed for candidate evaluation")
            return {
                "LLM_Analysis": {
                    "reason": "LLM analysis failed",
                    "skill_match_analysis": {},
                    "skill_not_matched": []
                },
                "extra_questions": [],
                "highlights": [],
                "Score": 0.0,
            }
        analysis_response = analysis_response.model_dump()
        score = analysis_response.get("Score", 0.0)
        try:
            score = float(score)
        except ValueError:
            score = 0.0

        logger.info(
            "Respuesta del LLM procesada con ponderación mejorada",
            extra={"custom_dimensions": {"score": score}}
        )
        return analysis_response


def evaluate_position(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> PositionCandidateAnalysis:
    """
    Evaluate a position for a given candidate using the LLM.
    Uses template from position_analysis.py
    """
    system_prompt = get_position_analysis_prompt()

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Candidate description: {candidate}"},
        {"role": "user", "content": f"Position description: {position}"},
        # {"role": "user", "content": f"Initial similarity score: {initial_score}"}
    ]

    # Attempt models in order
    default_response = {
        "LLM_Analysis": {},
        "extra_questions": {},
        "highlights": {},
        "Score": 0.0,
    }

    analysis_response = _invoke_llm_with_fallback(
        messages, default_response, model_order=models_order
    )

    # Ensure Score is float
    score = analysis_response.get("Score", 0.0)
    try:
        score = float(score)
    except ValueError:
        score = 0.0

    return PositionCandidateAnalysis(
        LLM_Analysis=analysis_response.get("LLM_Analysis", {}),
        extra_questions=analysis_response.get("extra_questions", {}),
        highlights=analysis_response.get("highlights", {}),
        Score=score,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def get_enhanced_batch_candidate_analysis_prompt() -> str:
    """
    Enhanced batch prompt template that incorporates proper technical skills weighting
    for multiple candidates evaluation.
    """
    return """Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and multiple candidates' CVs using a two-factor assessment approach with proper technical skills weighting.

**CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

**ASSESSMENT METHODOLOGY:**
1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and each candidate's background.
   - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
   - **Same role type**: High compatibility potential (can score 70-100%)
   - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
   - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

**STRICT SKILL MATCHING RULES:**
- ONLY match skills that are LITERALLY mentioned in the candidate's CV
- DO NOT make technical inferences or assumptions (e.g., C# ≠ .NET, Java ≠ Spring, Python ≠ Django)
- DO NOT assume related technologies even if commonly used together
- If position requires ".NET" but candidate only mentions "C#", this is NOT a match
- If position requires "Spring Framework" but candidate only mentions "Java", this is NOT a match

**COMPATIBILITY CALCULATION RULES:**
- Final Score = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
- **ROLE TYPE ASSESSMENT GUIDELINES:**
  * Major role mismatch (e.g., Developer vs QA): Focus on fundamental role incompatibility
  * Moderate role mismatch (e.g., Frontend vs Backend): Consider transferable skills within domain
  * Minor role mismatch (e.g., Junior vs Senior same role): Emphasize experience level differences
  * Same role type: Evaluate based on technology and experience alignment

**OUTPUT REQUIREMENTS:**
Return a JSON object with "candidates_analysis" array containing analysis for each candidate in order.

Each candidate analysis must have EXACTLY this structure (do not nest extra_questions, highlights, or Score inside LLM_Analysis):

{
  "candidates_analysis": [
    {
      "LLM_Analysis": {
        "reason": "Detailed explanation including role type analysis and weighting applied",
        "skill_match_analysis": {"skill_name": "explanation for matched skills only"},
        "skill_not_matched": ["list of skills not matched"]
      },
      "extra_questions": ["list of additional questions"],
      "highlights": ["list of key points about suitability"],
      "Score": 85.5
    }
  ]
}

**CRITICAL:**
- The Score field must be at the TOP LEVEL of each candidate analysis, not inside LLM_Analysis
- The extra_questions field must be at the TOP LEVEL, not inside LLM_Analysis
- The highlights field must be at the TOP LEVEL, not inside LLM_Analysis
- Each Score must reflect the proper weighting (60% role function, 40% technology skills) and accurate role type assessment
- In each reason field, explain the role type assessment and overall compatibility evaluation"""


def evaluate_candidates_batch(candidates: List[str], position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> BatchMatchAnalysis:
    """
    Evaluate multiple candidates against a position in a single LLM call.
    Returns a BatchMatchAnalysis with individual analyses and overall summary.
    """
    with tracer.start_as_current_span("evaluate_candidates_batch_llm") as span:
        span.set_attribute("eval.candidates_count", len(candidates))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando %d candidatos en modo batch con ponderación mejorada de habilidades técnicas", len(candidates))

        task_prompt = get_enhanced_batch_candidate_analysis_prompt()

        # Prepare candidates for batch analysis
        candidates_prompt = "\n".join([
            f"[Init Candidate] Candidate {i + 1}:\n{candidate} [End of Candidate]" 
            for i, candidate in enumerate(candidates)
        ])

        # Create user messages
        user_messages = [
            HumanMessage(content=f"Position Description:\n{position}"),
            HumanMessage(content=f"Candidates to Evaluate:\n{candidates_prompt}")
        ]

        # Get schema text for BatchMatchAnalysis
        schema_text = """
 

class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")

class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    ) #
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.") #

class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
        """

        with log_time_block("Batch LLM Analysis"):
            try:
                # Use inference_with_fallback with structured output
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=BatchMatchAnalysis,
                    user_messages=user_messages,
                    model_schema_text=schema_text,
                    models_order=models_order
                )

                if not result:
                    raise RuntimeError("All LLM providers failed")

                # Validate and normalize scores
                for analysis in result.candidates_analysis:
                    try:
                        score = float(analysis.Score)
                        score = max(0.0, min(100.0, score))  # Clamp between 0 and 100
                        analysis.Score = score
                    except (ValueError, TypeError):
                        analysis.Score = 0.0

                logger.info(
                    "Batch analysis completed successfully",
                    extra={
                        "custom_dimensions": {
                            "candidates_count": len(candidates),
                            "analysis_count": len(result.candidates_analysis)
                        }
                    }
                )

                return result

            except Exception as e:
                logger.error("Error in batch analysis: %s", str(e), exc_info=True)
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR))
                raise RuntimeError(f"Batch analysis failed: {str(e)}")


def get_candidate_analysis_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    logger.info("🔧 CANDIDATE → POSITIONS MATCHING: get_candidate_analysis_custom_prompt")
    logger.info(f"🔧 Candidate text preview: {candidate_text[:100]}...")
    logger.info(f"🔧 Position text preview: {processed_position[:100]}...")

    # 1) Build prompt
    task_prompt = (
        f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using a two-factor assessment approach.

        **CRITICAL: ROLE TYPE COMPATIBILITY IS THE PRIMARY FACTOR**

        **ASSESSMENT METHODOLOGY:**
        1. **Role Function Alignment (PRIMARY - 60% weight)**: First, identify the core job function/role type from both the position and candidate's background.
           - Common role types: Developer, QA/Tester, DevOps, Data Scientist, Product Manager, Designer, Business Analyst, etc.
           - **Same role type**: High compatibility potential (can score 70-100%)
           - **Related role types**: Medium compatibility (maximum 60% - e.g., Developer → DevOps, QA → Test Automation)
           - **Different role types**: Low compatibility (maximum 35% - e.g., Developer → QA, Frontend → Backend)

        2. **Technology Skills Alignment (SECONDARY - 40% weight)**: Assess overlap in required technologies, frameworks, and tools.

        **COMPATIBILITY CALCULATION RULES:**
        - Final Compatibility = (Role Function Score × 0.6) + (Technology Skills Score × 0.4)
        - **ROLE TYPE COMPATIBILITY ASSESSMENT:**
          * Major role mismatch (e.g., Developer vs QA): Low compatibility due to fundamental role differences
          * Moderate role mismatch (e.g., Frontend vs Backend): Moderate compatibility with transferable skills
          * Minor role mismatch (e.g., Junior vs Senior same role): High compatibility with experience considerations
          * Same role type: Full compatibility potential based on technology and experience alignment

        **CRITICAL EXAMPLES:**
        - Salesforce Developer → Salesforce QA Position: MAX 35% (same technology, completely different job functions)
        - React Developer → Angular Developer Position: MAX 75% (same role type, different but related technology)
        - Python Developer → Python Developer Position: Up to 100% (perfect alignment)
        - Manual QA → Automation QA Position: MAX 75% (same domain, different approach)

        **ANALYSIS REQUIREMENTS:**
        1. Percentage of compatibility with the position (applying the role type compatibility rules above)
        2. Recommendation: Whether the candidate should move forward (consider both role fit and technology alignment)
        3. Matches Found: Points where candidate meets requirements (distinguish between role-relevant and technology matches)
        4. Missing: Key requirements not evident in candidate's background (prioritize role function gaps over technology gaps)

        I will provide you with the Job Description and CV for analysis.\n\n"""
        f"""Job Description:\n{processed_position}\n\n"""
        f"""CV:\n{candidate_text}\n\n"""
        f"""Return the analysis in strict JSON format according to the CompatibilityEvaluation model, containing the following required fields:
        - compatibilityPercentage (float with one decimal place between 0.0 and 100.0, e.g., 83.7, 42.6 — avoid round numbers or multiples of 5)
        - recommendation (boolean)
        - justification (string - must explain role type compatibility assessment)
        - matchesFound (array of strings)
        - missing (array of strings)

        Requirements:
        1. The field compatibilityPercentage **must be a float, not an integer**, and must include **exactly one decimal place** (e.g., 87.3, 46.8).
        2. **Do not round or simplify** compatibilityPercentage to multiples of 5 or integers.
        3. **MANDATORY**: Apply the role type compatibility rules for accurate assessment.
        4. All fields must be filled, even if matchesFound or missing are empty arrays.
        5. Output must be valid JSON — do not include explanations or extra formatting.
        6. In justification, explain the role type compatibility assessment focusing on role alignment and skill match.
        """
    )
    schema_text = get_related_class_definitions(CompatibilityEvaluation)

    # 2) Call the inference function with the task prompt
    # Using inference_with_fallback to handle multiple models and fallbacks
    analysis = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=CompatibilityEvaluation,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if analysis is None or empty
    if not analysis:
        return None

    # 4) Apply role type mismatch caps to compatibilityPercentage
    # CRITICAL FIX: Don't assume all positions are QA - use the actual position text
    original_percentage = analysis.compatibilityPercentage
    capped_percentage = apply_role_type_caps_to_compatibility(
        original_percentage, candidate_text, processed_position  # Use actual position text, not hardcoded "Quality Assurance"
    )

    # Update the analysis with capped percentage
    analysis.compatibilityPercentage = capped_percentage

    if capped_percentage < original_percentage:
        logger.info(f"Applied fallback compatibility cap: {original_percentage:.1f}% → {capped_percentage:.1f}%")

    return analysis


def get_roles_to_candidates(candidate: dict) -> List[str]:
    # 1) Build prompt
    task_prompt = (
        f"""From the candidate’s resume, output exactly five specific job titles the candidate is qualified to apply for. Return only a JSON array of strings with the job titles (e.g., ["Senior .NET Developer", "..."]). Do not include any explanations or additional fields."""
        f"""\n\nCandidate Resume:\n{candidate}\n\n"""
    )
    # 2) Call the inference function with the task prompt
    roles = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=dict,  # Expecting a simple list of strings
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if roles is None or empty
    if not roles:
        return None
    return roles.get("job_titles", [])


def _invoke_llm_with_fallback(
    messages, default_response, model_order: list = MODELS_CONFIG["default_models_order"]
) -> Dict[str, Any]:
    """
    Attempt to invoke LLM models in order until one succeeds.
    Return default_response if all fail.
    The model is expected to return JSON with required fields.
    """
    print("----------------Evaluating in invoke llm with fallback--------------")
    print(model_order)
    print("=====default_response========")
    print(default_response)
    print("-----------------------------------------------------")
    for model_name in model_order:
        try:
            structured_llm = models_pool[model_name].with_structured_output(
                default_response, method="json_mode"
            )
            result = structured_llm.invoke(messages).model_dump()
            # Validate result is JSON with required keys
            if isinstance(result, dict) and all(
                k in result
                for k in ["LLM_Analysis", "extra_questions", "highlights", "Score"]
            ):
                return result
        except Exception as e:
            print(f"Model {model_name} failed: {e}")

    return default_response


def build_compatibility_evaluation(
    compatibility_percentage: float,
    justification: str,
    matches_found: List[str],
    missing_requirements: List[str]
) -> CompatibilityEvaluation:
    """
    Builds a CompatibilityEvaluation object with the provided parameters.
    """
    return CompatibilityEvaluation(
        compatibilityPercentage=compatibility_percentage,
        recommendation=compatibility_percentage >= 65.0,
        justification=justification,
        matchesFound=matches_found,
        missing=missing_requirements
    )


def extract_position_skills_for_matching(position_description: str, models_order: list = MODELS_CONFIG["default_models_order"]) -> PositionSkillAnalysis:
    """
    Extract and prioritize skills from a job position specifically for candidate matching.

    This function analyzes a job description to identify the most important skills,
    categorizes them by type and importance, and assigns weights for matching calculations.

    Args:
        position_description (str): The job description text to analyze
        models_order (list): Order of LLM models to try for inference

    Returns:
        PositionSkillAnalysis: Structured analysis with prioritized skills and weights

    Example:
        For a "Salesforce QA Engineer" position, this would identify:
        - QA skills as CRITICAL with high weight (core competency)
        - Salesforce tools as IMPORTANT with medium weight (technical tool)
        - Test automation as IMPORTANT (methodology)
    """
    with tracer.start_as_current_span("extract_position_skills_for_matching") as span:
        span.set_attribute("position.description_length", len(position_description))

        logger.info("Extracting prioritized skills for matching from position description")

        task_prompt = """You are an expert recruiter and skills analyst. Analyze the provided job description to identify and prioritize the most important skills for candidate matching.

**CRITICAL INSTRUCTIONS:**

1. **Identify the Primary Role Function**: Determine the core job function (e.g., "Quality Assurance", "Software Development", "Data Analysis")

2. **Extract and Prioritize Skills**: Find 5-8 most important skills, focusing on:
   - Skills that are absolutely essential for success in this role
   - Skills that differentiate good candidates from great ones
   - Both explicitly mentioned skills and those implied by the role requirements

3. **Categorize Each Skill**:
   - **core_competency**: Primary job function skills (e.g., QA testing for QA roles, coding for developer roles)
   - **technical_tool**: Specific technologies, platforms, tools (e.g., Salesforce, Python, AWS)
   - **methodology**: Processes, frameworks, approaches (e.g., Agile, DevOps, Test Automation)
   - **soft_skill**: Communication, leadership, problem-solving
   - **domain_knowledge**: Industry or business domain expertise

4. **Assign Importance Levels**:
   - **critical**: Must-have skills without which the candidate cannot succeed
   - **important**: Highly valuable skills that significantly impact performance
   - **nice_to_have**: Beneficial but not essential skills

5. **Calculate Weights**: Assign numerical weights (0.0 to 1.0) where:
   - critical skills: 0.8-1.0
   - important skills: 0.5-0.7
   - nice_to_have skills: 0.2-0.4
   - Ensure weights reflect the relative importance within the role

**EXAMPLE LOGIC:**
For "Salesforce QA Engineer":
- Quality Assurance (critical, core_competency, weight: 0.95) - This is the PRIMARY job function, most important
- Test Automation (critical, methodology, weight: 0.85) - Essential for modern QA, second most important
- Manual Testing (important, methodology, weight: 0.70) - Core QA skill, more important than tools
- Salesforce Platform (important, technical_tool, weight: 0.60) - Important but SECONDARY to QA skills

**OUTPUT REQUIREMENTS:**
Return a JSON response matching the PositionSkillAnalysis schema with:
- position_title: Extract or infer the job title
- primary_role_function: The core job function
- prioritized_skills: Array of skills with name, importance, category, weight, and description
- skill_weight_rationale: Explanation of the prioritization logic used

**CRITICAL**: Ensure the primary role function skills (core_competency) receive the highest weights, as these are most predictive of job success."""

        user_messages = [
            HumanMessage(content=f"Job Description to Analyze:\n\n{position_description}")
        ]

        with log_time_block("Extract Position Skills for Matching"):
            try:
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=PositionSkillAnalysis,
                    user_messages=user_messages,
                    models_order=models_order
                )

                if not result:
                    logger.error("Failed to extract skills for matching - all models failed")
                    # Return a basic fallback analysis
                    return PositionSkillAnalysis(
                        position_title="Unknown Position",
                        primary_role_function="General",
                        prioritized_skills=[],
                        skill_weight_rationale="Skill extraction failed - using fallback"
                    )

                # Validate and normalize weights
                for skill in result.prioritized_skills:
                    if skill.weight < 0.0:
                        skill.weight = 0.0
                    elif skill.weight > 1.0:
                        skill.weight = 1.0

                logger.info(
                    "Successfully extracted %d prioritized skills for matching",
                    len(result.prioritized_skills),
                    extra={
                        "custom_dimensions": {
                            "position_title": result.position_title,
                            "primary_role_function": result.primary_role_function,
                            "skills_count": len(result.prioritized_skills)
                        }
                    }
                )

                return result

            except Exception as e:
                logger.error("Error extracting skills for matching: %s", str(e), exc_info=True)
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR))

                # Return fallback analysis
                return PositionSkillAnalysis(
                    position_title="Unknown Position",
                    primary_role_function="General",
                    prioritized_skills=[],
                    skill_weight_rationale=f"Skill extraction failed due to error: {str(e)}"
                )


def calculate_skill_match_score(candidate_skills: List[str], position_skills: PositionSkillAnalysis) -> Dict[str, Any]:
    """
    Calculate a weighted skill match score between candidate skills and position requirements.

    Args:
        candidate_skills (List[str]): List of skills extracted from candidate's profile
        position_skills (PositionSkillAnalysis): Prioritized skills from the position

    Returns:
        Dict containing:
        - weighted_score: Overall weighted match score (0.0 to 1.0)
        - skill_matches: Dict of matched skills with their weights
        - missing_critical: List of critical skills not found in candidate
        - match_breakdown: Detailed breakdown by skill category
    """
    if not position_skills.prioritized_skills:
        return {
            "weighted_score": 0.0,
            "skill_matches": {},
            "missing_critical": [],
            "match_breakdown": {}
        }

    # Normalize candidate skills for matching (lowercase, remove extra spaces)
    normalized_candidate_skills = [skill.lower().strip() for skill in candidate_skills]

    skill_matches = {}
    missing_critical = []
    total_weight = 0.0
    matched_weight = 0.0
    category_breakdown = {}

    for position_skill in position_skills.prioritized_skills:
        skill_name = position_skill.name
        skill_weight = position_skill.weight
        skill_importance = position_skill.importance
        skill_category = position_skill.category.value

        total_weight += skill_weight

        # Check for skill match (fuzzy matching)
        is_matched = _is_skill_matched(skill_name, normalized_candidate_skills)

        if is_matched:
            skill_matches[skill_name] = {
                "weight": skill_weight,
                "importance": skill_importance.value,
                "category": skill_category
            }
            matched_weight += skill_weight
        elif skill_importance.value == "critical":
            missing_critical.append(skill_name)

        # Track by category
        if skill_category not in category_breakdown:
            category_breakdown[skill_category] = {
                "total_weight": 0.0,
                "matched_weight": 0.0,
                "skills_count": 0,
                "matched_count": 0
            }

        category_breakdown[skill_category]["total_weight"] += skill_weight
        category_breakdown[skill_category]["skills_count"] += 1

        if is_matched:
            category_breakdown[skill_category]["matched_weight"] += skill_weight
            category_breakdown[skill_category]["matched_count"] += 1

    # Calculate overall weighted score
    weighted_score = matched_weight / total_weight if total_weight > 0 else 0.0

    return {
        "weighted_score": weighted_score,
        "skill_matches": skill_matches,
        "missing_critical": missing_critical,
        "match_breakdown": category_breakdown
    }


def _is_skill_matched(position_skill: str, candidate_skills: List[str]) -> bool:
    """
    Check if a position skill is matched by any candidate skill using fuzzy matching.

    Args:
        position_skill (str): The skill from the position requirements
        candidate_skills (List[str]): Normalized candidate skills (lowercase)

    Returns:
        bool: True if skill is matched, False otherwise
    """
    position_skill_normalized = position_skill.lower().strip()

    # Direct match
    if position_skill_normalized in candidate_skills:
        return True

    # Partial match - check if position skill is contained in any candidate skill
    for candidate_skill in candidate_skills:
        if position_skill_normalized in candidate_skill or candidate_skill in position_skill_normalized:
            return True

    # Common skill synonyms and variations
    skill_synonyms = {
        "quality assurance": ["qa", "testing", "quality control", "qc"],
        "software testing": ["qa", "quality assurance", "testing"],
        "test automation": ["automated testing", "automation testing", "test automation"],
        "manual testing": ["manual qa", "manual test"],
        "python": ["python programming", "python development"],
        "javascript": ["js", "javascript programming"],
        "salesforce": ["sfdc", "salesforce platform", "salesforce.com"],
        "agile": ["agile methodology", "agile development", "scrum"],
        "devops": ["dev ops", "development operations"],
    }

    # Check synonyms
    for canonical_skill, synonyms in skill_synonyms.items():
        if canonical_skill in position_skill_normalized:
            for synonym in synonyms:
                if any(synonym in candidate_skill for candidate_skill in candidate_skills):
                    return True

        if any(synonym in position_skill_normalized for synonym in synonyms):
            if canonical_skill in " ".join(candidate_skills):
                return True

    return False


def normalize_skill_weights(position_skills: PositionSkillAnalysis) -> PositionSkillAnalysis:
    """
    Normalize skill weights to ensure they sum to 1.0 while maintaining relative importance.

    Args:
        position_skills (PositionSkillAnalysis): Position skills analysis to normalize

    Returns:
        PositionSkillAnalysis: Updated analysis with normalized weights
    """
    if not position_skills.prioritized_skills:
        return position_skills

    total_weight = sum(skill.weight for skill in position_skills.prioritized_skills)

    if total_weight == 0:
        # If all weights are 0, distribute equally
        equal_weight = 1.0 / len(position_skills.prioritized_skills)
        for skill in position_skills.prioritized_skills:
            skill.weight = equal_weight
    else:
        # Normalize to sum to 1.0
        for skill in position_skills.prioritized_skills:
            skill.weight = skill.weight / total_weight

    return position_skills


def apply_role_type_caps_to_compatibility(percentage: float, candidate_text: str, target_role_function: str) -> float:
    """
    Apply role type mismatch caps to compatibilityPercentage for CompatibilityEvaluation.
    This ensures that candidates with different job functions are properly capped.

    Args:
        percentage (float): Original compatibility percentage
        candidate_text (str): Candidate description/CV text
        target_role_function (str): Target position's primary role function

    Returns:
        float: Capped percentage if role type mismatch detected
    """
    logger.info(f"🔍 CAP FUNCTION CALLED: percentage={percentage:.1f}%, target_role='{target_role_function}'")
    logger.info(f"🔍 Candidate text: {candidate_text[:100]}...")

    # Additional debug logging for Business Analyst detection
    candidate_lower = candidate_text.lower()
    if "business analyst" in candidate_lower or "consultant" in candidate_lower:
        logger.info(f"🚨 BUSINESS ANALYST CANDIDATE DETECTED: {candidate_text[:200]}...")
    if "qa" in target_role_function.lower() or "quality" in target_role_function.lower():
        logger.info(f"🚨 QA POSITION DETECTED: target_role='{target_role_function}'")

    try:
        # Use LLM-based role classification instead of brittle keyword matching
        role_assessment = classify_role_compatibility_with_llm(candidate_text, target_role_function)

        logger.info(f"🤖 ROLE ASSESSMENT: {role_assessment.candidate_primary_role} → {role_assessment.position_target_role}")
        logger.info(f"🤖 COMPATIBILITY LEVEL: {role_assessment.compatibility_level}")
        logger.info(f"🤖 REASONING: {role_assessment.reasoning}")

        # Apply VERY CONSERVATIVE role-specific capping logic based on LLM assessment
        # ONLY apply 35% cap for MAJOR role mismatches - NOT for related roles
        cap_applied = False
        cap_percentage = 35.0  # ONLY cap for major role mismatches
        reason = ""

        # Use LLM assessment to determine if cap should be applied
        if role_assessment.compatibility_level == "major_mismatch":
            cap_applied = True
            reason = f"major role mismatch ({role_assessment.candidate_primary_role} → {role_assessment.position_target_role})"
            logger.info(f"🤖 MAJOR ROLE MISMATCH DETECTED: {reason}")
        elif role_assessment.compatibility_level == "same":
            logger.info(f"🤖 SAME ROLE MATCH: {role_assessment.candidate_primary_role} → {role_assessment.position_target_role} - no cap applied")
        elif role_assessment.compatibility_level == "related":
            logger.info(f"🤖 RELATED ROLES: {role_assessment.candidate_primary_role} → {role_assessment.position_target_role} - no cap applied")
        else:
            logger.info(f"🤖 UNKNOWN COMPATIBILITY LEVEL: {role_assessment.compatibility_level} - no cap applied")



        # Apply the cap if determined necessary
        if cap_applied:
            capped_percentage = min(percentage, cap_percentage)
            logger.info(f"🔥 APPLYING ROLE TYPE CAP: {percentage:.1f}% → {capped_percentage:.1f}% ({reason})")
            if capped_percentage < percentage:
                logger.info(f"Applied compatibility role type cap: {percentage:.1f}% → {capped_percentage:.1f}% ({reason})")
            return capped_percentage
        else:
            logger.info(f"🔍 No role type mismatch detected - candidate can score based on skills match")

        return percentage

    except Exception as e:
        logger.warning(f"Error applying compatibility role type caps: {e}")
        return percentage


def apply_role_type_caps(score: float, candidate_text: str, target_role_function: str) -> float:
    """
    Apply role type mismatch caps to override LLM scores that are too high.
    This ensures that candidates with different job functions are properly capped.

    Args:
        score (float): Original LLM score
        candidate_text (str): Candidate description/CV text
        target_role_function (str): Target position's primary role function

    Returns:
        float: Capped score if role type mismatch detected
    """
    try:
        # Convert to lowercase for easier matching
        candidate_lower = candidate_text.lower()
        target_lower = target_role_function.lower()

        # Define role type keywords for different job functions
        qa_keywords = ['qa engineer', 'quality assurance', 'test engineer', 'software tester', 'testing engineer', 'sdet']
        consultant_keywords = ['consultant', 'business analyst', 'analyst', 'administrator', 'admin']
        developer_keywords = ['developer', 'software engineer', 'programmer', 'software developer']

        # Check if target is QA-related
        is_qa_position = any(keyword in target_lower for keyword in ['qa', 'quality', 'test', 'testing'])

        if is_qa_position:
            # Check if candidate has actual QA experience
            has_qa_experience = any(keyword in candidate_lower for keyword in qa_keywords)

            if not has_qa_experience:
                # Check if candidate is primarily consultant/analyst/developer
                is_consultant = any(keyword in candidate_lower for keyword in consultant_keywords)
                is_developer = any(keyword in candidate_lower for keyword in developer_keywords)

                if is_consultant or is_developer:
                    # Apply 35% cap for role type mismatch
                    capped_score = min(score, 35.0)
                    if capped_score < score:
                        logger.info(f"Applied role type cap: {score:.1f}% → {capped_score:.1f}% (no QA experience)")
                    return capped_score

        return score

    except Exception as e:
        logger.warning(f"Error applying role type caps: {e}")
        return score


def get_skill_aware_candidate_analysis_prompt(position_skills: PositionSkillAnalysis) -> str:
    """
    Generate a skill-aware prompt template that uses prioritized skills for candidate evaluation.

    Args:
        position_skills (PositionSkillAnalysis): Prioritized skills analysis for the position

    Returns:
        str: Enhanced prompt template that incorporates skill priorities
    """
    # Build skill priority information for the prompt
    skills_info = []
    for skill in position_skills.prioritized_skills:
        importance_desc = {
            "critical": "CRITICAL (Must-have)",
            "important": "IMPORTANT (Highly valuable)",
            "nice_to_have": "NICE-TO-HAVE (Beneficial)"
        }.get(skill.importance.value, skill.importance.value)

        category_desc = skill.category.value.replace("_", " ").title()

        skills_info.append(
            f"• **{skill.name}** ({importance_desc}, {category_desc}, Weight: {skill.weight:.2f})"
            + (f" - {skill.description}" if skill.description else "")
        )

    skills_section = "\n".join(skills_info) if skills_info else "No specific skills identified."

    return f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using an intelligent skill prioritization approach.

**POSITION ANALYSIS:**
- **Position Title:** {position_skills.position_title}
- **Primary Role Function:** {position_skills.primary_role_function}
- **Skill Prioritization Rationale:** {position_skills.skill_weight_rationale}

**PRIORITIZED SKILLS FOR THIS POSITION:**
{skills_section}

**MANDATORY PRE-SCORING ANALYSIS:**
BEFORE calculating any scores, you MUST perform this EXACT analysis:

1. **Position Role Type Identification**:
   - Extract the PRIMARY job function from position title (e.g., "Salesforce QA Engineer" → PRIMARY: "QA Engineer", CONTEXT: "Salesforce")
   - Prioritize COMPOUND role names: "QA Engineer" > "Tester", "Data Engineer" > "Analyst"

2. **Candidate Experience Extraction**:
   - Look ONLY at actual job titles in work history - IGNORE skill lists
   - List ALL role types candidate has worked as (exact titles from their CV)
   - Prioritize COMPOUND role names and recent experience
   - Example: "Manual QA Engineer", "Salesforce Technical Consultant", "Business Analyst"

3. **Role Type Match Assessment**:
   - **EXACT MATCH**: Same compound role (QA Engineer → QA Engineer) = Can score 70-100%
   - **RELATED MATCH**: Related QA roles (Manual QA → Automation QA) = Max 60%
   - **ROLE TYPE MISMATCH**: Different job functions (Consultant → QA Engineer) = Max 35%
   - **NO EXCEPTIONS**: Skills cannot override role type mismatches

**ASSESSMENT METHODOLOGY:**

1. **Role Function Alignment (PRIMARY - 80% weight)**:

   **STEP 1 - EXTRACT CANDIDATE'S ACTUAL WORK EXPERIENCE:**
   - Scan the candidate's work history section (NOT skills section)
   - List every job title they have held (exact titles from CV)
   - Focus on compound role names: "QA Engineer", "Data Engineer", "Technical Consultant"
   - Recent experience (last 3-5 years) has higher weight

   **STEP 2 - ROLE TYPE CLASSIFICATION:**
   - **QA/Testing Roles**: QA Engineer, Test Engineer, Manual QA, Automation QA, SDET, Tester
   - **Data Roles**: Data Engineer, Data Architect, Data Scientist, Analytics Engineer
   - **Consultant Roles**: Technical Consultant, Business Consultant, Solution Consultant
   - **Developer Roles**: Software Developer, Software Engineer, Full Stack Developer
   - **Analyst Roles**: Business Analyst, Data Analyst, Systems Analyst

   **STEP 3 - MATCH ASSESSMENT:**
   - **EXACT MATCH**: Same compound role type = High compatibility potential
   - **RELATED MATCH**: Same domain, different level = Moderate compatibility
   - **ROLE MISMATCH**: Different job functions = Low compatibility due to role differences

2. **Skill-Based Evaluation (SECONDARY - 20% weight)**: Evaluate the candidate against the prioritized skills above:
   - **critical skills**: Must be present for candidate viability
   - **important skills**: Significantly impact the overall score
   - **nice_to_have skills**: Provide bonus points but don't penalize if missing
   - Weight each skill match according to the specified weights above

**CRITICAL MATCHING RULES:**
- **Role type is PRIMARY**: Job function alignment is more important than skill overlap
- **Mandatory role assessment**: Apply role type compatibility evaluation - prioritize role alignment
- **Skill weight application**: Use the specified weights to calculate the skill match portion - higher weights = more impact on score
- **STRICT SKILL MATCHING**: ONLY match skills that are LITERALLY mentioned in the candidate's CV
- **NO TECHNICAL INFERENCES**: DO NOT assume related technologies (C# ≠ .NET, Java ≠ Spring, Python ≠ Django)
- **EVIDENCE REQUIREMENT**: If a skill is not explicitly mentioned, it is NOT a match

**CRITICAL EXAMPLES - FOLLOW THESE EXACTLY:**

**MAJOR ROLE TYPE MISMATCHES:**
- **"Salesforce Technical Consultant" → "Salesforce QA Engineer"**: Low compatibility (Consultant ≠ QA Engineer, even with Salesforce skills)
- **"Business Analyst" → "QA Engineer"**: Low compatibility (Analyst ≠ QA Engineer)
- **"Software Developer" → "QA Engineer"**: Low compatibility (Developer ≠ QA Engineer)
- **"Data Analyst" → "Data Engineer"**: Low compatibility (Analyst ≠ Engineer)

**EXACT ROLE MATCHES:**
- **"QA Engineer" → "QA Engineer"**: High compatibility based on technical skills
- **"Manual QA Engineer" → "Automation QA Engineer"**: High compatibility based on automation skills
- **"Data Engineer" → "Senior Data Engineer"**: High compatibility based on seniority match

**RELATED ROLE MATCHES:**
- **"Manual Tester" → "QA Engineer"**: Moderate compatibility (related but different levels)
- **"Test Analyst" → "QA Engineer"**: Moderate compatibility (related but different focus)
- **Evidence requirement**: Only count skills where you can find clear evidence in the candidate's background
- **CRITICAL SKILL CLASSIFICATION RULE**: If a skill is not explicitly mentioned in the candidate's CV, it MUST go in "skill_not_matched" and CANNOT appear in "skill_match_analysis"
- **NO CONTRADICTIONS**: A skill cannot appear in both matched and not matched lists
- **Partial matches**: Give partial credit for related or similar skills, but be conservative
- **Flexible scoring**: Score based on weighted skill alignment - candidates strong in high-weight skills can score well even if missing some lower-weight skills

**SCORING CALCULATION:**
1. **FIRST**: Assess role function alignment (0-100%) - this is the PRIMARY factor
2. **SECOND**: Calculate weighted skill match score using the skill weights above
3. **Final Score = (Role Function Score × 0.8) + (Skill Match Score × 0.2)**
4. **ROLE TYPE ASSESSMENT**: Apply proper role type evaluation in final calculation:
   - Major role mismatches result in low compatibility scores
   - Different role types receive low compatibility assessment
   - Related role types receive moderate compatibility assessment
5. Ensure role type alignment dominates - skills cannot compensate for fundamental role mismatches

**OUTPUT REQUIREMENTS:**
Return a JSON response with EXACTLY this structure:

{{
  "LLM_Analysis": {{
    "reason": "Detailed explanation including skill-by-skill analysis and how weights were applied",
    "skill_match_analysis": {{"skill_name": "explanation for each matched skill with evidence"}},
    "skill_not_matched": ["list of skills from the prioritized list that were not found"]
  }},
  "extra_questions": ["list of additional questions for further evaluation"],
  "highlights": ["list of key points about the candidate's suitability"],
  "Score": 85.5
}}

**CRITICAL REQUIREMENTS:**
- The Score field must be at the TOP LEVEL, not inside LLM_Analysis
- **MANDATORY FINAL VALIDATION CHECKLIST** - Complete BEFORE outputting Score:

  ✓ **Step 1**: List candidate's actual job titles from work history
  ✓ **Step 2**: Identify target role from position (e.g., "QA Engineer", "Data Engineer")
  ✓ **Step 3**: Check role type match:
    - SAME compound role (QA Engineer → QA Engineer): High compatibility potential
    - RELATED roles (Manual QA → QA Engineer): Moderate compatibility potential
    - DIFFERENT roles (Consultant → QA Engineer): Low compatibility due to role mismatch
  ✓ **Step 4**: Apply role type assessment - prioritize role alignment over technical skills
  ✓ **Step 5**: In reason field, state: "Role analysis: [candidate roles] → [target role] = [compatibility assessment]"

**FINAL VALIDATION QUESTIONS:**
- "What job titles has this candidate actually held?" (Look at work history, not skills)
- "Is this the SAME job function as the target role?"
- "If different job functions, is my score ≤ 35%?"
- "Have I prioritized role experience over technical skills?"
- Only include skills from the prioritized list above in skill_match_analysis and skill_not_matched
- Be specific about evidence found for each skill match
- Apply the scoring methodology exactly as specified above

**MANDATORY SKILL MATCHING VALIDATION:**
Before finalizing your response, check each skill:
1. For each skill in "skill_match_analysis": Is this skill EXPLICITLY mentioned in the candidate's CV? If NO, move it to "skill_not_matched"
2. For each skill in "skill_not_matched": Is this skill actually missing from the candidate's CV? If YES, keep it there
3. NO SKILL can appear in both lists
4. NO TECHNICAL INFERENCES: C# ≠ .NET, Java ≠ Spring, Python ≠ Django

**ABSOLUTE RULE**: A Salesforce Consultant has NEVER worked as a QA Engineer → MAXIMUM 35% SCORE"""


def get_skill_aware_batch_candidate_analysis_prompt(position_skills: PositionSkillAnalysis) -> str:
    """
    Generate a skill-aware batch prompt template for evaluating multiple candidates.

    Args:
        position_skills (PositionSkillAnalysis): Prioritized skills analysis for the position

    Returns:
        str: Enhanced batch prompt template that incorporates skill priorities
    """
    # Build skill priority information for the prompt
    skills_info = []
    for skill in position_skills.prioritized_skills:
        importance_desc = {
            "critical": "CRITICAL (Must-have)",
            "important": "IMPORTANT (Highly valuable)",
            "nice_to_have": "NICE-TO-HAVE (Beneficial)"
        }.get(skill.importance.value, skill.importance.value)

        category_desc = skill.category.value.replace("_", " ").title()

        skills_info.append(
            f"• **{skill.name}** ({importance_desc}, {category_desc}, Weight: {skill.weight:.2f})"
            + (f" - {skill.description}" if skill.description else "")
        )

    skills_section = "\n".join(skills_info) if skills_info else "No specific skills identified."

    return f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and multiple candidates' CVs using an intelligent skill prioritization approach.

**POSITION ANALYSIS:**
- **Position Title:** {position_skills.position_title}
- **Primary Role Function:** {position_skills.primary_role_function}
- **Skill Prioritization Rationale:** {position_skills.skill_weight_rationale}

**PRIORITIZED SKILLS FOR THIS POSITION:**
{skills_section}

**ASSESSMENT METHODOLOGY FOR EACH CANDIDATE:**

1. **Skill-Based Evaluation (PRIMARY - 70% weight)**: Evaluate each candidate against the prioritized skills above:
   - **critical skills**: Must be present for candidate viability (if missing, maximum score is 40%)
   - **important skills**: Significantly impact the overall score
   - **nice_to_have skills**: Provide bonus points but don't penalize if missing
   - Weight each skill match according to the specified weights above

2. **Role Function Alignment (SECONDARY - 30% weight)**: Assess overall alignment with the primary role function

**CRITICAL MATCHING RULES:**
- **Primary skill priority**: Core competency skills (primary job function) are most important
- **Skill weight application**: Use the specified weights to calculate the skill match portion - higher weights = more impact on score
- **STRICT SKILL MATCHING**: ONLY match skills that are LITERALLY mentioned in the candidate's CV
- **NO TECHNICAL INFERENCES**: DO NOT assume related technologies (C# ≠ .NET, Java ≠ Spring, Python ≠ Django)
- **Evidence requirement**: Only count skills where you can find clear evidence in the candidate's background
- **CRITICAL SKILL CLASSIFICATION RULE**: If a skill is not explicitly mentioned in the candidate's CV, it MUST go in "skill_not_matched" and CANNOT appear in "skill_match_analysis"
- **NO CONTRADICTIONS**: A skill cannot appear in both matched and not matched lists
- **Partial matches**: Give partial credit for related or similar skills, but be conservative
- **Flexible scoring**: Score based on weighted skill alignment - candidates strong in high-weight skills can score well even if missing some lower-weight skills

**OUTPUT REQUIREMENTS:**
Return a JSON object with "candidates_analysis" array containing analysis for each candidate in order.

Each candidate analysis must have EXACTLY this structure:

{{
  "candidates_analysis": [
    {{
      "LLM_Analysis": {{
        "reason": "Detailed explanation including skill-by-skill analysis and how weights were applied",
        "skill_match_analysis": {{"skill_name": "explanation for each matched skill with evidence"}},
        "skill_not_matched": ["list of skills from the prioritized list that were not found"]
      }},
      "extra_questions": ["list of additional questions"],
      "highlights": ["list of key points about suitability"],
      "Score": 85.5
    }}
  ]
}}

**CRITICAL REQUIREMENTS:**
- The Score field must be at the TOP LEVEL of each candidate analysis, not inside LLM_Analysis
- In each reason field, explicitly mention how skill weights were applied and any score caps
- Only include skills from the prioritized list above in skill_match_analysis and skill_not_matched
- Be specific about evidence found for each skill match
- Apply the scoring methodology exactly as specified above for each candidate"""


def evaluate_candidate_with_skill_priority(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> dict:
    """
    Enhanced candidate evaluation that first extracts prioritized skills from the position,
    then performs skill-aware matching.

    Args:
        candidate (str): Candidate description/CV text
        position (str): Position description text
        models_order (list): Order of LLM models to try

    Returns:
        dict: Analysis response with skill-aware evaluation
    """
    with tracer.start_as_current_span("evaluate_candidate_with_skill_priority") as span:
        span.set_attribute("eval.candidate_length", len(candidate))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluating candidate with skill priority analysis")

        try:
            # Step 1: Extract prioritized skills from position
            with log_time_block("Extract Position Skills"):
                position_skills = extract_position_skills_for_matching(position, models_order)

            span.set_attribute("eval.skills_extracted", len(position_skills.prioritized_skills))

            # Step 2: Generate skill-aware prompt
            system_prompt = get_skill_aware_candidate_analysis_prompt(position_skills)

            # Step 3: Evaluate candidate using skill-aware prompt
            messages = [
                {"role": "user", "content": f"Candidate description: {candidate}"},
                {"role": "user", "content": f"Position description: {position}"},
            ]

            with log_time_block("Skill-Aware Candidate Evaluation"):
                analysis_response = inference_with_fallback(
                    task_prompt=system_prompt,
                    model_schema=PositionCandidateAnalysis,
                    user_messages=messages,
                    models_order=models_order
                )

            if not analysis_response:
                logger.error("Skill-aware LLM analysis failed for candidate evaluation")
                return {
                    "LLM_Analysis": {
                        "reason": "Skill-aware LLM analysis failed",
                        "skill_match_analysis": {},
                        "skill_not_matched": []
                    },
                    "extra_questions": [],
                    "highlights": [],
                    "Score": 0.0,
                    "position_skills_analysis": position_skills.model_dump()
                }

            analysis_response = analysis_response.model_dump()
            score = analysis_response.get("Score", 0.0)
            try:
                score = float(score)
            except ValueError:
                score = 0.0

            # CRITICAL: Apply role type mismatch caps in code to override LLM if needed
            score = apply_role_type_caps(score, candidate, position_skills.primary_role_function)

            # Update the score in the response
            analysis_response["Score"] = score

            # Add position skills analysis to response for transparency
            analysis_response["position_skills_analysis"] = position_skills.model_dump()

            logger.info(
                "Skill-aware candidate evaluation completed",
                extra={
                    "custom_dimensions": {
                        "score": score,
                        "position_title": position_skills.position_title,
                        "skills_count": len(position_skills.prioritized_skills)
                    }
                }
            )

            return analysis_response

        except Exception as e:
            logger.error("Error in skill-aware candidate evaluation: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback to regular evaluation
            logger.info("Falling back to regular candidate evaluation")
            return evaluate_candidate(candidate, position, models_order)


def evaluate_candidates_batch_with_skill_priority(candidates: List[str], position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> BatchMatchAnalysis:
    """
    Enhanced batch candidate evaluation that first extracts prioritized skills from the position,
    then performs skill-aware matching for multiple candidates.

    Args:
        candidates (List[str]): List of candidate descriptions/CV texts
        position (str): Position description text
        models_order (list): Order of LLM models to try

    Returns:
        BatchMatchAnalysis: Batch analysis with skill-aware evaluation
    """
    with tracer.start_as_current_span("evaluate_candidates_batch_with_skill_priority") as span:
        span.set_attribute("eval.candidates_count", len(candidates))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluating %d candidates with skill priority analysis in batch mode", len(candidates))

        try:
            # Step 1: Extract prioritized skills from position
            with log_time_block("Extract Position Skills for Batch"):
                position_skills = extract_position_skills_for_matching(position, models_order)

            span.set_attribute("eval.skills_extracted", len(position_skills.prioritized_skills))

            # Step 2: Generate skill-aware batch prompt
            task_prompt = get_skill_aware_batch_candidate_analysis_prompt(position_skills)

            # Step 3: Prepare candidates for batch analysis
            candidates_prompt = "\n".join([
                f"[Init Candidate] Candidate {i + 1}:\n{candidate} [End of Candidate]"
                for i, candidate in enumerate(candidates)
            ])

            # Create user messages
            user_messages = [
                HumanMessage(content=f"Position Description:\n{position}"),
                HumanMessage(content=f"Candidates to Evaluate:\n{candidates_prompt}")
            ]

            # Get schema text for BatchMatchAnalysis
            schema_text = """
class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")

class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    )
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.")

class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
            """

            with log_time_block("Skill-Aware Batch LLM Analysis"):
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=BatchMatchAnalysis,
                    user_messages=user_messages,
                    model_schema_text=schema_text,
                    models_order=models_order
                )

                if not result:
                    logger.error("Skill-aware batch analysis failed - falling back to regular batch analysis")
                    return evaluate_candidates_batch(candidates, position, models_order)

                # Validate and normalize scores
                for analysis in result.candidates_analysis:
                    try:
                        score = float(analysis.Score)
                        score = max(0.0, min(100.0, score))  # Clamp between 0 and 100
                        analysis.Score = score
                    except (ValueError, TypeError):
                        analysis.Score = 0.0

                # Add position skills analysis to the result for transparency
                # Note: We add this as a custom attribute to the result object
                result.position_skills_analysis = position_skills.model_dump()

                logger.info(
                    "Skill-aware batch analysis completed successfully",
                    extra={
                        "custom_dimensions": {
                            "candidates_count": len(candidates),
                            "analysis_count": len(result.candidates_analysis),
                            "position_title": position_skills.position_title,
                            "skills_count": len(position_skills.prioritized_skills)
                        }
                    }
                )

                return result

        except Exception as e:
            logger.error("Error in skill-aware batch analysis: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback to regular batch evaluation
            logger.info("Falling back to regular batch candidate evaluation")
            return evaluate_candidates_batch(candidates, position, models_order)


def get_candidate_analysis_with_skill_priority(candidate_text: str, processed_position: str, models_order: list = MODELS_CONFIG["default_models_order"]) -> CompatibilityEvaluation:
    """
    Enhanced custom prompt evaluation that first extracts prioritized skills from the position,
    then performs skill-aware compatibility evaluation.

    Args:
        candidate_text (str): Candidate's CV/resume text
        processed_position (str): Processed position description
        models_order (list): Order of LLM models to try

    Returns:
        CompatibilityEvaluation: Enhanced compatibility evaluation with skill prioritization
    """
    logger.info("🚀 FUNCTION CALLED: get_candidate_analysis_with_skill_priority")

    # Convert candidate_text to string if it's not already
    if not isinstance(candidate_text, str):
        logger.warning(f"🚀 candidate_text is not a string, type: {type(candidate_text)}")
        if isinstance(candidate_text, dict):
            # If it's a dict, try to extract text content or convert to JSON string
            candidate_text = str(candidate_text)
        else:
            candidate_text = str(candidate_text)

    # Safe logging for candidate text preview
    try:
        logger.info(f"🚀 Candidate text preview: {candidate_text[:100]}...")
    except Exception as e:
        logger.warning(f"🚀 Could not log candidate text preview: {e}")

    with tracer.start_as_current_span("get_candidate_analysis_with_skill_priority") as span:
        span.set_attribute("eval.candidate_length", len(candidate_text))
        span.set_attribute("eval.position_length", len(processed_position))

        logger.info("Performing skill-aware compatibility evaluation")

        try:
            # Step 1: Extract prioritized skills from position
            with log_time_block("Extract Position Skills for Custom Analysis"):
                position_skills = extract_position_skills_for_matching(processed_position, models_order)

            span.set_attribute("eval.skills_extracted", len(position_skills.prioritized_skills))

            # Step 2: Build skill-aware custom prompt
            skills_info = []
            for skill in position_skills.prioritized_skills:
                importance_desc = {
                    "critical": "CRITICAL (Must-have)",
                    "important": "IMPORTANT (Highly valuable)",
                    "nice_to_have": "NICE-TO-HAVE (Beneficial)"
                }.get(skill.importance.value, skill.importance.value)

                category_desc = skill.category.value.replace("_", " ").title()

                skills_info.append(
                    f"• **{skill.name}** ({importance_desc}, {category_desc}, Weight: {skill.weight:.2f})"
                    + (f" - {skill.description}" if skill.description else "")
                )

            skills_section = "\n".join(skills_info) if skills_info else "No specific skills identified."

            task_prompt = f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV using intelligent skill prioritization.

**POSITION ANALYSIS:**
- **Position Title:** {position_skills.position_title}
- **Primary Role Function:** {position_skills.primary_role_function}
- **Skill Prioritization Rationale:** {position_skills.skill_weight_rationale}

**PRIORITIZED SKILLS FOR THIS POSITION:**
{skills_section}

**ASSESSMENT METHODOLOGY:**

1. **Skill-Based Evaluation (PRIMARY - 70% weight)**: Evaluate the candidate against the prioritized skills above:
   - **critical skills**: Must be present for candidate viability (if missing, maximum compatibility is 40%)
   - **important skills**: Significantly impact the overall compatibility
   - **nice_to_have skills**: Provide bonus points but don't penalize if missing
   - Weight each skill match according to the specified weights above

2. **Role Function Alignment (SECONDARY - 30% weight)**: Assess overall alignment with the primary role function

**CRITICAL MATCHING RULES:**
- **Primary skill priority**: Core competency skills (primary job function) are most important
- **Skill weight application**: Use the specified weights to calculate the skill match portion - higher weights = more impact on score
- **STRICT SKILL MATCHING**: ONLY match skills that are LITERALLY mentioned in the candidate's CV
- **NO TECHNICAL INFERENCES**: DO NOT assume related technologies (C# ≠ .NET, Java ≠ Spring, Python ≠ Django)
- **Evidence requirement**: Only count skills where you can find clear evidence in the candidate's background
- **CRITICAL SKILL CLASSIFICATION RULE**: If a skill is not explicitly mentioned in the candidate's CV, it MUST go in "skill_not_matched" and CANNOT appear in "skill_match_analysis"
- **NO CONTRADICTIONS**: A skill cannot appear in both matched and not matched lists
- **Partial matches**: Give partial credit for related or similar skills, but be conservative
- **Flexible scoring**: Score based on weighted skill alignment - candidates strong in high-weight skills can score well even if missing some lower-weight skills

**COMPATIBILITY CALCULATION:**
1. Calculate weighted skill match score using the skill weights above (higher weight skills contribute more to the score)
2. Assess role function alignment (0-100%)
3. Final Compatibility = (Skill Match Score × 0.7) + (Role Function Score × 0.3)
4. Ensure scoring reflects skill prioritization - candidates with strong core competency skills should score higher than those with only technical tool skills

**ANALYSIS REQUIREMENTS:**
1. Compatibility percentage with the position (applying the skill prioritization rules above)
2. Recommendation: Whether the candidate should move forward (consider both skill fit and role alignment)
3. Matches Found: Points where candidate meets requirements (focus on prioritized skills)
4. Missing: Key requirements not evident in candidate's background (prioritize critical skill gaps)

Job Description:
{processed_position}

CV:
{candidate_text}

Return the analysis in strict JSON format according to the CompatibilityEvaluation model, containing the following required fields:
- compatibilityPercentage (float with one decimal place between 0.0 and 100.0, e.g., 83.7, 42.6 — avoid round numbers or multiples of 5)
- recommendation (boolean)
- justification (string - must explain skill prioritization assessment and role alignment)
- matchesFound (array of strings - focus on prioritized skills)
- missing (array of strings - focus on critical and important skills)

Requirements:
1. The field compatibilityPercentage **must be a float, not an integer**, and must include **exactly one decimal place** (e.g., 87.3, 46.8).
2. **Do not round or simplify** compatibilityPercentage to multiples of 5 or integers.
3. **MANDATORY**: Apply skill prioritization weighting - candidates with strong core competency skills should score higher than those with only technical tool skills.
4. All fields must be filled, even if matchesFound or missing are empty arrays.
5. Output must be valid JSON — do not include explanations or extra formatting.
6. In justification, explicitly mention the skill prioritization assessment and how skill weights influenced the score.
"""

            schema_text = get_related_class_definitions(CompatibilityEvaluation)

            # Step 3: Call the inference function with the skill-aware prompt
            with log_time_block("Skill-Aware Custom Analysis"):
                analysis = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=CompatibilityEvaluation,
                    user_messages=[HumanMessage(content="")],  # no extra user message needed
                    model_schema_text=schema_text,
                    models_order=models_order,
                )

            if not analysis:
                logger.error("Skill-aware custom analysis failed - falling back to regular analysis")
                return get_candidate_analysis_custom_prompt(candidate_text, processed_position)

            logger.info(
                "Skill-aware custom analysis completed",
                extra={
                    "custom_dimensions": {
                        "compatibility_percentage": analysis.compatibilityPercentage,
                        "position_title": position_skills.position_title,
                        "skills_count": len(position_skills.prioritized_skills)
                    }
                }
            )

            # CRITICAL: Apply role type mismatch caps to compatibilityPercentage
            original_percentage = analysis.compatibilityPercentage
            logger.info(f"BEFORE CAP ENFORCEMENT: compatibilityPercentage = {original_percentage:.1f}%")
            logger.info(f"Candidate text contains: {candidate_text[:200]}...")
            logger.info(f"Position role function: {position_skills.primary_role_function}")

            capped_percentage = apply_role_type_caps_to_compatibility(
                original_percentage, candidate_text, position_skills.primary_role_function
            )

            # Update the analysis with capped percentage
            analysis.compatibilityPercentage = capped_percentage
            logger.info(f"AFTER CAP ENFORCEMENT: compatibilityPercentage = {analysis.compatibilityPercentage:.1f}%")
            logger.info(f"DEBUG: capped_percentage variable = {capped_percentage:.1f}%")
            logger.info(f"DEBUG: analysis object type = {type(analysis)}")

            if capped_percentage < original_percentage:
                logger.info(f"✓ Applied compatibility cap: {original_percentage:.1f}% → {capped_percentage:.1f}%")
            else:
                logger.warning(f"⚠️ NO CAP APPLIED: Score remained {original_percentage:.1f}%")

            return analysis

        except Exception as e:
            logger.error("Error in skill-aware custom analysis: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback to regular custom analysis
            logger.info("Falling back to regular custom analysis")
            return get_candidate_analysis_custom_prompt(candidate_text, processed_position)


# Experience-based reranking functions
def get_experience_extraction_prompt() -> str:
    """
    Generate a prompt template for extracting structured work experience from candidate CVs.
    Focuses on role-specific experience that's relevant for matching.
    """
    return """You are an expert HR analyst specializing in extracting structured work experience from candidate resumes and CVs. Your task is to analyze the provided candidate information and extract detailed work experience in a structured format.

**EXTRACTION METHODOLOGY:**

1. **Identify All Work Experiences**: Extract every professional role, including full-time, part-time, contract, and consulting positions.

2. **Focus on Role-Specific Details**: Pay special attention to:
   - Exact job titles and role responsibilities
   - Technologies, tools, and platforms used
   - Industry domains and business contexts
   - Duration and progression of responsibilities

3. **Calculate Experience Duration**: Convert all date ranges to years (e.g., "Jan 2020 - Jun 2022" = 2.5 years)

4. **Extract Technical Progression**: Identify how the candidate's skills and responsibilities have evolved over time.

**CRITICAL FOCUS AREAS:**

- **Role Types**: Clearly identify if the candidate has experience as QA/Tester, Developer, Data Architect, DevOps, Product Manager, etc.
- **Technology Stack**: Extract specific technologies (e.g., Salesforce, Python, AWS, React, SQL, etc.)
- **Domain Expertise**: Identify industry experience (Healthcare, Finance, E-commerce, etc.)
- **Seniority Indicators**: Look for leadership, mentoring, architecture, or strategic responsibilities

**EXAMPLE ANALYSIS:**
For a candidate with "Salesforce QA Engineer at TechCorp (2020-2023)":
- role_title: "Salesforce QA Engineer"
- company: "TechCorp"
- duration_years: 3.0
- key_responsibilities: ["Automated testing of Salesforce applications", "Manual testing of custom Apex code", "Test case design and execution"]
- technologies_used: ["Salesforce", "Apex", "Selenium", "TestNG", "SOQL"]
- industry_domain: "Technology"

**OUTPUT REQUIREMENTS:**
Return a JSON response matching the ExperienceAnalysis schema with:
- total_years_experience: Total professional experience in years
- relevant_experiences: Array of WorkExperience objects for each role
- primary_role_types: List of main role types (e.g., ["QA Engineer", "Test Automation Engineer"])
- domain_expertise: List of industries/domains with significant experience
- technical_progression: Summary of skill evolution over time

**CRITICAL INSTRUCTIONS:**
- Only extract information that is explicitly stated or clearly implied in the CV
- Be conservative with duration calculations - if unclear, estimate lower
- Focus on professional experience, not academic projects unless specifically relevant
- If a role spans multiple technologies or responsibilities, capture all of them
- Pay special attention to role-specific experience like "Salesforce QA", "Data Architecture", "DevOps Engineering"

Analyze the following candidate information and extract structured work experience:"""


def extract_candidate_experience(candidate_text: str, models_order: list = MODELS_CONFIG["default_models_order"]) -> Optional[ExperienceAnalysis]:
    """
    Extract structured work experience from candidate CV using LLM analysis.

    Args:
        candidate_text (str): Candidate's CV/resume text
        models_order (list): Order of LLM models to try

    Returns:
        ExperienceAnalysis: Structured experience data or None if extraction fails
    """
    with tracer.start_as_current_span("extract_candidate_experience") as span:
        span.set_attribute("candidate.text_length", len(candidate_text))

        logger.info("Extracting structured experience from candidate CV")

        try:
            task_prompt = get_experience_extraction_prompt()

            user_messages = [
                HumanMessage(content=f"Candidate CV/Resume:\n\n{candidate_text}")
            ]

            with log_time_block("Experience Extraction LLM Call"):
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=ExperienceAnalysis,
                    user_messages=user_messages,
                    models_order=models_order
                )

            if not result:
                logger.error("Experience extraction failed - all models failed")
                return None

            # Validate extracted data
            if result.total_years_experience < 0:
                result.total_years_experience = 0.0

            # Ensure duration calculations are reasonable
            for exp in result.relevant_experiences:
                if exp.duration_years < 0:
                    exp.duration_years = 0.0
                elif exp.duration_years > 50:  # Sanity check
                    exp.duration_years = 50.0

            logger.info(
                "Successfully extracted experience data",
                extra={
                    "custom_dimensions": {
                        "total_years": result.total_years_experience,
                        "experiences_count": len(result.relevant_experiences),
                        "primary_roles": len(result.primary_role_types)
                    }
                }
            )

            return result

        except Exception as e:
            logger.error("Error extracting candidate experience: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))
            return None


def get_experience_relevance_scoring_prompt() -> str:
    """
    Generate a prompt template for scoring how relevant a candidate's experience is to a specific position.
    """
    return """You are an expert technical recruiter specializing in evaluating how well a candidate's work experience aligns with specific job requirements. Your task is to analyze the candidate's extracted experience against the position requirements and provide detailed relevance scoring.

**SCORING METHODOLOGY:**

1. **Role Type Match Analysis (40% weight - MOST CRITICAL FACTOR)**:
   - Compare candidate's primary role types with the target position role
   - **Perfect Match**: Same role type (e.g., QA Engineer → QA Engineer) = 90-100 points
   - **Strong Match**: Related role types (e.g., Manual QA → Automation QA, QA Analyst → QA Engineer) = 70-89 points
   - **Moderate Match**: Adjacent role types with relevant skills (e.g., Test Engineer → QA Engineer) = 40-69 points
   - **Weak Match**: Different role types with minimal transferable skills (e.g., Developer → QA) = 15-39 points
   - **No Match**: Completely different role types (e.g., Salesforce Admin → QA Engineer) = 0-14 points
   - **CRITICAL**: Technology expertise cannot compensate for role type mismatches

2. **Technology Match Analysis (20% weight)**:
   - Evaluate overlap between candidate's technology experience and position requirements
   - Consider both exact matches and related technologies
   - Weight by importance of each technology to the role

3. **Domain Match Analysis (15% weight)**:
   - Assess alignment between candidate's industry experience and position context
   - Consider domain-specific knowledge and business understanding

4. **Seniority Match Analysis (25% weight)**:
   - Compare candidate's experience level with position requirements
   - Consider years of experience, leadership roles, and responsibility progression
   - Account for over-qualification or under-qualification

**CRITICAL EVALUATION RULES:**

- **ROLE TYPE IS PRIMARY**: The candidate's role type alignment is THE MOST IMPORTANT factor. A QA Engineer with limited Salesforce experience is better than a Salesforce Developer with no QA experience for a QA role.
- **Technology is Secondary**: Technology skills are important but should NEVER override role type mismatches. Technology can be learned, but role expertise takes years to develop.
- **Experience Depth Over Breadth**: Prioritize deep, relevant role experience over broad but shallow technology experience
- **Recent Experience Priority**: More recent experience should be weighted higher than older experience
- **Role-Specific Experience**: Direct role experience (e.g., "QA Engineer" for QA roles) should score very highly regardless of technology domain
- **Technology Context**: Technology skills should enhance role matches, not compensate for role mismatches
- **Progressive Responsibility**: Evidence of growing responsibility and complexity should boost scores

**SCORING EXAMPLES:**

For a "Senior Salesforce QA Engineer" position (PRIMARY ROLE: QA Engineer, TECHNOLOGY CONTEXT: Salesforce):

**CORRECT PRIORITIZATION (with 40% role weight, 20% technology weight):**
- Candidate A: 5 years as "Salesforce QA Engineer"
  → role_type_match_score = 95, technology_match_score = 90, overall ≈ 89
- Candidate B: 4 years as "QA Engineer" (non-Salesforce) + 1 year Salesforce exposure
  → role_type_match_score = 85, technology_match_score = 40, overall ≈ 70
- Candidate C: 6 years as "Salesforce Developer" with minimal testing
  → role_type_match_score = 25, technology_match_score = 85, overall ≈ 44

**KEY INSIGHT**: Candidate B (QA with limited Salesforce) should score HIGHER than Candidate C (Salesforce expert with no QA) because role expertise is primary.

For a "Data Architect" position:
- Strong Data Architect with Oracle experience vs Strong Oracle DBA with no architecture experience
- The Data Architect should score higher even if Oracle skills are less developed

**OUTPUT REQUIREMENTS:**
Return a JSON response matching the ExperienceRelevanceScore schema with:
- overall_relevance_score: Weighted average of all component scores (0.0 to 100.0)
- role_type_match_score: Role alignment score (0.0 to 100.0)
- technology_match_score: Technology alignment score (0.0 to 100.0)
- domain_match_score: Domain/industry alignment score (0.0 to 100.0)
- seniority_match_score: Experience level alignment score (0.0 to 100.0)
- relevance_explanation: Detailed explanation of scoring rationale
- key_experience_highlights: Most relevant experience points for this position

**CALCULATION FORMULA:**
overall_relevance_score = (role_type_match_score × 0.40) + (technology_match_score × 0.20) + (domain_match_score × 0.15) + (seniority_match_score × 0.25)

**CRITICAL INSTRUCTIONS:**
- **ROLE TYPE MUST BE PRIMARY**: When scoring, always prioritize role type alignment over technology alignment
- **NO TECHNOLOGY COMPENSATION**: High technology scores should NEVER compensate for low role type scores
- **Position Analysis**: Carefully identify the PRIMARY role (e.g., "QA Engineer") vs TECHNOLOGY CONTEXT (e.g., "Salesforce") in the position title
- Be objective and evidence-based in scoring
- Explain your reasoning clearly in the relevance_explanation, specifically addressing role vs technology prioritization
- Highlight specific experience elements that drive the scores
- Consider both positive matches and gaps in experience
- Account for the specific requirements and context of the target position

Analyze the following candidate experience against the position requirements:"""


def score_experience_relevance(
    experience_analysis: ExperienceAnalysis,
    position_text: str,
    models_order: list = MODELS_CONFIG["default_models_order"]
) -> Optional[ExperienceRelevanceScore]:
    """
    Score how relevant a candidate's experience is to a specific position using LLM analysis.

    Args:
        experience_analysis (ExperienceAnalysis): Structured experience data from candidate
        position_text (str): Position description text
        models_order (list): Order of LLM models to try

    Returns:
        ExperienceRelevanceScore: Relevance scoring or None if scoring fails
    """
    with tracer.start_as_current_span("score_experience_relevance") as span:
        span.set_attribute("experience.total_years", experience_analysis.total_years_experience)
        span.set_attribute("experience.roles_count", len(experience_analysis.primary_role_types))
        span.set_attribute("position.text_length", len(position_text))

        logger.info("Scoring experience relevance against position requirements")

        try:
            task_prompt = get_experience_relevance_scoring_prompt()

            # Format experience data for the prompt
            experience_summary = f"""
CANDIDATE EXPERIENCE SUMMARY:
- Total Years of Experience: {experience_analysis.total_years_experience}
- Primary Role Types: {', '.join(experience_analysis.primary_role_types)}
- Domain Expertise: {', '.join(experience_analysis.domain_expertise)}
- Technical Progression: {experience_analysis.technical_progression}

DETAILED WORK EXPERIENCE:
"""
            for i, exp in enumerate(experience_analysis.relevant_experiences, 1):
                experience_summary += f"""
{i}. {exp.role_title} at {exp.company} ({exp.duration_years} years)
   - Key Responsibilities: {'; '.join(exp.key_responsibilities)}
   - Technologies Used: {', '.join(exp.technologies_used)}
   - Industry Domain: {exp.industry_domain or 'Not specified'}
"""

            user_messages = [
                HumanMessage(content=f"Position Requirements:\n\n{position_text}"),
                HumanMessage(content=experience_summary)
            ]

            with log_time_block("Experience Relevance Scoring LLM Call"):
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=ExperienceRelevanceScore,
                    user_messages=user_messages,
                    models_order=models_order
                )

            if not result:
                logger.error("Experience relevance scoring failed - all models failed")
                return None

            # Validate and normalize scores
            for score_field in ['overall_relevance_score', 'role_type_match_score', 'technology_match_score',
                                'domain_match_score', 'seniority_match_score']:
                score_value = getattr(result, score_field)
                if score_value < 0.0:
                    setattr(result, score_field, 0.0)
                elif score_value > 100.0:
                    setattr(result, score_field, 100.0)

            logger.info(
                "Successfully scored experience relevance",
                extra={
                    "custom_dimensions": {
                        "overall_score": result.overall_relevance_score,
                        "role_match": result.role_type_match_score,
                        "tech_match": result.technology_match_score
                    }
                }
            )

            return result

        except Exception as e:
            logger.error("Error scoring experience relevance: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))
            return None


def rerank_candidates_by_experience(
    results: List[tuple],
    processed_position: str,
    experience_weight: float = 0.8,
    similarity_weight: float = 0.2,
    models_order: list = MODELS_CONFIG["default_models_order"],
    timeout_seconds: int = 300
) -> List[tuple]:
    """
    Rerank candidates based on experience relevance to the position.

    This function takes the vector similarity results and reorders candidates by combining
    vector similarity scores with experience-based relevance scores.

    Args:
        results (List[tuple]): List of candidate tuples from vector similarity search
                              Format: [candidate_id, proj_id, candidate_info, similarity_score, candidate_text]
        processed_position (str): Position description text
        experience_weight (float): Weight for experience relevance score (default: 0.8)
        similarity_weight (float): Weight for vector similarity score (default: 0.2)
        models_order (list): Order of LLM models to try
        timeout_seconds (int): Maximum time to spend on reranking (default: 300)

    Returns:
        List[tuple]: Reordered list of candidate tuples with same structure

    Error Handling:
        - Falls back to similarity-only scoring if experience extraction fails
        - Falls back to original order if entire reranking process fails
        - Logs all errors and fallback actions for debugging
    """
    with tracer.start_as_current_span("rerank_candidates_by_experience") as span:
        span.set_attribute("candidates.count", len(results))
        span.set_attribute("reranking.experience_weight", experience_weight)
        span.set_attribute("reranking.similarity_weight", similarity_weight)

        logger.info("Starting experience-based reranking for %d candidates", len(results))

        if not results:
            logger.warning("No candidates to rerank")
            return results

        # Validate input parameters
        if not processed_position or not processed_position.strip():
            logger.error("Position description is empty, cannot perform experience-based reranking")
            return results

        if experience_weight < 0 or similarity_weight < 0 or (experience_weight + similarity_weight) <= 0:
            logger.error("Invalid weights for reranking: experience_weight=%f, similarity_weight=%f",
                         experience_weight, similarity_weight)
            return results

        # Normalize weights to sum to 1.0
        total_weight = experience_weight + similarity_weight
        experience_weight = experience_weight / total_weight
        similarity_weight = similarity_weight / total_weight

        try:
            reranked_candidates = []

            with log_time_block("Experience-Based Reranking"):
                for idx, result_row in enumerate(results):
                    candidate_id = result_row[0]
                    similarity_score = result_row[3] if len(result_row) > 3 else 0.0
                    candidate_text = result_row[4] if len(result_row) > 4 else ""

                    logger.debug("Processing candidate %s (%d/%d)", candidate_id, idx + 1, len(results))

                    # Step 1: Extract candidate experience
                    experience_analysis = extract_candidate_experience(candidate_text, models_order)

                    if not experience_analysis:
                        logger.warning("Failed to extract experience for candidate %s, using similarity score only", candidate_id)
                        # Use only similarity score if experience extraction fails
                        combined_score = similarity_score * 100  # Convert to 0-100 scale
                        reranked_candidates.append({
                            'result_row': result_row,
                            'combined_score': combined_score,
                            'experience_score': 0.0,
                            'similarity_score': similarity_score,
                            'experience_analysis': None,
                            'relevance_scoring': None
                        })
                        continue

                    # Step 2: Score experience relevance
                    relevance_scoring = score_experience_relevance(experience_analysis, processed_position, models_order)

                    if not relevance_scoring:
                        logger.warning("Failed to score experience relevance for candidate %s, using similarity score only", candidate_id)
                        # Use only similarity score if relevance scoring fails
                        combined_score = similarity_score * 100  # Convert to 0-100 scale
                        reranked_candidates.append({
                            'result_row': result_row,
                            'combined_score': combined_score,
                            'experience_score': 0.0,
                            'similarity_score': similarity_score,
                            'experience_analysis': experience_analysis,
                            'relevance_scoring': None
                        })
                        continue

                    # Step 3: Calculate combined score
                    # Normalize similarity score to 0-100 scale
                    normalized_similarity = similarity_score * 100
                    experience_score = relevance_scoring.overall_relevance_score

                    # Calculate weighted combined score
                    combined_score = (experience_score * experience_weight) + (normalized_similarity * similarity_weight)

                    # CRITICAL: Apply role type mismatch caps to combined score
                    original_combined_score = combined_score
                    capped_combined_score = apply_role_type_caps_to_compatibility(
                        combined_score, candidate_text, "Quality Assurance"  # Default assumption for QA positions
                    )

                    if capped_combined_score < original_combined_score:
                        logger.info(f"🔥 RERANKING CAP APPLIED: Candidate {candidate_id} score {original_combined_score:.1f}% → {capped_combined_score:.1f}%")
                        combined_score = capped_combined_score
                    else:
                        logger.info(f"🔥 RERANKING NO CAP: Candidate {candidate_id} score remains {combined_score:.1f}%")

                    reranked_candidates.append({
                        'result_row': result_row,
                        'combined_score': combined_score,
                        'experience_score': experience_score,
                        'similarity_score': similarity_score,
                        'experience_analysis': experience_analysis,
                        'relevance_scoring': relevance_scoring
                    })

                    logger.debug(
                        "Candidate %s scores - Experience: %.1f, Similarity: %.1f, Combined: %.1f",
                        candidate_id, experience_score, normalized_similarity, combined_score
                    )

            # Step 4: Sort by combined score (descending)
            reranked_candidates.sort(key=lambda x: x['combined_score'], reverse=True)

            # Step 5: Extract reordered result rows
            reordered_results = [candidate['result_row'] for candidate in reranked_candidates]

            # Log reranking summary
            if reranked_candidates:
                top_candidate = reranked_candidates[0]
                logger.info(
                    "Experience-based reranking completed. Top candidate: %s (Combined: %.1f, Experience: %.1f, Similarity: %.1f)",
                    top_candidate['result_row'][0],
                    top_candidate['combined_score'],
                    top_candidate['experience_score'],
                    top_candidate['similarity_score'] * 100
                )

                # Log score distribution
                experience_scores = [c['experience_score'] for c in reranked_candidates if c['experience_score'] > 0]
                if experience_scores:
                    avg_exp_score = sum(experience_scores) / len(experience_scores)
                    logger.info(
                        "Reranking summary - Candidates processed: %d, Avg experience score: %.1f",
                        len(results), avg_exp_score
                    )

            span.set_attribute("reranking.success", True)
            span.set_attribute("reranking.candidates_processed", len(results))

            return reordered_results

        except Exception as e:
            logger.error("Error during experience-based reranking: %s", str(e), exc_info=True)
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR))

            # Fallback: return original order
            logger.warning("Falling back to original candidate order due to reranking error")
            return results
