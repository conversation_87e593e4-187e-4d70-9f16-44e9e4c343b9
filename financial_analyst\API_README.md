# Database Analyst FastAPI Backend

A comprehensive REST API for intelligent database analysis using AI-powered agents.

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- GROQ API Key
- SQLite database (or PostgreSQL for production)

### Installation

1. **Clone and setup**:
```bash
cd financial_analyst
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
# Copy and edit environment file
cp .env.example .env
# Add your GROQ_API_KEY and other settings
```

3. **Start the API server**:
```bash
python start_api.py
```

The API will be available at `http://localhost:8000`

## 📚 API Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🔗 Key Endpoints

### Health & System
- `GET /health` - System health check
- `GET /health/live` - Liveness probe
- `GET /health/ready` - Readiness probe
- `GET /system/info` - System information
- `GET /system/metrics` - Performance metrics

### Workflows
- `POST /workflows` - Create new workflow
- `POST /workflows/{id}/execute` - Execute workflow
- `GET /workflows/{id}/status` - Get workflow status
- `GET /workflows/{id}` - Get workflow details
- `POST /workflows/{id}/cancel` - Cancel workflow

### Agents
- `POST /agents/sql/query` - Natural language SQL queries
- `POST /agents/sql/direct` - Direct SQL execution
- `POST /agents/pandas/analyze` - Data analysis
- `POST /agents/schema/analyze` - Schema analysis
- `GET /agents/status` - Agent status

## 🐳 Docker Deployment

### Using Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down
```

### Using Docker directly

```bash
# Build image
docker build -t database-analyst-api .

# Run container
docker run -d \
  --name analyst-api \
  -p 8000:8000 \
  -e GROQ_API_KEY=your_key_here \
  -v $(pwd)/financial_data.db:/app/financial_data.db \
  database-analyst-api
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GROQ_API_KEY` | GROQ API key (required) | - |
| `DATABASE_URL` | Database connection URL | `sqlite:///financial_data.db` |
| `DEBUG` | Enable debug mode | `False` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `RATE_LIMIT_PER_MINUTE` | API rate limit | `60` |
| `MAX_CONCURRENT_WORKFLOWS` | Max concurrent workflows | `10` |

### Command Line Options

```bash
python start_api.py --help

Options:
  --host TEXT          Host to bind (default: 0.0.0.0)
  --port INTEGER       Port to bind (default: 8000)
  --reload             Enable auto-reload for development
  --log-level TEXT     Set logging level (DEBUG, INFO, WARNING, ERROR)
  --workers INTEGER    Number of worker processes
  --check-env          Check environment and exit
```

## 🔒 Security Features

- **Rate Limiting**: Configurable per-endpoint rate limits
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Query sanitization and validation
- **Error Handling**: Secure error responses
- **Request Logging**: Detailed request/response logging

## 📊 Monitoring

### Health Checks

```bash
# Basic health check
curl http://localhost:8000/health

# Liveness probe (for Kubernetes)
curl http://localhost:8000/health/live

# Readiness probe (for Kubernetes)
curl http://localhost:8000/health/ready
```

### Metrics

```bash
# System metrics
curl http://localhost:8000/system/metrics

# Agent status
curl http://localhost:8000/agents/status
```

## 🧪 Testing

### Run API Tests

```bash
# Run all tests
curl -X POST http://localhost:8000/test/run

# Run specific test
curl -X POST http://localhost:8000/test/run \
  -H "Content-Type: application/json" \
  -d '{"test_name": "database"}'

# Get test results
curl http://localhost:8000/test/results
```

## 📝 Usage Examples

### Create and Execute Workflow

```bash
# Create workflow
curl -X POST http://localhost:8000/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Analyze sales data for the last quarter",
    "workflow_type": "analysis",
    "priority": 2
  }'

# Execute workflow (replace {workflow_id})
curl -X POST http://localhost:8000/workflows/{workflow_id}/execute

# Check status
curl http://localhost:8000/workflows/{workflow_id}/status
```

### Direct SQL Query

```bash
curl -X POST http://localhost:8000/agents/sql/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Show me the top 10 customers by revenue",
    "limit_results": 10,
    "explain_query": true
  }'
```

### Data Analysis

```bash
curl -X POST http://localhost:8000/agents/pandas/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Analyze customer churn patterns",
    "analysis_type": "statistical",
    "include_visualizations": true
  }'
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DATABASE_URL environment variable
   - Ensure database file exists and is accessible
   - Verify database permissions

2. **GROQ API Key Error**
   - Ensure GROQ_API_KEY is set in environment
   - Verify API key is valid and has sufficient credits

3. **Rate Limit Exceeded**
   - Check rate limiting configuration
   - Implement proper retry logic in clients
   - Consider increasing rate limits for production

4. **Agent Not Available**
   - Check agent initialization in logs
   - Verify all dependencies are installed
   - Restart the API service

### Logs

```bash
# View API logs
tail -f logs/api.log

# View Docker logs
docker-compose logs -f api

# Check specific log level
grep "ERROR" logs/api.log
```

## 🔧 Development

### Development Mode

```bash
# Start with auto-reload
python start_api.py --reload --log-level DEBUG

# Check environment
python start_api.py --check-env
```

### Adding New Endpoints

1. Create router in `api/routers/`
2. Add models in `api/models/`
3. Implement service logic in `api/services/`
4. Include router in `api/main.py`
5. Add tests in `api/testing.py`

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Support

For issues and questions:
- Check the API documentation at `/docs`
- Review logs for error details
- Run API tests to validate functionality
- Check system health endpoints
