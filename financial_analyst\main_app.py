"""
Main Streamlit application for the Database Analyst System.
Provides interactive chatbot interface and dashboard visualization.
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import uuid
import sys
import os
import requests
import tempfile

# Add project directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'dashboard'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from agents.agent_coordinator import get_agent_coordinator
from dashboard.dashboard_generator import GeneradorDashboard
from dashboard.chart_factory import FabricaGraficos
from database.database_config import get_database_config

# Configure Streamlit page
st.set_page_config(
    page_title="Database Analyst System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
        background-color: #f8f9fa;
    }
    
    .user-message {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
    }
    
    .assistant-message {
        background-color: #f1f8e9;
        border-left-color: #4caf50;
    }
    
    .error-message {
        background-color: #ffebee;
        border-left-color: #f44336;
    }
    
    .metric-card {
        background-color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

class DatabaseAnalystApp:
    """Main application class for the Database Analyst System."""
    
    def __init__(self):
        """Initialize the application."""
        self.coordinator = get_agent_coordinator()
        self.dashboard_generator = GeneradorDashboard()
        self.chart_factory = FabricaGraficos()
        self.db_config = get_database_config()
        
        # Initialize session state
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = []
        if 'current_workflow' not in st.session_state:
            st.session_state.current_workflow = None
        if 'dashboard_data' not in st.session_state:
            st.session_state.dashboard_data = {}
        if 'user_session_id' not in st.session_state:
            st.session_state.user_session_id = str(uuid.uuid4())
    
    def run(self):
        """Run the main application."""
        # Header
        st.markdown('<h1 class="main-header">🤖 Database Analyst System</h1>', unsafe_allow_html=True)
        st.markdown("---")

        # Initialize session state first
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "Chat"

        # Sidebar
        self.render_sidebar()

        # Render page based on selection
        if st.session_state.current_page == "Chat":
            self.render_chat_page()
        elif st.session_state.current_page == "Dashboard":
            self.render_dashboard_page()
        elif st.session_state.current_page == "Upload Status":
            self.render_upload_status_page()
        elif st.session_state.current_page == "System Info":
            self.render_system_info_page()

    def render_chat_page(self):
        """Render the main chat page."""
        col1, col2 = st.columns([1, 2])

        with col1:
            self.render_chat_interface()

        with col2:
            self.render_dashboard_area()

    def render_dashboard_page(self):
        """Render the dashboard page."""
        st.header("📊 Dashboard")
        self.render_dashboard_area()

    def render_upload_status_page(self):
        """Render the upload status page."""
        st.header("📁 Upload Status")

        try:
            # Get uploads list
            api_url = "http://localhost:8000/upload/"
            response = requests.get(api_url, timeout=10)

            if response.status_code == 200:
                uploads_data = response.json()
                uploads = uploads_data.get("uploads", [])

                if uploads:
                    st.subheader(f"Total Uploads: {uploads_data.get('total', 0)}")

                    for upload in uploads:
                        with st.expander(f"Upload {upload['upload_id'][:8]}... - {upload['status'].title()}", expanded=False):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.write(f"**Status:** {upload['status'].title()}")
                                st.write(f"**Progress:** {upload['progress']:.1f}%")
                                st.write(f"**Created:** {upload['created_at']}")

                            with col2:
                                st.write(f"**Message:** {upload['message']}")
                                if upload.get('started_at'):
                                    st.write(f"**Started:** {upload['started_at']}")
                                if upload.get('completed_at'):
                                    st.write(f"**Completed:** {upload['completed_at']}")

                            # Progress bar
                            st.progress(upload['progress'] / 100.0)

                            # Error details if failed
                            if upload['status'] == 'failed' and upload.get('error_details'):
                                st.error(f"Error: {upload['error_details']}")
                else:
                    st.info("No uploads found.")
            else:
                st.error("Failed to fetch upload status")

        except requests.exceptions.RequestException:
            st.error("Cannot connect to API server. Make sure it's running on http://localhost:8000")
        except Exception as e:
            st.error(f"Error: {str(e)}")

    def render_system_info_page(self):
        """Render the system information page."""
        st.header("🔧 System Information")

        # Database info
        st.subheader("Database")
        if self.check_database_connection():
            st.success("✅ Database Connected")

            # Show tables
            try:
                with self.db_config.get_connection() as conn:
                    tables = conn.execute(
                        "SELECT name FROM sqlite_master WHERE type='table'"
                    ).fetchall()

                    if tables:
                        st.write("**Available Tables:**")
                        for table in tables:
                            st.write(f"- {table[0]}")
                    else:
                        st.info("No tables found in database")
            except Exception as e:
                st.error(f"Error querying database: {e}")
        else:
            st.error("❌ Database Disconnected")

        # API status
        st.subheader("API Server")
        try:
            response = requests.get("http://localhost:8000/health/", timeout=5)
            if response.status_code == 200:
                st.success("✅ API Server Running")
                health_data = response.json()
                st.json(health_data)
            else:
                st.error("❌ API Server Error")
        except requests.exceptions.RequestException:
            st.error("❌ API Server Not Reachable")
    
    def render_sidebar(self):
        """Render the sidebar with system information and controls."""
        with st.sidebar:
            st.header("🔧 System Controls")

            # Data Upload Section
            st.subheader("📁 Data Upload")
            self.render_upload_section()

            st.divider()

            # Page Navigation
            st.subheader("📍 Navigation")
            page = st.radio(
                "Select Page",
                ["Chat", "Dashboard", "Upload Status", "System Info"],
                key="page_selector"
            )

            # Update current page
            if page != st.session_state.current_page:
                st.session_state.current_page = page
                st.rerun()

            st.divider()

            # Database connection status
            st.subheader("Database Status")
            if self.check_database_connection():
                st.success("✅ Database Connected")
            else:
                st.error("❌ Database Disconnected")
            
            # System metrics
            st.subheader("System Metrics")
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Active Sessions", "1")
                st.metric("Queries Today", len(st.session_state.chat_history))
            with col2:
                st.metric("Uptime", "99.9%")
                st.metric("Response Time", "1.2s")
            
            # Quick actions
            st.subheader("Quick Actions")
            if st.button("🔄 Refresh Dashboard"):
                self.refresh_dashboard()
            
            if st.button("🗑️ Clear Chat History"):
                st.session_state.chat_history = []
                st.rerun()
            
            if st.button("📊 Generate Sample Dashboard"):
                self.generate_sample_dashboard()
            
            # Settings
            st.subheader("Settings")
            auto_refresh = st.checkbox("Auto-refresh Dashboard", value=False)
            if auto_refresh:
                refresh_interval = st.slider("Refresh Interval (seconds)", 30, 300, 60)
            
            # Export options
            st.subheader("Export Options")
            if st.button("📥 Export Chat History"):
                self.export_chat_history()
            
            if st.button("📊 Export Dashboard"):
                self.export_dashboard()
    
    def render_chat_interface(self):
        """Render the chat interface."""
        st.header("💬 Chat Interface")
        
        # Chat history
        chat_container = st.container()
        with chat_container:
            for message in st.session_state.chat_history:
                self.render_chat_message(message)
        
        # Chat input
        with st.form("chat_form", clear_on_submit=True):
            user_input = st.text_area(
                "Ask me anything about your data:",
                placeholder="e.g., 'Show me the sales trends for the last quarter' or 'Create a dashboard for customer analysis'",
                height=100
            )
            
            col1, col2, col3 = st.columns([1, 1, 2])
            with col1:
                submit_button = st.form_submit_button("Send 🚀")
            with col2:
                voice_button = st.form_submit_button("🎤 Voice")
            
            if submit_button and user_input:
                self.process_user_message(user_input)
            
            if voice_button:
                st.info("Voice input feature coming soon!")
    
    def render_chat_message(self, message):
        """Render individual chat message."""
        message_type = message.get("type", "user")
        content = message.get("content", "")
        timestamp = message.get("timestamp", datetime.now())
        
        if message_type == "user":
            st.markdown(f"""
            <div class="chat-message user-message">
                <strong>You ({timestamp.strftime('%H:%M')})</strong><br>
                {content}
            </div>
            """, unsafe_allow_html=True)
        
        elif message_type == "assistant":
            st.markdown(f"""
            <div class="chat-message assistant-message">
                <strong>Assistant ({timestamp.strftime('%H:%M')})</strong><br>
                {content}
            </div>
            """, unsafe_allow_html=True)
            
            # Show additional data if available
            if "data" in message:
                with st.expander("View Details"):
                    st.json(message["data"])
        
        elif message_type == "error":
            st.markdown(f"""
            <div class="chat-message error-message">
                <strong>Error ({timestamp.strftime('%H:%M')})</strong><br>
                {content}
            </div>
            """, unsafe_allow_html=True)
    
    def render_dashboard_area(self):
        """Render the dashboard visualization area."""
        st.header("📊 Interactive Dashboard")
        
        # Dashboard tabs
        tab1, tab2, tab3 = st.tabs(["📈 Charts", "📋 Data Tables", "⚙️ Configuration"])
        
        with tab1:
            self.render_charts_tab()
        
        with tab2:
            self.render_data_tables_tab()
        
        with tab3:
            self.render_configuration_tab()
    
    def render_charts_tab(self):
        """Render the charts tab."""
        if st.session_state.dashboard_data:
            # Display generated charts
            for chart_id, chart_data in st.session_state.dashboard_data.items():
                if "chart" in chart_data:
                    st.subheader(chart_data.get("title", f"Chart {chart_id}"))
                    
                    try:
                        # Parse and display Plotly chart
                        fig_json = json.loads(chart_data["chart"])
                        fig = go.Figure(fig_json)
                        st.plotly_chart(fig, use_container_width=True)
                    except Exception as e:
                        st.error(f"Error displaying chart: {e}")
        else:
            st.info("No charts available. Start a conversation to generate visualizations!")
            
            # Sample chart button
            if st.button("Generate Sample Chart"):
                self.create_sample_chart()
    
    def render_data_tables_tab(self):
        """Render the data tables tab."""
        if st.session_state.dashboard_data:
            for data_id, data_info in st.session_state.dashboard_data.items():
                if "dataframe" in data_info:
                    st.subheader(f"Data: {data_id}")
                    st.dataframe(data_info["dataframe"], use_container_width=True)
        else:
            st.info("No data tables available.")
    
    def render_configuration_tab(self):
        """Render the configuration tab."""
        st.subheader("Dashboard Configuration")
        
        # Chart type selection
        chart_types = ["line", "bar", "scatter", "heatmap", "histogram", "pie"]
        selected_chart_type = st.selectbox("Default Chart Type", chart_types)
        
        # Color scheme
        color_schemes = ["financial", "professional", "modern", "cool"]
        selected_color_scheme = st.selectbox("Color Scheme", color_schemes)
        
        # Layout options
        layout_options = ["single_chart", "two_column", "grid_2x2"]
        selected_layout = st.selectbox("Dashboard Layout", layout_options)
        
        # Save configuration
        if st.button("Save Configuration"):
            st.success("Configuration saved!")
    
    def process_user_message(self, user_input: str):
        """Process user message and generate response."""
        # Add user message to chat history
        user_message = {
            "type": "user",
            "content": user_input,
            "timestamp": datetime.now()
        }
        st.session_state.chat_history.append(user_message)
        
        # Show processing indicator
        with st.spinner("Processing your request..."):
            try:
                # Create workflow
                workflow_id = f"workflow_{st.session_state.user_session_id}_{len(st.session_state.chat_history)}"
                workflow = self.coordinator.crear_workflow(workflow_id, user_input)
                
                # Execute workflow
                result = self.coordinator.ejecutar_workflow(workflow_id)
                
                # Process results
                if result.get("status") == "completed":
                    response_content = self.format_workflow_results(result)
                    
                    # Add assistant response
                    assistant_message = {
                        "type": "assistant",
                        "content": response_content,
                        "timestamp": datetime.now(),
                        "data": result
                    }
                    st.session_state.chat_history.append(assistant_message)
                    
                    # Update dashboard if visualization data is available
                    self.update_dashboard_from_results(result)
                    
                else:
                    # Handle errors
                    error_message = {
                        "type": "error",
                        "content": f"Sorry, I encountered an error processing your request: {result.get('errors', 'Unknown error')}",
                        "timestamp": datetime.now()
                    }
                    st.session_state.chat_history.append(error_message)
                
            except Exception as e:
                error_message = {
                    "type": "error",
                    "content": f"An unexpected error occurred: {str(e)}",
                    "timestamp": datetime.now()
                }
                st.session_state.chat_history.append(error_message)
        
        # Rerun to update the interface
        st.rerun()
    
    def format_workflow_results(self, result: dict) -> str:
        """Format workflow results for display."""
        response_parts = []
        
        # Add summary
        response_parts.append(f"✅ **Analysis Complete**")
        response_parts.append(f"Processed {result.get('tasks_completed', 0)} of {result.get('total_tasks', 0)} tasks")
        
        # Add key findings
        if "results" in result:
            for task_id, task_result in result["results"].items():
                if task_result and isinstance(task_result, dict):
                    if "analisis_principal" in task_result:
                        response_parts.append(f"\n**Analysis Results:**")
                        response_parts.append(task_result["analisis_principal"][:500] + "...")
                    
                    if "query_result" in task_result:
                        response_parts.append(f"\n**Query Results:**")
                        response_parts.append(str(task_result["query_result"])[:300] + "...")
        
        return "\n".join(response_parts)
    
    def update_dashboard_from_results(self, result: dict):
        """Update dashboard with results from workflow."""
        try:
            # Extract data for visualization
            dashboard_data = {}
            
            for task_id, task_result in result.get("results", {}).items():
                if task_result and isinstance(task_result, dict):
                    # Check for pandas analysis results
                    if "visualizaciones" in task_result:
                        for viz_id, viz_data in task_result["visualizaciones"].items():
                            dashboard_data[f"{task_id}_{viz_id}"] = {
                                "chart": viz_data,
                                "title": f"{task_id} - {viz_id}",
                                "type": "plotly"
                            }
                    
                    # Check for SQL results
                    if "data" in task_result and isinstance(task_result["data"], list):
                        df = pd.DataFrame(task_result["data"])
                        dashboard_data[f"{task_id}_data"] = {
                            "dataframe": df,
                            "title": f"Data from {task_id}"
                        }
            
            # Update session state
            st.session_state.dashboard_data.update(dashboard_data)
            
        except Exception as e:
            st.error(f"Error updating dashboard: {e}")
    
    def check_database_connection(self) -> bool:
        """Check database connection status."""
        try:
            return self.db_config.test_connection()
        except:
            return False
    
    def refresh_dashboard(self):
        """Refresh dashboard data."""
        st.success("Dashboard refreshed!")
        st.rerun()
    
    def generate_sample_dashboard(self):
        """Generate sample dashboard for demonstration."""
        # Create sample data
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        sample_data = pd.DataFrame({
            'date': dates,
            'value': np.random.randn(len(dates)).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        })
        
        # Generate sample chart
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=sample_data['date'], y=sample_data['value'], mode='lines', name='Sample Data'))
        fig.update_layout(title="Sample Time Series", template="plotly_white")
        
        # Update dashboard
        st.session_state.dashboard_data["sample_chart"] = {
            "chart": fig.to_json(),
            "title": "Sample Chart",
            "type": "plotly"
        }
        
        st.session_state.dashboard_data["sample_data"] = {
            "dataframe": sample_data,
            "title": "Sample Data"
        }
        
        st.success("Sample dashboard generated!")
        st.rerun()
    
    def create_sample_chart(self):
        """Create a sample chart."""
        self.generate_sample_dashboard()
    
    def export_chat_history(self):
        """Export chat history."""
        chat_data = json.dumps(st.session_state.chat_history, default=str, indent=2)
        st.download_button(
            label="Download Chat History",
            data=chat_data,
            file_name=f"chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
    
    def export_dashboard(self):
        """Export dashboard data."""
        dashboard_data = json.dumps(st.session_state.dashboard_data, default=str, indent=2)
        st.download_button(
            label="Download Dashboard Data",
            data=dashboard_data,
            file_name=f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )

    def render_upload_section(self):
        """Render the data upload section."""
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a data file",
            type=['csv', 'xlsx', 'xls', 'json'],
            help="Upload CSV, Excel, or JSON files (max 100MB)"
        )

        if uploaded_file is not None:
            # Upload options
            with st.expander("Upload Options", expanded=True):
                data_type = st.selectbox(
                    "Data Type",
                    ["financial", "general", "time_series", "custom"],
                    help="Type of data being uploaded"
                )

                table_name = st.text_input(
                    "Table Name (optional)",
                    placeholder="Leave empty for auto-generation",
                    help="Name for the database table"
                )

                description = st.text_area(
                    "Description (optional)",
                    placeholder="Describe your data...",
                    help="Optional description of the data"
                )

                col1, col2 = st.columns(2)
                with col1:
                    overwrite = st.checkbox(
                        "Overwrite existing table",
                        help="Replace table if it already exists"
                    )
                with col2:
                    validate = st.checkbox(
                        "Validate data quality",
                        value=True,
                        help="Perform data quality checks"
                    )

            # Preview and Upload buttons
            col1, col2 = st.columns(2)
            with col1:
                if st.button("👁️ Preview Data"):
                    self.preview_uploaded_file(uploaded_file)
            with col2:
                if st.button("🚀 Upload Data", type="primary"):
                    self.handle_file_upload(
                        uploaded_file, data_type, table_name,
                        description, overwrite, validate
                    )

    def handle_file_upload(self, uploaded_file, data_type, table_name, description, overwrite, validate):
        """Handle file upload process."""
        try:
            # Show progress
            progress_bar = st.progress(0)
            status_text = st.empty()

            status_text.text("Preparing upload...")
            progress_bar.progress(10)

            # Prepare upload data
            files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
            data = {
                "data_type": data_type,
                "overwrite_existing": overwrite,
                "validate_data": validate
            }

            if table_name:
                data["table_name"] = table_name
            if description:
                data["description"] = description

            status_text.text("Uploading file...")
            progress_bar.progress(30)

            # Make API request
            api_url = "http://localhost:8000/upload/"  # Adjust URL as needed
            response = requests.post(api_url, files=files, data=data, timeout=300)

            if response.status_code == 201:
                upload_result = response.json()
                upload_id = upload_result["upload_id"]

                status_text.text("Processing data...")
                progress_bar.progress(50)

                # Poll for completion
                self.poll_upload_status(upload_id, progress_bar, status_text)

            else:
                st.error(f"Upload failed: {response.text}")

        except requests.exceptions.RequestException as e:
            st.error(f"Connection error: {str(e)}")
            st.info("Make sure the API server is running on http://localhost:8000")
        except Exception as e:
            st.error(f"Upload error: {str(e)}")

    def poll_upload_status(self, upload_id, progress_bar, status_text):
        """Poll upload status until completion."""
        import time

        max_attempts = 60  # 5 minutes max
        attempt = 0

        while attempt < max_attempts:
            try:
                # Check status
                status_url = f"http://localhost:8000/upload/{upload_id}/status"
                response = requests.get(status_url, timeout=10)

                if response.status_code == 200:
                    status_data = response.json()
                    progress = status_data["progress"]
                    message = status_data["message"]
                    status = status_data["status"]

                    # Update UI
                    progress_bar.progress(int(progress))
                    status_text.text(message)

                    if status == "completed":
                        st.success("✅ Data uploaded successfully!")
                        st.balloons()
                        break
                    elif status == "failed":
                        st.error(f"❌ Upload failed: {message}")
                        if "error_details" in status_data:
                            st.error(f"Details: {status_data['error_details']}")
                        break
                    elif status == "cancelled":
                        st.warning("⚠️ Upload was cancelled")
                        break

                time.sleep(5)  # Wait 5 seconds before next check
                attempt += 1

            except Exception as e:
                st.error(f"Error checking status: {str(e)}")
                break

        if attempt >= max_attempts:
            st.warning("⏰ Upload status check timed out. Check the upload list for final status.")

    def preview_uploaded_file(self, uploaded_file):
        """Preview uploaded file data."""
        try:
            # Read file based on type
            file_ext = os.path.splitext(uploaded_file.name)[1].lower()

            if file_ext == '.csv':
                df = pd.read_csv(uploaded_file)
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(uploaded_file)
            elif file_ext == '.json':
                import json
                data = json.loads(uploaded_file.getvalue().decode('utf-8'))
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                else:
                    df = pd.DataFrame([data])
            else:
                st.error("Unsupported file format")
                return

            # Show preview
            st.subheader("📋 Data Preview")

            # Basic info
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Rows", len(df))
            with col2:
                st.metric("Columns", len(df.columns))
            with col3:
                st.metric("Size", f"{uploaded_file.size / 1024:.1f} KB")

            # Column info
            st.subheader("Column Information")
            col_info = pd.DataFrame({
                'Column': df.columns,
                'Type': df.dtypes.astype(str),
                'Non-Null': df.count(),
                'Null Count': df.isnull().sum()
            })
            st.dataframe(col_info, use_container_width=True)

            # Sample data
            st.subheader("Sample Data (First 10 rows)")
            st.dataframe(df.head(10), use_container_width=True)

            # Basic statistics for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                st.subheader("Numeric Column Statistics")
                st.dataframe(df[numeric_cols].describe(), use_container_width=True)

        except Exception as e:
            st.error(f"Error previewing file: {str(e)}")

# Main execution
if __name__ == "__main__":
    import numpy as np
    app = DatabaseAnalystApp()
    app.run()
