# 🚀 LumusAI Background Processing Guide

## ✅ **Problem Solved: No More Timeouts!**

Your `/process` endpoint now returns **immediately** and processes documents in the background. No more 5-6 minute waits or timeout errors!

## 🔄 **New Flow**

**Before (Timeout Issues):**
```
Client → /process → Wait 5-6 minutes → Get Result (or timeout)
```

**After (No Timeouts):**
```
Client → /process → Get Task ID (immediate)
       → Check /task/status/{task_id} → Get progress
       → Get /task/result/{task_id} → Get final result
       → Receive webhook (optional) → Get notified when done
```

## 🚀 **Quick Start**

### 1. **Install Dependencies**
```bash
pip install aiohttp==3.10.10 aiosignal==1.3.1
```

### 2. **Start Service**
```bash
uvicorn main:app --reload
```

### 3. **Submit Document (Returns Immediately)**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "webhook_url=http://localhost:8000/webhook/test"
```

**Response (Immediate):**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "processing",
  "message": "Document processing started in background",
  "status_url": "/task/status/cv-1703123456-abc12345",
  "result_url": "/task/result/cv-1703123456-abc12345",
  "webhook_url": "http://localhost:8000/webhook/test",
  "estimated_completion": "2-5 minutes"
}
```

### 4. **Check Status (Optional)**
```bash
curl -X GET "http://localhost:8000/task/status/cv-1703123456-abc12345"
```

**Response:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "action": "cv",
  "status": "processing",
  "created_at": 1703123456.789,
  "file_name": "resume.pdf"
}
```

### 5. **Get Result When Ready**
```bash
curl -X GET "http://localhost:8000/task/result/cv-1703123456-abc12345"
```

**Response:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "completed",
  "result": {
    "personal_info": {
      "full_name": "John Doe",
      "email": "<EMAIL>"
    },
    "skills": [...],
    "work_experience": [...]
  },
  "processing_time": 245.6
}
```

## 📡 **Available Endpoints**

| Endpoint | Method | Description | Response Time |
|----------|--------|-------------|---------------|
| `/process-test` | POST | Submit document for processing | **Immediate** |
| `/task/status/{task_id}` | GET | Check task status | Immediate |
| `/task/result/{task_id}` | GET | Get processing result | Immediate |
| `/tasks` | GET | List all tasks (monitoring) | Immediate |
| `/webhook/test` | POST | Test webhook endpoint | Immediate |

## 🔔 **Webhook Notifications**

When processing completes, your webhook URL receives:

**Success:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": { /* your processing results */ },
  "processing_time": 245.6
}
```

**Failure:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "failed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "error": "Error processing document: Invalid file format"
  },
  "processing_time": 12.3
}
```

## 💡 **Usage Patterns**

### **Pattern 1: Fire and Forget with Webhook**
```bash
# Submit and get notified via webhook
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "webhook_url=https://your-app.com/webhook"
```

### **Pattern 2: Submit and Poll for Results**
```bash
# 1. Submit
TASK_ID=$(curl -X POST "http://localhost:8000/process" \
          -F "action=cv" \
          -F "file=@resume.pdf" | jq -r '.task_id')

# 2. Wait a bit
sleep 30

# 3. Check if done
curl -X GET "http://localhost:8000/task/result/$TASK_ID"
```

### **Pattern 3: Submit and Monitor Progress**
```bash
# Submit
TASK_ID=$(curl -X POST "http://localhost:8000/process" \
          -F "action=cv" \
          -F "file=@resume.pdf" | jq -r '.task_id')

# Monitor status
while true; do
  STATUS=$(curl -s "http://localhost:8000/task/status/$TASK_ID" | jq -r '.status')
  echo "Status: $STATUS"
  
  if [ "$STATUS" != "processing" ]; then
    break
  fi
  
  sleep 10
done

# Get result
curl -X GET "http://localhost:8000/task/result/$TASK_ID"
```

## 🔧 **Features**

### **✅ Immediate Response**
- No more waiting 5-6 minutes
- Get task ID instantly
- Continue with other work

### **✅ Background Processing**
- Documents processed asynchronously
- No blocking of other requests
- Automatic cleanup of temporary files

### **✅ Status Tracking**
- Check processing status anytime
- Get task metadata and progress
- Monitor all active tasks

### **✅ Webhook Notifications**
- Get notified when processing completes
- Automatic retry with exponential backoff
- Support for success and failure notifications

### **✅ Result Retrieval**
- Get results when ready
- Structured error handling
- Processing time tracking

### **✅ Monitoring**
- List all active and completed tasks
- Clean up old completed tasks
- Built-in task management

## 🛠️ **Integration Examples**

### **JavaScript/Node.js Client**
```javascript
async function processDocument(file, action, webhookUrl) {
  // Submit document
  const formData = new FormData();
  formData.append('action', action);
  formData.append('file', file);
  if (webhookUrl) formData.append('webhook_url', webhookUrl);
  
  const response = await fetch('/process', {
    method: 'POST',
    body: formData
  });
  
  const { task_id } = await response.json();
  console.log(`Task submitted: ${task_id}`);
  
  // Poll for result
  while (true) {
    const statusResponse = await fetch(`/task/status/${task_id}`);
    const status = await statusResponse.json();
    
    if (status.status !== 'processing') {
      break;
    }
    
    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5s
  }
  
  // Get result
  const resultResponse = await fetch(`/task/result/${task_id}`);
  return await resultResponse.json();
}
```

### **Python Client**
```python
import requests
import time

def process_document(file_path, action, webhook_url=None):
    # Submit document
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {'action': action}
        if webhook_url:
            data['webhook_url'] = webhook_url
            
        response = requests.post('/process', files=files, data=data)
        task_id = response.json()['task_id']
    
    print(f"Task submitted: {task_id}")
    
    # Poll for result
    while True:
        status_response = requests.get(f'/task/status/{task_id}')
        status = status_response.json()
        
        if status['status'] != 'processing':
            break
            
        time.sleep(5)
    
    # Get result
    result_response = requests.get(f'/task/result/{task_id}')
    return result_response.json()
```

## 🎯 **Benefits**

1. **✅ No Timeouts** - Requests return immediately
2. **✅ Better UX** - Users don't wait for processing
3. **✅ Scalable** - Multiple documents can process simultaneously
4. **✅ Reliable** - Failed tasks don't affect other requests
5. **✅ Monitorable** - Full visibility into processing status
6. **✅ Flexible** - Support for webhooks and polling
7. **✅ Backward Compatible** - Same processing logic, just async

---

**🎉 Your timeout problems are now solved!** The system processes documents in the background while returning immediately to clients.
