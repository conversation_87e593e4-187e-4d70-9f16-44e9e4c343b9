#!/usr/bin/env python3
"""
Streamlit app for Excel file upload and analysis.
Simple, focused interface for uploading and analyzing Excel files.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import io
import tempfile
import os
from datetime import datetime
import json

# Configure Streamlit page
st.set_page_config(
    page_title="Excel Upload & Analysis",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        color: #1f77b4;
        text-align: center;
        padding: 1rem 0;
        border-bottom: 2px solid #1f77b4;
        margin-bottom: 2rem;
    }
    .upload-section {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

class ExcelUploadApp:
    """Streamlit app for Excel upload and analysis."""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8000"
        self.api_available = self._check_api_availability()
        
    def _check_api_availability(self):
        """Check if the API server is available."""
        try:
            response = requests.get(f"{self.api_base_url}/health/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def run(self):
        """Run the main application."""
        # Header
        st.markdown('<h1 class="main-header">📊 Excel Upload & Analysis System</h1>', unsafe_allow_html=True)
        
        # API Status
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if self.api_available:
                st.success("✅ API Server: Online")
            else:
                st.warning("⚠️ API Server: Offline (Direct mode)")
        with col2:
            st.info(f"🌐 Server: {self.api_base_url}")
        with col3:
            if st.button("🔄 Refresh Status"):
                self.api_available = self._check_api_availability()
                st.rerun()
        
        st.markdown("---")
        
        # Main content
        col1, col2 = st.columns([1, 1])
        
        with col1:
            self.render_upload_section()
        
        with col2:
            self.render_analysis_section()
    
    def render_upload_section(self):
        """Render the file upload section."""
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.header("📁 File Upload")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose an Excel or CSV file",
            type=['xlsx', 'xls', 'csv'],
            help="Upload Excel (.xlsx, .xls) or CSV files (max 100MB)",
            key="file_uploader"
        )
        
        if uploaded_file is not None:
            # File info
            st.write(f"**File:** {uploaded_file.name}")
            st.write(f"**Size:** {uploaded_file.size:,} bytes")
            st.write(f"**Type:** {uploaded_file.type}")
            
            # Upload options
            with st.expander("📋 Upload Options", expanded=True):
                col1, col2 = st.columns(2)
                with col1:
                    table_name = st.text_input("Table Name (optional)", 
                                             value=f"excel_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                    data_type = st.selectbox("Data Type", 
                                           ["financial", "general", "time_series", "custom"])
                with col2:
                    description = st.text_area("Description (optional)", 
                                             value=f"Excel file upload: {uploaded_file.name}")
                    overwrite = st.checkbox("Overwrite existing data", value=True)
                    validate = st.checkbox("Validate data quality", value=True)
            
            # Action buttons
            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("👁️ Preview Data", type="secondary", use_container_width=True):
                    self.preview_file(uploaded_file)
            with col2:
                if st.button("🚀 Upload & Analyze", type="primary", use_container_width=True):
                    self.handle_upload(uploaded_file, table_name, data_type, description, overwrite, validate)
            with col3:
                if st.button("📊 Direct Analysis", type="secondary", use_container_width=True):
                    self.direct_analysis(uploaded_file)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def render_analysis_section(self):
        """Render the analysis results section."""
        st.header("📈 Analysis Results")
        
        if 'analysis_results' in st.session_state:
            results = st.session_state.analysis_results
            
            # Data overview
            if 'data_overview' in results:
                overview = results['data_overview']
                st.subheader("📋 Data Overview")
                
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Rows", f"{overview['shape'][0]:,}")
                with col2:
                    st.metric("Columns", overview['shape'][1])
                with col3:
                    st.metric("Memory", f"{overview.get('memory_usage_mb', 0):.1f} MB")
                with col4:
                    st.metric("Data Types", len(set(overview.get('data_types', {}).values())))
            
            # Data quality
            if 'data_quality' in results:
                quality = results['data_quality']
                st.subheader("🔍 Data Quality")
                
                col1, col2 = st.columns(2)
                with col1:
                    quality_color = "green" if quality['overall_quality'] == "Good" else "orange"
                    st.markdown(f"**Overall Quality:** :{quality_color}[{quality['overall_quality']}]")
                with col2:
                    st.write(f"**Duplicate Rows:** {quality['duplicate_rows']}")
                
                if quality.get('issues'):
                    st.warning("⚠️ Issues found:")
                    for issue in quality['issues'][:3]:
                        st.write(f"• {issue}")
            
            # Visualizations
            if 'visualizations' in results:
                self.render_visualizations(results['visualizations'])
            
            # Insights
            if 'insights' in results:
                st.subheader("💡 Key Insights")
                for i, insight in enumerate(results['insights'], 1):
                    st.write(f"{i}. {insight}")
        
        else:
            st.info("Upload a file to see analysis results here.")
    
    def preview_file(self, uploaded_file):
        """Preview the uploaded file."""
        try:
            # Read file
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            else:
                df = pd.read_excel(uploaded_file)
            
            st.subheader("👁️ File Preview")
            st.write(f"**Shape:** {df.shape[0]:,} rows × {df.shape[1]} columns")
            st.write(f"**Columns:** {', '.join(df.columns.tolist())}")
            
            # Show first few rows
            st.dataframe(df.head(10), use_container_width=True)
            
            # Basic statistics for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                st.subheader("📊 Numeric Columns Summary")
                st.dataframe(df[numeric_cols].describe(), use_container_width=True)
            
        except Exception as e:
            st.error(f"❌ Preview failed: {str(e)}")
    
    def handle_upload(self, uploaded_file, table_name, data_type, description, overwrite, validate):
        """Handle file upload via API."""
        if not self.api_available:
            st.error("❌ API server is not available. Try 'Direct Analysis' instead.")
            return
        
        try:
            # Show progress
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            status_text.text("Preparing upload...")
            progress_bar.progress(10)
            
            # Prepare upload
            files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
            data = {
                "data_type": data_type,
                "table_name": table_name,
                "description": description,
                "overwrite_existing": overwrite,
                "validate_data": validate
            }
            
            status_text.text("Uploading file...")
            progress_bar.progress(30)
            
            # Upload
            response = requests.post(f"{self.api_base_url}/upload/", files=files, data=data, timeout=300)
            
            if response.status_code == 201:
                result = response.json()
                upload_id = result["upload_id"]
                
                status_text.text("Processing data...")
                progress_bar.progress(50)
                
                # Monitor status
                self.monitor_upload_status(upload_id, progress_bar, status_text)
                
            else:
                st.error(f"❌ Upload failed: {response.text}")
                
        except Exception as e:
            st.error(f"❌ Upload error: {str(e)}")
    
    def monitor_upload_status(self, upload_id, progress_bar, status_text):
        """Monitor upload processing status."""
        max_attempts = 30
        attempt = 0
        
        while attempt < max_attempts:
            try:
                response = requests.get(f"{self.api_base_url}/upload/{upload_id}/status", timeout=10)
                
                if response.status_code == 200:
                    status_data = response.json()
                    progress = status_data.get("progress", 0)
                    message = status_data.get("message", "Processing...")
                    status = status_data.get("status")
                    
                    progress_bar.progress(int(progress))
                    status_text.text(message)
                    
                    if status == "completed":
                        st.success("✅ Upload completed successfully!")
                        st.balloons()
                        break
                    elif status == "failed":
                        st.error(f"❌ Upload failed: {message}")
                        break
                    elif status == "cancelled":
                        st.warning("⚠️ Upload was cancelled")
                        break
                
                attempt += 1
                
            except Exception as e:
                st.error(f"❌ Status check error: {str(e)}")
                break
    
    def direct_analysis(self, uploaded_file):
        """Perform direct analysis without API."""
        try:
            # Read file
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            else:
                df = pd.read_excel(uploaded_file)
            
            st.success("✅ File loaded successfully for direct analysis!")
            
            # Perform analysis
            analysis_results = self.analyze_dataframe(df)
            st.session_state.analysis_results = analysis_results
            
            st.rerun()
            
        except Exception as e:
            st.error(f"❌ Direct analysis failed: {str(e)}")
    
    def analyze_dataframe(self, df):
        """Analyze a pandas DataFrame."""
        results = {
            "data_overview": {
                "shape": df.shape,
                "columns": df.columns.tolist(),
                "data_types": df.dtypes.astype(str).to_dict(),
                "memory_usage_mb": df.memory_usage(deep=True).sum() / 1024 / 1024
            },
            "data_quality": {
                "missing_values": df.isnull().sum().to_dict(),
                "duplicate_rows": df.duplicated().sum(),
                "overall_quality": "Good",
                "issues": []
            },
            "visualizations": [],
            "insights": []
        }
        
        # Check for issues
        missing_pct = (df.isnull().sum() / len(df) * 100)
        for col, pct in missing_pct.items():
            if pct > 50:
                results["data_quality"]["issues"].append(f"Column '{col}' has {pct:.1f}% missing values")
        
        if results["data_quality"]["duplicate_rows"] > 0:
            results["data_quality"]["issues"].append(f"Found {results['data_quality']['duplicate_rows']} duplicate rows")
        
        if len(results["data_quality"]["issues"]) > 0:
            results["data_quality"]["overall_quality"] = "Needs attention"
        
        # Create visualizations
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            # Create some basic charts
            for col in numeric_cols[:2]:  # First 2 numeric columns
                fig = px.histogram(df, x=col, title=f"Distribution of {col}")
                results["visualizations"].append({
                    "title": f"Distribution of {col}",
                    "figure": fig
                })
        
        # Generate insights
        results["insights"] = [
            f"Dataset contains {df.shape[0]:,} rows and {df.shape[1]} columns",
            f"Found {len(numeric_cols)} numeric columns for analysis",
            f"Data quality is {results['data_quality']['overall_quality'].lower()}"
        ]
        
        if len(numeric_cols) > 1:
            results["insights"].append(f"Multiple numeric columns available for correlation analysis")
        
        return results
    
    def render_visualizations(self, visualizations):
        """Render visualization charts."""
        if visualizations:
            st.subheader("📊 Visualizations")
            
            for viz in visualizations:
                if 'figure' in viz:
                    st.plotly_chart(viz['figure'], use_container_width=True)
                elif 'title' in viz:
                    st.write(f"**{viz['title']}**")

def main():
    """Main function to run the Streamlit app."""
    app = ExcelUploadApp()
    app.run()

if __name__ == "__main__":
    main()
