#!/usr/bin/env python3
"""
Simple connectivity test for Azure OpenAI embeddings
"""

import os
import sys
import requests
from dotenv import load_dotenv

load_dotenv()

def test_azure_openai_connectivity():
    """Test basic connectivity to Azure OpenAI"""
    print("🔍 Testing Azure OpenAI Connectivity")
    print("=" * 50)
    
    # Get environment variables
    endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    api_key = os.getenv("AZURE_OPENAI_API_KEY")
    deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS")
    api_version = os.getenv("OPENAI_API_VERSION")
    
    print(f"Endpoint: {endpoint}")
    print(f"Deployment: {deployment}")
    print(f"API Version: {api_version}")
    print(f"API Key: {'*' * 20 if api_key else 'NOT SET'}")
    
    if not all([endpoint, api_key, deployment, api_version]):
        print("\n❌ Missing required environment variables")
        return False
    
    # Construct the full URL
    if endpoint.endswith('/'):
        endpoint = endpoint[:-1]
    
    url = f"{endpoint}/openai/deployments/{deployment}/embeddings?api-version={api_version}"
    print(f"\nTesting URL: {url}")
    
    headers = {
        "Content-Type": "application/json",
        "api-key": api_key
    }
    
    data = {
        "input": "Hello world",
        "model": deployment
    }
    
    try:
        print("\n🚀 Making test request...")
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and len(result['data']) > 0:
                embedding_length = len(result['data'][0]['embedding'])
                print(f"✅ SUCCESS! Received embedding with {embedding_length} dimensions")
                return True
            else:
                print(f"❌ Unexpected response format: {result}")
                return False
        else:
            print(f"❌ Request failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        print("This could indicate:")
        print("  • Network connectivity issues")
        print("  • Firewall blocking the connection")
        print("  • Incorrect endpoint URL")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_network_connectivity():
    """Test basic network connectivity"""
    print("\n🌐 Testing Network Connectivity")
    print("=" * 50)
    
    test_urls = [
        "https://www.google.com",
        "https://azure.microsoft.com",
        "https://openai-smarthr.openai.azure.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url}: {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {e}")

if __name__ == "__main__":
    test_network_connectivity()
    success = test_azure_openai_connectivity()
    
    if success:
        print("\n🎉 Connectivity test passed! Your Azure OpenAI setup is working.")
        print("The issue might be with the specific embedding function implementation.")
    else:
        print("\n❌ Connectivity test failed. Please check your configuration and network.")
