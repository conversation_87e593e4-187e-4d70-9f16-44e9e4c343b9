"""Integration tests for end-to-end workflow validation."""

import os
import tempfile
import pytest
from unittest.mock import patch, AsyncMock, Mock
from httpx import AsyncClient
from app.main import app
from app.orchestrator import JobOrchestrator
from app.schemas import FieldSpec


class TestEndToEndWorkflow:
    """Integration tests for complete ParserGPT workflow."""
    
    @pytest.mark.asyncio
    async def test_complete_job_workflow_mock(self, override_get_session, mock_settings):
        """Test complete job workflow with mocked external dependencies."""
        
        # Mock external dependencies
        with patch('app.sampling.sample_pages') as mock_sample, \
             patch('app.learning.learn_adapter') as mock_learn, \
             patch('app.fallback.get_fallback_extractor') as mock_fallback:
            
            # Setup mocks
            mock_sample.return_value = [
                Mock(url="https://example.com/page1", html="<h1>Test</h1>", domain="example.com"),
                <PERSON><PERSON>(url="https://example.com/page2", html="<h1>Test 2</h1>", domain="example.com")
            ]
            
            mock_adapter_data = Mock()
            mock_adapter_data.dict.return_value = {
                "domain": "example.com",
                "selectors": {
                    "title": {"css": "h1", "xpath": "", "regex": ""}
                }
            }
            mock_learn.return_value = mock_adapter_data
            
            mock_extractor = Mock()
            mock_extractor.extract_missing_fields = AsyncMock(return_value={})
            mock_fallback.return_value = mock_extractor
            
            # Create job via API
            job_data = {
                "start_url": "https://example.com",
                "allowed_domains": ["example.com"],
                "max_depth": 2,
                "max_pages": 10,
                "field_spec": [
                    {"name": "title", "dtype": "string", "description": "Page title"}
                ]
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                # Create job
                response = await client.post("/jobs", json=job_data)
                assert response.status_code == 200
                
                job_id = response.json()["job_id"]
                
                # Check initial status
                response = await client.get(f"/jobs/{job_id}")
                assert response.status_code == 200
                assert response.json()["status"] == "created"
    
    @pytest.mark.asyncio
    async def test_orchestrator_adapter_caching(self, test_session, mock_settings):
        """Test adapter caching functionality in orchestrator."""
        
        with patch('app.orchestrator.sample_pages') as mock_sample, \
             patch('app.orchestrator.learn_adapter') as mock_learn:
            
            # Setup mocks
            mock_sample.return_value = [
                Mock(url="https://example.com/page1", html="<h1>Test</h1>", domain="example.com")
            ]
            
            mock_adapter_data = Mock()
            mock_adapter_data.dict.return_value = {
                "domain": "example.com",
                "version": 1,
                "selectors": {"title": {"css": "h1", "xpath": "", "regex": ""}}
            }
            mock_learn.return_value = mock_adapter_data
            
            # Create orchestrator
            orchestrator = JobOrchestrator()
            
            # Create test job
            from app.models import Job
            job = Job(
                start_url="https://example.com",
                allowed_domains="example.com",
                max_depth=2,
                max_pages=10,
                field_spec=[{"name": "title", "dtype": "string"}],
                status="created"
            )
            test_session.add(job)
            await test_session.commit()
            await test_session.refresh(job)
            
            # First call should learn adapter
            adapter1 = await orchestrator._get_or_create_adapter(test_session, "example.com", job)
            assert adapter1["domain"] == "example.com"
            
            # Second call should use cached adapter
            adapter2 = await orchestrator._get_or_create_adapter(test_session, "example.com", job)
            assert adapter2 == adapter1
            
            # Verify learn_adapter was called only once
            assert mock_learn.call_count == 1
    
    @pytest.mark.asyncio
    async def test_extraction_engine_with_fallback(self, mock_settings):
        """Test extraction engine with LLM fallback integration."""
        
        from app.extractor import ExtractionEngine
        from app.fallback import FallbackExtractor
        
        # Mock LLM
        with patch('app.fallback.ChatOpenAI') as mock_llm_class:
            mock_llm = Mock()
            mock_llm.with_structured_output.return_value.ainvoke = AsyncMock(
                return_value=Mock(dict=lambda: {"title": "LLM Title"})
            )
            mock_llm_class.return_value = mock_llm
            
            # Create engines
            extractor = ExtractionEngine()
            fallback = FallbackExtractor()
            
            # Test HTML with missing title
            html = "<html><body><p>No title here</p></body></html>"
            
            # Adapter with title selector
            adapter = {
                "selectors": {
                    "title": {"css": "h1", "xpath": "", "regex": ""}
                }
            }
            
            field_specs = [{"name": "title", "dtype": "string"}]
            
            # Extract with deterministic (should be empty)
            deterministic_result = await extractor.extract_with_adapter(html, adapter, field_specs)
            assert deterministic_result["title"] == ""
            
            # Extract with fallback
            fallback_result = await fallback.extract_missing_fields(
                "https://example.com",
                html,
                [FieldSpec(**spec) for spec in field_specs],
                ["title"]
            )
            assert fallback_result["title"] == "LLM Title"
    
    @pytest.mark.asyncio
    async def test_job_status_progression(self, override_get_session, mock_settings):
        """Test job status progression through workflow stages."""
        
        with patch('app.orchestrator.sample_pages') as mock_sample, \
             patch('app.orchestrator.learn_adapter') as mock_learn, \
             patch('app.extractor.get_extractor') as mock_extractor_factory, \
             patch('app.fallback.get_fallback_extractor') as mock_fallback_factory:
            
            # Setup mocks
            mock_sample.return_value = [
                Mock(url="https://example.com/page1", html="<h1>Test</h1>", domain="example.com")
            ]
            
            mock_adapter_data = Mock()
            mock_adapter_data.dict.return_value = {
                "domain": "example.com",
                "selectors": {"title": {"css": "h1", "xpath": "", "regex": ""}}
            }
            mock_learn.return_value = mock_adapter_data
            
            mock_extractor = Mock()
            mock_extractor.extract_with_adapter = AsyncMock(return_value={"title": "Test Title"})
            mock_extractor_factory.return_value = mock_extractor
            
            mock_fallback = Mock()
            mock_fallback.extract_missing_fields = AsyncMock(return_value={})
            mock_fallback_factory.return_value = mock_fallback
            
            # Create job
            job_data = {
                "start_url": "https://example.com",
                "field_spec": [{"name": "title", "dtype": "string"}]
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                # Create job
                response = await client.post("/jobs", json=job_data)
                job_id = response.json()["job_id"]
                
                # Allow some time for background processing
                import asyncio
                await asyncio.sleep(0.1)
                
                # Check status
                response = await client.get(f"/jobs/{job_id}")
                status_data = response.json()
                
                # Job should exist and have valid status
                assert status_data["job_id"] == job_id
                assert status_data["status"] in ["created", "running", "completed", "failed"]
    
    @pytest.mark.asyncio
    async def test_csv_generation_and_download(self, override_get_session, temp_dir):
        """Test CSV generation and download functionality."""
        
        # Create a completed job with extractions
        from app.models import Job, Page, Extraction
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Get session from dependency override
            session = None
            async for s in app.dependency_overrides[app.dependency_overrides.__iter__().__next__()]():
                session = s
                break
            
            # Create job
            job = Job(
                start_url="https://example.com",
                field_spec=[
                    {"name": "title", "dtype": "string"},
                    {"name": "tags", "dtype": "string[]"}
                ],
                status="completed"
            )
            session.add(job)
            await session.commit()
            await session.refresh(job)
            
            # Create page
            page = Page(
                job_id=job.id,
                url="https://example.com/page1",
                domain="example.com",
                html_content="<h1>Test</h1>",
                status_code=200,
                content_type="text/html",
                content_length=100,
                fetch_method="httpx"
            )
            session.add(page)
            await session.commit()
            await session.refresh(page)
            
            # Create extraction
            extraction = Extraction(
                job_id=job.id,
                page_id=page.id,
                extracted_data={
                    "title": "Test Title",
                    "tags": ["tag1", "tag2"]
                },
                extraction_method="deterministic",
                confidence_score=0.95
            )
            session.add(extraction)
            await session.commit()
            
            # Generate CSV (mock the file creation)
            csv_filename = f"job_{job.id}.csv"
            with open(csv_filename, 'w') as f:
                f.write("title,tags\n")
                f.write('Test Title,"[""tag1"", ""tag2""]"\n')
            
            try:
                # Test CSV download
                response = await client.get(f"/jobs/{job.id}/csv")
                assert response.status_code == 200
                assert response.headers["content-type"] == "text/csv; charset=utf-8"
                
            finally:
                # Cleanup
                if os.path.exists(csv_filename):
                    os.remove(csv_filename)
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, override_get_session):
        """Test error handling throughout the workflow."""
        
        # Test invalid URL handling
        async with AsyncClient(app=app, base_url="http://test") as client:
            invalid_job = {
                "start_url": "not-a-url",
                "field_spec": [{"name": "title", "dtype": "string"}]
            }
            
            response = await client.post("/jobs", json=invalid_job)
            assert response.status_code == 422
            
            # Test invalid field spec
            invalid_field_job = {
                "start_url": "https://example.com",
                "field_spec": [{"name": "title", "dtype": "invalid_type"}]
            }
            
            response = await client.post("/jobs", json=invalid_field_job)
            assert response.status_code == 422
