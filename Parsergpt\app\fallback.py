"""LLM fallback extraction for ParserGPT POC."""

import logging
from typing import Dict, List, Any, Optional, Type
from pydantic import BaseModel, Field, create_model
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from .schemas import FieldSpec
from .config import get_settings

logger = logging.getLogger(__name__)


class FallbackExtractor:
    """LLM-powered fallback extraction for missing fields."""
    
    def __init__(self):
        self.settings = get_settings()
        self.llm = self._create_llm()
    
    def _create_llm(self):
        """Create LLM instance based on configuration."""
        if self.settings.openai_api_key:
            return ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0,
                api_key=self.settings.openai_api_key
            )
        else:
            # TODO: Add Ollama support
            raise ValueError("OpenAI API key required for LLM operations")
    
    def _create_dynamic_model(self, field_specs: List[FieldSpec]) -> Type[BaseModel]:
        """Create a dynamic Pydantic model based on field specifications."""
        fields = {}
        
        for spec in field_specs:
            # Determine Python type from dtype
            if spec.dtype == "string":
                field_type = str
                default_value = ""
            elif spec.dtype == "int":
                field_type = int
                default_value = 0
            elif spec.dtype == "float":
                field_type = float
                default_value = 0.0
            elif spec.dtype == "bool":
                field_type = bool
                default_value = False
            elif spec.dtype == "string[]":
                field_type = List[str]
                default_value = []
            elif spec.dtype == "int[]":
                field_type = List[int]
                default_value = []
            elif spec.dtype == "float[]":
                field_type = List[float]
                default_value = []
            else:
                # Default to string
                field_type = str
                default_value = ""
            
            # Create field with description
            fields[spec.name] = (
                field_type,
                Field(
                    default=default_value,
                    description=spec.description or f"Extract {spec.name}"
                )
            )
        
        # Create dynamic model
        return create_model("ExtractionResult", **fields)
    
    def _create_prompt_template(self) -> ChatPromptTemplate:
        """Create the prompt template for LLM extraction."""
        return ChatPromptTemplate.from_messages([
            ("system", """You are a precise data extraction specialist. Your task is to extract specific fields from HTML content.

CRITICAL RULES:
1. Return ONLY valid JSON matching the exact schema provided
2. If a field cannot be found, use the default value (empty string, empty list, 0, etc.)
3. For array fields, extract ALL relevant values as a list
4. Be precise - extract only what is asked for
5. Clean and normalize extracted text (remove extra whitespace, etc.)

EXTRACTION STRATEGY:
- Look for the most relevant content for each field
- Use context clues from surrounding HTML structure
- Prefer visible text over hidden attributes
- For arrays, collect all matching items"""),
            ("human", """URL: {url}

HTML CONTENT (truncated):
{html_snippet}

FIELDS TO EXTRACT:
{field_descriptions}

Extract the requested fields and return as JSON. If a field cannot be found, use the appropriate default value.""")
        ])
    
    async def extract_missing_fields(
        self,
        url: str,
        html: str,
        field_specs: List[FieldSpec],
        missing_fields: List[str],
        current_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Extract only the missing fields using LLM.
        
        Args:
            url: Page URL
            html: HTML content
            field_specs: All field specifications
            missing_fields: List of field names that need extraction
            current_data: Current extracted data (for context)
            
        Returns:
            Dictionary with extracted values for missing fields only
        """
        if not missing_fields:
            return {}
        
        logger.info(f"LLM fallback extraction for {len(missing_fields)} missing fields: {missing_fields}")
        
        # Filter field specs to only missing fields
        missing_field_specs = [spec for spec in field_specs if spec.name in missing_fields]
        
        if not missing_field_specs:
            logger.warning("No field specs found for missing fields")
            return {}
        
        try:
            # Create dynamic Pydantic model for missing fields only
            ExtractionModel = self._create_dynamic_model(missing_field_specs)
            
            # Create structured output chain
            prompt = self._create_prompt_template()
            structured_llm = self.llm.with_structured_output(ExtractionModel)
            chain = prompt | structured_llm
            
            # Prepare field descriptions
            field_descriptions = []
            for spec in missing_field_specs:
                desc = f"- {spec.name} ({spec.dtype})"
                if spec.description:
                    desc += f": {spec.description}"
                field_descriptions.append(desc)
            
            # Truncate HTML to avoid token limits
            html_snippet = html[:12000]  # Roughly 3000 tokens
            
            # Extract data
            result = await chain.ainvoke({
                "url": url,
                "html_snippet": html_snippet,
                "field_descriptions": "\n".join(field_descriptions)
            })
            
            # Convert Pydantic model to dict
            extracted_data = result.dict()
            
            # Log results
            extracted_count = sum(1 for v in extracted_data.values() if v not in ["", [], 0, 0.0, False])
            logger.info(f"LLM extracted {extracted_count}/{len(missing_fields)} missing fields")
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"LLM fallback extraction failed: {e}")
            # Return empty values for missing fields
            fallback_data = {}
            for spec in missing_field_specs:
                if spec.dtype.endswith("[]"):
                    fallback_data[spec.name] = []
                elif spec.dtype == "int":
                    fallback_data[spec.name] = 0
                elif spec.dtype == "float":
                    fallback_data[spec.name] = 0.0
                elif spec.dtype == "bool":
                    fallback_data[spec.name] = False
                else:
                    fallback_data[spec.name] = ""
            
            return fallback_data
    
    async def extract_all_fields(
        self,
        url: str,
        html: str,
        field_specs: List[FieldSpec]
    ) -> Dict[str, Any]:
        """
        Extract all fields using LLM (for comparison/validation).
        
        Args:
            url: Page URL
            html: HTML content
            field_specs: Field specifications
            
        Returns:
            Dictionary with extracted values for all fields
        """
        logger.info(f"LLM extraction for all {len(field_specs)} fields")
        
        try:
            # Create dynamic Pydantic model
            ExtractionModel = self._create_dynamic_model(field_specs)
            
            # Create structured output chain
            prompt = self._create_prompt_template()
            structured_llm = self.llm.with_structured_output(ExtractionModel)
            chain = prompt | structured_llm
            
            # Prepare field descriptions
            field_descriptions = []
            for spec in field_specs:
                desc = f"- {spec.name} ({spec.dtype})"
                if spec.description:
                    desc += f": {spec.description}"
                field_descriptions.append(desc)
            
            # Truncate HTML to avoid token limits
            html_snippet = html[:12000]
            
            # Extract data
            result = await chain.ainvoke({
                "url": url,
                "html_snippet": html_snippet,
                "field_descriptions": "\n".join(field_descriptions)
            })
            
            # Convert to dict
            extracted_data = result.dict()
            
            logger.info(f"LLM extracted all fields successfully")
            return extracted_data
            
        except Exception as e:
            logger.error(f"LLM full extraction failed: {e}")
            # Return empty data structure
            empty_data = {}
            for spec in field_specs:
                if spec.dtype.endswith("[]"):
                    empty_data[spec.name] = []
                elif spec.dtype == "int":
                    empty_data[spec.name] = 0
                elif spec.dtype == "float":
                    empty_data[spec.name] = 0.0
                elif spec.dtype == "bool":
                    empty_data[spec.name] = False
                else:
                    empty_data[spec.name] = ""
            
            return empty_data


def merge_extraction_results(
    deterministic_data: Dict[str, Any],
    llm_data: Dict[str, Any],
    prefer_llm_fields: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Merge deterministic and LLM extraction results.
    
    Args:
        deterministic_data: Results from deterministic extraction
        llm_data: Results from LLM extraction
        prefer_llm_fields: Fields where LLM results should be preferred
        
    Returns:
        Merged extraction results
    """
    prefer_llm_fields = prefer_llm_fields or []
    merged = deterministic_data.copy()
    
    for field_name, llm_value in llm_data.items():
        deterministic_value = merged.get(field_name)
        
        # Prefer LLM for specified fields
        if field_name in prefer_llm_fields and _has_value(llm_value):
            merged[field_name] = llm_value
            continue
        
        # Use LLM value if deterministic is empty
        if not _has_value(deterministic_value) and _has_value(llm_value):
            merged[field_name] = llm_value
    
    return merged


def _has_value(value: Any) -> bool:
    """Check if a value is considered non-empty."""
    if value is None:
        return False
    if isinstance(value, str):
        return bool(value.strip())
    if isinstance(value, (list, dict)):
        return len(value) > 0
    if isinstance(value, (int, float)):
        return value != 0
    if isinstance(value, bool):
        return True  # Both True and False are valid values
    return bool(value)


# Global fallback extractor instance
_fallback_extractor = None


def get_fallback_extractor() -> FallbackExtractor:
    """Get the global fallback extractor instance."""
    global _fallback_extractor
    if _fallback_extractor is None:
        _fallback_extractor = FallbackExtractor()
    return _fallback_extractor


async def extract_missing_fields(
    url: str,
    html: str,
    field_specs: List[FieldSpec],
    missing_fields: List[str],
    current_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Convenience function for missing field extraction."""
    extractor = get_fallback_extractor()
    return await extractor.extract_missing_fields(url, html, field_specs, missing_fields, current_data)
