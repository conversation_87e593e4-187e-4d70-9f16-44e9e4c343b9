"""
Workflow management endpoints for creating, executing, and monitoring agent workflows.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.responses import JSONResponse

from api.models.workflow_models import (
    WorkflowCreateRequest, WorkflowResponse, WorkflowStatusResponse,
    WorkflowListResponse, WorkflowCancelRequest, WorkflowExecutionRequest,
    CustomWorkflowRequest, WorkflowStatus
)
from api.models.common_models import ResponseStatus, SuccessResponse
from api.services.workflow_service import WorkflowService
from api.dependencies import (
    get_workflow_service, require_database, require_agents,
    validate_workflow_id, get_pagination_params
)
from api.middleware.rate_limiting import limiter
from fastapi import Request

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", response_model=WorkflowResponse, status_code=status.HTTP_201_CREATED)
@limiter.limit("10/minute")
async def create_workflow(
    request: Request,
    workflow_request: WorkflowCreateRequest,
    workflow_service: WorkflowService = Depends(get_workflow_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Create a new workflow from a natural language query.
    
    The system will automatically analyze the query and create an appropriate
    workflow with the necessary agents and tasks.
    
    - **query**: Natural language description of what you want to analyze
    - **workflow_type**: Type of workflow (auto, custom, analysis)
    - **priority**: Workflow priority (1=highest, 5=lowest)
    - **timeout**: Maximum execution time in seconds
    - **metadata**: Additional workflow metadata
    """
    try:
        logger.info(f"Creating workflow with query: {workflow_request.query[:100]}...")
        
        workflow = await workflow_service.create_workflow(workflow_request)
        
        logger.info(f"Workflow {workflow.workflow_id} created successfully")
        return workflow
        
    except ValueError as e:
        logger.warning(f"Invalid workflow request: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create workflow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workflow"
        )


@router.post("/custom", response_model=WorkflowResponse, status_code=status.HTTP_201_CREATED)
@limiter.limit("5/minute")
async def create_custom_workflow(
    request: Request,
    custom_request: CustomWorkflowRequest,
    workflow_service: WorkflowService = Depends(get_workflow_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Create a custom workflow with specific task configuration.
    
    This endpoint allows you to define exactly which agents to use
    and how they should be orchestrated.
    
    - **workflow_name**: Name for the custom workflow
    - **description**: Optional description
    - **tasks**: List of tasks with specific agent assignments
    - **priority**: Workflow priority
    - **timeout**: Maximum execution time
    """
    try:
        logger.info(f"Creating custom workflow: {custom_request.workflow_name}")
        
        # Convert custom request to standard workflow request
        # This would need additional logic to handle custom task definitions
        workflow_request = WorkflowCreateRequest(
            query=custom_request.description or custom_request.workflow_name,
            workflow_type="custom",
            priority=custom_request.priority,
            timeout=custom_request.timeout,
            metadata={
                "custom_workflow": True,
                "workflow_name": custom_request.workflow_name,
                "task_count": len(custom_request.tasks)
            }
        )
        
        workflow = await workflow_service.create_workflow(workflow_request)
        
        logger.info(f"Custom workflow {workflow.workflow_id} created successfully")
        return workflow
        
    except ValueError as e:
        logger.warning(f"Invalid custom workflow request: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create custom workflow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create custom workflow"
        )


@router.post("/{workflow_id}/execute", response_model=WorkflowResponse)
@limiter.limit("20/minute")
async def execute_workflow(
    request: Request,
    workflow_id: str = Path(..., description="Workflow ID to execute"),
    execution_request: Optional[WorkflowExecutionRequest] = None,
    workflow_service: WorkflowService = Depends(get_workflow_service),
    _: bool = Depends(require_database),
    __: bool = Depends(require_agents)
):
    """
    Execute a created workflow.
    
    Starts the execution of a previously created workflow. Can be executed
    synchronously or asynchronously.
    
    - **workflow_id**: ID of the workflow to execute
    - **async_execution**: Whether to execute asynchronously (default: true)
    - **parameters**: Additional execution parameters
    """
    try:
        validated_id = validate_workflow_id(workflow_id)
        
        async_execution = True
        if execution_request:
            async_execution = execution_request.async_execution
        
        logger.info(f"Executing workflow {validated_id}, async: {async_execution}")
        
        workflow = await workflow_service.execute_workflow(validated_id, async_execution)
        
        logger.info(f"Workflow {validated_id} execution started")
        return workflow
        
    except ValueError as e:
        logger.warning(f"Invalid workflow execution request: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to execute workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute workflow"
        )


@router.get("/{workflow_id}/status", response_model=WorkflowStatusResponse)
async def get_workflow_status(
    workflow_id: str = Path(..., description="Workflow ID"),
    workflow_service: WorkflowService = Depends(get_workflow_service)
):
    """
    Get the current status and progress of a workflow.
    
    Returns detailed status information including:
    - Current execution status
    - Progress percentage
    - Number of completed/total tasks
    - Estimated completion time
    - Currently executing task
    """
    try:
        validated_id = validate_workflow_id(workflow_id)
        
        status_response = await workflow_service.get_workflow_status(validated_id)
        
        return status_response
        
    except ValueError as e:
        logger.warning(f"Invalid workflow ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get workflow status for {workflow_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: str = Path(..., description="Workflow ID"),
    workflow_service: WorkflowService = Depends(get_workflow_service)
):
    """
    Get complete workflow information including tasks and results.
    
    Returns detailed workflow information including:
    - Workflow metadata
    - All tasks and their status
    - Execution results
    - Error information if applicable
    """
    try:
        validated_id = validate_workflow_id(workflow_id)
        
        workflow = await workflow_service.get_workflow(validated_id)
        
        return workflow
        
    except ValueError as e:
        logger.warning(f"Invalid workflow ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )


@router.post("/{workflow_id}/cancel", response_model=WorkflowResponse)
async def cancel_workflow(
    workflow_id: str = Path(..., description="Workflow ID to cancel"),
    cancel_request: Optional[WorkflowCancelRequest] = None,
    workflow_service: WorkflowService = Depends(get_workflow_service)
):
    """
    Cancel a running workflow.
    
    Stops the execution of a workflow and marks it as cancelled.
    Running tasks may complete before the workflow is fully cancelled.
    
    - **workflow_id**: ID of the workflow to cancel
    - **reason**: Optional reason for cancellation
    - **force**: Whether to force cancellation of running tasks
    """
    try:
        validated_id = validate_workflow_id(workflow_id)
        
        reason = None
        if cancel_request:
            reason = cancel_request.reason
        
        logger.info(f"Cancelling workflow {validated_id}, reason: {reason}")
        
        workflow = await workflow_service.cancel_workflow(validated_id, reason)
        
        logger.info(f"Workflow {validated_id} cancelled successfully")
        return workflow
        
    except ValueError as e:
        logger.warning(f"Invalid workflow cancellation request: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to cancel workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel workflow"
        )


@router.get("/", response_model=List[WorkflowResponse])
async def list_workflows(
    status_filter: Optional[WorkflowStatus] = Query(None, description="Filter by workflow status"),
    pagination: dict = Depends(get_pagination_params),
    workflow_service: WorkflowService = Depends(get_workflow_service)
):
    """
    List all workflows with optional filtering and pagination.
    
    Returns a list of workflows with optional status filtering.
    
    - **status**: Filter workflows by status (pending, in_progress, completed, failed, cancelled)
    - **page**: Page number for pagination (default: 1)
    - **page_size**: Number of items per page (default: 20, max: 100)
    """
    try:
        logger.info(f"Listing workflows, status filter: {status_filter}")
        
        workflows = await workflow_service.list_workflows(status_filter)
        
        # Apply pagination
        start_idx = pagination["offset"]
        end_idx = start_idx + pagination["page_size"]
        paginated_workflows = workflows[start_idx:end_idx]
        
        logger.info(f"Retrieved {len(paginated_workflows)} workflows")
        return paginated_workflows
        
    except Exception as e:
        logger.error(f"Failed to list workflows: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list workflows"
        )
