"""LLM-powered selector proposal for ParserGPT POC."""

import logging
from typing import List, Dict, Any
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from .schemas import AdapterDraft, FieldSpec
from .sampling import PageSample
from .config import get_settings

logger = logging.getLogger(__name__)


class SelectorProposer:
    """LLM-powered selector proposal system."""
    
    def __init__(self):
        self.settings = get_settings()
        self.llm = self._create_llm()
    
    def _create_llm(self):
        """Create LLM instance based on configuration."""
        if self.settings.openai_api_key:
            return ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0,
                api_key=self.settings.openai_api_key
            )
        else:
            # TODO: Add Ollama support
            raise ValueError("OpenAI API key required for LLM operations")
    
    def _prepare_samples_text(self, samples: List[PageSample], max_length: int = 8000) -> str:
        """Prepare HTML samples for LLM input."""
        samples_text = []
        
        for i, sample in enumerate(samples):
            # Truncate HTML to avoid token limits
            html_snippet = sample.html[:max_length]
            samples_text.append(f"=== SAMPLE {i+1} ({sample.page_type}) ===\nURL: {sample.url}\n{html_snippet}")
        
        return "\n\n".join(samples_text)
    
    def _create_prompt_template(self) -> ChatPromptTemplate:
        """Create the prompt template for selector proposal."""
        return ChatPromptTemplate.from_messages([
            ("system", """You are an expert web scraping engineer. Your task is to analyze HTML samples and propose precise extraction rules.

IMPORTANT RULES:
1. Return ONLY valid JSON with keys: url_patterns, selectors
2. Keep selectors precise and minimal - avoid overly broad selectors
3. Prefer CSS selectors when possible, use XPath for complex cases, regex as last resort
4. For array fields (ending with []), select multiple elements
5. Test your selectors mentally against the samples

SELECTOR STRATEGY:
- CSS: Use for standard HTML elements and classes
- XPath: Use for complex navigation or text content matching
- Regex: Use only for extracting patterns from text content

URL PATTERNS:
- Classify URLs into: "detail", "list", "other"
- Use glob patterns like "*/product/*", "*category*"
"""),
            ("human", """FIELDS TO EXTRACT:
{fields}

HTML SAMPLES:
{samples}

Analyze these samples and propose extraction rules. Return JSON with:
- url_patterns: categorize URLs by type
- selectors: for each field, provide css/xpath/regex selectors

Focus on accuracy and reliability over complexity.""")
        ])
    
    async def propose_selectors(
        self,
        field_specs: List[FieldSpec],
        samples: List[PageSample]
    ) -> AdapterDraft:
        """
        Propose selectors for given fields based on HTML samples.
        
        Args:
            field_specs: List of fields to extract
            samples: List of HTML samples
            
        Returns:
            AdapterDraft with proposed selectors
        """
        logger.info(f"Proposing selectors for {len(field_specs)} fields using {len(samples)} samples")
        
        # Prepare input data
        fields_text = ", ".join(f"{spec.name}:{spec.dtype}" for spec in field_specs)
        samples_text = self._prepare_samples_text(samples)
        
        # Create structured output chain
        prompt = self._create_prompt_template()
        structured_llm = self.llm.with_structured_output(AdapterDraft)
        chain = prompt | structured_llm
        
        try:
            # Generate selectors
            result = await chain.ainvoke({
                "fields": fields_text,
                "samples": samples_text
            })
            
            logger.info(f"Successfully proposed selectors for {len(result.selectors)} fields")
            logger.debug(f"Proposed selectors: {result.selectors}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error proposing selectors: {e}")
            # Return empty draft on error
            return AdapterDraft()
    
    async def refine_selectors(
        self,
        current_draft: AdapterDraft,
        field_specs: List[FieldSpec],
        validation_failures: List[Dict[str, Any]]
    ) -> AdapterDraft:
        """
        Refine selectors based on validation failures.
        
        Args:
            current_draft: Current adapter draft
            field_specs: Field specifications
            validation_failures: List of validation failures
            
        Returns:
            Refined AdapterDraft
        """
        logger.info(f"Refining selectors based on {len(validation_failures)} failures")
        
        # Create refinement prompt
        refinement_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are refining web scraping selectors based on validation failures.

TASK: Fix the selectors that failed validation by providing better alternatives.

RULES:
1. Return ONLY valid JSON with the same structure
2. Keep working selectors unchanged
3. Fix only the failing selectors
4. Consider the failure reasons and adjust accordingly
5. Try different selector strategies (CSS -> XPath -> Regex)
"""),
            ("human", """CURRENT SELECTORS:
{current_selectors}

VALIDATION FAILURES:
{failures}

FIELD SPECIFICATIONS:
{fields}

Fix the failing selectors while keeping successful ones unchanged. Return the complete updated JSON structure.""")
        ])
        
        # Prepare failure information
        failures_text = []
        for failure in validation_failures:
            failures_text.append(
                f"Field: {failure.get('field', 'unknown')}\n"
                f"Reason: {failure.get('reason', 'unknown')}\n"
                f"Current selector: {failure.get('selector', 'none')}\n"
                f"Expected: {failure.get('expected', 'none')}\n"
                f"Got: {failure.get('actual', 'none')}"
            )
        
        fields_text = ", ".join(f"{spec.name}:{spec.dtype}" for spec in field_specs)
        
        structured_llm = self.llm.with_structured_output(AdapterDraft)
        chain = refinement_prompt | structured_llm
        
        try:
            result = await chain.ainvoke({
                "current_selectors": current_draft.dict(),
                "failures": "\n\n".join(failures_text),
                "fields": fields_text
            })
            
            logger.info(f"Successfully refined selectors")
            return result
            
        except Exception as e:
            logger.error(f"Error refining selectors: {e}")
            # Return current draft on error
            return current_draft


# Global proposer instance
_selector_proposer = None


def get_selector_proposer() -> SelectorProposer:
    """Get the global selector proposer instance."""
    global _selector_proposer
    if _selector_proposer is None:
        _selector_proposer = SelectorProposer()
    return _selector_proposer


async def propose_selectors(
    field_specs: List[FieldSpec],
    samples: List[PageSample]
) -> AdapterDraft:
    """Convenience function to propose selectors."""
    proposer = get_selector_proposer()
    return await proposer.propose_selectors(field_specs, samples)
