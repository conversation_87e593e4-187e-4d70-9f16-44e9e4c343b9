"""
Matching service that uses ideal candidate embeddings instead of position embeddings for RAG.
"""

import logging
import psycopg2
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException

from core.config import settings
from controllers.ideal_candidate_controller import get_ideal_candidate_by_position_id
from services.ideal_candidate_workflow import ideal_candidate_workflow
from utils import match_evaluations as match_functions
from utils.utils_embeddings import generate_openai_embedding

logger = logging.getLogger(__name__)


class IdealCandidateMatchingService:
    """Service for matching candidates using ideal candidate embeddings instead of position embeddings."""
    
    def __init__(self):
        pass
    
    def get_or_create_ideal_candidate_for_position(self, position_id: str) -> Optional[Dict[str, Any]]:
        """
        Generate ideal candidate for position using stateless approach and create embedding on-the-fly.

        Args:
            position_id: The position ID to generate ideal candidate for

        Returns:
            Dictionary containing ideal candidate data and embedding or None if failed
        """
        try:
            logger.info(f"Generating stateless ideal candidate for position {position_id}")

            # Use the stateless service to generate ideal candidate
            from services.stateless_ideal_candidate_service import stateless_ideal_candidate_service

            response = stateless_ideal_candidate_service.generate_ideal_candidate_for_position(
                position_id=position_id,
                position_info=None
            )

            if not response.generation_success:
                logger.error(f"Failed to generate ideal candidate for position {position_id}: {response.error_message}")
                return None

            # Generate embedding for the ideal candidate
            from utils.ideal_candidate_text_utils import prepare_ideal_candidate_for_embedding
            from utils.utils_embeddings import generate_openai_embedding

            # Prepare text for embedding
            embed_text = prepare_ideal_candidate_for_embedding(response.ideal_candidate_info)

            if not embed_text or embed_text.strip() == "":
                logger.warning("Empty text generated for ideal candidate embedding")
                return None

            # Generate embedding
            embedding = generate_openai_embedding(embed_text)

            if embedding is None:
                logger.error("Failed to generate embedding for ideal candidate due to Azure OpenAI connection issues")
                # Return a special error response indicating embedding service is unavailable
                return {
                    "error": "embedding_service_unavailable",
                    "message": "Cannot perform matching due to embedding service connectivity issues. Please try again later."
                }

            logger.info(f"Successfully generated ideal candidate and embedding for position {position_id}")

            return {
                "id": f"stateless_{position_id}",  # Temporary ID for stateless approach
                "ideal_candidate_info": response.ideal_candidate_info,
                "to_be_embedded": embed_text,
                "embedding": embedding,
                "embedding_generated": True
            }

        except Exception as e:
            logger.error(f"Error generating stateless ideal candidate for position {position_id}: {e}")
            return None
    
    def match_candidates_with_ideal_candidate(
        self,
        position_id: str,
        limit: int = 5,
        hasFeedback: int = 2,
        batch_mode: bool = True
    ) -> Dict[str, Any]:
        """
        Match candidates against a position using ideal candidate embeddings.
        
        Args:
            position_id: The position ID to match candidates for
            limit: Maximum number of candidates to return
            hasFeedback: Feedback filter (0=without, 1=with, 2=both)
            batch_mode: Whether to use batch processing
            
        Returns:
            Dictionary containing matched candidates and metadata
        """
        try:
            logger.info(f"Starting ideal candidate-based matching for position {position_id}")
            
            # 1. Get or create ideal candidate for the position
            ideal_candidate_data = self.get_or_create_ideal_candidate_for_position(position_id)
            if not ideal_candidate_data:
                raise ValueError(f"Failed to get/create ideal candidate for position {position_id}")

            # Check if embedding service is unavailable
            if ideal_candidate_data.get("error") == "embedding_service_unavailable":
                return {
                    "position_id": position_id,
                    "total_candidates_found": 0,
                    "candidates": [],
                    "matching_success": False,
                    "error_message": ideal_candidate_data.get("message", "Embedding service unavailable"),
                    "metadata": {
                        "matching_type": "ideal_candidate_based",
                        "embedding_service_status": "unavailable"
                    }
                }

            if not ideal_candidate_data.get("embedding_generated", False):
                raise ValueError(f"Ideal candidate for position {position_id} does not have embedding generated")
            
            # 2. Get ideal candidate embedding from the generated data
            ideal_candidate_embedding = ideal_candidate_data["embedding"]

            # Format embedding for PostgreSQL vector operations
            from utils.utils_embeddings import format_vector
            embedding_vector = format_vector(ideal_candidate_embedding)

            # 3. Search for similar candidates using ideal candidate embedding
            conn = psycopg2.connect(settings.DATABASE_URL)
            cur = conn.cursor()
            
            # 3. Build candidate query based on feedback requirements
            if hasFeedback == 0:
                # Without feedback
                candidate_query = """
                    SELECT 
                        c.id, 
                        c.proj_id, 
                        c.candidate_info, 
                        1 - (c.embedding <=> %s) AS cosine_similarity, 
                        c.to_be_embebbed,
                        COUNT(i.id) AS num_feedbacks
                    FROM 
                        candidates_smarthr c
                    LEFT JOIN 
                        interviews i ON i.candidate_id = c.id AND i.position_id = %s
                    WHERE 
                        c.is_deleted = false 
                        AND c.is_active = true
                    GROUP BY 
                        c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
                    HAVING 
                        COUNT(i.id) = 0
                    ORDER BY 
                        cosine_similarity DESC
                    LIMIT %s
                """
                cur.execute(candidate_query, (embedding_vector, position_id, limit))
            elif hasFeedback == 1:
                # With feedback
                candidate_query = """
                    SELECT 
                        c.id, 
                        c.proj_id, 
                        c.candidate_info, 
                        1 - (c.embedding <=> %s) AS cosine_similarity, 
                        c.to_be_embebbed,
                        COUNT(i.id) AS num_feedbacks
                    FROM 
                        candidates_smarthr c
                    INNER JOIN 
                        interviews i ON i.candidate_id = c.id AND i.position_id = %s
                    WHERE 
                        c.is_deleted = false 
                        AND c.is_active = true
                    GROUP BY 
                        c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
                    HAVING 
                        COUNT(i.id) > 0
                    ORDER BY 
                        cosine_similarity DESC
                    LIMIT %s
                """
                cur.execute(candidate_query, (embedding_vector, position_id, limit))
            else:
                # Both (hasFeedback == 2)
                candidate_query = """
                    SELECT 
                        c.id, 
                        c.proj_id, 
                        c.candidate_info, 
                        1 - (c.embedding <=> %s) AS cosine_similarity, 
                        c.to_be_embebbed
                    FROM 
                        candidates_smarthr c
                    WHERE 
                        c.is_deleted = false 
                        AND c.is_active = true
                    ORDER BY 
                        cosine_similarity DESC
                    LIMIT %s
                """
                cur.execute(candidate_query, (embedding_vector, limit))
            
            results = cur.fetchall()
            cur.close()
            conn.close()
            
            logger.info(f"Found {len(results)} candidates for ideal candidate-based matching")
            
            # 4. Process results with LLM analysis
            candidates_result = []
            ideal_candidate_text = ideal_candidate_data["to_be_embedded"]
            
            if batch_mode and len(results) > 1:
                # Batch processing
                candidate_texts = [r[4] for r in results]  # to_be_embebbed field
                
                batch_analysis = match_functions.evaluate_candidates_batch(
                    candidates=candidate_texts,
                    position=ideal_candidate_text
                )
                
                for i, r in enumerate(results):
                    candidate_id_db = r[0]
                    proj_id_db = r[1]
                    candidate_info_db = r[2]
                    similarity_score = r[3]
                    candidate_text = r[4]
                    
                    # Get analysis for this candidate from batch result
                    if i < len(batch_analysis.candidates_analysis):
                        analysis = batch_analysis.candidates_analysis[i].model_dump()
                    else:
                        # Fallback to individual analysis
                        analysis = match_functions.evaluate_candidate(
                            candidate_text, ideal_candidate_text
                        )
                    
                    candidates_result.append({
                        "id": candidate_id_db,
                        "proj_id": proj_id_db,
                        "candidate_info": candidate_info_db,
                        "cosine_similarity": similarity_score,
                        "analysis": dict(analysis),
                    })
            else:
                # Individual processing
                for r in results:
                    candidate_id_db = r[0]
                    proj_id_db = r[1]
                    candidate_info_db = r[2]
                    similarity_score = r[3]
                    candidate_text = r[4]
                    
                    analysis = match_functions.evaluate_candidate(
                        candidate_text, ideal_candidate_text
                    )
                    
                    candidates_result.append({
                        "id": candidate_id_db,
                        "proj_id": proj_id_db,
                        "candidate_info": candidate_info_db,
                        "cosine_similarity": similarity_score,
                        "analysis": dict(analysis),
                    })
            
            # 5. Prepare final result
            result = {
                "matched_candidates": candidates_result,
                "ideal_candidate_used": {
                    "id": ideal_candidate_data["id"],
                    "ideal_candidate_info": ideal_candidate_data["ideal_candidate_info"]
                },
                "processed_ideal_candidate": ideal_candidate_text,
                "matching_method": "ideal_candidate_based",
                "timestamp": datetime.now(),
            }
            
            logger.info(f"Completed ideal candidate-based matching for position {position_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error in ideal candidate-based matching for position {position_id}: {e}")
            raise

    def match_positions_with_candidate(
        self,
        candidate_id: str,
        limit: int = 5
    ) -> Dict[str, Any]:
        """
        Match positions against a candidate using ideal candidate embeddings.

        Args:
            candidate_id: The candidate ID to match positions for
            limit: Maximum number of positions to return

        Returns:
            Dictionary containing matched positions and metadata
        """
        try:
            logger.info(f"Starting ideal candidate-based position matching for candidate {candidate_id}")

            # 1. Get candidate data and embedding
            conn = psycopg2.connect(settings.DATABASE_URL)
            cur = conn.cursor()

            cur.execute(
                "SELECT to_be_embebbed, embedding FROM candidates_smarthr WHERE id = %s AND is_deleted = false AND is_active = true",
                (candidate_id,)
            )
            candidate_row = cur.fetchone()

            if not candidate_row:
                cur.close()
                conn.close()
                raise ValueError(f"Candidate {candidate_id} not found or inactive")

            candidate_text = candidate_row[0]
            candidate_embedding = candidate_row[1]

            # 2. Search for positions with ideal candidates that are similar to the candidate
            position_query = """
                SELECT
                    p.id,
                    p.proj_id,
                    p.position_info,
                    1 - (ic.embedding <=> %s) AS cosine_similarity,
                    ic.to_be_embedded,
                    ic.id as ideal_candidate_id
                FROM
                    positions_smarthr p
                INNER JOIN
                    ideal_candidates_smarthr ic ON ic.position_id = p.id
                WHERE
                    ic.is_active = true
                    AND ic.embedding_generated = true
                ORDER BY
                    cosine_similarity DESC
                LIMIT %s
            """

            cur.execute(position_query, (candidate_embedding, limit))
            results = cur.fetchall()
            cur.close()
            conn.close()

            logger.info(f"Found {len(results)} positions for ideal candidate-based matching")

            # 3. Process results with LLM analysis
            positions_result = []

            for r in results:
                position_id_db = r[0]
                proj_id_db = r[1]
                position_info_db = r[2]
                similarity_score = r[3]
                ideal_candidate_text = r[4]
                ideal_candidate_id = r[5]

                # Evaluate position using ideal candidate text instead of position text
                analysis = match_functions.evaluate_position(
                    candidate_text, ideal_candidate_text
                )

                positions_result.append({
                    "id": position_id_db,
                    "proj_id": proj_id_db,
                    "position_info": position_info_db,
                    "cosine_similarity": similarity_score,
                    "analysis": dict(analysis),
                    "ideal_candidate_id": ideal_candidate_id,
                })

            # 4. Prepare final result
            result = {
                "matched_positions": positions_result,
                "processed_candidate": candidate_text,
                "matching_method": "ideal_candidate_based",
                "timestamp": datetime.now(),
            }

            logger.info(f"Completed ideal candidate-based position matching for candidate {candidate_id}")
            return result

        except Exception as e:
            logger.error(f"Error in ideal candidate-based position matching for candidate {candidate_id}: {e}")
            raise

    def match_candidates_by_ideal_candidate_id(
        self,
        ideal_candidate_id: str,
        limit: int = 5,
        hasFeedback: int = 2,
        batch_mode: bool = True
    ) -> Dict[str, Any]:
        """
        Match all candidates against a specific ideal candidate by its ID.

        Args:
            ideal_candidate_id: The ideal candidate ID to match against
            limit: Maximum number of candidates to return
            hasFeedback: Feedback filter (0=without, 1=with, 2=both)
            batch_mode: Whether to use batch processing

        Returns:
            Dictionary containing matched candidates and metadata
        """
        try:
            logger.info(f"Starting candidate matching for ideal candidate {ideal_candidate_id}")

            # 1. Get the ideal candidate and its embedding
            conn = get_db_connection()
            cur = conn.cursor()

            ideal_candidate_query = """
                SELECT
                    ic.position_id,
                    ic.to_be_embedded,
                    ic.embedding,
                    ic.ideal_candidate_info
                FROM
                    ideal_candidates_smarthr ic
                WHERE
                    ic.id = %s
                    AND ic.is_active = true
                    AND ic.embedding_generated = true
            """

            cur.execute(ideal_candidate_query, (ideal_candidate_id,))
            ideal_candidate_row = cur.fetchone()

            if not ideal_candidate_row:
                cur.close()
                conn.close()
                raise HTTPException(
                    status_code=404,
                    detail=f"Ideal candidate {ideal_candidate_id} not found or has no embedding"
                )

            position_id = ideal_candidate_row[0]
            ideal_candidate_text = ideal_candidate_row[1]
            ideal_candidate_embedding = ideal_candidate_row[2]
            ideal_candidate_info = ideal_candidate_row[3]

            logger.info(f"Found ideal candidate for position {position_id}")

            # 2. Build candidate query based on feedback requirements
            if hasFeedback == 0:
                # Without feedback
                candidate_query = """
                    SELECT
                        c.id,
                        c.proj_id,
                        c.candidate_info,
                        1 - (c.embedding <=> %s) AS cosine_similarity,
                        c.to_be_embebbed,
                        COUNT(i.id) AS num_feedbacks
                    FROM
                        candidates_smarthr c
                    LEFT JOIN
                        interviews i ON i.candidate_id = c.id AND i.position_id = %s
                    WHERE
                        c.is_deleted = false
                        AND c.is_active = true
                    GROUP BY
                        c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
                    HAVING
                        COUNT(i.id) = 0
                    ORDER BY
                        cosine_similarity DESC
                    LIMIT %s
                """
                cur.execute(candidate_query, (ideal_candidate_embedding, position_id, limit))
            elif hasFeedback == 1:
                # With feedback
                candidate_query = """
                    SELECT
                        c.id,
                        c.proj_id,
                        c.candidate_info,
                        1 - (c.embedding <=> %s) AS cosine_similarity,
                        c.to_be_embebbed,
                        COUNT(i.id) AS num_feedbacks
                    FROM
                        candidates_smarthr c
                    LEFT JOIN
                        interviews i ON i.candidate_id = c.id AND i.position_id = %s
                    WHERE
                        c.is_deleted = false
                        AND c.is_active = true
                    GROUP BY
                        c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
                    HAVING
                        COUNT(i.id) > 0
                    ORDER BY
                        cosine_similarity DESC
                    LIMIT %s
                """
                cur.execute(candidate_query, (ideal_candidate_embedding, position_id, limit))
            else:
                # Both (default)
                candidate_query = """
                    SELECT
                        c.id,
                        c.proj_id,
                        c.candidate_info,
                        1 - (c.embedding <=> %s) AS cosine_similarity,
                        c.to_be_embebbed,
                        COUNT(i.id) AS num_feedbacks
                    FROM
                        candidates_smarthr c
                    LEFT JOIN
                        interviews i ON i.candidate_id = c.id
                    WHERE
                        c.is_deleted = false
                        AND c.is_active = true
                    GROUP BY
                        c.id, c.proj_id, c.candidate_info, c.to_be_embebbed, c.embedding
                    ORDER BY
                        cosine_similarity DESC
                    LIMIT %s
                """
                cur.execute(candidate_query, (ideal_candidate_embedding, limit))

            results = cur.fetchall()
            cur.close()
            conn.close()

            logger.info(f"Found {len(results)} candidates for ideal candidate-based matching")

            # 3. Process results with LLM analysis
            candidates_result = []

            for r in results:
                candidate_id_db = r[0]
                proj_id_db = r[1]
                candidate_info_db = r[2]
                similarity_score = r[3]
                candidate_text = r[4]
                num_feedbacks = r[5]

                # Evaluate candidate using ideal candidate text
                analysis = match_functions.evaluate_candidate(
                    candidate_text, ideal_candidate_text
                )

                candidates_result.append({
                    "id": candidate_id_db,
                    "proj_id": proj_id_db,
                    "candidate_info": candidate_info_db,
                    "cosine_similarity": similarity_score,
                    "analysis": dict(analysis),
                    "num_feedbacks": num_feedbacks,
                })

            # 4. Prepare final result
            result = {
                "candidates": candidates_result,
                "ideal_candidate_id": ideal_candidate_id,
                "position_id": position_id,
                "ideal_candidate_text": ideal_candidate_text,
                "matching_method": "ideal_candidate_id_based",
                "timestamp": datetime.now(),
            }

            logger.info(f"Completed candidate matching for ideal candidate {ideal_candidate_id}")
            return result

        except Exception as e:
            logger.error(f"Error in candidate matching for ideal candidate {ideal_candidate_id}: {e}")
            raise


# Global instance
ideal_candidate_matching_service = IdealCandidateMatchingService()
