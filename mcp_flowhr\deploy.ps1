# PowerShell deployment script for FlowHR MCP Server

param(
    [Parameter(Mandatory=$true)]
    [string]$FunctionAppName,
    
    [Parameter(Mandatory=$false)]
    [string]$ResourceGroup = "rg-flowhr-mcp",
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory=$false)]
    [string]$StorageAccount = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateResources
)

Write-Host "FlowHR MCP Server Deployment Script" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Check if Azure CLI is installed
if (!(Get-Command az -ErrorAction SilentlyContinue)) {
    Write-Error "Azure CLI is not installed. Please install it first."
    exit 1
}

# Check if Functions Core Tools is installed
if (!(Get-Command func -ErrorAction SilentlyContinue)) {
    Write-Error "Azure Functions Core Tools is not installed. Please install it first."
    exit 1
}

# Login to Azure if not already logged in
$account = az account show --query "user.name" -o tsv 2>$null
if (!$account) {
    Write-Host "Logging in to Azure..." -ForegroundColor Yellow
    az login
}

if ($CreateResources) {
    Write-Host "Creating Azure resources..." -ForegroundColor Yellow
    
    # Create resource group
    Write-Host "Creating resource group: $ResourceGroup" -ForegroundColor Cyan
    az group create --name $ResourceGroup --location $Location
    
    # Generate storage account name if not provided
    if ([string]::IsNullOrEmpty($StorageAccount)) {
        $StorageAccount = "stflowhr" + (Get-Random -Minimum 1000 -Maximum 9999)
    }
    
    # Create storage account
    Write-Host "Creating storage account: $StorageAccount" -ForegroundColor Cyan
    az storage account create --name $StorageAccount --resource-group $ResourceGroup --location $Location --sku Standard_LRS
    
    # Create function app
    Write-Host "Creating function app: $FunctionAppName" -ForegroundColor Cyan
    az functionapp create --resource-group $ResourceGroup --consumption-plan-location $Location --runtime python --runtime-version 3.9 --functions-version 4 --name $FunctionAppName --storage-account $StorageAccount
    
    # Configure app settings for MCP
    Write-Host "Configuring app settings..." -ForegroundColor Cyan
    az functionapp config appsettings set --name $FunctionAppName --resource-group $ResourceGroup --settings "FUNCTIONS_EXTENSION_VERSION=~4" "WEBSITE_PYTHON_DEFAULT_VERSION=3.9"
}

# Deploy the function
Write-Host "Deploying function app..." -ForegroundColor Yellow
func azure functionapp publish $FunctionAppName --python

# Get the function app URL and system key
Write-Host "Getting deployment information..." -ForegroundColor Yellow
$functionAppUrl = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query "defaultHostName" -o tsv
$systemKey = az functionapp keys list --name $FunctionAppName --resource-group $ResourceGroup --query "systemKeys.default" -o tsv

Write-Host ""
Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "Function App URL: https://$functionAppUrl" -ForegroundColor Cyan
Write-Host "MCP SSE Endpoint: https://$functionAppUrl/runtime/webhooks/mcp/sse?code=$systemKey" -ForegroundColor Cyan
Write-Host ""
Write-Host "You can now connect to this MCP server using the SSE endpoint above." -ForegroundColor Yellow
Write-Host "Add this endpoint to your MCP client configuration (VS Code Copilot, MCP Inspector, etc.)" -ForegroundColor Yellow
