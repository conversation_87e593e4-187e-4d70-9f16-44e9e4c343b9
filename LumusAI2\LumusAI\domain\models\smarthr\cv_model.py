# cv_model.py

from datetime import date
from typing import List, Optional
from pydantic import BaseModel, Field

# Información personal
class PersonalInfo(BaseModel):
    full_name: Optional[str] = Field(None, description="Nombre completo de la persona")
    country: Optional[str] = Field(None, description="Pais de residencia")
    city: Optional[str] = Field(None, description="Ciudad de residencia")
    address: Optional[str] = Field(None, description="Dirección de residencia")
    phone_number: Optional[str] = Field(None, description="Número de teléfono")
    email: Optional[str] = Field(None, description="Correo electrónico. Si hay múltiples correos, sepáralos con punto y coma (;)")
    linkedin_profile: Optional[str] = Field(None, description="URL del perfil de LinkedIn")
    website: Optional[str] = Field(None, description="Sitio web o portafolio personal")

# Educación
class Education(BaseModel):
    institution_name: Optional[str] = Field(None, description="Nombre de la institución educativa")
    degree: Optional[str] = Field(None, description="Título obtenido o en curso")
    field_of_study: Optional[str] = Field(None, description="Campo de estudio o especialización")
    start_date: Optional[str] = Field(None, description="Fecha de inicio de los estudios")
    end_date: Optional[str] = Field(None, description="Fecha de finalización o fecha esperada de graduación")
    location: Optional[str] = Field(None, description="Ubicación de la institución (ciudad, país)")
    description: Optional[str] = Field(None, description="Descripción o logros durante el periodo de estudio")

# Habilidades
class Skill(BaseModel):
    name: Optional[str] = Field(None, description="Nombre de la habilidad")
    proficiency_level: Optional[str] = Field(None, description="Nivel de dominio (e.g., Básico, Intermedio, Avanzado)")
    years_of_experience: Optional[float] = Field(None, description="Años de experiencia con la habilidad")

# Experiencia laboral
class WorkExperience(BaseModel):
    job_title: Optional[str] = Field(None, description="Título del puesto o cargo")
    company_name: Optional[str] = Field(None, description="Nombre de la empresa u organización")
    start_date: Optional[str] = Field(None, description="Fecha de inicio del empleo")
    end_date: Optional[str] = Field(None, description="Fecha de finalización del empleo o 'Presente' si aún está empleado")
    location: Optional[str] = Field(None, description="Ubicación de la empresa (ciudad, país)")
    responsibilities: Optional[List[str]] = Field(None, description="Lista de responsabilidades y logros")
    skills: Optional[List[Skill]] = Field(None, description="Lista de habilidades")

# Certificaciones
class Certification(BaseModel):
    name: Optional[str] = Field(None, description="Nombre de la certificación")
    issuing_organization: Optional[str] = Field(None, description="Organización que emitió la certificación")
    issue_date: Optional[str] = Field(None, description="Fecha de emisión")
    expiration_date: Optional[str] = Field(None, description="Fecha de expiración, si aplica")
    credential_id: Optional[str] = Field(None, description="ID de la credencial")
    credential_url: Optional[str] = Field(None, description="URL para verificar la credencial")

# Idiomas
class Language(BaseModel):
    language: Optional[str] = Field(None, description="Nombre del idioma")
    proficiency_level: Optional[str] = Field(None, description="Nivel de dominio (e.g., Nativo, Fluido, Intermedio, C1, A1)")

# Proyectos
class Project(BaseModel):
    name: Optional[str] = Field(None, description="Nombre del proyecto")
    description: Optional[str] = Field(None, description="Descripción breve del proyecto")
    role: Optional[str] = Field(None, description="Rol desempeñado en el proyecto")
    technologies_used: Optional[List[str]] = Field(None, description="Lista de tecnologías o herramientas utilizadas")
    start_date: Optional[str] = Field(None, description="Fecha de inicio del proyecto")
    end_date: Optional[str] = Field(None, description="Fecha de finalización del proyecto")
    url: Optional[str] = Field(None, description="URL o enlace al proyecto")

# Referencias
class Reference(BaseModel):
    name: str = Field(None, description="Nombre de la persona de referencia")
    relationship: Optional[str] = Field(None, description="Relación con la persona")
    phone_number: Optional[str] = Field(None, description="Número de teléfono")
    email: Optional[str] = Field(None, description="Correo electrónico. Si hay múltiples correos, sepáralos con punto y coma (;)")

# Soft skills
class SoftSkill(BaseModel):
    name: Optional[str] = Field(None, description="Nombre de la habilidad blanda")
    description: Optional[str] = Field(None, description="Descripción o ejemplo de cómo se aplica la habilidad")

# Información de costos de extracción
class TokenUsage(BaseModel):
    prompt_tokens: Optional[int] = Field(None, description="Número de tokens del prompt")
    completion_tokens: Optional[int] = Field(None, description="Número de tokens de la respuesta")
    total_tokens: Optional[int] = Field(None, description="Total de tokens utilizados")

class ExtractionCost(BaseModel):
    tokens: Optional[TokenUsage] = Field(None, description="Información detallada del uso de tokens")
    cost_usd: Optional[float] = Field(None, description="Costo total en dólares estadounidenses")

# Clase principal del CV
class CV(BaseModel):
    personal_info: Optional[PersonalInfo] = Field(None, description="Información personal del individuo")
    summary: Optional[str] = Field(None, description="Resumen profesional u objetivo")
    education: Optional[List[Education]] = Field(None, description="Lista de antecedentes educativos")
    work_experience: Optional[List[WorkExperience]] = Field(None, description="Lista de experiencias laborales")
    roles: Optional[List] = Field(None, description="Listado de roles en la sección **Roles**")
    skills: Optional[List[Skill]] = Field(None, description="Lista de habilidades")
    soft_skills: Optional[List[SoftSkill]] = Field(None, description="Lista de habilidades blandas")
    certifications: Optional[List[Certification]] = Field(None, description="Lista de certificaciones")
    languages: Optional[List[Language]] = Field(None, description="Lista de idiomas conocidos")
    projects: Optional[List[Project]] = Field(None, description="Lista de proyectos realizados")
    references: Optional[List[Reference]] = Field(None, description="Lista de referencias")
    extraction_cost: Optional[ExtractionCost] = Field(None, description="Información sobre los costos de extracción del CV")