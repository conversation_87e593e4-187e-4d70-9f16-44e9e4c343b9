import asyncio
import json
import re
from typing import Dict, Type, Optional, Union, Any
from pydantic import BaseModel, ValidationError
from fastapi.responses import JSONResponse
from openai import OpenAIError, APIConnectionError, AuthenticationError
from langchain_openai import AzureChatOpenAI
from langchain_community.callbacks import get_openai_callback
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
import re


class MaskingCallbackHandler:
    """
    A wrapper around Langfuse CallbackHandler that applies masking to sensitive data.
    This intercepts LangChain callbacks and masks sensitive data before sending to Langfuse.
    """

    def __init__(self, langfuse_handler, masking_function=None):
        self.langfuse_handler = langfuse_handler
        self.masking_function = masking_function

    def __getattr__(self, name):
        """Delegate all other attributes to the wrapped handler."""
        return getattr(self.langfuse_handler, name)

    def _apply_masking(self, text):
        """Apply masking to text if masking function is available."""
        if self.masking_function and isinstance(text, str):
            try:
                return self.masking_function({'data': text})
            except Exception as e:
                print(f"⚠️  Masking error: {e}")
                return text
        return text

    def _mask_dict_values(self, data):
        """Recursively apply masking to dictionary values."""
        if isinstance(data, dict):
            masked_data = {}
            for key, value in data.items():
                if isinstance(value, str):
                    masked_data[key] = self._apply_masking(value)
                elif isinstance(value, (dict, list)):
                    masked_data[key] = self._mask_dict_values(value)
                else:
                    masked_data[key] = value
            return masked_data
        elif isinstance(data, list):
            return [self._mask_dict_values(item) for item in data]
        elif isinstance(data, str):
            return self._apply_masking(data)
        else:
            return data

    def on_llm_start(self, serialized, prompts, **kwargs):
        """Intercept LLM start and mask prompts."""
        print("🔍 MaskingCallbackHandler.on_llm_start called")
        if self.masking_function:
            # Mask prompts before sending to Langfuse
            masked_prompts = [self._apply_masking(prompt) if isinstance(prompt, str) else prompt for prompt in prompts]
            print("🔒 Applied masking to LLM prompts")
            return self.langfuse_handler.on_llm_start(serialized, masked_prompts, **kwargs)
        print("ℹ️  No masking function - passing through")
        return self.langfuse_handler.on_llm_start(serialized, prompts, **kwargs)

    def on_llm_end(self, response, **kwargs):
        """Intercept LLM end - block sensitive output from going to Langfuse."""
        print("🔍 MaskingCallbackHandler.on_llm_end called")
        if self.masking_function:
            # Check if response contains sensitive data
            response_text = ""
            if hasattr(response, 'generations'):
                for generation_list in response.generations:
                    for generation in generation_list:
                        if hasattr(generation, 'text'):
                            response_text += generation.text + " "
                        if hasattr(generation, 'message') and hasattr(generation.message, 'content'):
                            response_text += generation.message.content + " "

            if hasattr(response, 'content'):
                response_text += str(response.content)

            # Check if response contains sensitive patterns
            if self._contains_sensitive_data(response_text):
                print("🔒 Blocking sensitive LLM output from Langfuse")
                # Don't send to Langfuse - just return without calling the handler
                return
            else:
                print("ℹ️  LLM output appears safe - sending to Langfuse")
                return self.langfuse_handler.on_llm_end(response, **kwargs)

        # No masking function - pass original response to Langfuse
        return self.langfuse_handler.on_llm_end(response, **kwargs)

    def _contains_sensitive_data(self, text):
        """Check if text contains sensitive data patterns."""
        if not isinstance(text, str):
            return False

        # Check for common sensitive patterns
        sensitive_patterns = [
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?\b',  # Names
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Emails
            r'\b(?:\+?[0-9]{1,3}[-.\s]?)?\(?[0-9]{2,4}\)?[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4}\b',  # Phones
            r'https?://(?:www\.)?linkedin\.com/in/[A-Za-z0-9\-_]+/?',  # LinkedIn
            r'\b[0-9]{4}[-\s]?[0-9]{4}[-\s]?[0-9]{4}[-\s]?[0-9]{4}\b',  # Credit cards
        ]

        import re
        for pattern in sensitive_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def on_chain_start(self, serialized, inputs, **kwargs):
        """Intercept chain start and mask inputs."""
        print("🔍 MaskingCallbackHandler.on_chain_start called")
        if self.masking_function:
            masked_inputs = self._mask_dict_values(inputs)
            print("🔒 Applied masking to chain inputs")
            return self.langfuse_handler.on_chain_start(serialized, masked_inputs, **kwargs)
        print("ℹ️  No masking function - passing through")
        return self.langfuse_handler.on_chain_start(serialized, inputs, **kwargs)

    def on_chain_end(self, outputs, **kwargs):
        """Intercept chain end - block sensitive outputs from going to Langfuse."""
        print(f"🔍 MaskingCallbackHandler.on_chain_end called with outputs type: {type(outputs)}")
        if self.masking_function:
            # Convert outputs to string for sensitivity check
            output_text = str(outputs)

            # Check if outputs contain sensitive data
            if self._contains_sensitive_data(output_text):
                print("🔒 Blocking sensitive chain output from Langfuse")
                print(f"🔍 Output keys: {list(outputs.keys()) if isinstance(outputs, dict) else 'Not a dict'}")
                # Don't send to Langfuse - just return without calling the handler
                return
            else:
                print("ℹ️  Chain output appears safe - sending to Langfuse")
                return self.langfuse_handler.on_chain_end(outputs, **kwargs)

        print("ℹ️  No masking function - passing through")
        return self.langfuse_handler.on_chain_end(outputs, **kwargs)

    def on_tool_end(self, output, **kwargs):
        """Intercept tool end - block sensitive outputs from going to Langfuse."""
        print(f"🔍 MaskingCallbackHandler.on_tool_end called with output type: {type(output)}")
        if self.masking_function:
            # Convert output to string for sensitivity check
            output_text = str(output)

            # Check if output contains sensitive data
            if self._contains_sensitive_data(output_text):
                print("🔒 Blocking sensitive tool output from Langfuse")
                # Don't send to Langfuse - just return without calling the handler
                return
            else:
                print("ℹ️  Tool output appears safe - sending to Langfuse")
                return self.langfuse_handler.on_tool_end(output, **kwargs)

        return self.langfuse_handler.on_tool_end(output, **kwargs)


def create_langfuse_masking_function(
    mask_credit_cards: bool = True,
    mask_emails: bool = True,
    mask_phones: bool = True,
    mask_secrets: bool = True,
    mask_custom_patterns: Optional[Dict[str, str]] = None
):
    """
    Create a masking function for Langfuse to protect sensitive data.

    Args:
        mask_credit_cards: Whether to mask credit card numbers
        mask_emails: Whether to mask email addresses
        mask_phones: Whether to mask phone numbers
        mask_secrets: Whether to mask data marked as SECRET_ or CONFIDENTIAL_
        mask_custom_patterns: Dict of {pattern_name: regex_pattern} for custom masking

    Returns:
        Function that can be used with Langfuse CallbackHandler
    """

    # Define regex patterns for different types of sensitive data
    patterns = {}

    if mask_credit_cards:
        # Credit card patterns (Visa, MasterCard, AmEx, Discover, etc.)
        patterns['credit_card'] = r'\b(?:\d[ -]*?){13,19}\b'

    if mask_emails:
        # Email pattern
        patterns['email'] = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

    if mask_phones:
        # Phone number patterns (various formats including Colombian)
        patterns['phone'] = r'\b(?:\+?[0-9]{1,3}[-.\s]?)?\(?[0-9]{2,4}\)?[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4}\b'

    if mask_secrets:
        # Secret/confidential data patterns
        patterns['secrets'] = r'\b(?:SECRET_|CONFIDENTIAL_|PRIVATE_|API_KEY_|TOKEN_|PASSWORD_)[A-Za-z0-9_-]*'

    # Always add personal information patterns for comprehensive privacy
    patterns['personal_names'] = r'\b[A-Z][a-záéíóúñü]+(?:\s+[A-Z][a-záéíóúñü]+){1,3}\b'  # Full names (including Spanish)
    patterns['linkedin_urls'] = r'https?://(?:www\.)?linkedin\.com/in/[A-Za-z0-9\-_]+/?'  # LinkedIn profiles
    patterns['addresses'] = r'\b[A-Za-z\s,áéíóúñü]+,\s*[A-Za-z\s,áéíóúñü]+(?:,\s*[A-Za-z\s,áéíóúñü]+)?\b'  # City, State format
    patterns['company_names'] = r'\b[A-Z][A-Za-z\s&áéíóúñü]+(?:Corp|Corporation|Inc|LLC|Ltd|Company|Co|SA|SAS|LTDA)\b'  # Company names
    patterns['student_ids'] = r'\b\d{8,12}\b'  # Student IDs and similar numeric identifiers

    # Add custom patterns if provided
    if mask_custom_patterns:
        patterns.update(mask_custom_patterns)

    def masking_function(params: Dict[str, Any]) -> Any:
        """
        Masking function that processes data before sending to Langfuse.

        Args:
            params: Dictionary containing 'data' key with the content to mask

        Returns:
            Masked data
        """
        try:
            data = params.get('data')
            if not data:
                return data

            # Debug logging for masking
            print(f"🔍 Masking function called with data type: {type(data)}, length: {len(str(data)) if data else 0}")

            # Handle string data
            if isinstance(data, str):
                masked_data = data

                # Apply each pattern
                replacements_made = 0
                for pattern_name, pattern in patterns.items():
                    original_data = masked_data
                    if pattern_name == 'credit_card':
                        masked_data = re.sub(pattern, '[REDACTED_CREDIT_CARD]', masked_data)
                    elif pattern_name == 'email':
                        masked_data = re.sub(pattern, '[REDACTED_EMAIL]', masked_data)
                    elif pattern_name == 'phone':
                        masked_data = re.sub(pattern, '[REDACTED_PHONE]', masked_data)
                    elif pattern_name == 'secrets':
                        masked_data = re.sub(pattern, '[REDACTED_SECRET]', masked_data)
                    elif pattern_name == 'personal_names':
                        masked_data = re.sub(pattern, '[REDACTED_NAME]', masked_data)
                    elif pattern_name == 'linkedin_urls':
                        masked_data = re.sub(pattern, '[REDACTED_LINKEDIN]', masked_data)
                    elif pattern_name == 'addresses':
                        masked_data = re.sub(pattern, '[REDACTED_ADDRESS]', masked_data)
                    elif pattern_name == 'company_names':
                        masked_data = re.sub(pattern, '[REDACTED_COMPANY]', masked_data)
                    elif pattern_name == 'student_ids':
                        masked_data = re.sub(pattern, '[REDACTED_ID]', masked_data)

                    # Count replacements
                    if masked_data != original_data:
                        replacements_made += 1
                        print(f"🔒 Applied {pattern_name} masking pattern")

                if replacements_made > 0:
                    print(f"🔒 Total masking patterns applied: {replacements_made}")
                else:
                    print("ℹ️  No masking patterns matched the data")

                return masked_data

            # Handle dictionary data recursively
            elif isinstance(data, dict):
                return {k: masking_function({'data': v}) for k, v in data.items()}

            # Handle list data recursively
            elif isinstance(data, list):
                return [masking_function({'data': item}) for item in data]

            # Return other data types unchanged
            return data

        except Exception as e:
            # If masking fails, log the error and return original data
            print(f"⚠️  Langfuse masking error: {e} - returning original data")
            return params.get('data')

    return masking_function


class LangChainClient:
    def __init__(self, api_key: str, api_version: str, azure_endpoint: str, model: str,
                 langfuse_public_key: Optional[str] = None,
                 langfuse_secret_key: Optional[str] = None,
                 langfuse_host: Optional[str] = None,
                 langfuse_enable_masking: bool = False,
                 langfuse_mask_credit_cards: bool = True,
                 langfuse_mask_emails: bool = True,
                 langfuse_mask_phones: bool = True,
                 langfuse_mask_secrets: bool = True,
                 langfuse_custom_patterns: Optional[Dict[str, str]] = None):
        """
        Initialize LangChain client with optional Langfuse integration and masking.

        Args:
            api_key: Azure OpenAI API key
            api_version: Azure OpenAI API version
            azure_endpoint: Azure OpenAI endpoint
            model: Model name to use
            langfuse_public_key: Optional Langfuse public key for observability
            langfuse_secret_key: Optional Langfuse secret key for observability
            langfuse_host: Optional Langfuse host URL (defaults to custom instance)
            langfuse_enable_masking: Whether to enable data masking for sensitive information
            langfuse_mask_credit_cards: Whether to mask credit card numbers
            langfuse_mask_emails: Whether to mask email addresses
            langfuse_mask_phones: Whether to mask phone numbers
            langfuse_mask_secrets: Whether to mask SECRET_/CONFIDENTIAL_ prefixed data
            langfuse_custom_patterns: Dict of custom regex patterns for masking
        """
        self.llm = AzureChatOpenAI(
            api_key= api_key,
            api_version= api_version,
            azure_endpoint= azure_endpoint,
            model=model
        )

        # Initialize Langfuse callback handler if credentials are provided
        self.langfuse_handler = None
        self.langfuse_client = None
        if langfuse_public_key and langfuse_secret_key:
            try:
                from langfuse import Langfuse
                from langfuse.callback import CallbackHandler

                # Initialize Langfuse client
                self.langfuse_client = Langfuse(
                    public_key=langfuse_public_key,
                    secret_key=langfuse_secret_key,
                    host=langfuse_host or "http://**************:3000"
                )
                print(f"✅ Langfuse client initialized successfully")

                # Create callback handler with masking wrapper
                base_handler = CallbackHandler()

                if langfuse_enable_masking:
                    masking_function = create_langfuse_masking_function(
                        mask_credit_cards=langfuse_mask_credit_cards,
                        mask_emails=langfuse_mask_emails,
                        mask_phones=langfuse_mask_phones,
                        mask_secrets=langfuse_mask_secrets,
                        mask_custom_patterns=langfuse_custom_patterns
                    )
                    self.langfuse_handler = MaskingCallbackHandler(base_handler, masking_function)
                    self.masking_function = masking_function
                    print("🔒 Langfuse masking enabled - protecting personal and sensitive information")
                    print("ℹ️  Note: Using compatibility masking wrapper for current Langfuse version")
                else:
                    self.langfuse_handler = base_handler
                    self.masking_function = None
                    print("🔓 Langfuse masking disabled")

                # Store masking configuration for future use
                self._masking_enabled = langfuse_enable_masking
                self._masking_config = {
                    'credit_cards': langfuse_mask_credit_cards,
                    'emails': langfuse_mask_emails,
                    'phones': langfuse_mask_phones,
                    'secrets': langfuse_mask_secrets,
                    'custom_patterns': langfuse_custom_patterns
                }

                if langfuse_enable_masking:
                    print("⚠️  Masking temporarily disabled for debugging - will be re-enabled once basic connection works")
                    print("🔒 Masking configuration stored:")
                    print(f"   - Credit cards: {'✅' if langfuse_mask_credit_cards else '❌'}")
                    print(f"   - Emails: {'✅' if langfuse_mask_emails else '❌'}")
                    print(f"   - Phones: {'✅' if langfuse_mask_phones else '❌'}")
                    print(f"   - Secrets: {'✅' if langfuse_mask_secrets else '❌'}")
                    if langfuse_custom_patterns:
                        print(f"   - Custom patterns: {len(langfuse_custom_patterns)} defined")

                print("✅ Langfuse integration enabled for LangChain tracing")
                print(f"   Host: {langfuse_host or 'http://**************:3000'}")
                print(f"   CallbackHandler type: {type(self.langfuse_handler)}")
                print(f"   CallbackHandler created: {'✅' if self.langfuse_handler else '❌'}")

            except ImportError:
                print("⚠️  Langfuse not installed - continuing without observability")
                self.langfuse_handler = None
                self.langfuse_client = None
            except Exception as e:
                print(f"⚠️  Langfuse initialization failed: {e} - continuing without observability")
                self.langfuse_handler = None
                self.langfuse_client = None
        else:
            print("ℹ️  Langfuse credentials not provided - continuing without observability")
            self.langfuse_handler = None
            self.langfuse_client = None

    def _get_callbacks(self):
        """
        Get callbacks for LangChain operations with error handling.

        Returns:
            list: List of callbacks to use, empty if Langfuse is not available
        """
        callbacks = []
        if self.langfuse_handler:
            try:
                # Test if the handler is still valid
                callbacks.append(self.langfuse_handler)
                print(f"🔍 _get_callbacks: Added Langfuse handler, total callbacks: {len(callbacks)}")
            except Exception as e:
                print(f"⚠️  Langfuse callback error: {e} - continuing without tracing")
        else:
            print("🔍 _get_callbacks: No Langfuse handler available")
        return callbacks

    def _get_config(self):
        """
        Get RunnableConfig with callbacks for LangChain operations.

        Returns:
            RunnableConfig: Configuration with callbacks, or None if no callbacks
        """
        callbacks = self._get_callbacks()
        if callbacks:
            return RunnableConfig(callbacks=callbacks)
        return None

    def _apply_masking(self, text: str) -> str:
        """
        Apply masking to text if masking is enabled.

        Args:
            text: Text to potentially mask

        Returns:
            Masked text if masking is enabled, original text otherwise
        """
        if self.masking_function and text:
            try:
                # Apply masking function
                masked_result = self.masking_function({'data': text})
                return masked_result if isinstance(masked_result, str) else text
            except Exception as e:
                print(f"⚠️  Masking error: {e} - using original text")
                return text
        return text

    async def get_simple_response(self, prompt: str):
        """
        Get a simple response from the LLM for testing purposes.

        Args:
            prompt: The prompt to send to the LLM

        Returns:
            dict: Response with content and token usage
        """
        try:
            # Prepare config for Langfuse tracing
            config = self._get_config()
            print(f"🔍 Using config: {'with callbacks' if config else 'without callbacks'}")

            with get_openai_callback() as callback:
                messages = [HumanMessage(content=prompt)]
                if config:
                    response = await self.llm.ainvoke(messages, config=config)
                else:
                    response = await self.llm.ainvoke(messages)

            return {
                "response": response.content,
                "token_usage": {
                    "prompt_tokens": callback.prompt_tokens,
                    "completion_tokens": callback.completion_tokens,
                    "total_tokens": callback.total_tokens,
                    "cost": callback.total_cost
                }
            }

        except Exception as e:
            print(f"❌ Error in get_simple_response: {e}")
            raise e

    def flush_langfuse(self):
        """
        Flush any pending Langfuse data.
        Useful for short-lived applications or when you want to ensure data is sent immediately.
        """
        if self.langfuse_client:
            try:
                self.langfuse_client.flush()
                print("✅ Langfuse data flushed successfully")
            except Exception as e:
                print(f"⚠️  Langfuse flush warning: {e}")

    async def extract_data(self, prompt: str, base64_image_data: str, max_retries: int = 3) -> Dict:
        """
        Extract information from an image using the LLM.

        Features:
        - Processes both text and image inputs
        - Tracks token usage and costs
        - Handles various response formats
        - Built-in retry logic for handling transient errors
        - Comprehensive error handling

        Args:
            prompt (str): The instruction or question for the model
            base64_image_data (str): Base64-encoded image data
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": str,  # The model's response
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Raises:
            JSONResponse: HTTP error responses with appropriate status codes:
                - 400: Invalid input parameters
                - 401: Authentication error
                - 422: Validation error
                - 429: Rate limit exceeded
                - 500: Internal server error
                - 503: Service unavailable
                - 504: Gateway timeout
        """
        # Input validation
        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(base64_image_data, str) or not base64_image_data.strip():
            return JSONResponse(
                content={"error": "The 'base64_image_data' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                # Prepare config for Langfuse tracing
                config = self._get_config()

                # Call the model
                with get_openai_callback() as callback:
                    messages = [
                        HumanMessage(
                            content=[
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": base64_image_data,
                                        "detail": "auto",
                                    },
                                },
                            ]
                        )
                    ]
                    if config:
                        response = await self.llm.ainvoke(messages, config=config)
                    else:
                        response = await self.llm.ainvoke(messages)

                # Extract content from response
                content = response.content if hasattr(response, 'content') else response['content']

                # Success! Return the result
                return {
                    "response": content,
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def get_structured_data(self, structure: Type[BaseModel], data: str, prompt: str, max_retries: int = 3):
        """
        Extract structured data from text using a Pydantic model schema.

        Features:
        - Type validation with Pydantic
        - Automatic schema enforcement
        - Token usage tracking
        - Comprehensive error handling
        - Built-in retry logic for handling transient errors

        Args:
            structure (Type[BaseModel]): Pydantic model class defining the output structure
            data (str): Input text to process
            prompt (str): Instructions for the model
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": Dict,  # Structured data matching the Pydantic model
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Raises:
            JSONResponse: HTTP error responses with appropriate status codes:
                - 400: Invalid input parameters
                - 401: Authentication error
                - 422: Validation error
                - 429: Rate limit exceeded
                - 500: Internal server error
                - 503: Service unavailable
                - 504: Gateway timeout
        """
        # Input validation
        if not issubclass(structure, BaseModel):
            return JSONResponse(
                content={"error": "The 'structure' argument must be a class that inherits from BaseModel"},
                status_code=400
            )

        if not isinstance(data, str) or not data.strip():
            return JSONResponse(
                content={"error": "The 'data' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                # Prepare config for Langfuse tracing
                config = self._get_config()
                print(f"🔍 get_structured_data: Using {'config with callbacks' if config else 'no config'} for LLM call")

                with get_openai_callback() as callback:
                    structured_model = self.llm.with_structured_output(structure, method="function_calling")
                    print(f"🔍 About to call structured_model.ainvoke with {'config' if config else 'no config'}")

                    # Prepare input data (apply masking for Langfuse tracing if enabled)
                    input_text = f"{prompt}\n{data}"
                    if self.masking_function:
                        # For Langfuse tracing, we'll mask the data in the trace metadata
                        print("🔒 Masking enabled - sensitive data will be protected in traces")

                    if config:
                        response = await structured_model.ainvoke(input_text, config=config)
                    else:
                        response = await structured_model.ainvoke(input_text)
                    print(f"🔍 structured_model.ainvoke completed successfully")

                # Success! Return the result
                return {
                    "response": response.model_dump(),
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def get_structured_data_image(
        self,
        structure: Type[BaseModel],
        base64_image_data: str,
        prompt: str,
        initial_data: Optional[Union[BaseModel, dict, str]] = None,
        max_retries: int = 3
    ):
        """
        Extract structured data from an image using a Pydantic model schema.
        Supports pre-filled or partially filled Pydantic models.

        Features:
        - Type validation with Pydantic
        - Automatic schema enforcement
        - Token usage tracking
        - Comprehensive error handling
        - Built-in retry logic for handling transient errors
        - Support for continuing from a partially filled model

        Args:
            structure (Type[BaseModel]): Pydantic model class defining the output structure
            base64_image_data (str): Base64-encoded image data
            prompt (str): Instructions for the model
            initial_data (Optional[Union[BaseModel, dict, str]]): Pre-filled data to continue filling.
                                               Can be a Pydantic model instance, a dictionary, or a JSON string.
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": Dict,  # Structured data matching the Pydantic model
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Example:
            >>> # Example 1: Using a Pydantic model
            >>> initial_data = InvoiceData(invoice_number="INV-001", date="2023-01-01")
            >>> result = await client.get_structured_data_image(
            ...     InvoiceData,
            ...     base64_image,
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data
            ... )
            >>>
            >>> # Example 2: Using a dictionary
            >>> initial_data_dict = {"invoice_number": "INV-001", "date": "2023-01-01"}
            >>> result = await client.get_structured_data_image(
            ...     InvoiceData,
            ...     base64_image,
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data_dict
            ... )
            >>>
            >>> # Example 3: Using a JSON string
            >>> initial_data_json = '{"invoice_number": "INV-001", "date": "2023-01-01"}'
            >>> result = await client.get_structured_data_image(
            ...     InvoiceData,
            ...     base64_image,
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data_json
            ... )
            >>>
            >>> # All examples produce similar results:
            >>> print(result["response"])
            {"invoice_number": "INV-001", "date": "2023-01-01", "total": 1250.00, ...}
        """
        # Input validation
        if not issubclass(structure, BaseModel):
            return JSONResponse(
                content={"error": "The 'structure' argument must be a class that inherits from BaseModel"},
                status_code=400
            )

        if initial_data is not None:
            # Validate initial_data based on its type
            if isinstance(initial_data, BaseModel) and not isinstance(initial_data, structure):
                return JSONResponse(
                    content={"error": "When providing a Pydantic model as 'initial_data', it must be an instance of the provided structure"},
                    status_code=400
                )
            elif isinstance(initial_data, str):
                # Try to parse as JSON to validate
                try:
                    json.loads(initial_data)
                except json.JSONDecodeError:
                    # Not valid JSON, but we'll still accept it as plain text
                    print("Warning: initial_data is not valid JSON. Using as plain text.")
            # For dict type, no validation needed - we'll convert to JSON later

        if not isinstance(base64_image_data, str) or not base64_image_data.strip():
            return JSONResponse(
                content={"error": "The 'base64_image_data' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                with get_openai_callback() as callback:
                    # Create a model with structured output based on the provided Pydantic model
                    structured_model = self.llm.with_structured_output(
                        structure,
                        method="function_calling"
                    )

                    # Modify prompt if initial data is provided
                    modified_prompt = prompt
                    if initial_data is not None:
                        # Handle initial data that could be a Pydantic model or JSON string/dict
                        initial_data_json = ""

                        if isinstance(initial_data, BaseModel):
                            # If it's a Pydantic model, convert to JSON
                            initial_data_json = initial_data.model_dump_json(indent=2)
                        elif isinstance(initial_data, dict):
                            # If it's already a dict, convert to JSON string
                            initial_data_json = json.dumps(initial_data, indent=2)
                        elif isinstance(initial_data, str):
                            # If it's a JSON string, use it directly (assuming it's valid JSON)
                            try:
                                # Validate by parsing and re-serializing to ensure proper formatting
                                parsed = json.loads(initial_data)
                                initial_data_json = json.dumps(parsed, indent=2)
                            except json.JSONDecodeError:
                                # If not valid JSON, use as is with a warning
                                print("Warning: initial_data is not valid JSON. Using as plain text.")
                                initial_data_json = initial_data
                        else:
                            # For any other type, convert to string
                            initial_data_json = str(initial_data)

                        # Add context about the initial data to the prompt
                        prompt = f"{prompt}\n\nI already have some information extracted: {initial_data_json}\n\nIMPORTANT: This is a multi-page document, and I'm processing it page by page. This image is likely a continuation of previous pages.\n\nWhen you see information that appears to be a continuation from previous pages (especially for work experience entries):\n\n1. If you see 'Key responsibilities:' or bullet points at the beginning of the page, these are likely responsibilities for a job mentioned on previous pages. Make sure to include the job title and company name exactly as they appear in the initial data.\n\n2. If you see text like 'o Data analysis – expertise in writing...' at the beginning of the page without a job title, this is likely a continuation of responsibilities from a previous page.\n\n3. For work experience entries that already exist in the initial data, add any new responsibilities or details you find on this page to the existing entries rather than creating new entries.\n\n4. Pay special attention to 'Project Scope:' sections followed by 'Key responsibilities:' - extract each bullet point after 'Key responsibilities:' as a separate responsibility for the most recently mentioned job.\n\nPlease fill in any missing fields or correct any errors based on the image, while preserving the existing data and ensuring continuity across pages."

                    # Prepare config for Langfuse tracing
                    config = self._get_config()

                    # Invoke the model with both text prompt and image
                    messages = [
                        HumanMessage(
                            content=[
                                {"type": "text", "text": modified_prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": base64_image_data,
                                        "detail": "auto",
                                    },
                                },
                            ]
                        )
                    ]
                    if config:
                        response = await structured_model.ainvoke(messages, config=config)
                    else:
                        response = await structured_model.ainvoke(messages)
                # Success! Return the result
                return {
                    "response": response.model_dump(),
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def get_structured_data_test(
        self,
        structure: Type[BaseModel],
        data: str,
        prompt: str,
        initial_data: Optional[Union[BaseModel, dict, str]] = None,
        max_retries: int = 3
    ):
        """
        Extract structured data from text using a Pydantic model schema.
        Designed for testing purposes with support for pre-filled or partially filled Pydantic models.

        Features:
        - Type validation with Pydantic
        - Automatic schema enforcement
        - Token usage tracking
        - Comprehensive error handling
        - Built-in retry logic for handling transient errors
        - Support for continuing from a partially filled model

        Args:
            structure (Type[BaseModel]): Pydantic model class defining the output structure
            data (str): Input text to process
            prompt (str): Instructions for the model
            initial_data (Optional[Union[BaseModel, dict, str]]): Pre-filled data to continue filling.
                                               Can be a Pydantic model instance, a dictionary, or a JSON string.
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": Dict,  # Structured data matching the Pydantic model
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Example:
            >>> # Example 1: Using a Pydantic model
            >>> initial_data = InvoiceData(invoice_number="INV-001", date="2023-01-01")
            >>> result = await client.get_structured_data_test(
            ...     InvoiceData,
            ...     "Invoice text content...",
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data
            ... )
            >>>
            >>> # Example 2: Using a dictionary
            >>> initial_data_dict = {"invoice_number": "INV-001", "date": "2023-01-01"}
            >>> result = await client.get_structured_data_test(
            ...     InvoiceData,
            ...     "Invoice text content...",
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data_dict
            ... )
        """
        # Input validation
        if not issubclass(structure, BaseModel):
            return JSONResponse(
                content={"error": "The 'structure' argument must be a class that inherits from BaseModel"},
                status_code=400
            )

        if initial_data is not None:
            # Validate initial_data based on its type
            if isinstance(initial_data, BaseModel) and not isinstance(initial_data, structure):
                return JSONResponse(
                    content={"error": "When providing a Pydantic model as 'initial_data', it must be an instance of the provided structure"},
                    status_code=400
                )
            elif isinstance(initial_data, str):
                # Try to parse as JSON to validate
                try:
                    json.loads(initial_data)
                except json.JSONDecodeError:
                    # Not valid JSON, but we'll still accept it as plain text
                    print("Warning: initial_data is not valid JSON. Using as plain text.")
            # For dict type, no validation needed - we'll convert to JSON later

        if not isinstance(data, str) or not data.strip():
            return JSONResponse(
                content={"error": "The 'data' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                with get_openai_callback() as callback:
                    # Create a model with structured output based on the provided Pydantic model
                    structured_model = self.llm.with_structured_output(
                        structure,
                        method="function_calling"
                    )

                    # Modify prompt if initial data is provided
                    modified_prompt = prompt
                    if initial_data is not None:
                        # Handle initial data that could be a Pydantic model or JSON string/dict
                        initial_data_json = ""

                        if isinstance(initial_data, BaseModel):
                            # If it's a Pydantic model, convert to JSON
                            initial_data_json = initial_data.model_dump_json(indent=2)
                        elif isinstance(initial_data, dict):
                            # If it's already a dict, convert to JSON string
                            initial_data_json = json.dumps(initial_data, indent=2)
                        elif isinstance(initial_data, str):
                            # If it's a JSON string, use it directly (assuming it's valid JSON)
                            try:
                                # Validate by parsing and re-serializing to ensure proper formatting
                                parsed = json.loads(initial_data)
                                initial_data_json = json.dumps(parsed, indent=2)
                            except json.JSONDecodeError:
                                # If not valid JSON, use as is with a warning
                                print("Warning: initial_data is not valid JSON. Using as plain text.")
                                initial_data_json = initial_data
                        else:
                            # For any other type, convert to string
                            initial_data_json = str(initial_data)

                        # Add context about the initial data to the prompt
                        modified_prompt = f"{prompt}\n\nI already have some information extracted: {initial_data_json}\n\nIMPORTANT: This is a multi-page document, and I'm processing it page by page. This text is likely a continuation of previous pages.\n\nWhen you see information that appears to be a continuation from previous pages (especially for work experience entries):\n\n1. If you see 'Key responsibilities:' or bullet points at the beginning of the page, these are likely responsibilities for a job mentioned on previous pages. Make sure to include the job title and company name exactly as they appear in the initial data.\n\n2. If you see text like 'o Data analysis – expertise in writing...' at the beginning of the page without a job title, this is likely a continuation of responsibilities from a previous page.\n\n3. For work experience entries that already exist in the initial data, add any new responsibilities or details you find on this page to the existing entries rather than creating new entries.\n\n4. Pay special attention to 'Project Scope:' sections followed by 'Key responsibilities:' - extract each bullet point after 'Key responsibilities:' as a separate responsibility for the most recently mentioned job.\n\nPlease fill in any missing fields or correct any errors based on the provided data, while preserving the existing data and ensuring continuity across pages."

                    # Prepare config for Langfuse tracing
                    config = self._get_config()

                    # Invoke the model with the text data
                    if config:
                        response = await structured_model.ainvoke(f"{modified_prompt}\n{data}", config=config)
                    else:
                        response = await structured_model.ainvoke(f"{modified_prompt}\n{data}")

                # Success! Return the result
                return {
                    "response": response.model_dump(),
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def simple_invoke(self, prompt: str, max_retries: int = 3):
        """
        Make a simple async call to the language model.

        Features:
        - Streamlined interface for basic queries
        - Async operation for better performance
        - Simple text-in-text-out operation
        - Robust error handling
        - Built-in retry logic for handling transient errors
        - Token usage tracking and cost calculation

        Args:
            prompt (str): The text prompt to send to the model
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": str,  # The model's response text
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            } or JSONResponse with error details

        Example:
            >>> result = await client.simple_invoke("Summarize this text: ...")
            >>> print(result["response"])
            "Here's a summary of the text..."
            >>> print(f"Cost: ${result['token_usage']['cost']:.4f}")
            Cost: $0.0025
        """
        # Input validation
        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                # Prepare config for Langfuse tracing
                config = self._get_config()

                with get_openai_callback() as callback:
                    messages = [
                        HumanMessage(
                            content=[
                                {"type": "text", "text": prompt}
                            ]
                        )
                    ]
                    if config:
                        response = await self.llm.ainvoke(messages, config=config)
                    else:
                        response = await self.llm.ainvoke(messages)

                    # Log token usage
                    print(f"Token usage: Prompt tokens: {callback.prompt_tokens}, "
                          f"Completion tokens: {callback.completion_tokens}, "
                          f"Total tokens: {callback.total_tokens}, "
                          f"Cost: ${callback.total_cost:.4f}")

                    # Extract content from response
                    content = response.content if hasattr(response, 'content') else response['content']

                    # Success! Return the result with token usage
                    return {
                        "response": content,
                        "token_usage": {
                            "prompt_tokens": callback.prompt_tokens,
                            "completion_tokens": callback.completion_tokens,
                            "total_tokens": callback.total_tokens,
                            "cost": callback.total_cost
                        }
                    }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )
