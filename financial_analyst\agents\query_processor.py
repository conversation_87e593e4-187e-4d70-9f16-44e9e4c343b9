"""
Query Processor Agent for analyzing and categorizing user queries.
This agent determines the intent and type of user queries to route them appropriately.
"""

import logging
import re
from typing import Dict, Any, List
from datetime import datetime

logger = logging.getLogger(__name__)


class ProcesadorConsultas:
    """
    Query Processor Agent that analyzes user queries and determines processing strategy.
    """
    
    def __init__(self):
        """Initialize the query processor."""
        self.query_patterns = {
            'sql_keywords': [
                'select', 'from', 'where', 'join', 'group by', 'order by',
                'insert', 'update', 'delete', 'create', 'alter', 'drop',
                'table', 'database', 'column', 'row', 'index'
            ],
            'analysis_keywords': [
                'analyze', 'analysis', 'trend', 'pattern', 'correlation',
                'statistics', 'average', 'mean', 'median', 'sum', 'count',
                'chart', 'graph', 'plot', 'visualization', 'dashboard'
            ],
            'schema_keywords': [
                'schema', 'structure', 'tables', 'columns', 'relationships',
                'foreign key', 'primary key', 'constraints', 'indexes'
            ],
            'financial_keywords': [
                'revenue', 'profit', 'sales', 'financial', 'budget',
                'cost', 'expense', 'income', 'roi', 'margin'
            ]
        }
        
        logger.info("Query processor initialized successfully")
    
    def procesar_consulta(self, consulta: str) -> Dict[str, Any]:
        """
        Process and analyze a user query to determine its intent and routing.
        
        Args:
            consulta (str): The user query to process
            
        Returns:
            Dict[str, Any]: Analysis results with query classification and routing info
        """
        try:
            logger.info(f"Processing query: {consulta[:100]}...")
            
            # Clean and normalize the query
            consulta_clean = consulta.lower().strip()
            
            # Analyze query intent
            intent_analysis = self._analyze_intent(consulta_clean)
            
            # Determine required agents
            required_agents = self._determine_agents(intent_analysis)
            
            # Extract key entities and parameters
            entities = self._extract_entities(consulta_clean)
            
            # Generate processing strategy
            strategy = self._generate_strategy(intent_analysis, required_agents, entities)
            
            result = {
                "success": True,
                "query_original": consulta,
                "query_normalized": consulta_clean,
                "intent_analysis": intent_analysis,
                "required_agents": required_agents,
                "entities": entities,
                "processing_strategy": strategy,
                "confidence": self._calculate_confidence(intent_analysis),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Query processed successfully. Intent: {intent_analysis['primary_intent']}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "success": False,
                "error": str(e),
                "query_original": consulta,
                "timestamp": datetime.now().isoformat()
            }
    
    def _analyze_intent(self, consulta: str) -> Dict[str, Any]:
        """Analyze the intent of the user query."""
        intents = {
            'sql_query': 0,
            'data_analysis': 0,
            'schema_inquiry': 0,
            'financial_analysis': 0,
            'general_question': 0
        }
        
        # Check for SQL-related intent
        sql_score = sum(1 for keyword in self.query_patterns['sql_keywords'] 
                       if keyword in consulta)
        intents['sql_query'] = min(sql_score / len(self.query_patterns['sql_keywords']), 1.0)
        
        # Check for analysis intent
        analysis_score = sum(1 for keyword in self.query_patterns['analysis_keywords'] 
                           if keyword in consulta)
        intents['data_analysis'] = min(analysis_score / len(self.query_patterns['analysis_keywords']), 1.0)
        
        # Check for schema intent
        schema_score = sum(1 for keyword in self.query_patterns['schema_keywords'] 
                         if keyword in consulta)
        intents['schema_inquiry'] = min(schema_score / len(self.query_patterns['schema_keywords']), 1.0)
        
        # Check for financial intent
        financial_score = sum(1 for keyword in self.query_patterns['financial_keywords'] 
                            if keyword in consulta)
        intents['financial_analysis'] = min(financial_score / len(self.query_patterns['financial_keywords']), 1.0)
        
        # Default to general question if no specific intent detected
        if max(intents.values()) == 0:
            intents['general_question'] = 1.0
        
        # Determine primary intent
        primary_intent = max(intents, key=intents.get)
        
        return {
            'primary_intent': primary_intent,
            'intent_scores': intents,
            'complexity': self._assess_complexity(consulta)
        }
    
    def _determine_agents(self, intent_analysis: Dict[str, Any]) -> List[str]:
        """Determine which agents are needed based on intent analysis."""
        required_agents = []
        primary_intent = intent_analysis['primary_intent']
        scores = intent_analysis['intent_scores']
        
        # Always include SQL agent if there's any data-related intent
        if (primary_intent in ['sql_query', 'data_analysis', 'financial_analysis'] or 
            scores['sql_query'] > 0.3 or scores['data_analysis'] > 0.3):
            required_agents.append('sql_agent')
        
        # Include pandas agent for analysis tasks
        if (primary_intent in ['data_analysis', 'financial_analysis'] or 
            scores['data_analysis'] > 0.4):
            required_agents.append('pandas_agent')
        
        # Include schema analyzer for structure inquiries
        if primary_intent == 'schema_inquiry' or scores['schema_inquiry'] > 0.5:
            required_agents.append('schema_analyzer')
        
        return required_agents
    
    def _extract_entities(self, consulta: str) -> Dict[str, List[str]]:
        """Extract key entities from the query."""
        entities = {
            'tables': [],
            'columns': [],
            'time_periods': [],
            'metrics': [],
            'filters': []
        }
        
        # Simple entity extraction (can be enhanced with NLP)
        # Extract potential table names (words after 'from', 'table', etc.)
        table_patterns = [r'from\s+(\w+)', r'table\s+(\w+)', r'in\s+(\w+)']
        for pattern in table_patterns:
            matches = re.findall(pattern, consulta, re.IGNORECASE)
            entities['tables'].extend(matches)
        
        # Extract time-related terms
        time_patterns = [r'last\s+(\w+)', r'(\d+)\s+(day|week|month|year)s?', r'(today|yesterday|tomorrow)']
        for pattern in time_patterns:
            matches = re.findall(pattern, consulta, re.IGNORECASE)
            entities['time_periods'].extend([' '.join(match) if isinstance(match, tuple) else match for match in matches])
        
        return entities
    
    def _generate_strategy(self, intent_analysis: Dict[str, Any], required_agents: List[str], entities: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a processing strategy based on analysis."""
        strategy = {
            'execution_order': required_agents,
            'parallel_execution': len(required_agents) <= 2,
            'estimated_complexity': intent_analysis['complexity'],
            'requires_user_input': False,
            'expected_output_type': self._determine_output_type(intent_analysis['primary_intent'])
        }
        
        return strategy
    
    def _assess_complexity(self, consulta: str) -> str:
        """Assess the complexity of the query."""
        word_count = len(consulta.split())
        
        if word_count <= 5:
            return 'simple'
        elif word_count <= 15:
            return 'medium'
        else:
            return 'complex'
    
    def _calculate_confidence(self, intent_analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for the analysis."""
        max_score = max(intent_analysis['intent_scores'].values())
        return min(max_score * 1.2, 1.0)  # Boost confidence slightly
    
    def _determine_output_type(self, primary_intent: str) -> str:
        """Determine expected output type based on intent."""
        output_types = {
            'sql_query': 'tabular_data',
            'data_analysis': 'analysis_report',
            'schema_inquiry': 'schema_info',
            'financial_analysis': 'financial_report',
            'general_question': 'text_response'
        }
        
        return output_types.get(primary_intent, 'text_response')


# Export the agent class
__all__ = ['ProcesadorConsultas']
