version: '3.8'

services:
  # Main LumusAI application for testing
  lumusai-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lumusai-test
    ports:
      - "8000:8000"
    environment:
      # Add your Azure OpenAI credentials here or use .env file
      - API_KEY=${API_KEY}
      - API_VERSION=${API_VERSION:-2024-02-15-preview}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - MODEL=${MODEL:-gpt-4o}
      # Langfuse configuration for LLM observability
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_HOST=${LANGFUSE_HOST:-http://**************:3000}
      # Langfuse masking configuration (optional)
      - LANGFUSE_ENABLE_MASKING=${LANGFUSE_ENABLE_MASKING:-false}
      - LANGFUSE_MASK_CREDIT_CARDS=${LANGFUSE_MASK_CREDIT_CARDS:-true}
      - LANGFUSE_MASK_EMAILS=${LANGFUSE_MASK_EMAILS:-true}
      - LANGFUSE_MASK_PHONES=${LANGFUSE_MASK_PHONES:-true}
      - LANGFUSE_MASK_SECRETS=${LANGFUSE_MASK_SECRETS:-true}
    volumes:
      - ./logs:/app/logs
      - ./test_files:/app/test_files  # Mount test files directory
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Simple test runner container
  test-runner:
    image: python:3.12-alpine
    container_name: lumusai-test-runner
    depends_on:
      lumusai-test:
        condition: service_healthy
    volumes:
      - .:/workspace
    working_dir: /workspace
    environment:
      - BASE_URL=http://lumusai-test:8000
    command: |
      sh -c "
        echo '🧪 Installing test dependencies...'
        pip install requests
        echo '🚀 Running background processing tests...'
        python test_docker_compose.py
      "
    profiles:
      - test

networks:
  default:
    name: lumusai-test-network
