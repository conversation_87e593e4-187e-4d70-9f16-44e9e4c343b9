from .embedding_utils import generate_openai_embedding
import psycopg2

def format_vector(vec):
    return '[' + ','.join(map(str, vec)) + ']'

def cosine_similarity_search(user_query, top_n=5):
    embedding = generate_openai_embedding(user_query)
    print("Embedding:")
    if embedding is None:
        print("Could not generate embedding for the user query.")
        return
    
    embedding_vector = format_vector(embedding)
    print("Embedding formatted")
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor()
        query = """
            SELECT *, 1 - (embedding_small3 <=> %s) AS cosine_similarity
               FROM candidates
               ORDER BY cosine_similarity DESC LIMIT %s
        """ 
        cursor.execute(query, (embedding_vector, top_n))
        results = cursor.fetchall()

        # Get column names from the cursor description
        column_names = [desc[0] for desc in cursor.description]

        # Create a list of dictionaries mapping column names to row values
        mapped_results = [dict(zip(column_names, row)) for row in results]

            
        cursor.close()
        connection.close()
        
        # Return the mapped dictionary results
        return mapped_results

    except Exception as e:
        print(f"Error during database operation: {e}")
        if 'connection' in locals():
            connection.close()

def cosine_similarity_search_arroyo(user_query, top_n=5):
    embedding = generate_openai_embedding(user_query)
    print("Embedding:")
    if embedding is None:
        print("Could not generate embedding for the user query.")
        return
    
    embedding_vector = format_vector(embedding)
    print("Embedding formatted")
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor()
        query = """
            SELECT identifier, full_text , 1 - (vector_embedding <=> %s) AS cosine_similarity
               FROM arroyo_candidates
               ORDER BY cosine_similarity DESC LIMIT %s
        """ 
        cursor.execute(query, (embedding_vector, top_n))
        results = cursor.fetchall()

        # Get column names from the cursor description
        column_names = [desc[0] for desc in cursor.description]

        # Create a list of dictionaries mapping column names to row values
        mapped_results = [dict(zip(column_names, row)) for row in results]

            
        cursor.close()
        connection.close()
        
        # Return the mapped dictionary results
        return mapped_results

    except Exception as e:
        print(f"Error during database operation: {e}")
        if 'connection' in locals():
            connection.close()


import psycopg2
from psycopg2.extras import execute_values

def insert_vector_data(identifier: str, vector_embedding: list):
    connection = psycopg2.connect(
        user="arroyo_postgres_admin",
        password="@rr0y02024$",
        host="ai-db-poc-server.postgres.database.azure.com",
        port=5432,
        database="postgres",
    )
    cursor = connection.cursor()

    sql = """
        INSERT INTO candidate_vectors (identifier, vector_embedding)
        VALUES %s
        ON CONFLICT (identifier) DO UPDATE
        SET vector_embedding = EXCLUDED.vector_embedding
    """
    values = [(identifier, vector_embedding)]

    execute_values(cursor, sql, values)
    connection.commit()
    cursor.close()
    connection.close()