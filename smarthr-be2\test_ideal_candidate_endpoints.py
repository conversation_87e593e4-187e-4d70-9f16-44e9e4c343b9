"""
Quick test script to verify ideal candidate endpoints are working.
"""

import requests
import json

BASE_URL = "http://localhost:8080"

def test_endpoints():
    print("🧪 Testing Ideal Candidate Endpoints...")
    
    # Test 1: Check if the API is running
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ API is running and accessible")
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the application is running on port 8080")
        return False
    
    # Test 2: Check ideal candidate statistics endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/ideal-candidates/matching/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Statistics endpoint working. Total positions: {stats.get('total_positions', 'N/A')}")
            print(f"   Positions with ideal candidates: {stats.get('positions_with_ideal_candidates', 'N/A')}")
        else:
            print(f"⚠️  Statistics endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Statistics endpoint error: {e}")
    
    # Test 3: Check embedding statistics
    try:
        response = requests.get(f"{BASE_URL}/api/ideal-candidates/embeddings/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Embedding statistics endpoint working. Total ideal candidates: {stats.get('total_ideal_candidates', 'N/A')}")
        else:
            print(f"⚠️  Embedding statistics endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Embedding statistics endpoint error: {e}")
    
    # Test 4: Check positions with ideal candidates
    try:
        response = requests.get(f"{BASE_URL}/api/ideal-candidates/positions/with-ideal-candidates")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Positions with ideal candidates endpoint working. Found: {data.get('count', 'N/A')} positions")
        else:
            print(f"⚠️  Positions endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Positions endpoint error: {e}")
    
    print("\n🎉 Basic endpoint tests completed!")
    print("\n📋 Next steps:")
    print("1. Open http://localhost:8080/docs to see all available endpoints")
    print("2. Try generating an ideal candidate for an existing position")
    print("3. Test the new matching functionality")
    
    return True

if __name__ == "__main__":
    test_endpoints()
