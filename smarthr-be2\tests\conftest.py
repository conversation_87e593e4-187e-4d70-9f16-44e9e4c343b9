import pytest
from fastapi.testclient import TestClient
from main import app


@pytest.fixture(scope="session")
def client():
    """
    Create a test client for the FastAPI application.
    This fixture will be available for all test files.
    """
    return TestClient(app)


@pytest.fixture(autouse=True)
def setup_logging():
    """
    Configure logging for tests.
    This fixture runs automatically before each test.
    """
    import logging
    logging.basicConfig(level=logging.DEBUG)
    return None
