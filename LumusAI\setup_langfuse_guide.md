# Langfuse Integration Setup Guide

## 🎯 Overview

This guide explains how to set up Langfuse observability for LumusAI to track all LLM calls from Facturius (invoice processing) and SmartHR (CV processing) systems.

## 📋 Prerequisites

1. **Langfuse Account**: Sign up at [https://cloud.langfuse.com](https://cloud.langfuse.com)
2. **LumusAI Environment**: Working LumusAI installation with Azure OpenAI configured

## 🔧 Setup Steps

### Step 1: Get Langfuse Credentials

1. Go to your Langfuse instance at [http://**************:3000](http://**************:3000)
2. Sign up or log in to your account
3. Create a new project or select an existing one
4. Go to **Settings** → **API Keys**
5. Copy your credentials:
   - **Public Key** (starts with `pk-lf-...`)
   - **Secret Key** (starts with `sk-lf-...`)

### Step 2: Configure Environment Variables

Add the following to your `.env` file:

```env
# Langfuse Configuration (Optional - for LLM observability)
# Custom Langfuse instance
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_HOST=http://**************:3000

# Optional: Data masking for privacy protection
LANGFUSE_ENABLE_MASKING=true
LANGFUSE_MASK_CREDIT_CARDS=true
LANGFUSE_MASK_EMAILS=true
LANGFUSE_MASK_PHONES=true
LANGFUSE_MASK_SECRETS=true
```

### Step 3: Restart the Application

```bash
# Stop the current application (Ctrl+C if running)
# Then restart:
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

You should see:
```
✅ Langfuse observability enabled
✅ Langfuse integration enabled for LangChain tracing
🔒 Langfuse data masking enabled (if configured)
```

## 🧪 Testing the Integration

### Test 1: Simple API Call

```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: multipart/form-data" \
  -F "action=cv" \
  -F "data=This is a test CV for John Doe, Software Engineer with 5 years experience."
```

### Test 2: Invoice Processing

```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: multipart/form-data" \
  -F "action=invoice" \
  -F "data=Invoice #12345 from ACME Corp for $1,500.00 dated 2024-01-15"
```

## 🔍 Verifying Traces in Langfuse

1. Go to your Langfuse dashboard: [http://**************:3000](http://**************:3000)
2. Navigate to **Traces** section
3. You should see traces for each LLM call with:
   - **Trace Name**: Based on the operation (e.g., "cv", "invoice")
   - **Input/Output**: The prompts and responses
   - **Token Usage**: Prompt tokens, completion tokens, total tokens
   - **Cost**: Estimated cost of the API call
   - **Latency**: Time taken for the call
   - **Model**: The Azure OpenAI model used

### Expected Trace Structure

```
📊 Trace: cv-processing
├── 🔗 Span: LangChain Execution
│   ├── 📝 Input: CV text and processing prompt
│   ├── 📤 Output: Structured CV data
│   ├── 🏷️  Model: lumus-npd-gpt-4o
│   ├── 🔢 Tokens: 1,250 total (800 prompt + 450 completion)
│   └── 💰 Cost: $0.0125
```

## 🔒 Data Masking Setup (Optional)

### What is Data Masking?
Data masking automatically protects sensitive information in your traces before sending them to Langfuse. This is crucial for:
- **Privacy Compliance**: GDPR, CCPA, HIPAA compliance
- **Security**: Prevent sensitive data exposure
- **Peace of Mind**: Process confidential documents safely

### Enabling Data Masking

1. **Add masking configuration to `.env`**:
```env
LANGFUSE_ENABLE_MASKING=true
LANGFUSE_MASK_CREDIT_CARDS=true
LANGFUSE_MASK_EMAILS=true
LANGFUSE_MASK_PHONES=true
LANGFUSE_MASK_SECRETS=true
```

2. **Restart the application**:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

3. **Verify masking is enabled**:
Look for this message in the logs:
```
🔒 Langfuse data masking enabled
   - Credit cards: ✅
   - Emails: ✅
   - Phones: ✅
   - Secrets: ✅
```

### What Gets Masked?

| Data Type | Example Input | Masked Output |
|-----------|---------------|---------------|
| Credit Cards | `4111 1111 1111 1111` | `[REDACTED_CREDIT_CARD]` |
| Email Addresses | `<EMAIL>` | `[REDACTED_EMAIL]` |
| Phone Numbers | `************` | `[REDACTED_PHONE]` |
| Secret Data | `SECRET_API_KEY` | `[REDACTED_SECRET]` |

### Testing Masking

Run the masking demo to see it in action:
```bash
python examples/langfuse_masking_demo.py
```

This will:
1. Process test data containing sensitive information
2. Show you the masked traces in Langfuse
3. Demonstrate the difference with/without masking

## 🚨 Troubleshooting

### Issue: "Langfuse observability disabled"

**Cause**: Langfuse credentials not configured or invalid

**Solution**:
1. Check your `.env` file has the correct credentials
2. Verify credentials are valid in Langfuse dashboard
3. Restart the application

### Issue: No traces appearing in dashboard

**Possible causes**:
1. **Credentials incorrect**: Double-check your public/secret keys
2. **Network issues**: Ensure the application can reach `https://cloud.langfuse.com`
3. **Buffering**: Langfuse batches requests - wait a few minutes or restart the app

### Issue: Application fails to start

**Check**:
1. Run `python test_langfuse_setup.py` to verify installation
2. Check logs for specific error messages
3. Ensure all dependencies are installed: `pip install langfuse`

## 📊 Monitoring and Analytics

Once set up, you can use Langfuse to:

- **Track Performance**: Monitor response times and token usage
- **Cost Analysis**: Track spending across different operations
- **Quality Monitoring**: Review inputs/outputs for quality assurance
- **Usage Patterns**: Understand which features are used most
- **Error Tracking**: Identify and debug failed requests

## 🔒 Security Notes

- **Never commit credentials**: Keep `.env` file in `.gitignore`
- **Use environment variables**: Don't hardcode credentials in code
- **Rotate keys regularly**: Generate new API keys periodically
- **Monitor usage**: Check Langfuse dashboard for unexpected activity

## 🎉 Success Indicators

✅ Application starts with "Langfuse integration enabled"  
✅ Traces appear in Langfuse dashboard within 1-2 minutes  
✅ Token usage and costs are tracked accurately  
✅ Both Facturius and SmartHR operations are traced  
✅ Error handling works (app continues if Langfuse is down)  

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Run the test scripts: `python test_langfuse_setup.py`
3. Check Langfuse documentation: [https://langfuse.com/docs](https://langfuse.com/docs)
4. Verify Azure OpenAI connectivity is working first
