"""Deterministic extraction engine for ParserGPT POC."""

import logging
import re
import time
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
from lxml import etree, html
from .schemas import FieldSpec

logger = logging.getLogger(__name__)


class ExtractionEngine:
    """BeautifulSoup-based deterministic extraction engine."""
    
    def __init__(self):
        self.parser = "lxml"
    
    def _extract_with_css(self, soup: BeautifulSoup, selector: str) -> List[str]:
        """Extract values using CSS selector."""
        try:
            elements = soup.select(selector)
            values = []
            
            for element in elements:
                # Get text content, handling different element types
                if element.name in ['input', 'textarea']:
                    value = element.get('value', '') or element.get_text(strip=True)
                elif element.name == 'img':
                    value = element.get('alt', '') or element.get('title', '')
                elif element.name == 'a':
                    value = element.get_text(strip=True) or element.get('href', '')
                else:
                    value = element.get_text(" ", strip=True)
                
                if value:
                    values.append(value)
            
            return values
            
        except Exception as e:
            logger.warning(f"CSS selector error '{selector}': {e}")
            return []
    
    def _extract_with_xpath(self, html_content: str, xpath: str) -> List[str]:
        """Extract values using XPath selector."""
        try:
            # Parse HTML with lxml
            doc = html.fromstring(html_content)
            elements = doc.xpath(xpath)
            values = []
            
            for element in elements:
                if isinstance(element, str):
                    # Text node
                    value = element.strip()
                elif hasattr(element, 'text_content'):
                    # Element node
                    value = element.text_content().strip()
                else:
                    # Other types
                    value = str(element).strip()
                
                if value:
                    values.append(value)
            
            return values
            
        except Exception as e:
            logger.warning(f"XPath selector error '{xpath}': {e}")
            return []
    
    def _extract_with_regex(self, text: str, pattern: str) -> List[str]:
        """Extract values using regex pattern."""
        try:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            
            # Handle different match types
            values = []
            for match in matches:
                if isinstance(match, tuple):
                    # Multiple groups - join them
                    value = " ".join(str(g) for g in match if g)
                else:
                    value = str(match)
                
                if value.strip():
                    values.append(value.strip())
            
            return values
            
        except Exception as e:
            logger.warning(f"Regex pattern error '{pattern}': {e}")
            return []
    
    def _normalize_field_value(self, values: List[str], field_spec: Dict[str, Any]) -> Any:
        """Normalize extracted values based on field specification."""
        field_name = field_spec.get("name", "unknown")
        dtype = field_spec.get("dtype", "string")
        
        if not values:
            # Return appropriate empty value
            if dtype.endswith("[]"):
                return []
            elif dtype == "int":
                return 0
            elif dtype == "float":
                return 0.0
            elif dtype == "bool":
                return False
            else:
                return ""
        
        # Remove duplicates while preserving order
        unique_values = []
        seen = set()
        for value in values:
            if value not in seen:
                unique_values.append(value)
                seen.add(value)
        
        # Handle array types
        if dtype.endswith("[]"):
            base_type = dtype[:-2]  # Remove "[]"
            converted_values = []
            
            for value in unique_values:
                try:
                    if base_type == "int":
                        converted_values.append(int(value))
                    elif base_type == "float":
                        converted_values.append(float(value))
                    else:
                        converted_values.append(str(value))
                except (ValueError, TypeError):
                    logger.warning(f"Failed to convert '{value}' to {base_type} for field {field_name}")
                    continue
            
            return converted_values
        
        # Handle single value types
        value = unique_values[0]  # Take first value
        
        try:
            if dtype == "int":
                # Extract numbers from string
                numbers = re.findall(r'-?\d+', value)
                return int(numbers[0]) if numbers else 0
            elif dtype == "float":
                # Extract float numbers from string
                numbers = re.findall(r'-?\d+\.?\d*', value)
                return float(numbers[0]) if numbers else 0.0
            elif dtype == "bool":
                return value.lower() in ["true", "yes", "1", "on", "enabled"]
            else:
                return str(value)
                
        except (ValueError, TypeError):
            logger.warning(f"Failed to convert '{value}' to {dtype} for field {field_name}")
            # Return default value
            if dtype == "int":
                return 0
            elif dtype == "float":
                return 0.0
            elif dtype == "bool":
                return False
            else:
                return str(value)
    
    async def extract_with_adapter(
        self,
        html: str,
        adapter: Dict[str, Any],
        field_specs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extract data using adapter selectors.
        
        Args:
            html: HTML content to extract from
            adapter: Adapter configuration with selectors
            field_specs: Field specifications
            
        Returns:
            Dictionary of extracted data
        """
        start_time = time.time()
        
        try:
            # Parse HTML
            soup = BeautifulSoup(html, self.parser)
            text_content = soup.get_text(" ", strip=True)
            
            extracted_data = {}
            selectors = adapter.get("selectors", {})
            
            for field_spec in field_specs:
                field_name = field_spec["name"]
                selector_config = selectors.get(field_name, {})
                
                if not selector_config:
                    logger.warning(f"No selector found for field '{field_name}'")
                    extracted_data[field_name] = self._normalize_field_value([], field_spec)
                    continue
                
                # Try different selector types in order of preference
                values = []
                
                # 1. Try CSS selector
                css_selector = selector_config.get("css", "").strip()
                if css_selector and not values:
                    values = self._extract_with_css(soup, css_selector)
                    if values:
                        logger.debug(f"CSS selector '{css_selector}' found {len(values)} values for {field_name}")
                
                # 2. Try XPath selector
                xpath_selector = selector_config.get("xpath", "").strip()
                if xpath_selector and not values:
                    values = self._extract_with_xpath(html, xpath_selector)
                    if values:
                        logger.debug(f"XPath selector '{xpath_selector}' found {len(values)} values for {field_name}")
                
                # 3. Try regex pattern
                regex_pattern = selector_config.get("regex", "").strip()
                if regex_pattern and not values:
                    values = self._extract_with_regex(text_content, regex_pattern)
                    if values:
                        logger.debug(f"Regex pattern '{regex_pattern}' found {len(values)} values for {field_name}")
                
                # Normalize the extracted values
                extracted_data[field_name] = self._normalize_field_value(values, field_spec)
            
            processing_time = time.time() - start_time
            logger.info(f"Extraction completed in {processing_time:.3f}s")
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Extraction error: {e}")
            # Return empty data with correct structure
            empty_data = {}
            for field_spec in field_specs:
                empty_data[field_spec["name"]] = self._normalize_field_value([], field_spec)
            return empty_data
    
    def validate_selectors(self, adapter: Dict[str, Any]) -> List[str]:
        """
        Validate adapter selectors for syntax errors.
        
        Args:
            adapter: Adapter configuration
            
        Returns:
            List of validation error messages
        """
        errors = []
        selectors = adapter.get("selectors", {})
        
        for field_name, selector_config in selectors.items():
            # Validate CSS selector
            css_selector = selector_config.get("css", "").strip()
            if css_selector:
                try:
                    # Test with empty soup
                    test_soup = BeautifulSoup("<html></html>", self.parser)
                    test_soup.select(css_selector)
                except Exception as e:
                    errors.append(f"Invalid CSS selector for '{field_name}': {css_selector} - {e}")
            
            # Validate XPath selector
            xpath_selector = selector_config.get("xpath", "").strip()
            if xpath_selector:
                try:
                    # Test with empty document
                    test_doc = html.fromstring("<html></html>")
                    test_doc.xpath(xpath_selector)
                except Exception as e:
                    errors.append(f"Invalid XPath selector for '{field_name}': {xpath_selector} - {e}")
            
            # Validate regex pattern
            regex_pattern = selector_config.get("regex", "").strip()
            if regex_pattern:
                try:
                    re.compile(regex_pattern)
                except Exception as e:
                    errors.append(f"Invalid regex pattern for '{field_name}': {regex_pattern} - {e}")
        
        return errors


# Global extractor instance
_extractor = None


def get_extractor() -> ExtractionEngine:
    """Get the global extraction engine instance."""
    global _extractor
    if _extractor is None:
        _extractor = ExtractionEngine()
    return _extractor


async def extract_data(
    html: str,
    adapter: Dict[str, Any],
    field_specs: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Convenience function to extract data."""
    extractor = get_extractor()
    return await extractor.extract_with_adapter(html, adapter, field_specs)
