"""Tests for Pydantic schemas."""

import pytest
from pydantic import ValidationError
from app.schemas import (
    FieldSpec, SelectorSpec, AdapterData, JobRequest,
    ExtractionResult, ValidationResult
)


class TestFieldSpec:
    """Test cases for FieldSpec schema."""
    
    def test_valid_field_spec(self):
        """Test valid field specification."""
        spec = FieldSpec(
            name="title",
            dtype="string",
            description="Page title",
            required=True
        )
        assert spec.name == "title"
        assert spec.dtype == "string"
        assert spec.description == "Page title"
        assert spec.required is True
    
    def test_invalid_dtype(self):
        """Test invalid data type."""
        with pytest.raises(ValidationError) as exc_info:
            FieldSpec(name="test", dtype="invalid_type")
        
        assert "Invalid dtype" in str(exc_info.value)
    
    def test_valid_dtypes(self):
        """Test all valid data types."""
        valid_types = ['string', 'int', 'float', 'bool', 'string[]', 'int[]', 'float[]']
        
        for dtype in valid_types:
            spec = FieldSpec(name="test", dtype=dtype)
            assert spec.dtype == dtype


class TestSelectorSpec:
    """Test cases for SelectorSpec schema."""
    
    def test_valid_selector_spec(self):
        """Test valid selector specification."""
        spec = SelectorSpec(
            css="h1.title",
            xpath="//h1[@class='title']",
            regex=r"Title: (.+)"
        )
        assert spec.css == "h1.title"
        assert spec.xpath == "//h1[@class='title']"
        assert spec.regex == r"Title: (.+)"
    
    def test_empty_selectors_allowed(self):
        """Test that empty selectors are allowed."""
        spec = SelectorSpec(css="h1", xpath="", regex="")
        assert spec.css == "h1"
        assert spec.xpath == ""
        assert spec.regex == ""


class TestAdapterData:
    """Test cases for AdapterData schema."""
    
    def test_valid_adapter_data(self):
        """Test valid adapter data."""
        data = AdapterData(
            domain="example.com",
            version=1,
            url_patterns={
                "detail": ["*/product/*", "*/item/*"],
                "list": ["*/category/*", "*/search*"]
            },
            selectors={
                "title": SelectorSpec(css="h1", xpath="", regex=""),
                "price": SelectorSpec(css=".price", xpath="", regex="")
            }
        )
        
        assert data.domain == "example.com"
        assert data.version == 1
        assert "detail" in data.url_patterns
        assert "title" in data.selectors
    
    def test_invalid_url_pattern_type(self):
        """Test invalid URL pattern type."""
        with pytest.raises(ValidationError) as exc_info:
            AdapterData(
                domain="example.com",
                url_patterns={"invalid_type": ["*/test/*"]}
            )
        
        assert "Invalid page type" in str(exc_info.value)


class TestJobRequest:
    """Test cases for JobRequest schema."""
    
    def test_valid_job_request(self):
        """Test valid job request."""
        request = JobRequest(
            start_url="https://example.com",
            allowed_domains=["example.com"],
            max_depth=2,
            max_pages=50,
            field_spec=[
                FieldSpec(name="title", dtype="string"),
                FieldSpec(name="tags", dtype="string[]")
            ]
        )
        
        assert request.start_url == "https://example.com"
        assert request.allowed_domains == ["example.com"]
        assert request.max_depth == 2
        assert request.max_pages == 50
        assert len(request.field_spec) == 2
    
    def test_invalid_start_url(self):
        """Test invalid start URL."""
        with pytest.raises(ValidationError) as exc_info:
            JobRequest(
                start_url="not-a-url",
                field_spec=[FieldSpec(name="title", dtype="string")]
            )
        
        assert "must be a valid HTTP/HTTPS URL" in str(exc_info.value)
    
    def test_field_validation_limits(self):
        """Test field validation limits."""
        # Test max_depth limits
        with pytest.raises(ValidationError):
            JobRequest(
                start_url="https://example.com",
                max_depth=10,  # Too high
                field_spec=[FieldSpec(name="title", dtype="string")]
            )
        
        # Test max_pages limits
        with pytest.raises(ValidationError):
            JobRequest(
                start_url="https://example.com",
                max_pages=2000,  # Too high
                field_spec=[FieldSpec(name="title", dtype="string")]
            )


class TestExtractionResult:
    """Test cases for ExtractionResult schema."""
    
    def test_valid_extraction_result(self):
        """Test valid extraction result."""
        result = ExtractionResult(
            url="https://example.com/page1",
            data={"title": "Test Title", "tags": ["tag1", "tag2"]},
            method="deterministic",
            confidence=0.95,
            processing_time=1.23
        )
        
        assert result.url == "https://example.com/page1"
        assert result.data["title"] == "Test Title"
        assert result.method == "deterministic"
        assert result.confidence == 0.95
        assert result.processing_time == 1.23


class TestValidationResult:
    """Test cases for ValidationResult schema."""
    
    def test_valid_validation_result(self):
        """Test valid validation result."""
        result = ValidationResult(
            field_name="title",
            success=True,
            expected="Sample Title",
            actual="Sample Title",
            confidence=1.0
        )
        
        assert result.field_name == "title"
        assert result.success is True
        assert result.expected == "Sample Title"
        assert result.actual == "Sample Title"
        assert result.confidence == 1.0
    
    def test_failed_validation_result(self):
        """Test failed validation result."""
        result = ValidationResult(
            field_name="price",
            success=False,
            expected="$29.99",
            actual="",
            confidence=0.0
        )
        
        assert result.field_name == "price"
        assert result.success is False
        assert result.expected == "$29.99"
        assert result.actual == ""
        assert result.confidence == 0.0
