#!/usr/bin/env python3
"""
Test script to call the actual process-test endpoint and check if Langfuse tracing works.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")
WEBHOOK_URL = "https://webhook.site/unique-id"  # Replace with your webhook URL for testing

def test_cv_processing():
    """Test CV processing endpoint with a simple text input."""
    
    print("🧪 Testing CV Processing Endpoint")
    print("=" * 50)
    
    # Test data - simple CV text
    cv_text = """
    John <PERSON>
    Software Engineer
    
    Email: <EMAIL>
    Phone: ************
    Credit Card: 4111 1111 1111 1111
    SECRET_API_KEY: SECRET_12345
    
    Experience:
    - Senior Developer at Tech Corp (2020-2023)
    - Junior <PERSON>per at StartupXYZ (2018-2020)
    
    Skills:
    - Python, JavaScript, React
    - Machine Learning, AI
    
    Education:
    - BS Computer Science, University ABC (2014-2018)
    """
    
    # Prepare the request
    url = f"{BASE_URL}/process-test"
    
    data = {
        "action": "cv",
        "callback_url": WEBHOOK_URL,
        "data": cv_text
    }
    
    print(f"📞 Making request to: {url}")
    print(f"📝 Action: {data['action']}")
    print(f"📊 Data length: {len(cv_text)} characters")
    print(f"🔗 Webhook URL: {WEBHOOK_URL}")
    
    try:
        # Make the request
        response = requests.post(url, data=data, timeout=30)
        
        print(f"\n📋 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            
            # Extract task ID if available
            task_id = result.get("task_id")
            if task_id:
                print(f"\n🆔 Task ID: {task_id}")
                print("⏳ Processing in background...")
                print("\n📋 What to check now:")
                print("1. Check the application logs for debug messages:")
                print("   - 🔍 _get_callbacks: Added Langfuse handler")
                print("   - 🔍 get_structured_data: Using X callback(s)")
                print("   - 🔍 About to call structured_model.ainvoke")
                print("2. Check Langfuse dashboard for traces:")
                print("   - http://157.230.167.30:3000")
                print("3. Wait for webhook callback (if configured)")
                
                return task_id
            else:
                print("⚠️  No task_id in response")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return None
    
    return None

def test_invoice_processing():
    """Test invoice processing endpoint with a simple text input."""
    
    print("\n🧪 Testing Invoice Processing Endpoint")
    print("=" * 50)
    
    # Test data - simple invoice text
    invoice_text = """
    INVOICE
    
    Invoice Number: INV-001
    Date: 2024-01-15
    
    Bill To:
    Customer Name: ACME Corp
    Email: <EMAIL>
    Phone: ************
    Credit Card: 5555 4444 3333 2222
    
    Items:
    - Software License: $500.00
    - Support Services: $200.00
    
    Total: $700.00
    
    SECRET_INVOICE_ID: SECRET_INV_789
    """
    
    # Prepare the request
    url = f"{BASE_URL}/process-test"
    
    data = {
        "action": "invoice",
        "callback_url": WEBHOOK_URL,
        "data": invoice_text
    }
    
    print(f"📞 Making request to: {url}")
    print(f"📝 Action: {data['action']}")
    print(f"📊 Data length: {len(invoice_text)} characters")
    
    try:
        # Make the request
        response = requests.post(url, data=data, timeout=30)
        
        print(f"\n📋 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            
            task_id = result.get("task_id")
            if task_id:
                print(f"\n🆔 Task ID: {task_id}")
                return task_id
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return None
    
    return None

def check_task_status(task_id):
    """Check the status of a background task."""
    
    if not task_id:
        return
    
    print(f"\n🔍 Checking task status: {task_id}")
    
    url = f"{BASE_URL}/task-status/{task_id}"
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 Task Status: {result.get('status', 'unknown')}")
            
            if result.get('status') == 'completed':
                print("✅ Task completed successfully!")
                if 'result' in result:
                    print("📄 Result available")
            elif result.get('status') == 'failed':
                print(f"❌ Task failed: {result.get('error', 'Unknown error')}")
            else:
                print("⏳ Task still processing...")
                
        else:
            print(f"⚠️  Could not check task status: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Error checking task status: {e}")

def main():
    """Main test function."""
    
    print("🚀 Testing Process Endpoints with Langfuse Tracing")
    print("=" * 60)
    
    # Check if the service is running
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Test CV processing
    cv_task_id = test_cv_processing()
    
    # Wait a bit
    print("\n⏳ Waiting 5 seconds...")
    time.sleep(5)
    
    # Test invoice processing
    invoice_task_id = test_invoice_processing()
    
    # Wait for processing
    print("\n⏳ Waiting 10 seconds for background processing...")
    time.sleep(10)
    
    # Check task statuses
    if cv_task_id:
        check_task_status(cv_task_id)
    
    if invoice_task_id:
        check_task_status(invoice_task_id)
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary")
    print("=" * 60)
    print("✅ Requests sent to process-test endpoints")
    print("📋 What to check now:")
    print("1. Application logs for debug messages:")
    print("   - Look for '🔍' debug messages")
    print("   - Check if callbacks are being used")
    print("2. Langfuse dashboard:")
    print("   - http://157.230.167.30:3000")
    print("   - Look for new traces with CV/Invoice processing")
    print("3. If masking is enabled, check if sensitive data is masked:")
    print("   - Credit cards should show as [REDACTED_CREDIT_CARD]")
    print("   - Emails should show as [REDACTED_EMAIL]")
    print("   - Phone numbers should show as [REDACTED_PHONE]")
    print("   - Secret data should show as [REDACTED_SECRET]")
    
    if not cv_task_id and not invoice_task_id:
        print("\n❌ No tasks were created successfully")
        print("🔧 Troubleshooting:")
        print("1. Check if the service is running correctly")
        print("2. Verify the endpoint URLs are correct")
        print("3. Check application logs for errors")

if __name__ == "__main__":
    main()
