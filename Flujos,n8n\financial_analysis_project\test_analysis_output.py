#!/usr/bin/env python3
"""
Quick test script to check what the analysis is returning.
"""

import sys
import os
sys.path.append('backend')
sys.path.append('.')

import pandas as pd
from datetime import datetime, timedelta
import json

# Create a small test dataset
def create_test_data():
    """Create a small test dataset for analysis."""
    data = {
        'supplier': ['Supplier A', 'Supplier B', 'Supplier C'] * 10,
        'voucher': [f'V{i:03d}' for i in range(30)],
        'gross_amount': [1000, 1500, 2000] * 10,
        'cost': [700, 1000, 1400] * 10,
        'date': [datetime.now() - timedelta(days=i) for i in range(30)]
    }
    return pd.DataFrame(data)

def test_small_file():
    """Test analysis with a small file (should use AI agents)."""
    print("=" * 60)
    print("TESTING SMALL FILE ANALYSIS")
    print("=" * 60)
    
    try:
        from backend.app.services.financial_analyzer import FinancialAnalyzer
        
        df = create_test_data()
        print(f"Created test data: {len(df)} records")
        
        analyzer = FinancialAnalyzer()
        config = {"assume_cost_percentage": 70.0, "low_margin_threshold": 10.0}
        
        # Convert to CSV bytes for processing
        csv_data = df.to_csv(index=False).encode()
        
        result = analyzer.analyze_file(csv_data, "test_small.csv", config)
        
        print(f"\nRESULT SUMMARY:")
        print(f"- File name: {result.file_name}")
        print(f"- Records processed: {result.total_records_processed}")
        print(f"- Final report length: {len(result.final_report) if result.final_report else 0}")
        print(f"- Assumptions count: {len(result.assumptions_used)}")
        print(f"- Warnings count: {len(result.warnings)}")
        
        print(f"\nASSUMPTIONS:")
        for i, assumption in enumerate(result.assumptions_used, 1):
            print(f"{i}. {assumption}")
        
        print(f"\nWARNINGS:")
        if result.warnings:
            for i, warning in enumerate(result.warnings, 1):
                print(f"{i}. {warning}")
        else:
            print("No warnings")
        
        print(f"\nFINAL REPORT PREVIEW:")
        if result.final_report:
            print(result.final_report[:300] + "..." if len(result.final_report) > 300 else result.final_report)
        else:
            print("No final report generated")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

def test_large_file():
    """Test analysis with a large file (should use batch processing)."""
    print("\n" + "=" * 60)
    print("TESTING LARGE FILE ANALYSIS")
    print("=" * 60)
    
    try:
        from backend.app.services.financial_analyzer import FinancialAnalyzer
        
        # Create a large dataset (600 records to trigger batch processing)
        large_data = {
            'supplier': [f'Supplier {chr(65 + i % 26)}' for i in range(600)],
            'voucher': [f'V{i:05d}' for i in range(600)],
            'gross_amount': [1000 + (i % 1000) for i in range(600)],
            'cost': [700 + (i % 700) for i in range(600)],
            'date': [datetime.now() - timedelta(days=i % 365) for i in range(600)]
        }
        df = pd.DataFrame(large_data)
        print(f"Created large test data: {len(df)} records")
        
        analyzer = FinancialAnalyzer()
        config = {"assume_cost_percentage": 75.0, "low_margin_threshold": 15.0}
        
        # Convert to CSV bytes for processing
        csv_data = df.to_csv(index=False).encode()
        
        result = analyzer.analyze_file(csv_data, "test_large.csv", config)
        
        print(f"\nRESULT SUMMARY:")
        print(f"- File name: {result.file_name}")
        print(f"- Records processed: {result.total_records_processed}")
        print(f"- Final report length: {len(result.final_report) if result.final_report else 0}")
        print(f"- Assumptions count: {len(result.assumptions_used)}")
        print(f"- Warnings count: {len(result.warnings)}")
        
        print(f"\nASSUMPTIONS:")
        for i, assumption in enumerate(result.assumptions_used, 1):
            print(f"{i}. {assumption}")
        
        print(f"\nWARNINGS:")
        if result.warnings:
            for i, warning in enumerate(result.warnings, 1):
                print(f"{i}. {warning}")
        else:
            print("No warnings")
        
        print(f"\nFINAL REPORT PREVIEW:")
        if result.final_report:
            print(result.final_report[:500] + "..." if len(result.final_report) > 500 else result.final_report)
        else:
            print("No final report generated")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing Financial Analysis Output")
    print("This script tests what the analysis functions are actually returning.")
    
    # Test small file (AI agents)
    test_small_file()
    
    # Test large file (batch processing)
    test_large_file()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETE")
    print("=" * 60)
