import asyncio
import json
from typing import Dict, <PERSON>, Optional, Union
from pydantic import BaseModel, ValidationError
from fastapi.responses import JSONResponse
from openai import OpenAIError, APIConnectionError, AuthenticationError
from langchain_openai import AzureChatOpenAI
from langchain_community.callbacks import get_openai_callback
from langchain_core.messages import HumanMessage

class LangChainClient:
    def __init__(self, api_key: str, api_version: str, azure_endpoint: str, model: str):

        self.llm = AzureChatOpenAI(
            api_key= api_key,
            api_version= api_version,
            azure_endpoint= azure_endpoint,
            model=model
        )

    async def extract_data(self, prompt: str, base64_image_data: str, max_retries: int = 3) -> Dict:
        """
        Extract information from an image using the LLM.

        Features:
        - Processes both text and image inputs
        - Tracks token usage and costs
        - Handles various response formats
        - Built-in retry logic for handling transient errors
        - Comprehensive error handling

        Args:
            prompt (str): The instruction or question for the model
            base64_image_data (str): Base64-encoded image data
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": str,  # The model's response
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Raises:
            JSONResponse: HTTP error responses with appropriate status codes:
                - 400: Invalid input parameters
                - 401: Authentication error
                - 422: Validation error
                - 429: Rate limit exceeded
                - 500: Internal server error
                - 503: Service unavailable
                - 504: Gateway timeout
        """
        # Input validation
        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(base64_image_data, str) or not base64_image_data.strip():
            return JSONResponse(
                content={"error": "The 'base64_image_data' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                # Call the model
                with get_openai_callback() as callback:
                    response = await self.llm.ainvoke(
                        [
                            HumanMessage(
                                content=[
                                    {"type": "text", "text": prompt},
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": base64_image_data,
                                            "detail": "auto",
                                        },
                                    },
                                ]
                            )
                        ]
                    )

                # Extract content from response
                content = response.content if hasattr(response, 'content') else response['content']

                # Success! Return the result
                return {
                    "response": content,
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def get_structured_data(self, structure: Type[BaseModel], data: str, prompt: str, max_retries: int = 3):
        """
        Extract structured data from text using a Pydantic model schema.

        Features:
        - Type validation with Pydantic
        - Automatic schema enforcement
        - Token usage tracking
        - Comprehensive error handling
        - Built-in retry logic for handling transient errors

        Args:
            structure (Type[BaseModel]): Pydantic model class defining the output structure
            data (str): Input text to process
            prompt (str): Instructions for the model
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": Dict,  # Structured data matching the Pydantic model
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Raises:
            JSONResponse: HTTP error responses with appropriate status codes:
                - 400: Invalid input parameters
                - 401: Authentication error
                - 422: Validation error
                - 429: Rate limit exceeded
                - 500: Internal server error
                - 503: Service unavailable
                - 504: Gateway timeout
        """
        # Input validation
        if not issubclass(structure, BaseModel):
            return JSONResponse(
                content={"error": "The 'structure' argument must be a class that inherits from BaseModel"},
                status_code=400
            )

        if not isinstance(data, str) or not data.strip():
            return JSONResponse(
                content={"error": "The 'data' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                with get_openai_callback() as callback:
                    structured_model = self.llm.with_structured_output(structure, method="function_calling")
                    response = await structured_model.ainvoke(f"{prompt}\n{data}")

                # Success! Return the result
                return {
                    "response": response.model_dump(),
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def get_structured_data_image(
        self,
        structure: Type[BaseModel],
        base64_image_data: str,
        prompt: str,
        initial_data: Optional[Union[BaseModel, dict, str]] = None,
        max_retries: int = 3
    ):
        """
        Extract structured data from an image using a Pydantic model schema.
        Supports pre-filled or partially filled Pydantic models.

        Features:
        - Type validation with Pydantic
        - Automatic schema enforcement
        - Token usage tracking
        - Comprehensive error handling
        - Built-in retry logic for handling transient errors
        - Support for continuing from a partially filled model

        Args:
            structure (Type[BaseModel]): Pydantic model class defining the output structure
            base64_image_data (str): Base64-encoded image data
            prompt (str): Instructions for the model
            initial_data (Optional[Union[BaseModel, dict, str]]): Pre-filled data to continue filling.
                                               Can be a Pydantic model instance, a dictionary, or a JSON string.
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": Dict,  # Structured data matching the Pydantic model
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Example:
            >>> # Example 1: Using a Pydantic model
            >>> initial_data = InvoiceData(invoice_number="INV-001", date="2023-01-01")
            >>> result = await client.get_structured_data_image(
            ...     InvoiceData,
            ...     base64_image,
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data
            ... )
            >>>
            >>> # Example 2: Using a dictionary
            >>> initial_data_dict = {"invoice_number": "INV-001", "date": "2023-01-01"}
            >>> result = await client.get_structured_data_image(
            ...     InvoiceData,
            ...     base64_image,
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data_dict
            ... )
            >>>
            >>> # Example 3: Using a JSON string
            >>> initial_data_json = '{"invoice_number": "INV-001", "date": "2023-01-01"}'
            >>> result = await client.get_structured_data_image(
            ...     InvoiceData,
            ...     base64_image,
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data_json
            ... )
            >>>
            >>> # All examples produce similar results:
            >>> print(result["response"])
            {"invoice_number": "INV-001", "date": "2023-01-01", "total": 1250.00, ...}
        """
        # Input validation
        if not issubclass(structure, BaseModel):
            return JSONResponse(
                content={"error": "The 'structure' argument must be a class that inherits from BaseModel"},
                status_code=400
            )

        if initial_data is not None:
            # Validate initial_data based on its type
            if isinstance(initial_data, BaseModel) and not isinstance(initial_data, structure):
                return JSONResponse(
                    content={"error": "When providing a Pydantic model as 'initial_data', it must be an instance of the provided structure"},
                    status_code=400
                )
            elif isinstance(initial_data, str):
                # Try to parse as JSON to validate
                try:
                    json.loads(initial_data)
                except json.JSONDecodeError:
                    # Not valid JSON, but we'll still accept it as plain text
                    print("Warning: initial_data is not valid JSON. Using as plain text.")
            # For dict type, no validation needed - we'll convert to JSON later

        if not isinstance(base64_image_data, str) or not base64_image_data.strip():
            return JSONResponse(
                content={"error": "The 'base64_image_data' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                with get_openai_callback() as callback:
                    # Create a model with structured output based on the provided Pydantic model
                    structured_model = self.llm.with_structured_output(
                        structure,
                        method="function_calling"
                    )

                    # Modify prompt if initial data is provided
                    modified_prompt = prompt
                    if initial_data is not None:
                        # Handle initial data that could be a Pydantic model or JSON string/dict
                        initial_data_json = ""

                        if isinstance(initial_data, BaseModel):
                            # If it's a Pydantic model, convert to JSON
                            initial_data_json = initial_data.model_dump_json(indent=2)
                        elif isinstance(initial_data, dict):
                            # If it's already a dict, convert to JSON string
                            initial_data_json = json.dumps(initial_data, indent=2)
                        elif isinstance(initial_data, str):
                            # If it's a JSON string, use it directly (assuming it's valid JSON)
                            try:
                                # Validate by parsing and re-serializing to ensure proper formatting
                                parsed = json.loads(initial_data)
                                initial_data_json = json.dumps(parsed, indent=2)
                            except json.JSONDecodeError:
                                # If not valid JSON, use as is with a warning
                                print("Warning: initial_data is not valid JSON. Using as plain text.")
                                initial_data_json = initial_data
                        else:
                            # For any other type, convert to string
                            initial_data_json = str(initial_data)

                        # Add context about the initial data to the prompt
                        prompt = f"{prompt}\n\nI already have some information extracted: {initial_data_json}\n\nIMPORTANT: This is a multi-page document, and I'm processing it page by page. This image is likely a continuation of previous pages.\n\nWhen you see information that appears to be a continuation from previous pages (especially for work experience entries):\n\n1. If you see 'Key responsibilities:' or bullet points at the beginning of the page, these are likely responsibilities for a job mentioned on previous pages. Make sure to include the job title and company name exactly as they appear in the initial data.\n\n2. If you see text like 'o Data analysis – expertise in writing...' at the beginning of the page without a job title, this is likely a continuation of responsibilities from a previous page.\n\n3. For work experience entries that already exist in the initial data, add any new responsibilities or details you find on this page to the existing entries rather than creating new entries.\n\n4. Pay special attention to 'Project Scope:' sections followed by 'Key responsibilities:' - extract each bullet point after 'Key responsibilities:' as a separate responsibility for the most recently mentioned job.\n\nPlease fill in any missing fields or correct any errors based on the image, while preserving the existing data and ensuring continuity across pages."

                    # Invoke the model with both text prompt and image
                    response = await structured_model.ainvoke(
                        [
                            HumanMessage(
                                content=[
                                    {"type": "text", "text": modified_prompt},
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": base64_image_data,
                                            "detail": "auto",
                                        },
                                    },
                                ]
                            )
                        ]
                    )
                # Success! Return the result
                return {
                    "response": response.model_dump(),
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def get_structured_data_test(
        self,
        structure: Type[BaseModel],
        data: str,
        prompt: str,
        initial_data: Optional[Union[BaseModel, dict, str]] = None,
        max_retries: int = 3
    ):
        """
        Extract structured data from text using a Pydantic model schema.
        Designed for testing purposes with support for pre-filled or partially filled Pydantic models.

        Features:
        - Type validation with Pydantic
        - Automatic schema enforcement
        - Token usage tracking
        - Comprehensive error handling
        - Built-in retry logic for handling transient errors
        - Support for continuing from a partially filled model

        Args:
            structure (Type[BaseModel]): Pydantic model class defining the output structure
            data (str): Input text to process
            prompt (str): Instructions for the model
            initial_data (Optional[Union[BaseModel, dict, str]]): Pre-filled data to continue filling.
                                               Can be a Pydantic model instance, a dictionary, or a JSON string.
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": Dict,  # Structured data matching the Pydantic model
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Example:
            >>> # Example 1: Using a Pydantic model
            >>> initial_data = InvoiceData(invoice_number="INV-001", date="2023-01-01")
            >>> result = await client.get_structured_data_test(
            ...     InvoiceData,
            ...     "Invoice text content...",
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data
            ... )
            >>>
            >>> # Example 2: Using a dictionary
            >>> initial_data_dict = {"invoice_number": "INV-001", "date": "2023-01-01"}
            >>> result = await client.get_structured_data_test(
            ...     InvoiceData,
            ...     "Invoice text content...",
            ...     "Extract all remaining invoice details",
            ...     initial_data=initial_data_dict
            ... )
        """
        # Input validation
        if not issubclass(structure, BaseModel):
            return JSONResponse(
                content={"error": "The 'structure' argument must be a class that inherits from BaseModel"},
                status_code=400
            )

        if initial_data is not None:
            # Validate initial_data based on its type
            if isinstance(initial_data, BaseModel) and not isinstance(initial_data, structure):
                return JSONResponse(
                    content={"error": "When providing a Pydantic model as 'initial_data', it must be an instance of the provided structure"},
                    status_code=400
                )
            elif isinstance(initial_data, str):
                # Try to parse as JSON to validate
                try:
                    json.loads(initial_data)
                except json.JSONDecodeError:
                    # Not valid JSON, but we'll still accept it as plain text
                    print("Warning: initial_data is not valid JSON. Using as plain text.")
            # For dict type, no validation needed - we'll convert to JSON later

        if not isinstance(data, str) or not data.strip():
            return JSONResponse(
                content={"error": "The 'data' argument must be a valid string"},
                status_code=400
            )

        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                with get_openai_callback() as callback:
                    # Create a model with structured output based on the provided Pydantic model
                    structured_model = self.llm.with_structured_output(
                        structure,
                        method="function_calling"
                    )

                    # Modify prompt if initial data is provided
                    modified_prompt = prompt
                    if initial_data is not None:
                        # Handle initial data that could be a Pydantic model or JSON string/dict
                        initial_data_json = ""

                        if isinstance(initial_data, BaseModel):
                            # If it's a Pydantic model, convert to JSON
                            initial_data_json = initial_data.model_dump_json(indent=2)
                        elif isinstance(initial_data, dict):
                            # If it's already a dict, convert to JSON string
                            initial_data_json = json.dumps(initial_data, indent=2)
                        elif isinstance(initial_data, str):
                            # If it's a JSON string, use it directly (assuming it's valid JSON)
                            try:
                                # Validate by parsing and re-serializing to ensure proper formatting
                                parsed = json.loads(initial_data)
                                initial_data_json = json.dumps(parsed, indent=2)
                            except json.JSONDecodeError:
                                # If not valid JSON, use as is with a warning
                                print("Warning: initial_data is not valid JSON. Using as plain text.")
                                initial_data_json = initial_data
                        else:
                            # For any other type, convert to string
                            initial_data_json = str(initial_data)

                        # Add context about the initial data to the prompt
                        modified_prompt = f"{prompt}\n\nI already have some information extracted: {initial_data_json}\n\nIMPORTANT: This is a multi-page document, and I'm processing it page by page. This text is likely a continuation of previous pages.\n\nWhen you see information that appears to be a continuation from previous pages (especially for work experience entries):\n\n1. If you see 'Key responsibilities:' or bullet points at the beginning of the page, these are likely responsibilities for a job mentioned on previous pages. Make sure to include the job title and company name exactly as they appear in the initial data.\n\n2. If you see text like 'o Data analysis – expertise in writing...' at the beginning of the page without a job title, this is likely a continuation of responsibilities from a previous page.\n\n3. For work experience entries that already exist in the initial data, add any new responsibilities or details you find on this page to the existing entries rather than creating new entries.\n\n4. Pay special attention to 'Project Scope:' sections followed by 'Key responsibilities:' - extract each bullet point after 'Key responsibilities:' as a separate responsibility for the most recently mentioned job.\n\nPlease fill in any missing fields or correct any errors based on the provided data, while preserving the existing data and ensuring continuity across pages."

                    # Invoke the model with the text data
                    response = await structured_model.ainvoke(f"{modified_prompt}\n{data}")

                # Success! Return the result
                return {
                    "response": response.model_dump(),
                    "token_usage": {
                        "prompt_tokens": callback.prompt_tokens,
                        "completion_tokens": callback.completion_tokens,
                        "total_tokens": callback.total_tokens,
                        "cost": callback.total_cost
                    }
                }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )

    async def simple_invoke(self, prompt: str, max_retries: int = 3):
        """
        Make a simple async call to the language model.

        Features:
        - Streamlined interface for basic queries
        - Async operation for better performance
        - Simple text-in-text-out operation
        - Robust error handling
        - Built-in retry logic for handling transient errors
        - Token usage tracking and cost calculation

        Args:
            prompt (str): The text prompt to send to the model
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.

        Returns:
            Dict: {
                "response": str,  # The model's response text
                "token_usage": {
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            } or JSONResponse with error details

        Example:
            >>> result = await client.simple_invoke("Summarize this text: ...")
            >>> print(result["response"])
            "Here's a summary of the text..."
            >>> print(f"Cost: ${result['token_usage']['cost']:.4f}")
            Cost: $0.0025
        """
        # Input validation
        if not isinstance(prompt, str) or not prompt.strip():
            return JSONResponse(
                content={"error": "The 'prompt' argument must be a valid string"},
                status_code=400
            )

        # Implement retry logic
        for attempt in range(max_retries):
            try:
                with get_openai_callback() as callback:
                    response = await self.llm.ainvoke(
                        input=[
                            HumanMessage(
                                content=[
                                    {"type": "text", "text": prompt}
                                ]
                            )
                        ]
                    )

                    # Log token usage
                    print(f"Token usage: Prompt tokens: {callback.prompt_tokens}, "
                          f"Completion tokens: {callback.completion_tokens}, "
                          f"Total tokens: {callback.total_tokens}, "
                          f"Cost: ${callback.total_cost:.4f}")

                    # Extract content from response
                    content = response.content if hasattr(response, 'content') else response['content']

                    # Success! Return the result with token usage
                    return {
                        "response": content,
                        "token_usage": {
                            "prompt_tokens": callback.prompt_tokens,
                            "completion_tokens": callback.completion_tokens,
                            "total_tokens": callback.total_tokens,
                            "cost": callback.total_cost
                        }
                    }

            except asyncio.CancelledError:
                # Properly handle task cancellation
                print("Task was cancelled during API call to OpenAI")
                raise  # Re-raise to ensure proper cleanup

            except ValidationError as e:
                # Handle validation errors from the model
                error_msg = str(e)
                print(f"Validation error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Model validation error", "details": error_msg},
                        status_code=422
                    )
                # Otherwise wait and retry
                print(f"Retrying after validation error (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(2)

            except AuthenticationError as e:
                # Authentication errors won't be resolved by retrying
                error_msg = str(e)
                print(f"Authentication error: {error_msg}")
                return JSONResponse(
                    content={"error": "Authentication error in the OpenAI API", "details": error_msg},
                    status_code=401
                )

            except APIConnectionError as e:
                error_msg = str(e)
                print(f"API Connection error: {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "Could not connect to the OpenAI API", "details": error_msg},
                        status_code=503
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)  # Exponential backoff
                print(f"Retrying after connection error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

            except OpenAIError as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"OpenAI error ({error_type}): {error_msg}")

                # Check for rate limiting errors
                if "rate limit" in error_msg.lower() or "capacity" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Rate limit exceeded", "details": error_msg},
                            status_code=429
                        )
                    # Exponential backoff for rate limits
                    wait_time = 30 * (attempt + 1)
                    print(f"Rate limit reached, waiting {wait_time}s before retrying (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Check for timeout errors
                elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Request timed out", "details": error_msg},
                            status_code=504
                        )
                    wait_time = 10 * (attempt + 1)
                    print(f"Request timed out, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

                # Other OpenAI errors
                else:
                    if attempt == max_retries - 1:
                        return JSONResponse(
                            content={"error": "Error in the call to the OpenAI API", "details": error_msg, "error_type": error_type},
                            status_code=500
                        )
                    wait_time = 5 * (attempt + 1)
                    print(f"OpenAI error, retrying in {wait_time}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"Unexpected error ({error_type}): {error_msg}")

                # If this is the last attempt, return an error response
                if attempt == max_retries - 1:
                    return JSONResponse(
                        content={"error": "An unexpected error occurred", "details": error_msg, "error_type": error_type},
                        status_code=500
                    )
                # Otherwise wait and retry
                wait_time = 5 * (attempt + 1)
                print(f"Retrying after unexpected error in {wait_time}s (attempt {attempt+1}/{max_retries})")
                await asyncio.sleep(wait_time)

        # This should never be reached due to the returns in the exception handlers
        return JSONResponse(
            content={"error": "Maximum retry attempts reached"},
            status_code=500
        )
