"""Tests for FastAPI main application."""

import pytest
from httpx import Async<PERSON><PERSON>
from app.main import app


class TestMainApp:
    """Test cases for the main FastAPI application."""
    
    @pytest.mark.asyncio
    async def test_root_endpoint(self):
        """Test the root endpoint."""
        async with <PERSON>ync<PERSON><PERSON>(app=app, base_url="http://test") as client:
            response = await client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "ParserGPT POC"
        assert data["version"] == "0.1.0"
        assert "endpoints" in data
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test the health check endpoint."""
        async with <PERSON>ync<PERSON><PERSON>(app=app, base_url="http://test") as client:
            response = await client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "ParserGPT POC"
    
    @pytest.mark.asyncio
    async def test_create_job_valid(self, override_get_session, sample_job_data):
        """Test creating a valid job."""
        async with <PERSON>ync<PERSON><PERSON>(app=app, base_url="http://test") as client:
            response = await client.post("/jobs", json=sample_job_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "job_id" in data
        assert data["status"] == "created"
        assert "message" in data
    
    @pytest.mark.asyncio
    async def test_create_job_invalid_url(self, override_get_session):
        """Test creating a job with invalid URL."""
        invalid_data = {
            "start_url": "not-a-url",
            "field_spec": [{"name": "title", "dtype": "string"}]
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/jobs", json=invalid_data)
        
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_get_job_status_existing(self, override_get_session, sample_job):
        """Test getting status of existing job."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(f"/jobs/{sample_job.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == sample_job.id
        assert data["status"] == sample_job.status
        assert data["start_url"] == sample_job.start_url
    
    @pytest.mark.asyncio
    async def test_get_job_status_nonexistent(self, override_get_session):
        """Test getting status of non-existent job."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/jobs/99999")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_list_jobs_empty(self, override_get_session):
        """Test listing jobs when none exist."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/jobs")
        
        assert response.status_code == 200
        data = response.json()
        assert data["jobs"] == []
        assert data["total"] == 0
        assert data["limit"] == 10
        assert data["offset"] == 0
    
    @pytest.mark.asyncio
    async def test_list_jobs_with_data(self, override_get_session, sample_job):
        """Test listing jobs with existing data."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/jobs")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["jobs"]) == 1
        assert data["jobs"][0]["job_id"] == sample_job.id
        assert data["jobs"][0]["status"] == sample_job.status
    
    @pytest.mark.asyncio
    async def test_list_jobs_pagination(self, override_get_session):
        """Test job listing pagination."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/jobs?limit=5&offset=10")
        
        assert response.status_code == 200
        data = response.json()
        assert data["limit"] == 5
        assert data["offset"] == 10
    
    @pytest.mark.asyncio
    async def test_download_csv_nonexistent_job(self, override_get_session):
        """Test downloading CSV for non-existent job."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/jobs/99999/csv")
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_download_csv_incomplete_job(self, override_get_session, sample_job):
        """Test downloading CSV for incomplete job."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(f"/jobs/{sample_job.id}/csv")
        
        assert response.status_code == 400
        data = response.json()
        assert "not completed" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_delete_job_existing(self, override_get_session, sample_job):
        """Test deleting an existing job."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.delete(f"/jobs/{sample_job.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert "deleted successfully" in data["message"]
    
    @pytest.mark.asyncio
    async def test_delete_job_nonexistent(self, override_get_session):
        """Test deleting a non-existent job."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.delete("/jobs/99999")
        
        assert response.status_code == 404


class TestJobValidation:
    """Test cases for job request validation."""
    
    @pytest.mark.asyncio
    async def test_max_depth_validation(self, override_get_session):
        """Test max_depth validation."""
        invalid_data = {
            "start_url": "https://example.com",
            "max_depth": 10,  # Too high
            "field_spec": [{"name": "title", "dtype": "string"}]
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/jobs", json=invalid_data)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_max_pages_validation(self, override_get_session):
        """Test max_pages validation."""
        invalid_data = {
            "start_url": "https://example.com",
            "max_pages": 2000,  # Too high
            "field_spec": [{"name": "title", "dtype": "string"}]
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/jobs", json=invalid_data)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_empty_field_spec_validation(self, override_get_session):
        """Test empty field_spec validation."""
        invalid_data = {
            "start_url": "https://example.com",
            "field_spec": []  # Empty
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/jobs", json=invalid_data)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_invalid_field_dtype(self, override_get_session):
        """Test invalid field dtype validation."""
        invalid_data = {
            "start_url": "https://example.com",
            "field_spec": [{"name": "title", "dtype": "invalid_type"}]
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/jobs", json=invalid_data)
        
        assert response.status_code == 422
