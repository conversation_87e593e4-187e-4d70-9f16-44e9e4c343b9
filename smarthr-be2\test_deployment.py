#!/usr/bin/env python3
"""
Quick deployment test for the ideal candidate system.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_api_health():
    """Test if the API is responding."""
    print("🔍 Testing API Health...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API is running and accessible")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure Docker container is running on port 8080")
        return False
    except requests.exceptions.Timeout:
        print("❌ API request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_ideal_candidate_endpoints():
    """Test the new ideal candidate endpoints."""
    print("\n🧪 Testing Ideal Candidate Endpoints...")
    
    endpoints_to_test = [
        {
            "url": "/api/ideal-candidates/matching/statistics",
            "method": "GET",
            "description": "Matching Statistics"
        },
        {
            "url": "/api/ideal-candidates/embeddings/statistics",
            "method": "GET", 
            "description": "Embedding Statistics"
        },
        {
            "url": "/api/ideal-candidates/positions/with-ideal-candidates",
            "method": "GET",
            "description": "Positions with Ideal Candidates"
        }
    ]
    
    results = []
    
    for endpoint in endpoints_to_test:
        try:
            print(f"  Testing {endpoint['description']}...")
            response = requests.get(f"{BASE_URL}{endpoint['url']}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ {endpoint['description']} - Success")
                results.append(True)
            else:
                print(f"    ⚠️  {endpoint['description']} - Status: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"    ❌ {endpoint['description']} - Error: {e}")
            results.append(False)
    
    return all(results)

def test_database_connection():
    """Test database connectivity through the statistics endpoint."""
    print("\n🗄️  Testing Database Connection...")
    try:
        response = requests.get(f"{BASE_URL}/api/ideal-candidates/matching/statistics", timeout=15)
        if response.status_code == 200:
            stats = response.json()
            print(f"    ✅ Database connected successfully")
            print(f"    📊 Total positions: {stats.get('total_positions', 'N/A')}")
            print(f"    📊 Positions with ideal candidates: {stats.get('positions_with_ideal_candidates', 'N/A')}")
            return True
        else:
            print(f"    ❌ Database connection test failed - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ Database connection test error: {e}")
        return False

def test_openapi_schema():
    """Test that the OpenAPI schema includes new endpoints."""
    print("\n📋 Testing OpenAPI Schema...")
    try:
        response = requests.get(f"{BASE_URL}/openapi.json", timeout=10)
        if response.status_code == 200:
            schema = response.json()
            paths = schema.get("paths", {})
            
            # Check for key ideal candidate endpoints
            expected_paths = [
                "/api/ideal-candidates/generate",
                "/api/ideal-candidates/match",
                "/api/ideal-candidates/matching/statistics"
            ]
            
            found_paths = []
            for expected_path in expected_paths:
                for path in paths.keys():
                    if expected_path in path:
                        found_paths.append(expected_path)
                        break
            
            if len(found_paths) == len(expected_paths):
                print("    ✅ All expected ideal candidate endpoints found in schema")
                return True
            else:
                print(f"    ⚠️  Found {len(found_paths)}/{len(expected_paths)} expected endpoints")
                return False
        else:
            print(f"    ❌ Failed to get OpenAPI schema - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ OpenAPI schema test error: {e}")
        return False

def main():
    """Run all deployment tests."""
    print("🚀 Starting Ideal Candidate System Deployment Tests")
    print("=" * 60)
    
    tests = [
        ("API Health", test_api_health),
        ("Database Connection", test_database_connection),
        ("Ideal Candidate Endpoints", test_ideal_candidate_endpoints),
        ("OpenAPI Schema", test_openapi_schema)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEPLOYMENT TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All deployment tests passed!")
        print("\n📝 Next Steps:")
        print("1. Visit http://localhost:8080/docs to explore the API")
        print("2. Try generating an ideal candidate for an existing position")
        print("3. Test the new matching functionality")
        print("4. Monitor the application logs for any issues")
        return True
    else:
        print(f"\n💥 {total - passed} tests failed!")
        print("\n🔧 Troubleshooting:")
        print("1. Check Docker container logs: docker-compose logs smarthr")
        print("2. Verify database connectivity")
        print("3. Check environment variables in .env file")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
