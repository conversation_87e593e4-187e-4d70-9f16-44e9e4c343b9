#!/usr/bin/env python3
"""
Advanced usage example for ParserGPT POC.

This example demonstrates:
1. Multiple concurrent jobs
2. Different field types and configurations
3. Job management (listing, deletion)
4. Error handling and retry logic

Run this after starting the ParserGPT server:
    python -m app.main
"""

import asyncio
import httpx
from typing import List, Dict, Any
import json


class ParserGPTClient:
    """Advanced client for ParserGPT API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = None
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(timeout=60.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def create_job(self, job_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new scraping job."""
        response = await self.client.post(f"{self.base_url}/jobs", json=job_config)
        response.raise_for_status()
        return response.json()
    
    async def get_job_status(self, job_id: int) -> Dict[str, Any]:
        """Get job status and progress."""
        response = await self.client.get(f"{self.base_url}/jobs/{job_id}")
        response.raise_for_status()
        return response.json()
    
    async def list_jobs(self, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """List recent jobs."""
        response = await self.client.get(f"{self.base_url}/jobs?limit={limit}&offset={offset}")
        response.raise_for_status()
        return response.json()
    
    async def delete_job(self, job_id: int) -> Dict[str, Any]:
        """Delete a job."""
        response = await self.client.delete(f"{self.base_url}/jobs/{job_id}")
        response.raise_for_status()
        return response.json()
    
    async def download_csv(self, job_id: int, filename: str) -> str:
        """Download job results as CSV."""
        response = await self.client.get(f"{self.base_url}/jobs/{job_id}/csv")
        response.raise_for_status()
        
        with open(filename, "wb") as f:
            f.write(response.content)
        
        return filename
    
    async def wait_for_completion(self, job_id: int, check_interval: int = 5) -> Dict[str, Any]:
        """Wait for job to complete and return final status."""
        while True:
            status = await self.get_job_status(job_id)
            
            if status["status"] in ["completed", "failed"]:
                return status
            
            print(f"Job {job_id}: {status['status']} - "
                  f"{status.get('pages_extracted', 0)}/{status.get('pages_discovered', 0)} pages")
            
            await asyncio.sleep(check_interval)


def create_news_job_config() -> Dict[str, Any]:
    """Create configuration for news article scraping."""
    return {
        "start_url": "https://quotes.toscrape.com",  # Public test site
        "allowed_domains": ["quotes.toscrape.com"],
        "max_depth": 2,
        "max_pages": 30,
        "field_spec": [
            {
                "name": "quote_text",
                "dtype": "string",
                "description": "The quote text content",
                "required": True
            },
            {
                "name": "author",
                "dtype": "string",
                "description": "Quote author name",
                "required": True
            },
            {
                "name": "tags",
                "dtype": "string[]",
                "description": "Quote tags or categories",
                "required": False
            }
        ]
    }


def create_books_job_config() -> Dict[str, Any]:
    """Create configuration for book catalog scraping."""
    return {
        "start_url": "https://books.toscrape.com/catalogue/category/books_1/index.html",
        "allowed_domains": ["books.toscrape.com"],
        "max_depth": 3,
        "max_pages": 50,
        "field_spec": [
            {
                "name": "title",
                "dtype": "string",
                "description": "Book title",
                "required": True
            },
            {
                "name": "price",
                "dtype": "string",
                "description": "Book price in pounds",
                "required": True
            },
            {
                "name": "rating",
                "dtype": "string",
                "description": "Star rating (One, Two, Three, Four, Five)",
                "required": False
            },
            {
                "name": "availability",
                "dtype": "string",
                "description": "Stock availability status",
                "required": False
            },
            {
                "name": "description",
                "dtype": "string",
                "description": "Book description or summary",
                "required": False
            }
        ]
    }


async def run_concurrent_jobs(client: ParserGPTClient) -> List[Dict[str, Any]]:
    """Run multiple jobs concurrently."""
    print("🚀 Starting concurrent jobs...")
    
    # Create job configurations
    jobs_config = [
        ("News/Quotes", create_news_job_config()),
        ("Books", create_books_job_config())
    ]
    
    # Start all jobs
    running_jobs = []
    for name, config in jobs_config:
        print(f"   Creating {name} job...")
        result = await client.create_job(config)
        job_id = result["job_id"]
        running_jobs.append({
            "name": name,
            "job_id": job_id,
            "config": config
        })
        print(f"   ✅ {name} job created: ID {job_id}")
    
    print(f"\n📊 Monitoring {len(running_jobs)} concurrent jobs...")
    
    # Wait for all jobs to complete
    completed_jobs = []
    for job_info in running_jobs:
        print(f"\n   Waiting for {job_info['name']} job {job_info['job_id']}...")
        final_status = await client.wait_for_completion(job_info['job_id'])
        
        job_info["final_status"] = final_status
        completed_jobs.append(job_info)
        
        if final_status["status"] == "completed":
            print(f"   ✅ {job_info['name']} completed: "
                  f"{final_status.get('pages_extracted', 0)} pages extracted")
        else:
            print(f"   ❌ {job_info['name']} failed: {final_status.get('error_message', 'Unknown error')}")
    
    return completed_jobs


async def download_all_results(client: ParserGPTClient, completed_jobs: List[Dict[str, Any]]):
    """Download results from all completed jobs."""
    print("\n📥 Downloading results...")
    
    for job_info in completed_jobs:
        if job_info["final_status"]["status"] == "completed":
            filename = f"results_{job_info['name'].lower().replace('/', '_')}_job_{job_info['job_id']}.csv"
            try:
                await client.download_csv(job_info['job_id'], filename)
                print(f"   ✅ {job_info['name']} results saved to {filename}")
            except Exception as e:
                print(f"   ❌ Failed to download {job_info['name']} results: {e}")


async def demonstrate_job_management(client: ParserGPTClient):
    """Demonstrate job listing and management features."""
    print("\n📋 Job Management Demo...")
    
    # List all jobs
    jobs_list = await client.list_jobs(limit=20)
    print(f"   Total jobs in system: {len(jobs_list['jobs'])}")
    
    for job in jobs_list['jobs']:
        print(f"   Job {job['job_id']}: {job['status']} - {job['start_url']}")
    
    # Demonstrate job deletion (optional)
    if jobs_list['jobs']:
        # Find a completed or failed job to delete
        deletable_jobs = [j for j in jobs_list['jobs'] if j['status'] in ['completed', 'failed']]
        
        if deletable_jobs:
            job_to_delete = deletable_jobs[0]
            print(f"\n   Deleting job {job_to_delete['job_id']} as demonstration...")
            
            try:
                result = await client.delete_job(job_to_delete['job_id'])
                print(f"   ✅ {result['message']}")
            except Exception as e:
                print(f"   ❌ Failed to delete job: {e}")


async def demonstrate_error_handling(client: ParserGPTClient):
    """Demonstrate error handling with invalid configurations."""
    print("\n⚠️  Error Handling Demo...")
    
    # Test invalid URL
    invalid_job = {
        "start_url": "not-a-valid-url",
        "field_spec": [{"name": "title", "dtype": "string"}]
    }
    
    try:
        await client.create_job(invalid_job)
        print("   ❌ Expected validation error but job was created!")
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 422:
            print("   ✅ Correctly rejected invalid URL")
        else:
            print(f"   ❌ Unexpected error code: {e.response.status_code}")
    
    # Test invalid field type
    invalid_field_job = {
        "start_url": "https://example.com",
        "field_spec": [{"name": "title", "dtype": "invalid_type"}]
    }
    
    try:
        await client.create_job(invalid_field_job)
        print("   ❌ Expected validation error but job was created!")
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 422:
            print("   ✅ Correctly rejected invalid field type")
        else:
            print(f"   ❌ Unexpected error code: {e.response.status_code}")


async def main():
    """Main advanced example function."""
    print("🚀 ParserGPT Advanced Usage Example")
    print("=" * 50)
    
    async with ParserGPTClient() as client:
        try:
            # Test server connectivity
            response = await client.client.get(f"{client.base_url}/health")
            response.raise_for_status()
            print("✅ Server is running and healthy")
            
            # Run concurrent jobs
            completed_jobs = await run_concurrent_jobs(client)
            
            # Download results
            await download_all_results(client, completed_jobs)
            
            # Demonstrate job management
            await demonstrate_job_management(client)
            
            # Demonstrate error handling
            await demonstrate_error_handling(client)
            
            # Final summary
            print("\n📊 Final Summary:")
            print("=" * 30)
            for job_info in completed_jobs:
                status = job_info["final_status"]
                print(f"   {job_info['name']} Job {job_info['job_id']}:")
                print(f"     Status: {status['status']}")
                print(f"     Pages: {status.get('pages_extracted', 0)}/{status.get('pages_discovered', 0)}")
                if status.get('error_message'):
                    print(f"     Error: {status['error_message']}")
            
            print("\n🎉 Advanced example completed!")
            
        except httpx.RequestError as e:
            print(f"\n❌ Connection Error: {e}")
            print("   Make sure the ParserGPT server is running on http://localhost:8000")
        
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    print("Starting ParserGPT advanced usage example...")
    print("Make sure the server is running: python -m app.main")
    print()
    
    asyncio.run(main())
