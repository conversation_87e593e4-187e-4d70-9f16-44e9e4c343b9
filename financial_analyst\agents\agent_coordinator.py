"""
Agent coordination and communication layer for the multi-agent database analyst system.
Manages workflow orchestration, data flow, and inter-agent communication.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Enumeration of available agent types."""
    SQL_AGENT = "sql_agent"
    PANDAS_AGENT = "pandas_agent"
    QUERY_PROCESSOR = "query_processor"
    FINANCIAL_ADVISOR = "financial_advisor"
    DASHBOARD_GENERATOR = "dashboard_generator"
    SCHEMA_ANALYZER = "schema_analyzer"

class TaskStatus(Enum):
    """Enumeration of task statuses."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AgentTask:
    """Data class representing a task for an agent."""
    task_id: str
    agent_type: AgentType
    task_data: Dict[str, Any]
    dependencies: List[str]
    status: TaskStatus
    result: Optional[Dict[str, Any]]
    error: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

@dataclass
class WorkflowState:
    """Data class representing the current workflow state."""
    workflow_id: str
    tasks: Dict[str, AgentTask]
    global_context: Dict[str, Any]
    current_step: str
    status: TaskStatus
    created_at: datetime
    updated_at: datetime

class CoordinadorAgentes:
    """Central coordinator for managing multi-agent workflows."""
    
    def __init__(self):
        """Initialize agent coordinator."""
        self.workflows: Dict[str, WorkflowState] = {}
        self.agent_registry: Dict[AgentType, Any] = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize and register all available agents."""
        try:
            # Import agents dynamically to avoid circular imports
            from .sql_agent import AgenteSQLConsultas
            from .enhanced_pandas_agent import AgenteAnalizarDatosAvanzado
            from .schema_analyzer import AnalizadorEsquema
            from .query_processor import ProcesadorConsultas
            from .financial_advisor import AsesorFinanciero

            # Register agents
            self.agent_registry[AgentType.SQL_AGENT] = AgenteSQLConsultas()
            self.agent_registry[AgentType.PANDAS_AGENT] = AgenteAnalizarDatosAvanzado()
            self.agent_registry[AgentType.SCHEMA_ANALYZER] = AnalizadorEsquema()
            self.agent_registry[AgentType.QUERY_PROCESSOR] = ProcesadorConsultas()
            self.agent_registry[AgentType.FINANCIAL_ADVISOR] = AsesorFinanciero()

            logger.info("Agent registry initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize agents: {e}")
            raise
    
    def crear_workflow(self, workflow_id: str, consulta_usuario: str) -> WorkflowState:
        """Create a new workflow for processing user query."""
        try:
            # Initialize workflow state
            workflow = WorkflowState(
                workflow_id=workflow_id,
                tasks={},
                global_context={
                    "consulta_original": consulta_usuario,
                    "timestamp": datetime.now().isoformat(),
                    "user_session": workflow_id
                },
                current_step="initialization",
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Analyze query to determine required agents and workflow
            workflow_plan = self._planificar_workflow(consulta_usuario)
            
            # Create tasks based on workflow plan
            for task_config in workflow_plan:
                task = AgentTask(
                    task_id=task_config["task_id"],
                    agent_type=AgentType(task_config["agent_type"]),
                    task_data=task_config["task_data"],
                    dependencies=task_config.get("dependencies", []),
                    status=TaskStatus.PENDING,
                    result=None,
                    error=None,
                    created_at=datetime.now(),
                    started_at=None,
                    completed_at=None
                )
                workflow.tasks[task.task_id] = task
            
            self.workflows[workflow_id] = workflow
            logger.info(f"Workflow {workflow_id} created with {len(workflow.tasks)} tasks")
            
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to create workflow {workflow_id}: {e}")
            raise
    
    def _planificar_workflow(self, consulta: str) -> List[Dict[str, Any]]:
        """Plan workflow based on user query analysis."""
        # Simple query analysis to determine workflow
        consulta_lower = consulta.lower()
        
        workflow_plan = []
        
        # Always start with query processing
        workflow_plan.append({
            "task_id": "query_analysis",
            "agent_type": AgentType.QUERY_PROCESSOR,
            "task_data": {"consulta": consulta},
            "dependencies": []
        })
        
        # Check if database query is needed
        if any(keyword in consulta_lower for keyword in ["database", "sql", "query", "table", "data"]):
            workflow_plan.append({
                "task_id": "sql_analysis",
                "agent_type": AgentType.SQL_AGENT,
                "task_data": {"consulta_natural": consulta},
                "dependencies": ["query_analysis"]
            })

            workflow_plan.append({
                "task_id": "pandas_analysis",
                "agent_type": AgentType.PANDAS_AGENT,
                "task_data": {"consulta": consulta, "tipo_analisis": "comprehensive"},
                "dependencies": ["sql_analysis"]
            })

        # Check if schema analysis is needed
        if any(keyword in consulta_lower for keyword in ["schema", "structure", "tables", "columns"]):
            workflow_plan.append({
                "task_id": "schema_analysis",
                "agent_type": AgentType.SCHEMA_ANALYZER,
                "task_data": {"analysis_type": "comprehensive"},
                "dependencies": []
            })
        
        # Always end with financial advisor for final report
        workflow_plan.append({
            "task_id": "final_report",
            "agent_type": AgentType.FINANCIAL_ADVISOR,
            "task_data": {"consulta": consulta},
            "dependencies": [task["task_id"] for task in workflow_plan]
        })
        
        return workflow_plan
    
    def ejecutar_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Execute workflow with proper task coordination."""
        try:
            if workflow_id not in self.workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            workflow = self.workflows[workflow_id]
            workflow.status = TaskStatus.IN_PROGRESS
            workflow.updated_at = datetime.now()
            
            # Execute tasks in dependency order
            completed_tasks = set()
            
            while len(completed_tasks) < len(workflow.tasks):
                # Find tasks ready to execute
                ready_tasks = []
                for task_id, task in workflow.tasks.items():
                    if (task.status == TaskStatus.PENDING and 
                        all(dep in completed_tasks for dep in task.dependencies)):
                        ready_tasks.append(task)
                
                if not ready_tasks:
                    # Check for failed tasks or circular dependencies
                    failed_tasks = [t for t in workflow.tasks.values() if t.status == TaskStatus.FAILED]
                    if failed_tasks:
                        workflow.status = TaskStatus.FAILED
                        break
                    else:
                        logger.warning(f"No ready tasks found in workflow {workflow_id}")
                        break
                
                # Execute ready tasks
                for task in ready_tasks:
                    try:
                        result = self._ejecutar_tarea(task, workflow.global_context)
                        task.result = result
                        task.status = TaskStatus.COMPLETED
                        task.completed_at = datetime.now()
                        completed_tasks.add(task.task_id)
                        
                        # Update global context with task results
                        workflow.global_context[f"result_{task.task_id}"] = result
                        
                        logger.info(f"Task {task.task_id} completed successfully")
                        
                    except Exception as e:
                        task.error = str(e)
                        task.status = TaskStatus.FAILED
                        task.completed_at = datetime.now()
                        logger.error(f"Task {task.task_id} failed: {e}")
            
            # Update workflow status
            if all(task.status == TaskStatus.COMPLETED for task in workflow.tasks.values()):
                workflow.status = TaskStatus.COMPLETED
            elif any(task.status == TaskStatus.FAILED for task in workflow.tasks.values()):
                workflow.status = TaskStatus.FAILED
            
            workflow.updated_at = datetime.now()
            
            # Compile final results
            return self._compilar_resultados_workflow(workflow)
            
        except Exception as e:
            logger.error(f"Failed to execute workflow {workflow_id}: {e}")
            if workflow_id in self.workflows:
                self.workflows[workflow_id].status = TaskStatus.FAILED
            raise
    
    def _ejecutar_tarea(self, task: AgentTask, global_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a specific task using the appropriate agent."""
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = datetime.now()
        
        agent = self.agent_registry.get(task.agent_type)
        if not agent:
            raise ValueError(f"Agent {task.agent_type} not found in registry")
        
        # Prepare task data with global context
        task_data = {**task.task_data, **global_context}
        
        # Execute based on agent type
        if task.agent_type == AgentType.QUERY_PROCESSOR:
            return agent.procesar_consulta(task_data["consulta"])

        elif task.agent_type == AgentType.SQL_AGENT:
            return agent.ejecutar_consulta_natural(task_data["consulta_natural"])

        elif task.agent_type == AgentType.PANDAS_AGENT:
            # Get data from previous SQL task if available
            sql_result = global_context.get("result_sql_analysis", {})
            if sql_result.get("success") and "data" in sql_result:
                # Convert data to DataFrame
                df = pd.DataFrame(sql_result["data"])
                return agent.ejecutar_analisis_avanzado(df, task_data["consulta"], task_data.get("tipo_analisis", "comprehensive"))
            else:
                # Use mock data or skip
                return {"success": False, "error": "No data available for pandas analysis"}

        elif task.agent_type == AgentType.SCHEMA_ANALYZER:
            return {"success": True, "schema_info": agent.generar_reporte_esquema()}

        elif task.agent_type == AgentType.FINANCIAL_ADVISOR:
            return agent.generar_reporte_financiero(task_data["consulta"], global_context)

        else:
            return {"success": False, "error": f"Unknown agent type: {task.agent_type}"}
    
    def _compilar_resultados_workflow(self, workflow: WorkflowState) -> Dict[str, Any]:
        """Compile final workflow results."""
        return {
            "workflow_id": workflow.workflow_id,
            "status": workflow.status.value,
            "consulta_original": workflow.global_context.get("consulta_original"),
            "tasks_completed": len([t for t in workflow.tasks.values() if t.status == TaskStatus.COMPLETED]),
            "total_tasks": len(workflow.tasks),
            "results": {task_id: task.result for task_id, task in workflow.tasks.items() if task.result},
            "errors": {task_id: task.error for task_id, task in workflow.tasks.items() if task.error},
            "execution_time": (workflow.updated_at - workflow.created_at).total_seconds(),
            "timestamp": workflow.updated_at.isoformat()
        }
    
    def obtener_estado_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get current workflow state."""
        if workflow_id not in self.workflows:
            return None
        
        workflow = self.workflows[workflow_id]
        return {
            "workflow_id": workflow_id,
            "status": workflow.status.value,
            "current_step": workflow.current_step,
            "tasks": {
                task_id: {
                    "status": task.status.value,
                    "agent_type": task.agent_type.value,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "error": task.error
                }
                for task_id, task in workflow.tasks.items()
            },
            "updated_at": workflow.updated_at.isoformat()
        }
    
    def cancelar_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        if workflow_id not in self.workflows:
            return False
        
        workflow = self.workflows[workflow_id]
        workflow.status = TaskStatus.CANCELLED
        workflow.updated_at = datetime.now()
        
        # Cancel pending tasks
        for task in workflow.tasks.values():
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
        
        logger.info(f"Workflow {workflow_id} cancelled")
        return True

# Global coordinator instance
coordinador_global = CoordinadorAgentes()

def get_agent_coordinator() -> CoordinadorAgentes:
    """Get the global agent coordinator instance."""
    return coordinador_global
