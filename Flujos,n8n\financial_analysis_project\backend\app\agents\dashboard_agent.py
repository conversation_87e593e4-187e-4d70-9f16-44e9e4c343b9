"""
<PERSON><PERSON><PERSON><PERSON> dashboard agent for generating KPI-based visualizations.
Creates dynamic dashboards based on user KPI queries using Plotly.
"""

from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langchain_groq import ChatGroq
from typing import List, Dict, Any, Optional
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import time
from io import StringIO
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@tool
def create_supplier_chart(data: str, kpi: str, chart_type: str = "bar") -> str:
    """
    Create a chart showing suppliers vs the specified KPI.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze (e.g., 'income', 'profit', 'cost')
        chart_type: Type of chart ('bar', 'pie', 'scatter')
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(StringIO(data))

        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            # Try mapped columns
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Find supplier column
        supplier_col = None
        for col in df.columns:
            if 'supplier' in col.lower() or 'vendor' in col.lower():
                supplier_col = col
                break
        
        if not supplier_col:
            return json.dumps({"error": "No supplier column found"})
        
        # Aggregate data by supplier
        supplier_data = df.groupby(supplier_col)[target_column].sum().reset_index()
        supplier_data = supplier_data.sort_values(target_column, ascending=False).head(10)
        
        # Create chart based on type
        if chart_type == "bar":
            fig = px.bar(
                supplier_data, 
                x=supplier_col, 
                y=target_column,
                title=f"Top 10 Suppliers by {kpi.title()}",
                labels={target_column: kpi.title(), supplier_col: "Supplier"}
            )
        elif chart_type == "pie":
            fig = px.pie(
                supplier_data, 
                values=target_column, 
                names=supplier_col,
                title=f"Supplier Distribution by {kpi.title()}"
            )
        else:  # scatter
            fig = px.scatter(
                supplier_data, 
                x=supplier_col, 
                y=target_column,
                size=target_column,
                title=f"Suppliers vs {kpi.title()}",
                labels={target_column: kpi.title(), supplier_col: "Supplier"}
            )
        
        fig.update_layout(height=400)
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating supplier chart: {str(e)}"})


@tool
def create_product_chart(data: str, kpi: str, chart_type: str = "bar") -> str:
    """
    Create a chart showing products vs the specified KPI.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze
        chart_type: Type of chart ('bar', 'pie', 'treemap')
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(StringIO(data))

        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Find product column
        product_col = None
        for col in df.columns:
            if any(term in col.lower() for term in ['product', 'item', 'description', 'concept']):
                product_col = col
                break
        
        if not product_col:
            return json.dumps({"error": "No product column found"})
        
        # Aggregate data by product
        product_data = df.groupby(product_col)[target_column].sum().reset_index()
        product_data = product_data.sort_values(target_column, ascending=False).head(15)
        
        # Create chart based on type
        if chart_type == "bar":
            fig = px.bar(
                product_data, 
                x=product_col, 
                y=target_column,
                title=f"Top 15 Products by {kpi.title()}",
                labels={target_column: kpi.title(), product_col: "Product"}
            )
            fig.update_xaxes(tickangle=45)
        elif chart_type == "pie":
            fig = px.pie(
                product_data.head(10), 
                values=target_column, 
                names=product_col,
                title=f"Top 10 Products by {kpi.title()}"
            )
        else:  # treemap
            fig = px.treemap(
                product_data.head(10),
                values=target_column,
                names=product_col,
                title=f"Product Treemap by {kpi.title()}"
            )
        
        fig.update_layout(height=500)
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating product chart: {str(e)}"})


@tool
def create_total_summary_chart(data: str, kpi: str) -> str:
    """
    Create a summary chart showing total KPI with key metrics.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(StringIO(data))

        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Calculate summary statistics
        total_value = df[target_column].sum()
        avg_value = df[target_column].mean()
        median_value = df[target_column].median()
        max_value = df[target_column].max()
        min_value = df[target_column].min()
        
        # Create gauge chart for total
        fig = go.Figure()
        
        # Add gauge
        fig.add_trace(go.Indicator(
            mode = "gauge+number+delta",
            value = total_value,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': f"Total {kpi.title()}"},
            gauge = {
                'axis': {'range': [None, max_value * 1.2]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, total_value * 0.5], 'color': "lightgray"},
                    {'range': [total_value * 0.5, total_value * 0.8], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': avg_value
                }
            }
        ))
        
        # Add annotations with key metrics
        fig.add_annotation(
            x=0.5, y=0.1,
            text=f"Avg: {avg_value:,.2f}<br>Median: {median_value:,.2f}<br>Max: {max_value:,.2f}",
            showarrow=False,
            font=dict(size=12)
        )
        
        fig.update_layout(
            title=f"{kpi.title()} Summary Dashboard",
            height=400
        )
        
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating summary chart: {str(e)}"})


@tool
def create_trend_chart(data: str, kpi: str) -> str:
    """
    Create a time-based trend chart for the specified KPI.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(StringIO(data))

        # Find date column
        date_col = None
        for col in df.columns:
            if any(term in col.lower() for term in ['date', 'time', 'month', 'year']):
                date_col = col
                break
        
        if not date_col:
            return json.dumps({"error": "No date column found for trend analysis"})
        
        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Convert date column to datetime
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        
        # Group by month and sum
        df['month_year'] = df[date_col].dt.to_period('M')
        monthly_data = df.groupby('month_year')[target_column].sum().reset_index()
        monthly_data['month_year'] = monthly_data['month_year'].astype(str)
        
        # Create line chart
        fig = px.line(
            monthly_data,
            x='month_year',
            y=target_column,
            title=f"{kpi.title()} Trend Over Time",
            labels={target_column: kpi.title(), 'month_year': 'Month'}
        )
        
        fig.update_traces(mode='lines+markers')
        fig.update_layout(height=400)
        fig.update_xaxes(tickangle=45)
        
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating trend chart: {str(e)}"})


class DashboardAgent:
    """LangChain agent for generating KPI-based dashboards with batch processing and rate limiting."""

    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama-3.1-8b-instant")
        self.last_api_call = 0
        self.min_delay = 2  # Minimum 2 seconds between API calls

        # Define the tools available to the agent
        self.tools = [
            create_supplier_chart,
            create_product_chart,
            create_total_summary_chart,
            create_trend_chart
        ]

        # Create the prompt template for batch processing
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a dashboard expert. Analyze data and provide brief insights.

Task: Analyze the data structure for the given KPI and provide 2-3 key insights.

Available KPIs: income, profit, cost, volume

Be very concise to avoid token limits. Maximum 50 words per insight."""),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}")
        ])

        # Create the agent
        self.agent = create_openai_tools_agent(self.llm, self.tools, self.prompt)
        self.agent_executor = AgentExecutor(agent=self.agent, tools=self.tools, verbose=False, max_iterations=2)

    def _rate_limit(self):
        """Implement rate limiting to avoid API limits."""
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call

        if time_since_last_call < self.min_delay:
            sleep_time = self.min_delay - time_since_last_call
            time.sleep(sleep_time)

        self.last_api_call = time.time()
    
    def generate_dashboard(self, data: pd.DataFrame, kpi_query: str) -> Dict[str, Any]:
        """
        Generate a dashboard based on the KPI query using df.describe() for efficient analysis.

        Args:
            data: The dataframe to analyze
            kpi_query: The KPI query (e.g., "income", "show me profit dashboard")

        Returns:
            Dictionary containing the generated charts and agent insights
        """
        try:
            # Extract KPI from query
            kpi = self._extract_kpi(kpi_query)

            # Use df.describe() approach for cost-effective analysis
            dashboard_result = self._analyze_with_describe(data, kpi, kpi_query)

            return dashboard_result

        except Exception as e:
            return {
                "success": False,
                "error": f"Error generating dashboard: {str(e)}",
                "kpi": kpi_query
            }

    def _analyze_with_describe(self, data: pd.DataFrame, kpi: str, kpi_query: str) -> Dict[str, Any]:
        """Analyze data using df.describe() for cost-effective agent analysis."""
        try:
            # Apply rate limiting
            self._rate_limit()

            # Get data description - much more efficient than sending full data!
            numeric_cols = data.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = data.select_dtypes(include=['object']).columns.tolist()

            # Very concise agent input using only summary statistics
            agent_input = f"""
            Analyze {kpi} KPI dashboard strategy:
            - Records: {len(data)}
            - Numeric cols: {numeric_cols[:3]}  # Limit to first 3
            - Categories: {categorical_cols[:3]}  # Limit to first 3

            Provide 2 key insights for {kpi} analysis in 25 words max.
            """

            # Get agent insights
            try:
                agent_result = self.agent_executor.invoke({"input": agent_input})
                agent_insights = [agent_result.get("output", "Analysis completed successfully")]
            except Exception as e:
                agent_insights = [f"Agent analysis: {kpi} dashboard generated with {len(data)} records"]

            # Generate charts using full data but without sending to agent
            charts = self._generate_all_charts(data, kpi)

            # Create final summary
            final_summary = f"""
**{kpi.upper()} Dashboard Analysis**

• Dataset: {len(data)} records analyzed
• Key columns identified: {', '.join(numeric_cols[:3])}
• Agent insight: {agent_insights[0] if agent_insights else 'Analysis complete'}
• Charts generated for comprehensive {kpi} analysis
            """.strip()

            return {
                "success": True,
                "kpi": kpi,
                "charts": charts,
                "agent_insights": agent_insights,
                "final_summary": final_summary,
                "charts_generated": len([c for c in charts.values() if not str(c).startswith("Error:")]),
                "batches_processed": 1,  # Single efficient analysis
                "successful_batches": 1,
                "total_records_analyzed": len(data),
                "method": "df.describe() optimization"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Describe-based analysis failed: {str(e)}",
                "kpi": kpi
            }

    def _generate_all_charts(self, data: pd.DataFrame, kpi: str) -> Dict[str, Any]:
        """Generate all charts using the full dataset with enhanced error handling."""
        charts = {}

        # Convert data to JSON once
        try:
            data_json = data.to_json()
        except Exception as e:
            return {
                "supplier_chart": f"Error: Data conversion failed - {str(e)}",
                "product_chart": f"Error: Data conversion failed - {str(e)}",
                "summary_chart": f"Error: Data conversion failed - {str(e)}",
                "trend_chart": f"Error: Data conversion failed - {str(e)}"
            }

        # Generate all chart types with individual error handling
        chart_configs = [
            ("supplier_chart", create_supplier_chart, {"data": data_json, "kpi": kpi, "chart_type": "bar"}),
            ("product_chart", create_product_chart, {"data": data_json, "kpi": kpi, "chart_type": "bar"}),
            ("summary_chart", create_total_summary_chart, {"data": data_json, "kpi": kpi}),
            ("trend_chart", create_trend_chart, {"data": data_json, "kpi": kpi})
        ]

        for chart_name, chart_function, chart_params in chart_configs:
            try:
                chart_result = chart_function.invoke(chart_params)

                # Validate that the result is valid JSON and not an error
                if isinstance(chart_result, str):
                    if chart_result.startswith('{"error"'):
                        charts[chart_name] = f"Chart Error: {chart_result}"
                    else:
                        # Try to parse as JSON to validate
                        try:
                            json.loads(chart_result)
                            charts[chart_name] = chart_result
                        except json.JSONDecodeError:
                            charts[chart_name] = f"Error: Invalid chart JSON format"
                else:
                    charts[chart_name] = str(chart_result)

            except Exception as e:
                charts[chart_name] = f"Error: Chart generation failed - {str(e)}"

        return charts

    def _process_data_in_batches(self, data: pd.DataFrame, kpi: str, kpi_query: str) -> List[Dict[str, Any]]:
        """Process data in smaller batches to avoid token limits."""
        batch_size = 30  # Slightly larger batch size
        batch_results = []

        # Reduce to max 2 batches to minimize API calls
        num_batches = min(2, (len(data) + batch_size - 1) // batch_size)

        for i in range(num_batches):
            # Add rate limiting between batches
            if i > 0:
                self._rate_limit()

            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(data))
            batch_data = data.iloc[start_idx:end_idx]

            # Process this batch with the agent
            batch_result = self._process_single_batch(batch_data, kpi, kpi_query, i + 1)
            batch_results.append(batch_result)

        return batch_results

    def _process_single_batch(self, batch_data: pd.DataFrame, kpi: str, kpi_query: str, batch_num: int) -> Dict[str, Any]:
        """Process a single batch of data with minimal agent calls using df.describe()."""
        try:
            # Apply rate limiting before any API call
            self._rate_limit()

            # Create a minimal data summary using df.describe() - much more efficient!
            data_description = batch_data.describe(include='all').to_dict()

            data_summary = {
                "batch_number": batch_num,
                "records": len(batch_data),
                "columns": list(batch_data.columns),
                "kpi": kpi,
                "statistics": data_description
            }

            # Use df.describe() for agent analysis - much more cost effective!
            numeric_cols = batch_data.select_dtypes(include=['number']).columns.tolist()
            stats_summary = f"Numeric columns: {numeric_cols}. Records: {len(batch_data)}."

            agent_input = f"Analyze {kpi} KPI from {stats_summary} Provide 1 insight in 15 words max."

            # Execute agent with minimal input
            try:
                agent_result = self.agent_executor.invoke({"input": agent_input})
                agent_analysis = agent_result.get("output", f"Batch {batch_num} processed successfully")
            except Exception as e:
                agent_analysis = f"Agent analysis failed: {str(e)}"

            # Generate charts using tools directly (no agent involvement)
            batch_data_json = batch_data.to_json()
            charts = {}

            # Generate specific charts per batch
            if batch_num == 1:
                # First batch: supplier and product analysis
                try:
                    supplier_chart = create_supplier_chart.invoke({
                        "data": batch_data_json,
                        "kpi": kpi,
                        "chart_type": "bar"
                    })
                    charts["supplier_chart"] = supplier_chart

                    product_chart = create_product_chart.invoke({
                        "data": batch_data_json,
                        "kpi": kpi,
                        "chart_type": "bar"
                    })
                    charts["product_chart"] = product_chart
                except Exception as e:
                    charts["supplier_chart"] = f"Error: {str(e)}"
                    charts["product_chart"] = f"Error: {str(e)}"

            else:
                # Second batch: summary and trends
                try:
                    summary_chart = create_total_summary_chart.invoke({
                        "data": batch_data_json,
                        "kpi": kpi
                    })
                    charts["summary_chart"] = summary_chart

                    trend_chart = create_trend_chart.invoke({
                        "data": batch_data_json,
                        "kpi": kpi
                    })
                    charts["trend_chart"] = trend_chart
                except Exception as e:
                    charts["summary_chart"] = f"Error: {str(e)}"
                    charts["trend_chart"] = f"Error: {str(e)}"

            return {
                "batch_number": batch_num,
                "agent_analysis": agent_analysis,
                "charts": charts,
                "data_summary": data_summary,
                "success": True
            }

        except Exception as e:
            return {
                "batch_number": batch_num,
                "error": f"Batch {batch_num} failed: {str(e)}",
                "success": False
            }

    def _combine_batch_results(self, batch_results: List[Dict[str, Any]], kpi: str) -> Dict[str, Any]:
        """Combine results from all batches into a final dashboard."""
        combined_charts = {}
        agent_insights = []
        total_records = 0
        successful_batches = 0

        for batch_result in batch_results:
            if batch_result.get("success"):
                successful_batches += 1

                # Combine charts
                batch_charts = batch_result.get("charts", {})
                combined_charts.update(batch_charts)

                # Collect agent insights
                agent_analysis = batch_result.get("agent_analysis", "")
                if agent_analysis:
                    agent_insights.append(f"Batch {batch_result.get('batch_number', 'N/A')}: {agent_analysis}")

                # Count records
                data_summary = batch_result.get("data_summary", {})
                total_records += data_summary.get("records", 0)

        # Generate final agent summary
        final_agent_summary = self._generate_final_summary(agent_insights, kpi, total_records)

        return {
            "success": True,
            "kpi": kpi,
            "charts": combined_charts,
            "agent_insights": agent_insights,
            "final_summary": final_agent_summary,
            "charts_generated": len([c for c in combined_charts.values() if not str(c).startswith("Error:")]),
            "batches_processed": len(batch_results),
            "successful_batches": successful_batches,
            "total_records_analyzed": total_records
        }

    def _generate_final_summary(self, agent_insights: List[str], kpi: str, total_records: int) -> str:
        """Generate a final summary with minimal API usage."""
        try:
            if not agent_insights:
                return f"Dashboard generated for {kpi.upper()} KPI with {total_records} records analyzed across multiple batches."

            # Apply rate limiting before final API call
            self._rate_limit()

            # Very concise input to minimize tokens
            summary_input = f"Summarize {kpi} analysis of {total_records} records in 20 words."

            try:
                summary_result = self.agent_executor.invoke({"input": summary_input})
                agent_summary = summary_result.get("output", "")
            except Exception as e:
                agent_summary = f"Agent summary failed: {str(e)}"

            # Combine agent summary with basic info
            final_summary = f"""
**{kpi.upper()} Dashboard Analysis Complete**

• {agent_summary}
• Total Records: {total_records}
• Batches Processed: {len(agent_insights)}
• Charts Generated: Multiple visualization types created
            """.strip()

            return final_summary

        except Exception as e:
            return f"Dashboard generated for {kpi.upper()} KPI with {total_records} records. Processing completed successfully."
    
    def _extract_kpi(self, query: str) -> str:
        """Extract the main KPI from the user query."""
        query_lower = query.lower()
        
        # Common KPI keywords
        kpi_keywords = {
            'income': ['income', 'revenue', 'sales', 'earnings'],
            'profit': ['profit', 'margin', 'profitability'],
            'cost': ['cost', 'expense', 'spending'],
            'volume': ['volume', 'quantity', 'units', 'count']
        }
        
        for kpi, keywords in kpi_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return kpi
        
        # Default to income if no specific KPI found
        return 'income'


# Initialize the dashboard agent
dashboard_agent = DashboardAgent()


def generate_kpi_dashboard(data: pd.DataFrame, kpi_query: str) -> Dict[str, Any]:
    """
    Main function to generate KPI dashboard.
    
    Args:
        data: The dataframe to analyze
        kpi_query: The KPI query from the user
    
    Returns:
        Dictionary containing dashboard results
    """
    return dashboard_agent.generate_dashboard(data, kpi_query)
