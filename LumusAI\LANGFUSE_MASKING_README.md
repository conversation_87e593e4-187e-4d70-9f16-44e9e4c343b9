# Langfuse Data Masking for LumusAI

## 🔒 Overview

This document explains how to use Lang<PERSON>'s data masking feature with LumusAI to protect sensitive information in your LLM traces. Data masking automatically redacts sensitive information before sending it to Langfuse, ensuring privacy compliance and data security.

## 🎯 Why Use Data Masking?

- **Privacy Compliance**: Meet GDPR, CCPA, HIPAA requirements
- **Security**: Prevent sensitive data exposure in traces
- **Peace of Mind**: Process confidential documents safely
- **Audit Trail**: Maintain observability without compromising privacy

## 🚀 Quick Start

### 1. Enable Masking

Add these variables to your `.env` file:

```env
# Enable data masking
LANGFUSE_ENABLE_MASKING=true

# Configure what to mask (all optional, defaults to true)
LANGFUSE_MASK_CREDIT_CARDS=true
LANGFUSE_MASK_EMAILS=true
LANGFUSE_MASK_PHONES=true
LANGFUSE_MASK_SECRETS=true
```

### 2. Restart Application

```bash
# Local development
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Docker Compose
docker-compose up --build
```

### 3. Verify Masking is Active

Look for this message in the logs:
```
🔒 Langfuse data masking enabled
   - Credit cards: ✅
   - Emails: ✅
   - Phones: ✅
   - Secrets: ✅
```

## 📋 What Gets Masked

| Data Type | Pattern | Example Input | Masked Output |
|-----------|---------|---------------|---------------|
| **Credit Cards** | Various formats | `4111 1111 1111 1111` | `[REDACTED_CREDIT_CARD]` |
| **Email Addresses** | Standard email format | `<EMAIL>` | `[REDACTED_EMAIL]` |
| **Phone Numbers** | US/International | `************` | `[REDACTED_PHONE]` |
| **Secret Data** | SECRET_*, CONFIDENTIAL_*, etc. | `SECRET_API_KEY` | `[REDACTED_SECRET]` |

### Detailed Patterns

#### Credit Cards
- Visa: `4xxx xxxx xxxx xxxx`
- MasterCard: `5xxx xxxx xxxx xxxx`
- American Express: `3xxx xxxxxx xxxxx`
- Discover: `6xxx xxxx xxxx xxxx`
- Various separators: spaces, hyphens, dots

#### Phone Numbers
- `(*************`
- `************`
- `************`
- `****** 123 4567`
- `5551234567`

#### Secret Data
- `SECRET_*`
- `CONFIDENTIAL_*`
- `PRIVATE_*`
- `API_KEY_*`
- `TOKEN_*`
- `PASSWORD_*`

## 🧪 Testing Masking

### Run the Demo Script

```bash
python examples/langfuse_masking_demo.py
```

This script will:
1. Process test data with sensitive information
2. Show you the difference with/without masking
3. Create traces in your Langfuse dashboard

### Run Integration Tests

```bash
python test_masking_integration.py
```

This verifies that masking is properly configured and working.

## 🔧 Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `LANGFUSE_ENABLE_MASKING` | `false` | Enable/disable all masking |
| `LANGFUSE_MASK_CREDIT_CARDS` | `true` | Mask credit card numbers |
| `LANGFUSE_MASK_EMAILS` | `true` | Mask email addresses |
| `LANGFUSE_MASK_PHONES` | `true` | Mask phone numbers |
| `LANGFUSE_MASK_SECRETS` | `true` | Mask secret/confidential data |

### Docker Compose

The `docker-compose.yml` file already includes all masking variables:

```yaml
environment:
  - LANGFUSE_ENABLE_MASKING=${LANGFUSE_ENABLE_MASKING:-false}
  - LANGFUSE_MASK_CREDIT_CARDS=${LANGFUSE_MASK_CREDIT_CARDS:-true}
  - LANGFUSE_MASK_EMAILS=${LANGFUSE_MASK_EMAILS:-true}
  - LANGFUSE_MASK_PHONES=${LANGFUSE_MASK_PHONES:-true}
  - LANGFUSE_MASK_SECRETS=${LANGFUSE_MASK_SECRETS:-true}
```

## 🔍 Verifying Masking Works

### 1. Check Application Logs

When masking is enabled, you'll see:
```
🔒 Langfuse data masking enabled
   - Credit cards: ✅
   - Emails: ✅
   - Phones: ✅
   - Secrets: ✅
```

### 2. Check Langfuse Dashboard

1. Go to your Langfuse dashboard: `http://**************:3000`
2. Process a document with sensitive data
3. View the trace - sensitive data should be masked

### 3. Compare With/Without Masking

Use the demo script to see the difference:
```bash
python examples/langfuse_masking_demo.py
```

## 🚨 Troubleshooting

### Masking Not Working

**Symptoms**: Sensitive data appears unmasked in Langfuse traces

**Solutions**:
1. Check `LANGFUSE_ENABLE_MASKING=true` in `.env`
2. Restart the application
3. Verify logs show masking is enabled
4. Run the test script: `python test_masking_integration.py`

### Performance Impact

**Symptoms**: Slower response times

**Solutions**:
- Masking adds minimal overhead (~1-2ms per request)
- If performance is critical, disable specific masking types
- Consider masking only in production environments

### Custom Patterns

**Need to mask custom data types?**

The masking function can be extended. Contact the development team to add custom regex patterns for your specific use case.

## 🛡️ Security Best Practices

1. **Always enable masking in production**
2. **Test masking with your actual data patterns**
3. **Regularly audit Langfuse traces to ensure masking is working**
4. **Use environment-specific configurations** (stricter masking in prod)
5. **Monitor application logs** for masking errors

## 📞 Support

If you encounter issues with masking:

1. **Check the logs** for masking-related messages
2. **Run the test script** to verify configuration
3. **Review this documentation** for configuration details
4. **Check Langfuse documentation**: https://langfuse.com/docs/observability/features/masking

## 🎉 Success!

With masking enabled, you can now:
- ✅ Process sensitive documents safely
- ✅ Maintain full LLM observability
- ✅ Comply with privacy regulations
- ✅ Protect customer data automatically

Your LumusAI application now provides enterprise-grade privacy protection while maintaining comprehensive observability!
