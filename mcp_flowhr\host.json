{"version": "2.0", "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle.Experimental", "version": "[4.*, 5.0.0)"}, "extensions": {"mcp": {"instructions": "Tools for time management and mathematical calculations", "serverName": "FlowHR-MCP-Server", "serverVersion": "1.0.0"}}, "functionTimeout": "00:05:00", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}}