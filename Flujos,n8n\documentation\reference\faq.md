# Frequently Asked Questions (FAQ)

## General Questions

### Q: What is this project about?
**A:** This is an AI-powered financial analysis platform that includes two main systems:
1. **Financial Analysis Project**: Analyzes Excel financial data using specialized AI agents
2. **Stock Analysis Workflow**: Analyzes stock market data with news integration

Both systems use <PERSON><PERSON><PERSON><PERSON> and LangGraph to orchestrate multiple AI agents that work together to provide comprehensive financial insights.

### Q: What AI models does the system use?
**A:** The system uses Groq-hosted models:
- **llama-3.1-8b-instant**: Fast processing tasks (query parsing, data validation)
- **llama3-70b-8192**: Complex analysis tasks (financial calculations, technical analysis)
- **gemma2-9b-it**: Creative tasks (report generation, synthesis)

### Q: Do I need to pay for AI services?
**A:** You need a Groq API key, which offers a generous free tier. Visit [console.groq.com](https://console.groq.com) to sign up for free.

### Q: Can I use this for commercial purposes?
**A:** Yes, but ensure you comply with:
- Groq's terms of service for API usage
- Yahoo Finance's terms for data usage
- Any relevant financial regulations in your jurisdiction

## Installation and Setup

### Q: What are the system requirements?
**A:** 
- Python 3.8 or higher
- 4GB RAM minimum (8GB recommended for large files)
- Internet connection for AI models and data sources
- Windows, macOS, or Linux

### Q: How do I get a Groq API key?
**A:**
1. Visit [console.groq.com](https://console.groq.com)
2. Sign up for a free account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key and add it to your `.env` file

### Q: Can I run this without internet?
**A:** No, the system requires internet for:
- AI model inference (Groq API)
- Stock data (Yahoo Finance)
- News data (DuckDuckGo)
- Python package downloads

### Q: How much disk space do I need?
**A:** 
- Base installation: ~2GB (including Python dependencies)
- Virtual environment: ~500MB
- Sample data: ~10MB
- Working space for analysis: 100MB-1GB depending on file sizes

## Financial Analysis (Excel Processing)

### Q: What Excel file formats are supported?
**A:** 
- `.xlsx` (Excel 2007+) - Recommended
- `.xls` (Excel 97-2003) - Supported
- `.csv` - Supported but less feature-rich

### Q: What should my Excel file contain?
**A:** Your file should have columns for:
- **Supplier/Vendor**: Company names
- **Voucher/Transaction ID**: Unique identifiers
- **Amount/Value**: Transaction amounts
- **Date**: Transaction dates
- **Cost** (optional): Cost of goods/services

### Q: How large can my Excel files be?
**A:** 
- **Recommended**: Under 10MB (faster processing)
- **Maximum**: 50MB (may be slow)
- **Rows**: Up to 100,000 rows efficiently
- **Columns**: Up to 50 columns

### Q: What if my data doesn't have cost information?
**A:** The system can estimate costs using configurable assumptions:
- Conservative: 80% of gross amount
- Moderate: 70% of gross amount (default)
- Aggressive: 60% of gross amount

### Q: Can I analyze data in languages other than English?
**A:** Yes! The system supports:
- Spanish (built-in sample data)
- English
- Other languages (may require prompt customization)

### Q: What financial metrics does the system calculate?
**A:**
- Gross amounts per supplier and voucher
- Profit margins and margin analysis
- Low-margin transaction identification (<10%)
- Month-over-month profitability trends
- Supplier performance rankings
- Risk assessments

## Stock Analysis

### Q: Which stock markets are supported?
**A:** Any market supported by Yahoo Finance:
- US markets (NYSE, NASDAQ)
- International markets (LSE, TSE, etc.)
- ETFs and mutual funds
- Cryptocurrencies (BTC-USD, ETH-USD, etc.)

### Q: How do I specify stock symbols?
**A:** You can use natural language:
- "análisis de Apple" → AAPL
- "reporte de Google" → GOOGL
- "Tesla stock analysis" → TSLA
- Or use direct symbols: "MSFT analysis"

### Q: What technical indicators are calculated?
**A:**
- 7-day average closing price
- 30-day trend analysis
- 200-period moving average
- Price momentum indicators
- Volume analysis (customizable)

### Q: How recent is the news data?
**A:** 
- Default: Last 5 days
- Customizable: 1-30 days
- Sources: DuckDuckGo aggregated news
- Languages: Primarily English

### Q: Can I analyze multiple stocks at once?
**A:** Yes, you can create batch analysis scripts:
```python
stocks = ['Apple', 'Microsoft', 'Google']
for stock in stocks:
    resultado = correr_modelo(f"análisis de {stock}")
```

## Technical Questions

### Q: How does the StateGraph workflow work?
**A:** StateGraph orchestrates multiple AI agents:
1. **State Management**: Shared data across all agents
2. **Parallel Processing**: Multiple agents work simultaneously
3. **Sequential Steps**: Results flow from one agent to the next
4. **Error Handling**: Graceful failure recovery

### Q: Can I customize the AI prompts?
**A:** Yes! You can modify prompts in:
- `financial_analysis_project/backend/app/agents/pandas_agents.py`
- `ejemplo.py`

### Q: How do I add new analysis features?
**A:** 
1. Create new agent classes
2. Add nodes to the StateGraph
3. Define execution flow with edges
4. Update state management

### Q: Is the system secure?
**A:** Security features include:
- API key encryption in environment variables
- Input validation and sanitization
- File upload restrictions
- No persistent data storage
- Controlled code execution in pandas agents

### Q: Can I deploy this to production?
**A:** Yes, the system supports:
- Docker containerization
- Cloud deployment (AWS, GCP, Azure)
- Load balancing
- Horizontal scaling

## Performance and Optimization

### Q: Why is analysis slow?
**A:** Common causes:
- Large Excel files (>10MB)
- Complex analysis requests
- Network latency to AI services
- Insufficient system resources

**Solutions:**
- Reduce file size
- Use faster models for initial analysis
- Implement caching
- Upgrade system resources

### Q: How can I improve accuracy?
**A:**
- Use high-quality, clean data
- Provide complete information (including costs)
- Use descriptive column names
- Standardize supplier names
- Remove empty rows/columns

### Q: Can I cache results?
**A:** Yes, implement caching:
```python
import functools

@functools.lru_cache(maxsize=100)
def cached_stock_data(ticker, period):
    return yf.download(ticker, period=period)
```

## Error Handling

### Q: What if the AI gives incorrect results?
**A:**
- Verify input data quality
- Try different models
- Adjust prompts for clarity
- Use multiple analysis runs for validation
- Cross-reference with manual calculations

### Q: How do I handle API rate limits?
**A:**
- Implement exponential backoff
- Add delays between requests
- Use caching to reduce API calls
- Monitor usage with Groq dashboard

### Q: What if Yahoo Finance is down?
**A:** The system will gracefully handle failures:
- Error messages instead of crashes
- Partial analysis with available data
- Retry mechanisms for temporary failures

## Integration and Customization

### Q: Can I integrate this with other systems?
**A:** Yes, integration options:
- REST API endpoints
- Python library imports
- Database connections
- Business Intelligence tools
- Custom web interfaces

### Q: How do I add new data sources?
**A:**
1. Create new tool functions
2. Add to agent workflows
3. Update state management
4. Handle new data formats

### Q: Can I modify the report format?
**A:** Yes, customize the ReportGenerator agent:
- Change report structure
- Add new sections
- Modify formatting
- Export to different formats (PDF, Word, etc.)

### Q: How do I add new languages?
**A:**
1. Translate prompts in agent classes
2. Update query processing logic
3. Add language-specific data sources
4. Test with sample data

## Troubleshooting

### Q: "ModuleNotFoundError" - what do I do?
**A:**
1. Ensure virtual environment is activated
2. Reinstall requirements: `pip install -r requirements.txt`
3. Check Python version compatibility
4. Verify you're in the correct directory

### Q: "GROQ_API_KEY not found" error?
**A:**
1. Create `.env` file in `financial_analysis_project/backend/`
2. Add: `GROQ_API_KEY=your_actual_key_here`
3. Restart the application
4. Verify the key is valid at console.groq.com

### Q: Excel file won't process?
**A:**
1. Check file format (.xlsx recommended)
2. Verify required columns exist
3. Remove empty rows/columns
4. Ensure dates are properly formatted
5. Try with sample data first

### Q: Stock analysis returns no data?
**A:**
1. Verify ticker symbol is correct
2. Check if stock is actively traded
3. Test internet connection
4. Try with well-known stocks (AAPL, MSFT)

## Best Practices

### Q: How should I prepare my data?
**A:**
1. **Clean Data**: Remove empty rows, standardize formats
2. **Consistent Naming**: Use consistent supplier names
3. **Complete Information**: Include all relevant columns
4. **Date Formats**: Use standard date formats
5. **Backup**: Keep original files as backup

### Q: What are the recommended workflows?
**A:**
1. **Start Small**: Test with sample data first
2. **Validate Results**: Cross-check with manual calculations
3. **Iterative Analysis**: Refine based on initial results
4. **Documentation**: Keep track of analysis parameters
5. **Regular Updates**: Keep system and dependencies updated

### Q: How do I ensure reliable results?
**A:**
1. **Data Quality**: Ensure high-quality input data
2. **Multiple Runs**: Run analysis multiple times for consistency
3. **Cross-Validation**: Compare with other analysis tools
4. **Expert Review**: Have domain experts review results
5. **Continuous Monitoring**: Monitor system performance

---

**Don't see your question?** Check our [Troubleshooting Guide](./troubleshooting.md) or create an issue with your specific question.
