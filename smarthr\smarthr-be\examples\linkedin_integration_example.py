"""
LinkedIn People Search Integration Usage Example

This example demonstrates how to use the LinkedIn people search integration system
to search for people/candidates and transform them to smartHR format.
"""

import asyncio
import logging
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import LinkedIn integration components
from models.linkedin_config import load_linkedin_config
from controllers.linkedin_workflow_orchestrator import execute_linkedin_candidate_search
from utils.linkedin_agent_communication import LinkedInAgentCommunicationProtocol


async def basic_linkedin_search_example():
    """
    Basic example of LinkedIn people/candidate search.
    """
    try:
        logger.info("=== Basic LinkedIn People Search Example ===")

        # Load configuration
        config = load_linkedin_config()

        # Define search parameters
        keywords = ["Python developer", "Software engineer", "Backend developer"]
        location = "San Francisco, CA"
        skills = ["Python", "Django", "FastAPI"]
        school = "Stanford University"
        limit = 10

        logger.info(f"Searching for people with keywords: {keywords}")
        logger.info(f"Location: {location}, Skills: {skills}, Limit: {limit}")

        # Execute search and transformation
        result = await execute_linkedin_candidate_search(
            config=config,
            keywords=keywords,
            location=location,
            skills=skills,
            school=school,
            limit=limit
        )
        
        # Process results
        if result["success"]:
            logger.info("✅ Search completed successfully!")
            
            # Search results
            search_results = result["search_results"]
            logger.info(f"📊 Found {search_results['total_results']} total results")
            logger.info(f"📋 Retrieved {search_results['profiles_returned']} profiles")
            
            # Transformation results
            if result["transformation_results"]:
                transformation_results = result["transformation_results"]
                logger.info(f"🔄 Transformed {transformation_results['candidates_transformed']} candidates")
                
                # Show sample transformed candidate
                if transformation_results["transformed_candidates"]:
                    sample_candidate = transformation_results["transformed_candidates"][0]
                    candidate_info = sample_candidate.candidate_info
                    
                    logger.info("📝 Sample transformed candidate:")
                    logger.info(f"   Name: {candidate_info.get('first_name', 'N/A')} {candidate_info.get('last_name', 'N/A')}")
                    logger.info(f"   Position: {candidate_info.get('current_position', 'N/A')}")
                    logger.info(f"   Location: {candidate_info.get('location', 'N/A')}")
                    logger.info(f"   Skills: {', '.join(candidate_info.get('skills', [])[:5])}")
                    logger.info(f"   Confidence: {sample_candidate.transformation_confidence:.2f}")
            
            # Quality assessment
            quality = result["quality_assessment"]
            logger.info(f"🎯 Overall quality score: {quality['overall_quality_score']:.2f}")
            
            if quality["recommendations"]:
                logger.info("💡 Recommendations:")
                for rec in quality["recommendations"]:
                    logger.info(f"   - {rec}")
        
        else:
            logger.error("❌ Search failed!")
            logger.error(f"Error: {result.get('error_message', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Example failed: {str(e)}")
        return None


async def advanced_linkedin_search_example():
    """
    Advanced example with multiple people search criteria and custom options.
    """
    try:
        logger.info("=== Advanced LinkedIn People Search Example ===")

        # Load configuration
        config = load_linkedin_config()

        # Define multiple search scenarios
        search_scenarios = [
            {
                "name": "Senior Python Developers",
                "keywords": ["Senior Python Developer", "Python", "Django", "FastAPI"],
                "location": "New York, NY",
                "experience_level": "senior",
                "skills": ["Python", "Django", "FastAPI"],
                "limit": 15
            },
            {
                "name": "Frontend Engineers",
                "keywords": ["Frontend Engineer", "React", "JavaScript", "TypeScript"],
                "location": "San Francisco, CA",
                "skills": ["React", "JavaScript", "TypeScript"],
                "school": "UC Berkeley",
                "limit": 10
            },
            {
                "name": "Data Scientists",
                "keywords": ["Data Scientist", "Machine Learning", "Python", "SQL"],
                "skills": ["Machine Learning", "Python", "SQL"],
                "school": "MIT",
                "limit": 12
            }
        ]
        
        all_results = []
        
        for scenario in search_scenarios:
            logger.info(f"\n🔍 Executing search: {scenario['name']}")
            
            # Extract search parameters
            search_params = {k: v for k, v in scenario.items() if k != "name"}
            
            # Execute search
            result = await execute_linkedin_candidate_search(
                config=config,
                **search_params
            )
            
            # Add scenario name to result
            result["scenario_name"] = scenario["name"]
            all_results.append(result)
            
            # Log summary
            if result["success"]:
                profiles_found = result["workflow_summary"]["profiles_found"]
                candidates_transformed = result["workflow_summary"]["candidates_transformed"]
                quality_score = result["workflow_summary"]["overall_quality_score"]
                
                logger.info(f"   ✅ Found {profiles_found} profiles, transformed {candidates_transformed}")
                logger.info(f"   📊 Quality score: {quality_score:.2f}")
            else:
                logger.error(f"   ❌ Failed: {result.get('error_message', 'Unknown error')}")
        
        # Summary of all searches
        logger.info("\n📈 Overall Summary:")
        total_profiles = sum(r["workflow_summary"]["profiles_found"] for r in all_results if r["success"])
        total_transformed = sum(r["workflow_summary"]["candidates_transformed"] for r in all_results if r["success"])
        successful_searches = sum(1 for r in all_results if r["success"])
        
        logger.info(f"   Total searches: {len(search_scenarios)}")
        logger.info(f"   Successful searches: {successful_searches}")
        logger.info(f"   Total profiles found: {total_profiles}")
        logger.info(f"   Total candidates transformed: {total_transformed}")
        
        return all_results
        
    except Exception as e:
        logger.error(f"Advanced example failed: {str(e)}")
        return None


async def linkedin_api_methods_example():
    """
    Example demonstrating different LinkedIn API methods.
    """
    try:
        logger.info("=== LinkedIn API Methods Example ===")

        # Load configuration
        config = load_linkedin_config()

        # 1. FINDER Method - Search for people
        logger.info("\n1️⃣  Testing FINDER method (people search)")
        test_keywords = ["Software Engineer"]
        test_limit = 3

        logger.info(f"Searching with keywords: {test_keywords}, limit: {test_limit}")

        # Execute FINDER search
        search_result = await execute_linkedin_candidate_search(
            config=config,
            keywords=test_keywords,
            limit=test_limit
        )

        if search_result["success"]:
            logger.info("✅ FINDER method successful!")
            profiles_found = search_result["workflow_summary"]["profiles_found"]
            logger.info(f"📊 Found {profiles_found} profiles using FINDER method")

            # Extract profile IDs for testing other methods
            if search_result.get("search_results") and search_result["search_results"].get("profiles"):
                profile_ids = []
                for profile in search_result["search_results"]["profiles"][:2]:  # Take first 2
                    if hasattr(profile, 'id') and profile.id:
                        profile_ids.append(profile.id)

                if profile_ids:
                    # 2. GET Method - Get single profile
                    logger.info(f"\n2️⃣  Testing GET method for profile: {profile_ids[0]}")

                    from utils.linkedin_client import LinkedInAPIClient
                    client = LinkedInAPIClient(config)

                    single_profile = await client.get_person_by_id(profile_ids[0])
                    if single_profile:
                        logger.info("✅ GET method successful!")
                        logger.info(f"📋 Retrieved profile: {single_profile.first_name} {single_profile.last_name}")
                    else:
                        logger.warning("❌ GET method failed")

                    # 3. BATCH_GET Method - Get multiple profiles
                    if len(profile_ids) > 1:
                        logger.info(f"\n3️⃣  Testing BATCH_GET method for {len(profile_ids)} profiles")

                        batch_profiles = await client.get_people_by_ids(profile_ids)
                        if batch_profiles:
                            logger.info("✅ BATCH_GET method successful!")
                            logger.info(f"📊 Retrieved {len(batch_profiles)} profiles in batch")

                            for i, profile in enumerate(batch_profiles):
                                logger.info(f"   {i+1}. {profile.first_name} {profile.last_name}")
                        else:
                            logger.warning("❌ BATCH_GET method failed")
                else:
                    logger.warning("No profile IDs available for GET/BATCH_GET testing")
        else:
            logger.error("❌ FINDER method failed!")
            logger.error(f"Error: {search_result.get('error_message', 'Unknown error')}")

        return search_result


async def linkedin_api_testing_example():
    """
    Example for testing LinkedIn API integration.
    """
    try:
        logger.info("=== LinkedIn API Testing Example ===")

        # Load configuration
        config = load_linkedin_config()

        # Test with minimal search
        test_keywords = ["Software Engineer"]
        test_limit = 3

        logger.info(f"Testing with keywords: {test_keywords}, limit: {test_limit}")

        # Execute test search
        result = await execute_linkedin_candidate_search(
            config=config,
            keywords=test_keywords,
            limit=test_limit
        )
        
        # Analyze test results
        if result["success"]:
            logger.info("✅ API test successful!")
            
            # Check API response time
            processing_time = result["processing_time_ms"]
            logger.info(f"⏱️  Processing time: {processing_time:.0f}ms")
            
            # Check data quality
            quality_score = result["quality_assessment"]["overall_quality_score"]
            logger.info(f"🎯 Data quality: {quality_score:.2f}")
            
            # Check transformation success
            if result["transformation_results"]:
                transformation_success = result["transformation_results"]["success"]
                candidates_count = result["transformation_results"]["candidates_transformed"]
                logger.info(f"🔄 Transformation: {'✅ Success' if transformation_success else '❌ Failed'}")
                logger.info(f"📊 Candidates transformed: {candidates_count}")
            
            # API provider info
            metadata = result["metadata"]
            api_provider = metadata["config_summary"]["api_provider"]
            logger.info(f"🔌 API Provider: {api_provider}")
            
        else:
            logger.error("❌ API test failed!")
            logger.error(f"Error: {result.get('error_message', 'Unknown error')}")
            
            # Check if it's a configuration issue
            if "not configured" in result.get("error_message", "").lower():
                logger.info("💡 Tip: Make sure LinkedIn integration is properly configured")
                logger.info("   Check environment variables and configuration files")
        
        return result
        
    except Exception as e:
        logger.error(f"API test failed: {str(e)}")
        return None


def print_candidate_details(candidate_info: Dict[str, Any], confidence: float):
    """
    Helper function to print candidate details in a formatted way.
    """
    print(f"\n👤 Candidate Profile (Confidence: {confidence:.2f})")
    print(f"   Name: {candidate_info.get('first_name', 'N/A')} {candidate_info.get('last_name', 'N/A')}")
    print(f"   Email: {candidate_info.get('email', 'N/A')}")
    print(f"   Phone: {candidate_info.get('phone', 'N/A')}")
    print(f"   Location: {candidate_info.get('location', 'N/A')}")
    print(f"   Current Position: {candidate_info.get('current_position', 'N/A')}")
    
    # Professional summary (truncated)
    summary = candidate_info.get('professional_summary', 'N/A')
    if len(summary) > 100:
        summary = summary[:100] + "..."
    print(f"   Summary: {summary}")
    
    # Experience
    experience = candidate_info.get('experience', [])
    if experience:
        print(f"   Recent Experience:")
        for i, exp in enumerate(experience[:2]):  # Show first 2 experiences
            print(f"     {i+1}. {exp.get('title', 'N/A')} at {exp.get('company', 'N/A')}")
    
    # Skills
    skills = candidate_info.get('skills', [])
    if skills:
        skills_str = ", ".join(skills[:8])  # Show first 8 skills
        if len(skills) > 8:
            skills_str += f" (+{len(skills) - 8} more)"
        print(f"   Skills: {skills_str}")
    
    # LinkedIn URL
    linkedin_url = candidate_info.get('linkedin_url', 'N/A')
    print(f"   LinkedIn: {linkedin_url}")


async def main():
    """
    Main function to run all examples.
    """
    logger.info("🚀 LinkedIn Integration Examples")
    logger.info("=" * 50)
    
    try:
        # Run basic example
        logger.info("\n1️⃣  Running Basic Search Example...")
        basic_result = await basic_linkedin_search_example()
        
        # Run API methods test
        logger.info("\n2️⃣  Running API Methods Example...")
        methods_result = await linkedin_api_methods_example()

        # Run API test
        logger.info("\n3️⃣  Running API Test Example...")
        test_result = await linkedin_api_testing_example()

        # Run advanced example (commented out by default to avoid too many API calls)
        # logger.info("\n4️⃣  Running Advanced Search Example...")
        # advanced_result = await advanced_linkedin_search_example()
        
        logger.info("\n✨ Examples completed!")
        
        # Show detailed results for one successful search
        if basic_result and basic_result["success"]:
            transformation_results = basic_result.get("transformation_results")
            if transformation_results and transformation_results["transformed_candidates"]:
                logger.info("\n📋 Detailed Candidate Examples:")
                
                # Show first 2 candidates in detail
                for i, candidate in enumerate(transformation_results["transformed_candidates"][:2]):
                    print_candidate_details(candidate.candidate_info, candidate.transformation_confidence)
        
    except Exception as e:
        logger.error(f"Examples failed: {str(e)}")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
