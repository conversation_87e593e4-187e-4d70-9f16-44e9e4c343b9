# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database
sqlalchemy[asyncio]>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0

# LLM and AI
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-core>=0.1.0
langgraph>=0.0.40
openai>=1.0.0

# Data Validation and Parsing
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Web Scraping
httpx>=0.25.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
playwright>=1.40.0

# Utilities
python-dotenv>=1.0.0
python-multipart>=0.0.6
aiofiles>=23.2.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# Optional: For local LLM support
# ollama>=0.1.0
