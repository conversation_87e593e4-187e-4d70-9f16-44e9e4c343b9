#!/usr/bin/env python3
"""
Quick test to verify the callback fix is working.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_callback_fix():
    """Test that the callback fix is working by calling a simple endpoint."""
    
    print("🔧 Testing Callback Fix for Langfuse Integration")
    print("=" * 60)
    
    # Simple CV test data with sensitive information to test masking later
    test_data = """
    Jane Smith
    Data Scientist
    
    Email: <EMAIL>
    Phone: ************
    Credit Card: 4532 1234 5678 9012
    SECRET_TOKEN: SECRET_ABC123
    
    Experience:
    - Data Scientist at AI Corp (2021-2024)
    - Analyst at DataTech (2019-2021)
    
    Skills: Python, Machine Learning, SQL
    """
    
    print("📞 Testing /process endpoint with CV data...")
    print(f"📊 Data length: {len(test_data)} characters")
    
    try:
        # Make the request
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "cv",
                "data": test_data
            },
            timeout=30
        )
        
        print(f"\n📋 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check token usage to confirm processing happened
            if 'token_usage' in result:
                tokens = result['token_usage']
                print(f"🎯 Tokens used: {tokens.get('total_tokens', 'N/A')}")
                print(f"💰 Cost: ${tokens.get('cost', 0):.4f}")
            
            print("\n🔍 What to check now:")
            print("1. Application logs should show:")
            print("   - '🔍 _get_callbacks: Added Langfuse handler, total callbacks: 1'")
            print("   - '🔍 get_structured_data: Using 1 callback(s) for LLM call'")
            print("   - '🔍 About to call structured_model.ainvoke with 1 callbacks'")
            print("   - '🔍 structured_model.ainvoke completed successfully'")
            
            print("\n2. Langfuse dashboard should show new trace:")
            print("   - URL: http://157.230.167.30:3000")
            print("   - Look for traces with current timestamp")
            print("   - Should show the CV processing operation")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False

def main():
    """Main test function."""
    
    # Check service health
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Run the test
    success = test_callback_fix()
    
    print("\n" + "=" * 60)
    print("🎯 Summary")
    print("=" * 60)
    
    if success:
        print("✅ Test completed successfully!")
        print("📋 Next steps:")
        print("1. Check application logs for debug messages")
        print("2. Check Langfuse dashboard for new traces")
        print("3. If traces appear, the callback fix worked!")
        print("4. If traces appear, we can re-enable masking")
    else:
        print("❌ Test failed")
        print("🔧 Troubleshooting:")
        print("1. Check if the service is running")
        print("2. Check application logs for errors")
        print("3. Verify environment variables are set")
    
    print("\n🔍 Expected behavior after fix:")
    print("- Debug logs should show callbacks being used")
    print("- Langfuse dashboard should show traces immediately")
    print("- Processing should work normally")

if __name__ == "__main__":
    main()
