# 💰 Financial Analysis Features

Comprehensive guide to the financial analysis capabilities of the system.

## 🎯 Core Analysis Features

### 1. Gross Amount Analysis
**Purpose**: Calculate total revenue by different dimensions

#### Supplier-Level Analysis
- **Total Gross Amount per Supplier**: Sum of all transactions
- **Transaction Count**: Number of vouchers per supplier
- **Average Transaction Size**: Mean transaction value
- **Supplier Ranking**: Ordered by total revenue

**Example Output:**
```
ABC Corporation: $125,000 (25 transactions)
XYZ Industries: $98,500 (18 transactions)
Global Tech: $87,200 (22 transactions)
```

#### Voucher-Level Analysis
- **Individual Transaction Details**: Each voucher's value
- **Transaction Distribution**: Value ranges and frequencies
- **Outlier Detection**: Unusually large/small transactions

### 2. Margin Analysis
**Purpose**: Calculate and analyze profit margins

#### Margin Calculation Methods

##### When Cost Data is Available
```
Margin % = ((Gross Amount - Cost) / Gross Amount) × 100
```

##### When Cost Data is Missing
```
Assumed Cost = Gross Amount × (Cost Assumption %)
Margin % = ((Gross Amount - Assumed Cost) / Gross Amount) × 100
```

**Default Cost Assumption**: 70% of gross amount

#### Margin Categories
- **High Margin**: > 20%
- **Normal Margin**: 10% - 20%
- **Low Margin**: 0% - 10% (flagged)
- **Negative Margin**: < 0% (critical)

#### Risk Analysis
- **Low Margin Transactions**: Below configurable threshold
- **Negative Margin Transactions**: Loss-making transactions
- **Margin Distribution**: Statistical analysis of margin spread

### 3. Trend Analysis
**Purpose**: Identify patterns and trends over time

#### Month-over-Month Analysis
- **Revenue Trends**: Monthly gross amount changes
- **Supplier Performance**: Individual supplier trends
- **Seasonality Detection**: Recurring patterns
- **Growth Rates**: Percentage changes between periods

#### Trend Indicators
- **Growing**: Consistent upward trend
- **Declining**: Consistent downward trend
- **Stable**: Minimal variation
- **Volatile**: High variation

### 4. Supplier Performance Analysis
**Purpose**: Evaluate supplier relationships and performance

#### Performance Metrics
- **Revenue Contribution**: Percentage of total revenue
- **Transaction Frequency**: How often they transact
- **Average Margin**: Profitability per supplier
- **Reliability Score**: Consistency of transactions

#### Supplier Categories
- **Key Suppliers**: High revenue, high frequency
- **Growth Suppliers**: Increasing trend
- **Risk Suppliers**: Low/negative margins
- **Declining Suppliers**: Decreasing trend

## 📊 Analysis Outputs

### 1. Executive Dashboard
**Key Performance Indicators (KPIs)**
- Total Records Processed
- Total Gross Amount
- Number of Suppliers
- Average Margin
- Risk Transaction Count

### 2. Supplier Summary Report
**For Each Supplier:**
```json
{
  "supplier_name": "ABC Corporation",
  "total_gross_amount": 125000.50,
  "total_transactions": 25,
  "average_margin": 23.5,
  "low_margin_transactions": 2,
  "negative_margin_transactions": 0,
  "trend": "growing",
  "risk_level": "low"
}
```

### 3. Risk Analysis Report
**Problematic Transactions:**
- Voucher ID and details
- Supplier information
- Margin percentage
- Risk category
- Recommended actions

### 4. Trend Analysis Report
**Time Series Data:**
- Monthly revenue totals
- Supplier performance over time
- Margin trends
- Growth rate calculations

## 🔍 Data Processing Pipeline

### 1. Data Ingestion
```mermaid
graph LR
    Excel[📄 Excel File] --> Validate[✅ Validation]
    Validate --> Clean[🧹 Data Cleaning]
    Clean --> Map[🗺️ Column Mapping]
    Map --> Process[⚙️ Processing]
```

#### Column Mapping
The system automatically maps various column names:

| Standard Name | Accepted Variations |
|---------------|-------------------|
| **supplier** | vendor, proveedor, supplier_name, vendor_name |
| **voucher** | invoice, factura, comprobante, voucher_id, invoice_id |
| **gross_amount** | amount, total, importe, monto, gross |
| **cost** | cost_amount, costo, cost_price |
| **date** | transaction_date, fecha, invoice_date |

### 2. Data Validation
**Quality Checks:**
- Required columns present
- Data type validation
- Range validation (no negative amounts)
- Duplicate detection
- Missing value analysis

### 3. Data Cleaning
**Cleaning Operations:**
- Remove currency symbols
- Standardize number formats
- Clean supplier names (trim, title case)
- Handle missing dates
- Remove empty rows

## 🤖 AI-Powered Analysis

### LangChain Agents Integration
The system uses specialized AI agents for different analysis tasks:

#### 1. Excel Processor Agent
- **Model**: llama-3.1-8b-instant
- **Task**: Data validation and preprocessing
- **Output**: Data quality assessment

#### 2. Gross Amount Analyzer
- **Model**: llama3-70b-8192
- **Task**: Revenue calculations and aggregations
- **Output**: Supplier and voucher summaries

#### 3. Margin Calculator
- **Model**: llama3-70b-8192
- **Task**: Profit margin analysis
- **Output**: Margin calculations and risk identification

#### 4. Trend Analyzer
- **Model**: llama3-70b-8192
- **Task**: Time series analysis
- **Output**: Trend patterns and growth rates

#### 5. Report Generator
- **Model**: gemma2-9b-it
- **Task**: Natural language report generation
- **Output**: Executive summary and insights

## ⚙️ Configuration Options

### Analysis Parameters

#### Cost Assumption Settings
```json
{
  "assume_cost_percentage": 70.0,
  "description": "Percentage of gross amount to assume as cost when missing",
  "range": "0.0 - 100.0",
  "default": 70.0
}
```

#### Margin Threshold Settings
```json
{
  "low_margin_threshold": 10.0,
  "description": "Threshold below which margins are flagged as low",
  "range": "0.0 - 50.0",
  "default": 10.0
}
```

### Data Processing Options
- **Date Range Filtering**: Analyze specific time periods
- **Supplier Filtering**: Focus on specific suppliers
- **Amount Thresholds**: Exclude small transactions
- **Currency Handling**: Multi-currency support (future)

## 📈 Visualization Features

### 1. Interactive Charts
- **Bar Charts**: Top suppliers by revenue
- **Line Charts**: Trend analysis over time
- **Pie Charts**: Risk distribution
- **Scatter Plots**: Margin vs. volume analysis

### 2. Data Tables
- **Sortable Columns**: Click to sort by any metric
- **Filtering**: Search and filter capabilities
- **Export Options**: CSV, Excel download
- **Pagination**: Handle large datasets

### 3. Key Metrics Cards
- **Summary Statistics**: Quick overview
- **Trend Indicators**: Up/down arrows
- **Color Coding**: Green/yellow/red status
- **Tooltips**: Detailed explanations

## 🚨 Risk Management Features

### 1. Automated Risk Detection
- **Negative Margins**: Immediate flagging
- **Low Margins**: Configurable thresholds
- **Outlier Detection**: Statistical anomalies
- **Trend Warnings**: Declining performance

### 2. Risk Categorization
- **Critical**: Negative margins, major losses
- **High**: Consistently low margins
- **Medium**: Declining trends
- **Low**: Stable, profitable transactions

### 3. Recommended Actions
- **Review Pricing**: For low-margin suppliers
- **Renegotiate Terms**: For negative margins
- **Monitor Closely**: For declining trends
- **Investigate**: For outliers

## 🔮 Future Enhancements

### Planned Features
- **Predictive Analytics**: Forecast future performance
- **Benchmarking**: Compare against industry standards
- **Advanced Segmentation**: Customer/product analysis
- **Real-time Monitoring**: Live data feeds
- **Custom Alerts**: Automated notifications

### Integration Capabilities
- **ERP Systems**: Direct data integration
- **Business Intelligence**: Dashboard embedding
- **Reporting Tools**: Automated report generation
- **APIs**: Third-party system integration

---

**Next**: Learn about [Margin Calculations](margin-calculations.md) for detailed calculation methodologies.
