"""Page sampling module for ParserGPT POC."""

import asyncio
import logging
import re
from typing import Dict, List, Set, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
from .fetching import fetch_with_fallback, FetchResult
from .config import get_settings

logger = logging.getLogger(__name__)


class PageSample:
    """Represents a sampled page for adapter learning."""
    
    def __init__(self, url: str, html: str, page_type: str = "unknown"):
        self.url = url
        self.html = html
        self.page_type = page_type  # "list", "detail", "other"
        self.domain = urlparse(url).netloc
    
    def __repr__(self):
        return f"<PageSample(url={self.url}, type={self.page_type})>"


class PageSampler:
    """Discovers and samples representative pages from a domain."""
    
    def __init__(self):
        self.settings = get_settings()
    
    def _extract_links(self, html: str, base_url: str) -> List[str]:
        """Extract all links from HTML content."""
        try:
            soup = BeautifulSoup(html, 'lxml')
            links = []
            
            for link in soup.find_all('a', href=True):
                href = link['href'].strip()
                if href:
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(base_url, href)
                    links.append(absolute_url)
            
            return links
        except Exception as e:
            logger.warning(f"Error extracting links from {base_url}: {e}")
            return []
    
    def _filter_links(self, links: List[str], allowed_domains: List[str]) -> List[str]:
        """Filter links to only include allowed domains and remove duplicates."""
        filtered = []
        seen = set()
        
        for link in links:
            try:
                parsed = urlparse(link)
                domain = parsed.netloc.lower()
                
                # Check if domain is allowed
                if not any(allowed_domain.lower() in domain for allowed_domain in allowed_domains):
                    continue
                
                # Remove fragments and normalize
                clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
                if parsed.query:
                    clean_url += f"?{parsed.query}"
                
                # Skip if already seen
                if clean_url in seen:
                    continue
                
                seen.add(clean_url)
                filtered.append(clean_url)
                
            except Exception as e:
                logger.warning(f"Error parsing URL {link}: {e}")
                continue
        
        return filtered
    
    def _classify_page_type(self, url: str, html: str) -> str:
        """Classify page type based on URL patterns and content."""
        url_lower = url.lower()
        
        # List page patterns
        list_patterns = [
            r'/list', r'/category', r'/categories', r'/browse', r'/search',
            r'/index', r'/archive', r'/all', r'/directory', r'/catalog',
            r'/products', r'/items', r'/posts', r'/articles', r'/news'
        ]
        
        # Detail page patterns
        detail_patterns = [
            r'/detail', r'/view', r'/show', r'/item/', r'/product/',
            r'/post/', r'/article/', r'/page/', r'/profile/', r'/user/',
            r'/\d+', r'/id/', r'\.html$', r'\.php$'
        ]
        
        # Check URL patterns
        for pattern in list_patterns:
            if re.search(pattern, url_lower):
                return "list"
        
        for pattern in detail_patterns:
            if re.search(pattern, url_lower):
                return "detail"
        
        # Check content patterns
        try:
            soup = BeautifulSoup(html, 'lxml')
            
            # Count links - list pages typically have many links
            links = soup.find_all('a', href=True)
            if len(links) > 20:
                return "list"
            
            # Check for common detail page indicators
            if soup.find(['article', 'main']) or soup.find(class_=re.compile(r'content|detail|article')):
                return "detail"
            
        except Exception as e:
            logger.warning(f"Error analyzing content for {url}: {e}")
        
        return "other"
    
    async def sample_pages(
        self,
        seed_url: str,
        allowed_domains: List[str],
        max_samples: int = 6,
        max_depth: int = 2
    ) -> List[PageSample]:
        """
        Sample representative pages from a domain.
        
        Args:
            seed_url: Starting URL for sampling
            allowed_domains: List of allowed domains
            max_samples: Maximum number of pages to sample
            max_depth: Maximum crawl depth
            
        Returns:
            List of PageSample objects
        """
        logger.info(f"Starting page sampling from {seed_url}")
        
        samples = []
        visited = set()
        to_visit = [(seed_url, 0)]  # (url, depth)
        
        # Track page types to ensure diversity
        type_counts = {"list": 0, "detail": 0, "other": 0}
        max_per_type = max_samples // 3 + 1
        
        while to_visit and len(samples) < max_samples:
            url, depth = to_visit.pop(0)
            
            # Skip if already visited or too deep
            if url in visited or depth > max_depth:
                continue
            
            visited.add(url)
            
            # Fetch the page
            result = await fetch_with_fallback(url)
            if not result.success:
                logger.warning(f"Failed to fetch {url}: {result.error}")
                continue
            
            # Classify page type
            page_type = self._classify_page_type(url, result.html)
            
            # Add to samples if we need this type
            if type_counts[page_type] < max_per_type:
                sample = PageSample(url, result.html, page_type)
                samples.append(sample)
                type_counts[page_type] += 1
                logger.info(f"Sampled {page_type} page: {url}")
            
            # Extract links for next level (if not at max depth)
            if depth < max_depth:
                links = self._extract_links(result.html, url)
                filtered_links = self._filter_links(links, allowed_domains)
                
                # Add new links to visit queue
                for link in filtered_links[:10]:  # Limit to avoid explosion
                    if link not in visited:
                        to_visit.append((link, depth + 1))
            
            # Small delay to be respectful
            await asyncio.sleep(0.5)
        
        logger.info(f"Sampling completed: {len(samples)} pages collected")
        logger.info(f"Page type distribution: {type_counts}")
        
        return samples
    
    async def sample_from_sitemap(
        self,
        domain: str,
        max_samples: int = 6
    ) -> List[PageSample]:
        """
        Sample pages from sitemap if available.
        
        Args:
            domain: Domain to check for sitemap
            max_samples: Maximum number of pages to sample
            
        Returns:
            List of PageSample objects
        """
        sitemap_urls = [
            f"https://{domain}/sitemap.xml",
            f"https://{domain}/sitemap_index.xml",
            f"https://{domain}/robots.txt"
        ]
        
        urls_to_sample = []
        
        for sitemap_url in sitemap_urls:
            result = await fetch_with_fallback(sitemap_url)
            if result.success:
                if sitemap_url.endswith('.xml'):
                    # Parse sitemap XML
                    try:
                        soup = BeautifulSoup(result.html, 'xml')
                        for loc in soup.find_all('loc'):
                            if loc.text:
                                urls_to_sample.append(loc.text)
                    except Exception as e:
                        logger.warning(f"Error parsing sitemap {sitemap_url}: {e}")
                
                elif sitemap_url.endswith('robots.txt'):
                    # Extract sitemap URLs from robots.txt
                    for line in result.html.split('\n'):
                        if line.lower().startswith('sitemap:'):
                            sitemap_ref = line.split(':', 1)[1].strip()
                            urls_to_sample.append(sitemap_ref)
                
                break  # Use first successful sitemap
        
        if not urls_to_sample:
            logger.info(f"No sitemap found for {domain}")
            return []
        
        # Sample from discovered URLs
        sampled_urls = urls_to_sample[:max_samples]
        samples = []
        
        for url in sampled_urls:
            result = await fetch_with_fallback(url)
            if result.success:
                page_type = self._classify_page_type(url, result.html)
                sample = PageSample(url, result.html, page_type)
                samples.append(sample)
            
            await asyncio.sleep(0.5)  # Be respectful
        
        logger.info(f"Sampled {len(samples)} pages from sitemap")
        return samples


# Global sampler instance
_page_sampler = None


def get_page_sampler() -> PageSampler:
    """Get the global page sampler instance."""
    global _page_sampler
    if _page_sampler is None:
        _page_sampler = PageSampler()
    return _page_sampler


async def sample_pages(
    seed_url: str,
    allowed_domains: List[str],
    max_samples: int = 6,
    max_depth: int = 2
) -> List[PageSample]:
    """Convenience function to sample pages."""
    sampler = get_page_sampler()
    return await sampler.sample_pages(seed_url, allowed_domains, max_samples, max_depth)
