"""
Utility functions for the financial analysis backend.
"""

import pandas as pd
from typing import Dict, List, Any
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


def format_currency(amount: float, currency_symbol: str = "$") -> str:
    """Format amount as currency string."""
    return f"{currency_symbol}{amount:,.2f}"


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values."""
    if old_value == 0:
        return 0.0 if new_value == 0 else 100.0
    return ((new_value - old_value) / old_value) * 100


def validate_dataframe_columns(df: pd.DataFrame, required_columns: List[str]) -> List[str]:
    """
    Validate that DataFrame contains required columns.
    
    Args:
        df: DataFrame to validate
        required_columns: List of required column names
        
    Returns:
        List of missing columns
    """
    missing_columns = []
    for col in required_columns:
        if col not in df.columns:
            missing_columns.append(col)
    return missing_columns


def clean_supplier_name(name: str) -> str:
    """Clean and standardize supplier name."""
    if pd.isna(name):
        return "Unknown Supplier"
    
    # Convert to string and clean
    name = str(name).strip()
    
    # Remove extra whitespace
    name = " ".join(name.split())
    
    # Title case
    name = name.title()
    
    return name if name else "Unknown Supplier"


def detect_date_columns(df: pd.DataFrame) -> List[str]:
    """Detect potential date columns in DataFrame."""
    date_columns = []
    
    for col in df.columns:
        # Check column name
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['date', 'fecha', 'time', 'timestamp']):
            date_columns.append(col)
            continue
        
        # Check data type
        if df[col].dtype == 'datetime64[ns]':
            date_columns.append(col)
            continue
        
        # Try to parse a sample of values
        sample = df[col].dropna().head(10)
        if len(sample) > 0:
            try:
                pd.to_datetime(sample, errors='raise')
                date_columns.append(col)
            except:
                pass
    
    return date_columns


def generate_summary_statistics(df: pd.DataFrame, amount_column: str = 'gross_amount') -> Dict[str, Any]:
    """Generate summary statistics for financial data."""
    if amount_column not in df.columns:
        return {}
    
    amounts = df[amount_column].dropna()
    
    return {
        "count": len(amounts),
        "total": float(amounts.sum()),
        "mean": float(amounts.mean()),
        "median": float(amounts.median()),
        "std": float(amounts.std()),
        "min": float(amounts.min()),
        "max": float(amounts.max()),
        "q25": float(amounts.quantile(0.25)),
        "q75": float(amounts.quantile(0.75))
    }


def identify_outliers(df: pd.DataFrame, column: str, method: str = 'iqr') -> pd.Series:
    """
    Identify outliers in a numeric column.
    
    Args:
        df: DataFrame
        column: Column name to analyze
        method: Method to use ('iqr' or 'zscore')
        
    Returns:
        Boolean series indicating outliers
    """
    if column not in df.columns:
        return pd.Series([False] * len(df))
    
    values = df[column].dropna()
    
    if method == 'iqr':
        Q1 = values.quantile(0.25)
        Q3 = values.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        return (df[column] < lower_bound) | (df[column] > upper_bound)
    
    elif method == 'zscore':
        mean = values.mean()
        std = values.std()
        z_scores = abs((df[column] - mean) / std)
        return z_scores > 3
    
    return pd.Series([False] * len(df))


def create_pivot_summary(df: pd.DataFrame, 
                        index_col: str, 
                        value_col: str, 
                        agg_func: str = 'sum') -> pd.DataFrame:
    """Create pivot table summary."""
    try:
        return df.pivot_table(
            index=index_col,
            values=value_col,
            aggfunc=agg_func,
            fill_value=0
        ).reset_index()
    except Exception as e:
        logger.error(f"Error creating pivot summary: {str(e)}")
        return pd.DataFrame()


def format_analysis_timestamp() -> str:
    """Format current timestamp for analysis reports."""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, returning default if denominator is zero."""
    if denominator == 0:
        return default
    return numerator / denominator


def extract_month_year(date_series: pd.Series) -> pd.Series:
    """Extract YYYY-MM format from date series."""
    return pd.to_datetime(date_series).dt.to_period('M').astype(str)


def calculate_growth_rate(current: float, previous: float) -> float:
    """Calculate growth rate between two periods."""
    if previous == 0:
        return 0.0 if current == 0 else 100.0
    return ((current - previous) / previous) * 100


def validate_numeric_column(df: pd.DataFrame, column: str) -> Dict[str, Any]:
    """Validate numeric column and return validation results."""
    if column not in df.columns:
        return {"valid": False, "error": f"Column '{column}' not found"}
    
    try:
        # Try to convert to numeric
        numeric_values = pd.to_numeric(df[column], errors='coerce')
        
        # Count valid values
        valid_count = numeric_values.notna().sum()
        total_count = len(df)
        
        # Check for negative values
        negative_count = (numeric_values < 0).sum()
        
        return {
            "valid": True,
            "total_values": total_count,
            "valid_numeric": int(valid_count),
            "invalid_count": total_count - valid_count,
            "negative_values": int(negative_count),
            "validation_rate": (valid_count / total_count) * 100 if total_count > 0 else 0
        }
        
    except Exception as e:
        return {"valid": False, "error": str(e)}
