"""
Monitoring and metrics utilities for ideal candidate functionality.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from contextlib import contextmanager
import psycopg2

from core.config import settings

logger = logging.getLogger(__name__)


class IdealCandidateMetrics:
    """Class for tracking ideal candidate metrics and performance."""
    
    def __init__(self):
        self.metrics = {
            "generation_requests": 0,
            "generation_successes": 0,
            "generation_failures": 0,
            "generation_total_time_ms": 0,
            "matching_requests": 0,
            "matching_successes": 0,
            "matching_failures": 0,
            "matching_total_time_ms": 0,
            "embedding_generations": 0,
            "embedding_failures": 0,
            "embedding_total_time_ms": 0
        }
    
    def record_generation_request(self):
        """Record an ideal candidate generation request."""
        self.metrics["generation_requests"] += 1
        logger.info("Ideal candidate generation request recorded")
    
    def record_generation_success(self, duration_ms: int):
        """Record a successful ideal candidate generation."""
        self.metrics["generation_successes"] += 1
        self.metrics["generation_total_time_ms"] += duration_ms
        logger.info(f"Ideal candidate generation success recorded: {duration_ms}ms")
    
    def record_generation_failure(self, error: str):
        """Record a failed ideal candidate generation."""
        self.metrics["generation_failures"] += 1
        logger.error(f"Ideal candidate generation failure recorded: {error}")
    
    def record_matching_request(self):
        """Record an ideal candidate matching request."""
        self.metrics["matching_requests"] += 1
        logger.info("Ideal candidate matching request recorded")
    
    def record_matching_success(self, duration_ms: int):
        """Record a successful ideal candidate matching."""
        self.metrics["matching_successes"] += 1
        self.metrics["matching_total_time_ms"] += duration_ms
        logger.info(f"Ideal candidate matching success recorded: {duration_ms}ms")
    
    def record_matching_failure(self, error: str):
        """Record a failed ideal candidate matching."""
        self.metrics["matching_failures"] += 1
        logger.error(f"Ideal candidate matching failure recorded: {error}")
    
    def record_embedding_generation(self, duration_ms: int):
        """Record an embedding generation."""
        self.metrics["embedding_generations"] += 1
        self.metrics["embedding_total_time_ms"] += duration_ms
        logger.info(f"Embedding generation recorded: {duration_ms}ms")
    
    def record_embedding_failure(self, error: str):
        """Record a failed embedding generation."""
        self.metrics["embedding_failures"] += 1
        logger.error(f"Embedding generation failure recorded: {error}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        # Calculate averages
        avg_generation_time = (
            self.metrics["generation_total_time_ms"] / self.metrics["generation_successes"]
            if self.metrics["generation_successes"] > 0 else 0
        )
        
        avg_matching_time = (
            self.metrics["matching_total_time_ms"] / self.metrics["matching_successes"]
            if self.metrics["matching_successes"] > 0 else 0
        )
        
        avg_embedding_time = (
            self.metrics["embedding_total_time_ms"] / self.metrics["embedding_generations"]
            if self.metrics["embedding_generations"] > 0 else 0
        )
        
        generation_success_rate = (
            self.metrics["generation_successes"] / self.metrics["generation_requests"] * 100
            if self.metrics["generation_requests"] > 0 else 0
        )
        
        matching_success_rate = (
            self.metrics["matching_successes"] / self.metrics["matching_requests"] * 100
            if self.metrics["matching_requests"] > 0 else 0
        )
        
        embedding_success_rate = (
            (self.metrics["embedding_generations"] / 
             (self.metrics["embedding_generations"] + self.metrics["embedding_failures"]) * 100)
            if (self.metrics["embedding_generations"] + self.metrics["embedding_failures"]) > 0 else 0
        )
        
        return {
            **self.metrics,
            "avg_generation_time_ms": avg_generation_time,
            "avg_matching_time_ms": avg_matching_time,
            "avg_embedding_time_ms": avg_embedding_time,
            "generation_success_rate_percent": generation_success_rate,
            "matching_success_rate_percent": matching_success_rate,
            "embedding_success_rate_percent": embedding_success_rate,
            "timestamp": datetime.now().isoformat()
        }
    
    def reset_metrics(self):
        """Reset all metrics to zero."""
        for key in self.metrics:
            self.metrics[key] = 0
        logger.info("Ideal candidate metrics reset")


# Global metrics instance
ideal_candidate_metrics = IdealCandidateMetrics()


@contextmanager
def monitor_generation_request():
    """Context manager for monitoring ideal candidate generation requests."""
    ideal_candidate_metrics.record_generation_request()
    start_time = time.time()
    
    try:
        yield
        duration_ms = int((time.time() - start_time) * 1000)
        ideal_candidate_metrics.record_generation_success(duration_ms)
    except Exception as e:
        ideal_candidate_metrics.record_generation_failure(str(e))
        raise


@contextmanager
def monitor_matching_request():
    """Context manager for monitoring ideal candidate matching requests."""
    ideal_candidate_metrics.record_matching_request()
    start_time = time.time()
    
    try:
        yield
        duration_ms = int((time.time() - start_time) * 1000)
        ideal_candidate_metrics.record_matching_success(duration_ms)
    except Exception as e:
        ideal_candidate_metrics.record_matching_failure(str(e))
        raise


@contextmanager
def monitor_embedding_generation():
    """Context manager for monitoring embedding generation."""
    start_time = time.time()
    
    try:
        yield
        duration_ms = int((time.time() - start_time) * 1000)
        ideal_candidate_metrics.record_embedding_generation(duration_ms)
    except Exception as e:
        ideal_candidate_metrics.record_embedding_failure(str(e))
        raise


def log_ideal_candidate_activity(
    activity_type: str,
    position_id: Optional[str] = None,
    candidate_id: Optional[str] = None,
    ideal_candidate_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    success: bool = True,
    error_message: Optional[str] = None
):
    """
    Log ideal candidate activity with structured logging.
    
    Args:
        activity_type: Type of activity (generation, matching, embedding, etc.)
        position_id: Position ID if applicable
        candidate_id: Candidate ID if applicable
        ideal_candidate_id: Ideal candidate ID if applicable
        details: Additional details to log
        success: Whether the activity was successful
        error_message: Error message if applicable
    """
    log_data = {
        "activity_type": activity_type,
        "success": success,
        "timestamp": datetime.now().isoformat()
    }
    
    if position_id:
        log_data["position_id"] = position_id
    
    if candidate_id:
        log_data["candidate_id"] = candidate_id
    
    if ideal_candidate_id:
        log_data["ideal_candidate_id"] = ideal_candidate_id
    
    if details:
        log_data["details"] = details
    
    if error_message:
        log_data["error_message"] = error_message
    
    if success:
        logger.info(f"Ideal candidate activity: {activity_type}", extra={"custom_dimensions": log_data})
    else:
        logger.error(f"Ideal candidate activity failed: {activity_type}", extra={"custom_dimensions": log_data})


def get_database_health_metrics() -> Dict[str, Any]:
    """
    Get health metrics for ideal candidate database operations.
    
    Returns:
        Dictionary containing database health metrics
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        # Get total ideal candidates
        cur.execute("SELECT COUNT(*) FROM ideal_candidates_smarthr WHERE is_active = true")
        total_ideal_candidates = cur.fetchone()[0]
        
        # Get ideal candidates with embeddings
        cur.execute("SELECT COUNT(*) FROM ideal_candidates_smarthr WHERE is_active = true AND embedding_generated = true")
        with_embeddings = cur.fetchone()[0]
        
        # Get recent activity (last 24 hours)
        cur.execute("""
            SELECT COUNT(*) FROM ideal_candidates_smarthr 
            WHERE is_active = true AND created_at >= NOW() - INTERVAL '24 hours'
        """)
        recent_creations = cur.fetchone()[0]
        
        # Get average generation time (if we had a performance log table)
        # For now, we'll use placeholder values
        
        cur.close()
        conn.close()
        
        health_metrics = {
            "database_connection": "healthy",
            "total_ideal_candidates": total_ideal_candidates,
            "ideal_candidates_with_embeddings": with_embeddings,
            "embedding_coverage_percent": (with_embeddings / total_ideal_candidates * 100) if total_ideal_candidates > 0 else 0,
            "recent_creations_24h": recent_creations,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info("Database health metrics retrieved", extra={"custom_dimensions": health_metrics})
        return health_metrics
        
    except Exception as e:
        error_metrics = {
            "database_connection": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        logger.error("Failed to retrieve database health metrics", extra={"custom_dimensions": error_metrics})
        return error_metrics


def get_performance_summary(hours: int = 24) -> Dict[str, Any]:
    """
    Get a performance summary for ideal candidate operations.
    
    Args:
        hours: Number of hours to look back for performance data
        
    Returns:
        Dictionary containing performance summary
    """
    try:
        # Get current metrics
        current_metrics = ideal_candidate_metrics.get_metrics()
        
        # Get database health
        db_health = get_database_health_metrics()
        
        # Combine into performance summary
        performance_summary = {
            "period_hours": hours,
            "timestamp": datetime.now().isoformat(),
            "generation_metrics": {
                "requests": current_metrics["generation_requests"],
                "successes": current_metrics["generation_successes"],
                "failures": current_metrics["generation_failures"],
                "success_rate_percent": current_metrics["generation_success_rate_percent"],
                "avg_time_ms": current_metrics["avg_generation_time_ms"]
            },
            "matching_metrics": {
                "requests": current_metrics["matching_requests"],
                "successes": current_metrics["matching_successes"],
                "failures": current_metrics["matching_failures"],
                "success_rate_percent": current_metrics["matching_success_rate_percent"],
                "avg_time_ms": current_metrics["avg_matching_time_ms"]
            },
            "embedding_metrics": {
                "generations": current_metrics["embedding_generations"],
                "failures": current_metrics["embedding_failures"],
                "success_rate_percent": current_metrics["embedding_success_rate_percent"],
                "avg_time_ms": current_metrics["avg_embedding_time_ms"]
            },
            "database_health": db_health
        }
        
        logger.info("Performance summary generated", extra={"custom_dimensions": performance_summary})
        return performance_summary
        
    except Exception as e:
        error_summary = {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        logger.error("Failed to generate performance summary", extra={"custom_dimensions": error_summary})
        return error_summary


def alert_on_high_failure_rate(threshold_percent: float = 20.0):
    """
    Check for high failure rates and log alerts.
    
    Args:
        threshold_percent: Failure rate threshold for alerting
    """
    try:
        metrics = ideal_candidate_metrics.get_metrics()
        
        # Check generation failure rate
        generation_failure_rate = 100 - metrics["generation_success_rate_percent"]
        if generation_failure_rate > threshold_percent and metrics["generation_requests"] > 5:
            alert_data = {
                "alert_type": "high_generation_failure_rate",
                "failure_rate_percent": generation_failure_rate,
                "threshold_percent": threshold_percent,
                "total_requests": metrics["generation_requests"],
                "failures": metrics["generation_failures"]
            }
            logger.warning("HIGH FAILURE RATE ALERT: Ideal candidate generation", extra={"custom_dimensions": alert_data})
        
        # Check matching failure rate
        matching_failure_rate = 100 - metrics["matching_success_rate_percent"]
        if matching_failure_rate > threshold_percent and metrics["matching_requests"] > 5:
            alert_data = {
                "alert_type": "high_matching_failure_rate",
                "failure_rate_percent": matching_failure_rate,
                "threshold_percent": threshold_percent,
                "total_requests": metrics["matching_requests"],
                "failures": metrics["matching_failures"]
            }
            logger.warning("HIGH FAILURE RATE ALERT: Ideal candidate matching", extra={"custom_dimensions": alert_data})
        
        # Check embedding failure rate
        embedding_failure_rate = 100 - metrics["embedding_success_rate_percent"]
        if embedding_failure_rate > threshold_percent and (metrics["embedding_generations"] + metrics["embedding_failures"]) > 5:
            alert_data = {
                "alert_type": "high_embedding_failure_rate",
                "failure_rate_percent": embedding_failure_rate,
                "threshold_percent": threshold_percent,
                "total_attempts": metrics["embedding_generations"] + metrics["embedding_failures"],
                "failures": metrics["embedding_failures"]
            }
            logger.warning("HIGH FAILURE RATE ALERT: Embedding generation", extra={"custom_dimensions": alert_data})
        
    except Exception as e:
        logger.error(f"Error checking failure rates for alerts: {e}")
