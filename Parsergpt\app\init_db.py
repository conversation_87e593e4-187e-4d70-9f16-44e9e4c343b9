"""Database initialization script for ParserGPT POC."""

import asyncio
import logging
from typing import Optional
from sqlalchemy import text
from .database import init_database, get_session, DatabaseSession
from .models import Job, Page, Extraction, Adapter
from .config import get_settings, validate_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_sample_data():
    """Create sample data for testing purposes."""
    async with DatabaseSession() as session:
        # Check if sample data already exists
        result = await session.execute(text("SELECT COUNT(*) FROM jobs"))
        job_count = result.scalar()
        
        if job_count > 0:
            logger.info("Sample data already exists, skipping creation")
            return
        
        logger.info("Creating sample data...")
        
        # Create a sample job
        sample_job = Job(
            start_url="https://example.com",
            allowed_domains="example.com",
            max_depth=2,
            max_pages=10,
            field_spec=[
                {"name": "title", "dtype": "string"},
                {"name": "description", "dtype": "string"},
                {"name": "tags", "dtype": "string[]"}
            ],
            status="created"
        )
        
        session.add(sample_job)
        await session.flush()  # Get the ID
        
        # Create a sample page
        sample_page = Page(
            job_id=sample_job.id,
            url="https://example.com/page1",
            domain="example.com",
            html_content="<html><head><title>Sample Page</title></head><body><h1>Sample Content</h1></body></html>",
            status_code=200,
            content_type="text/html",
            content_length=100,
            fetch_method="httpx"
        )
        
        session.add(sample_page)
        await session.flush()
        
        # Create a sample extraction
        sample_extraction = Extraction(
            job_id=sample_job.id,
            page_id=sample_page.id,
            extracted_data={
                "title": "Sample Page",
                "description": "This is a sample page",
                "tags": ["sample", "test"]
            },
            extraction_method="deterministic",
            confidence_score=0.95
        )
        
        session.add(sample_extraction)
        
        # Create a sample adapter
        sample_adapter = Adapter(
            domain="example.com",
            version=1,
            adapter_data={
                "domain": "example.com",
                "version": 1,
                "url_patterns": {
                    "detail": ["*/page/*"],
                    "list": ["*/list/*"]
                },
                "selectors": {
                    "title": {"css": "h1, title", "xpath": "", "regex": ""},
                    "description": {"css": "p.description", "xpath": "", "regex": ""},
                    "tags": {"css": ".tag", "xpath": "", "regex": ""}
                },
                "tests": [
                    {
                        "url": "https://example.com/page1",
                        "expects": {"title": "Sample Page"}
                    }
                ]
            },
            success_rate=0.95,
            avg_confidence=0.90,
            total_extractions=1,
            successful_extractions=1
        )
        
        session.add(sample_adapter)
        
        logger.info("Sample data created successfully")


async def check_database_connection():
    """Check if database connection is working."""
    try:
        async with DatabaseSession() as session:
            result = await session.execute(text("SELECT 1"))
            result.scalar()
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


async def reset_database():
    """Reset the database by dropping and recreating all tables."""
    from .models import Base
    from .database import engine, create_database_engine
    
    logger.warning("Resetting database - all data will be lost!")
    
    if engine is None:
        create_database_engine()
    
    async with engine.begin() as conn:
        # Drop all tables
        await conn.run_sync(Base.metadata.drop_all)
        logger.info("All tables dropped")
        
        # Recreate all tables
        await conn.run_sync(Base.metadata.create_all)
        logger.info("All tables recreated")


async def main():
    """Main initialization function."""
    logger.info("Starting database initialization...")
    
    try:
        # Validate settings
        validate_settings()
        logger.info("Settings validated")
        
        # Initialize database
        await init_database()
        logger.info("Database initialized")
        
        # Check connection
        if not await check_database_connection():
            raise Exception("Database connection check failed")
        
        # Create sample data
        await create_sample_data()
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
