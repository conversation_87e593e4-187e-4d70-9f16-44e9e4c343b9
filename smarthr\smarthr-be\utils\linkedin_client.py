import asyncio
import time
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from urllib.parse import urlencode, quote

import httpx
from httpx import AsyncClient, Response

# Internal imports
from models.linkedin import (
    LinkedInProfile,
    LinkedInSearchRequest,
    LinkedInSearchResponse,
    LinkedInSearchFilters,
    LinkedInAPIError,
    LinkedInLocation,
    LinkedInCompany,
    LinkedInExperience,
    LinkedInEducation,
    LinkedInSkill
)
from models.linkedin_config import LinkedInIntegrationConfig, LinkedInAPIProvider
from utils.linkedin_response_processor import process_linkedin_search_response

# Setup logging
logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiter for LinkedIn API requests."""
    
    def __init__(self, requests_per_minute: int = 30, requests_per_hour: int = 500):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.minute_requests = []
        self.hour_requests = []
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire rate limit permission."""
        async with self.lock:
            now = datetime.now()
            
            # Clean old requests
            self.minute_requests = [req_time for req_time in self.minute_requests 
                                  if now - req_time < timedelta(minutes=1)]
            self.hour_requests = [req_time for req_time in self.hour_requests 
                                if now - req_time < timedelta(hours=1)]
            
            # Check rate limits
            if len(self.minute_requests) >= self.requests_per_minute:
                wait_time = 60 - (now - min(self.minute_requests)).total_seconds()
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                return await self.acquire()
            
            if len(self.hour_requests) >= self.requests_per_hour:
                wait_time = 3600 - (now - min(self.hour_requests)).total_seconds()
                logger.info(f"Hourly rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                return await self.acquire()
            
            # Record request
            self.minute_requests.append(now)
            self.hour_requests.append(now)


class LinkedInAPIClient:
    """HTTP client for LinkedIn API integration."""
    
    def __init__(self, config: LinkedInIntegrationConfig):
        self.config = config
        self.rate_limiter = RateLimiter(
            config.api_config.rate_limits.requests_per_minute,
            config.api_config.rate_limits.requests_per_hour
        )
        
        # Initialize HTTP client
        self.client: Optional[AsyncClient] = None
        self._session_initialized = False
        
        logger.info(f"LinkedIn API client initialized with provider: {config.api_config.provider}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._close_session()
    
    async def _initialize_session(self) -> None:
        """Initialize HTTP session with authentication."""
        if self._session_initialized:
            return
        
        try:
            # Configure HTTP client
            timeout = httpx.Timeout(
                timeout=self.config.api_config.rate_limits.timeout_seconds,
                connect=10.0
            )
            
            headers = {
                "User-Agent": self.config.api_config.user_agent,
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
            
            # Add authentication headers based on provider
            if self.config.api_config.provider == LinkedInAPIProvider.LINKEDIN_OFFICIAL:
                if self.config.api_config.credentials.access_token:
                    headers["Authorization"] = f"Bearer {self.config.api_config.credentials.access_token}"
            
            self.client = AsyncClient(
                timeout=timeout,
                headers=headers,
                follow_redirects=True
            )
            
            self._session_initialized = True
            logger.info("LinkedIn API session initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize LinkedIn API session: {str(e)}")
            raise
    
    async def _close_session(self) -> None:
        """Close HTTP session."""
        if self.client:
            await self.client.aclose()
            self.client = None
            self._session_initialized = False
    
    async def get_person_by_id(self, profile_id: str) -> Optional[LinkedInProfile]:
        """
        Get a single LinkedIn profile by ID using GET method.

        Uses: GET https://api.linkedin.com/v2/people/{profileId}

        Args:
            profile_id: LinkedIn profile ID

        Returns:
            LinkedInProfile object or None if not found
        """
        if not profile_id:
            return None

        try:
            if self.config.api_config.provider == LinkedInAPIProvider.LINKEDIN_OFFICIAL:
                # GET method for single profile
                base_url = f"{self.config.api_config.base_url}/v2/people/{profile_id}"

                response = await self._make_request("GET", base_url)

                if response:
                    profile = self._parse_official_api_profile(response)
                    return profile
                else:
                    logger.error(f"LinkedIn GET failed for profile {profile_id}")
                    return None

            else:
                logger.warning(f"GET method not supported for provider {self.config.api_config.provider}")
                return None

        except Exception as e:
            logger.error(f"Error in GET person {profile_id}: {str(e)}")
            return None

    async def get_people_by_ids(self, profile_ids: List[str]) -> List[LinkedInProfile]:
        """
        Get multiple LinkedIn profiles by their IDs using BATCH_GET method.

        Uses: GET https://api.linkedin.com/v2/people?ids=List(id1,id2,id3)

        Args:
            profile_ids: List of LinkedIn profile IDs

        Returns:
            List of LinkedInProfile objects
        """
        if not profile_ids:
            return []

        try:
            if self.config.api_config.provider == LinkedInAPIProvider.LINKEDIN_OFFICIAL:
                # BATCH_GET method for multiple profiles
                base_url = f"{self.config.api_config.base_url}/v2/people"
                params = {
                    "ids": f"List({','.join(profile_ids)})"
                }

                response = await self._make_request("GET", base_url, params=params)

                if response and response.get("results"):
                    profiles = []

                    # Parse batch response
                    for profile_data in response["results"].values():
                        profile = self._parse_official_api_profile(profile_data)
                        profiles.append(profile)

                    return profiles
                else:
                    logger.error("LinkedIn BATCH_GET failed or returned no results")
                    return []

            else:
                # For non-official providers, fall back to individual requests
                logger.warning(f"BATCH_GET not supported for provider {self.config.api_config.provider}")
                return []

        except Exception as e:
            logger.error(f"Error in BATCH_GET people: {str(e)}")
            return []

    async def search_people(self, search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """
        Search for people on LinkedIn using FINDER method.

        Uses: GET https://api.linkedin.com/v2/people?q=search
        """
        try:
            await self.rate_limiter.acquire()
            
            if self.config.api_config.provider == LinkedInAPIProvider.MOCK_PROVIDER:
                return await self._mock_search_people(search_request)
            
            # Build search URL and parameters
            search_url, search_params = self._build_search_url(search_request)
            
            # Make API request
            response = await self._make_request("GET", search_url, params=search_params)
            
            # Parse response
            search_results = await self._parse_search_response(response, search_request)

            # Process and normalize response
            processed_results = process_linkedin_search_response(search_results)

            logger.info(f"LinkedIn search completed: {processed_results.total_results} results found")
            return processed_results
            
        except Exception as e:
            logger.error(f"LinkedIn people search failed: {str(e)}")
            raise LinkedInAPIError(
                error_code="search_failed",
                error_message=f"LinkedIn search operation failed: {str(e)}",
                error_details={"search_filters": search_request.filters.model_dump()}
            )
    
    def _build_search_url(self, search_request: LinkedInSearchRequest) -> tuple[str, Dict[str, Any]]:
        """Build search URL and parameters based on provider."""
        filters = search_request.filters
        
        if self.config.api_config.provider == LinkedInAPIProvider.LINKEDIN_OFFICIAL:
            # Official LinkedIn API using FINDER method
            # GET https://api.linkedin.com/v2/people?q=search
            base_url = f"{self.config.api_config.base_url}/v2/people"
            params = {
                "q": "search",  # FINDER method for people search
                "count": filters.limit,
                "start": filters.offset
            }

            # Add search parameters according to LinkedIn API specification
            if filters.keywords:
                params["keywords"] = filters.keywords
            if filters.location:
                params["locationName"] = filters.location
            if filters.first_name:
                params["firstName"] = filters.first_name
            if filters.last_name:
                params["lastName"] = filters.last_name
            if filters.school:
                params["school"] = filters.school
                
        else:
            # Alternative implementation for web scraping or third-party APIs
            base_url = f"{self.config.api_config.base_url}/search/results/people/"
            params = self._build_web_search_params(filters)
        
        return base_url, params
    
    def _build_web_search_params(self, filters: LinkedInSearchFilters) -> Dict[str, Any]:
        """Build search parameters for web-based LinkedIn search."""
        params = {
            "count": filters.limit,
            "start": filters.offset,
            "origin": "FACETED_SEARCH"
        }
        
        # Build facet filters
        facets = []
        
        if filters.location:
            facets.append(f"geoUrn:{quote(filters.location)}")

        if filters.experience_level:
            facets.append(f"experienceLevel:{filters.experience_level}")
        
        if facets:
            params["facetFilters"] = ",".join(facets)
        
        # Add keyword search
        if filters.keywords:
            params["keywords"] = filters.keywords
        
        return params
    
    async def _make_request(self, method: str, url: str, **kwargs) -> Response:
        """Make HTTP request with error handling and retries."""
        if not self.client:
            await self._initialize_session()
        
        max_retries = self.config.api_config.rate_limits.max_retries
        backoff_factor = self.config.api_config.rate_limits.backoff_factor
        
        for attempt in range(max_retries + 1):
            try:
                response = await self.client.request(method, url, **kwargs)
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 60))
                    logger.warning(f"Rate limited, waiting {retry_after} seconds")
                    await asyncio.sleep(retry_after)
                    continue
                
                # Handle authentication errors
                if response.status_code == 401:
                    raise LinkedInAPIError(
                        error_code="authentication_failed",
                        error_message="LinkedIn authentication failed",
                        error_details={"status_code": response.status_code}
                    )
                
                # Handle other HTTP errors
                if response.status_code >= 400:
                    error_text = await response.aread()
                    raise LinkedInAPIError(
                        error_code="http_error",
                        error_message=f"HTTP {response.status_code}: {error_text.decode()}",
                        error_details={"status_code": response.status_code}
                    )
                
                return response
                
            except httpx.TimeoutException:
                if attempt < max_retries:
                    wait_time = backoff_factor ** attempt
                    logger.warning(f"Request timeout, retrying in {wait_time} seconds")
                    await asyncio.sleep(wait_time)
                    continue
                raise LinkedInAPIError(
                    error_code="timeout_error",
                    error_message="Request timeout after all retries"
                )
            
            except httpx.NetworkError as e:
                if attempt < max_retries:
                    wait_time = backoff_factor ** attempt
                    logger.warning(f"Network error, retrying in {wait_time} seconds: {str(e)}")
                    await asyncio.sleep(wait_time)
                    continue
                raise LinkedInAPIError(
                    error_code="network_error",
                    error_message=f"Network error: {str(e)}"
                )
        
        raise LinkedInAPIError(
            error_code="max_retries_exceeded",
            error_message="Maximum retries exceeded"
        )

    async def _parse_search_response(self, response: Response, search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """Parse LinkedIn search response into structured format."""
        try:
            response_data = response.json()

            if self.config.api_config.provider == LinkedInAPIProvider.LINKEDIN_OFFICIAL:
                return await self._parse_official_api_response(response_data, search_request)
            else:
                return await self._parse_web_search_response(response_data, search_request)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LinkedIn response as JSON: {str(e)}")
            raise LinkedInAPIError(
                error_code="parsing_error",
                error_message="Invalid JSON response from LinkedIn"
            )

    async def _parse_official_api_response(self, data: Dict[str, Any], search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """Parse official LinkedIn API response."""
        profiles = []

        # Parse people results
        people_results = data.get("elements", [])
        for person_data in people_results:
            try:
                profile = self._parse_person_data(person_data)
                profiles.append(profile)
            except Exception as e:
                logger.warning(f"Failed to parse person data: {str(e)}")
                continue

        return LinkedInSearchResponse(
            search_id=search_request.search_id or f"search_{int(time.time())}",
            total_results=data.get("paging", {}).get("total", len(profiles)),
            returned_results=len(profiles),
            profiles=profiles,
            search_filters_used=search_request.filters,
            execution_time_ms=int(response.elapsed.total_seconds() * 1000) if hasattr(response, 'elapsed') else None
        )

    async def _parse_web_search_response(self, data: Dict[str, Any], search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """Parse web-based LinkedIn search response."""
        profiles = []

        # Handle different response formats from web scraping
        if "included" in data:
            # LinkedIn web API format
            people_results = [item for item in data["included"] if item.get("$type") == "com.linkedin.voyager.search.SearchProfile"]
        else:
            # Alternative format
            people_results = data.get("elements", data.get("results", []))

        for person_data in people_results:
            try:
                profile = self._parse_web_person_data(person_data)
                profiles.append(profile)
            except Exception as e:
                logger.warning(f"Failed to parse web person data: {str(e)}")
                continue

        return LinkedInSearchResponse(
            search_id=search_request.search_id or f"search_{int(time.time())}",
            total_results=data.get("paging", {}).get("total", len(profiles)),
            returned_results=len(profiles),
            profiles=profiles,
            search_filters_used=search_request.filters
        )

    def _parse_person_data(self, person_data: Dict[str, Any]) -> LinkedInProfile:
        """Parse official API person data into LinkedInProfile."""
        # Extract basic information
        first_name = person_data.get("firstName", {}).get("localized", {}).get("en_US", "")
        last_name = person_data.get("lastName", {}).get("localized", {}).get("en_US", "")
        headline = person_data.get("headline", {}).get("localized", {}).get("en_US", "")

        # Parse location
        location = None
        if "location" in person_data:
            location_data = person_data["location"]
            location = LinkedInLocation(
                name=location_data.get("name", ""),
                country_code=location_data.get("countryCode", ""),
                region=location_data.get("region", "")
            )

        # Parse experience (simplified)
        experience = []
        if "positions" in person_data:
            for pos_data in person_data["positions"].get("values", []):
                company = LinkedInCompany(
                    name=pos_data.get("company", {}).get("name", ""),
                    industry=pos_data.get("company", {}).get("industry", "")
                )

                exp = LinkedInExperience(
                    title=pos_data.get("title", ""),
                    company=company,
                    start_date=pos_data.get("startDate", {}).get("year", ""),
                    end_date=pos_data.get("endDate", {}).get("year", "") if pos_data.get("endDate") else None,
                    is_current=pos_data.get("isCurrent", False)
                )
                experience.append(exp)

        return LinkedInProfile(
            id=person_data.get("id", ""),
            first_name=first_name,
            last_name=last_name,
            headline=headline,
            location=location,
            experience=experience,
            education=[],
            skills=[]
        )

    def _parse_web_person_data(self, person_data: Dict[str, Any]) -> LinkedInProfile:
        """Parse web scraping person data into LinkedInProfile."""
        # Handle different web response formats
        if "hitInfo" in person_data:
            # Voyager API format
            hit_info = person_data["hitInfo"]
            first_name = hit_info.get("firstName", "")
            last_name = hit_info.get("lastName", "")
            headline = hit_info.get("headline", "")
            public_id = hit_info.get("publicIdentifier", "")
        else:
            # Alternative format
            first_name = person_data.get("firstName", person_data.get("first_name", ""))
            last_name = person_data.get("lastName", person_data.get("last_name", ""))
            headline = person_data.get("headline", "")
            public_id = person_data.get("publicIdentifier", person_data.get("public_id", ""))

        # Parse location
        location = None
        location_data = person_data.get("location", person_data.get("geoLocation", {}))
        if location_data:
            location = LinkedInLocation(
                name=location_data.get("name", location_data.get("displayName", "")),
                country_code=location_data.get("countryCode", ""),
                region=location_data.get("region", "")
            )

        return LinkedInProfile(
            id=person_data.get("entityUrn", person_data.get("id", "")),
            public_id=public_id,
            first_name=first_name,
            last_name=last_name,
            headline=headline,
            location=location,
            profile_url=f"https://www.linkedin.com/in/{public_id}" if public_id else None,
            experience=[],
            education=[],
            skills=[]
        )

    async def _mock_search_people(self, search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """Mock LinkedIn search for testing purposes."""
        # Create mock profiles based on search filters
        mock_profiles = []

        filters = search_request.filters
        num_results = min(filters.limit, 5)  # Limit mock results

        for i in range(num_results):
            mock_profile = LinkedInProfile(
                id=f"mock_profile_{i+1}",
                public_id=f"mock-user-{i+1}",
                first_name=f"John{i+1}" if not filters.first_name else filters.first_name,
                last_name=f"Doe{i+1}" if not filters.last_name else filters.last_name,
                headline=f"Software Engineer at Tech Company {i+1}",
                summary=f"Experienced professional with expertise in {', '.join(filters.keywords) if filters.keywords else 'technology'}",
                location=LinkedInLocation(
                    name=filters.location or filters.city or "San Francisco, CA",
                    country_code="US"
                ),
                profile_url=f"https://www.linkedin.com/in/mock-user-{i+1}",
                experience=[
                    LinkedInExperience(
                        title="Software Engineer",
                        company=LinkedInCompany(name=f"Tech Company {i+1}"),
                        is_current=True
                    )
                ],
                education=[],
                skills=[LinkedInSkill(name=skill) for skill in (filters.skills or ["Python", "JavaScript", "React"])]
            )
            mock_profiles.append(mock_profile)

        # Simulate processing delay
        await asyncio.sleep(0.1)

        return LinkedInSearchResponse(
            search_id=search_request.search_id or f"mock_search_{int(time.time())}",
            total_results=num_results,
            returned_results=num_results,
            profiles=mock_profiles,
            search_filters_used=filters,
            execution_time_ms=100
        )
