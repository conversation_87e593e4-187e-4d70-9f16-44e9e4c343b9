# User Manual
## LumusAI - Intelligent Document Processing Service

**Document Version:** 1.0  
**Date:** December 2024  
**Target Audience:** End Users, System Integrators  
**Project:** LumusAI  

---

## Table of Contents

1. [Introduction](#1-introduction)
2. [Getting Started](#2-getting-started)
3. [Document Processing](#3-document-processing)
4. [API Usage](#4-api-usage)
5. [Integration Examples](#5-integration-examples)
6. [Troubleshooting](#6-troubleshooting)
7. [Best Practices](#7-best-practices)
8. [Support](#8-support)

---

## 1. Introduction

### 1.1 What is LumusAI?
LumusAI is an intelligent document processing service that uses artificial intelligence to extract structured information from various types of documents. It can process CVs/resumes, legal documents, and invoices, converting unstructured document content into organized, machine-readable data.

### 1.2 Key Features
- **Multi-format Support:** Process PDF, DOCX, Excel, and image files
- **AI-Powered Extraction:** Advanced language models for accurate data extraction
- **Structured Output:** Consistent JSON responses with validated data models
- **Real-time Processing:** Fast document processing with concurrent handling
- **Easy Integration:** RESTful API for seamless system integration

### 1.3 Supported Document Types

#### SmartHR Module - CV/Resume Processing
- Extract personal information (name, contact details, location)
- Parse education history and qualifications
- Analyze work experience with duration calculations
- Identify technical and soft skills
- Generate professional summaries

#### Papirus Module - Legal Document Processing
- Process Colombian Tutela legal documents
- Handle contestación (response), fallo (ruling), and desacato (contempt) documents
- Extract case information and legal entities
- Process email communications related to legal cases

#### Facturius Module - Invoice Processing
- Process purchase invoices and utility bills
- Extract billing information and line items
- Handle electricity, water, and gas bills
- Process delivery tickets and credit notes
- Calculate totals and validate amounts

### 1.4 System Requirements
- Internet connection for API access
- Valid OpenAI/Azure OpenAI API credentials (for service operators)
- Supported file formats: PDF, DOCX, XLS, XLSX, PNG, JPG, JPEG, TXT
- Maximum file size: 50MB per document

## 2. Getting Started

### 2.1 Accessing LumusAI
LumusAI is accessed through its RESTful API. You can interact with the service using:
- HTTP client libraries (Python requests, JavaScript fetch, etc.)
- Command-line tools (cURL, HTTPie)
- API testing tools (Postman, Insomnia)
- Custom applications and integrations

### 2.2 Base URL
The service is typically available at:
- **Development:** `http://localhost:8000`
- **Production:** `https://your-domain.com/apis/lumusai`

### 2.3 API Documentation
Interactive API documentation is available at:
- **Swagger UI:** `{base_url}/docs`
- **ReDoc:** `{base_url}/redoc`

### 2.4 Quick Start Example

**Using cURL:**
```bash
# Process a CV
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf"

# Check service health
curl -X GET "http://localhost:8000/health"
```

**Using Python:**
```python
import requests

# Process a document
url = "http://localhost:8000/process"
files = {"file": open("resume.pdf", "rb")}
data = {"action": "cv"}

response = requests.post(url, files=files, data=data)
result = response.json()

print(f"Status: {result['status']}")
if result["status"] == "success":
    print(f"Extracted data: {result['data']}")
```

## 3. Document Processing

### 3.1 Processing Workflow

1. **Document Upload:** Submit document via API
2. **Validation:** System validates file format and size
3. **Text Extraction:** Convert document to processable text
4. **AI Analysis:** Send to language models for processing
5. **Data Structuring:** Parse AI response into structured format
6. **Response:** Return JSON with extracted data

### 3.2 Supported Actions

| Action | Description | Supported Formats |
|--------|-------------|-------------------|
| `cv` | CV/Resume processing | PDF, DOCX |
| `invoice` | Invoice processing | PDF, Excel, Images |
| `tutela_contestacion` | Legal contestation documents | PDF |
| `tutela_fallo` | Legal ruling documents | PDF |
| `tutela_desacato` | Legal contempt documents | PDF |
| `tutela_correo` | Legal email communications | TXT, PDF |

### 3.3 Input Methods

#### 3.3.1 File Upload
Upload documents directly as multipart form data:
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@document.pdf"
```

#### 3.3.2 URL Reference
Process documents from URLs:
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "data=https://example.com/document.pdf"
```

#### 3.3.3 Text Data
Process text content directly:
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=tutela_correo" \
     -F "data=Email content here..."
```

### 3.4 Response Format

**Success Response:**
```json
{
  "status": "success",
  "data": {
    // Structured data based on document type
  },
  "metadata": {
    "processing_time": 45.2,
    "token_usage": {
      "prompt_tokens": 1500,
      "completion_tokens": 800,
      "total_tokens": 2300,
      "cost": 0.046
    }
  }
}
```

**Error Response:**
```json
{
  "status": "error",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid document format",
    "details": "Supported formats: PDF, DOCX, XLS, XLSX"
  },
  "timestamp": "2024-12-01T10:30:00Z"
}
```

## 4. API Usage

### 4.1 Main Processing Endpoint

**Endpoint:** `POST /process`

**Parameters:**
- `action` (required): Document type identifier
- `file` (optional): Document file upload
- `data` (optional): URL or text content

**Note:** Either `file` or `data` must be provided, but not both.

### 4.2 CV Processing Example

**Request:**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@john_doe_resume.pdf"
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "personal_info": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "location": "New York, NY",
      "linkedin": "linkedin.com/in/johndoe"
    },
    "summary": "Experienced software engineer with 5+ years...",
    "education": [
      {
        "institution": "University of Technology",
        "degree": "Bachelor of Science",
        "field_of_study": "Computer Science",
        "graduation_date": "2018-05"
      }
    ],
    "work_experience": [
      {
        "company": "Tech Corp",
        "position": "Senior Software Engineer",
        "start_date": "2020-01",
        "end_date": "2023-12",
        "duration_months": 48,
        "responsibilities": [
          "Led development of web applications",
          "Mentored junior developers",
          "Implemented CI/CD pipelines"
        ],
        "technologies": ["Python", "React", "AWS"]
      }
    ],
    "skills": [
      {
        "name": "Python",
        "category": "Programming",
        "experience_months": 60
      },
      {
        "name": "Leadership",
        "category": "Soft Skills",
        "experience_months": 36
      }
    ]
  },
  "metadata": {
    "processing_time": 42.1,
    "token_usage": {
      "prompt_tokens": 1200,
      "completion_tokens": 650,
      "total_tokens": 1850,
      "cost": 0.037
    }
  }
}
```

### 4.3 Invoice Processing Example

**Request:**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=invoice" \
     -F "file=@electricity_bill.pdf"
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "invoice_number": "EB-2024-001234",
    "invoice_type": "Electric",
    "description": "Monthly electricity bill",
    "invoice_date": "2024-01-15",
    "due_date": "2024-02-15",
    "total_amount": {
      "subtotal": 85.50,
      "tax_amount": 8.55,
      "total": 94.05,
      "currency": "USD"
    },
    "consumption_details": {
      "current_reading": 15420,
      "previous_reading": 15120,
      "consumption": 300,
      "unit": "kWh"
    },
    "products": [
      {
        "description": "Electricity consumption",
        "quantity": 300.0,
        "unit_price": 0.285,
        "total_price": 85.50
      }
    ]
  }
}
```

### 4.4 Health Monitoring

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "ok",
  "version": "*********.12",
  "message": "Service is running",
  "system_metrics": {
    "cpu_usage_percent": 25.4,
    "memory_usage_percent": 68.2
  },
  "tasks": {
    "processing_count": 2,
    "processing_details": [
      {
        "task_id": "task_123",
        "action": "cv",
        "running_time_seconds": 12.5,
        "file_name": "resume.pdf"
      }
    ],
    "waiting_count": 1
  }
}
```

## 5. Integration Examples

### 5.1 Python Integration

**Basic Integration:**
```python
import requests
import json

class LumusAIClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    def process_document(self, action, file_path=None, data=None):
        url = f"{self.base_url}/process"
        
        form_data = {"action": action}
        files = {}
        
        if file_path:
            files["file"] = open(file_path, "rb")
        elif data:
            form_data["data"] = data
        else:
            raise ValueError("Either file_path or data must be provided")
        
        try:
            response = requests.post(url, data=form_data, files=files)
            return response.json()
        finally:
            if files:
                files["file"].close()
    
    def get_health(self):
        url = f"{self.base_url}/health"
        response = requests.get(url)
        return response.json()

# Usage example
client = LumusAIClient()

# Process a CV
result = client.process_document("cv", file_path="resume.pdf")
if result["status"] == "success":
    print(f"Candidate: {result['data']['personal_info']['name']}")

# Check service health
health = client.get_health()
print(f"Service status: {health['status']}")
```

**Async Integration:**
```python
import aiohttp
import asyncio

class AsyncLumusAIClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    async def process_document(self, action, file_path=None, data=None):
        url = f"{self.base_url}/process"
        
        form_data = aiohttp.FormData()
        form_data.add_field('action', action)
        
        if file_path:
            form_data.add_field('file', open(file_path, 'rb'))
        elif data:
            form_data.add_field('data', data)
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=form_data) as response:
                return await response.json()
    
    async def process_multiple(self, documents):
        tasks = []
        for doc in documents:
            task = self.process_document(doc["action"], doc["file_path"])
            tasks.append(task)
        
        return await asyncio.gather(*tasks)

# Usage
async def main():
    client = AsyncLumusAIClient()
    
    documents = [
        {"action": "cv", "file_path": "resume1.pdf"},
        {"action": "cv", "file_path": "resume2.pdf"},
        {"action": "invoice", "file_path": "invoice1.pdf"}
    ]
    
    results = await client.process_multiple(documents)
    for result in results:
        print(f"Status: {result['status']}")

asyncio.run(main())
```

### 5.2 JavaScript Integration

**Node.js Example:**
```javascript
const FormData = require('form-data');
const fs = require('fs');
const fetch = require('node-fetch');

class LumusAIClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }
    
    async processDocument(action, filePath = null, data = null) {
        const url = `${this.baseUrl}/process`;
        const formData = new FormData();
        
        formData.append('action', action);
        
        if (filePath) {
            formData.append('file', fs.createReadStream(filePath));
        } else if (data) {
            formData.append('data', data);
        } else {
            throw new Error('Either filePath or data must be provided');
        }
        
        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });
        
        return await response.json();
    }
    
    async getHealth() {
        const url = `${this.baseUrl}/health`;
        const response = await fetch(url);
        return await response.json();
    }
}

// Usage
const client = new LumusAIClient();

client.processDocument('cv', 'resume.pdf')
    .then(result => {
        if (result.status === 'success') {
            console.log(`Candidate: ${result.data.personal_info.name}`);
        } else {
            console.error(`Error: ${result.error.message}`);
        }
    })
    .catch(error => console.error('Request failed:', error));
```

**Browser JavaScript:**
```javascript
class LumusAIClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }
    
    async processDocument(action, file = null, data = null) {
        const url = `${this.baseUrl}/process`;
        const formData = new FormData();
        
        formData.append('action', action);
        
        if (file) {
            formData.append('file', file);
        } else if (data) {
            formData.append('data', data);
        } else {
            throw new Error('Either file or data must be provided');
        }
        
        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });
        
        return await response.json();
    }
}

// Usage with file input
const client = new LumusAIClient();
const fileInput = document.getElementById('fileInput');

fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        try {
            const result = await client.processDocument('cv', file);
            console.log('Processing result:', result);
        } catch (error) {
            console.error('Error processing document:', error);
        }
    }
});
```

## 6. Troubleshooting

### 6.1 Common Issues

#### Issue: "Unsupported file format"
**Cause:** File format not supported by the system
**Solution:** 
- Use supported formats: PDF, DOCX, XLS, XLSX, PNG, JPG, JPEG, TXT
- Check file extension matches actual file type
- Ensure file is not corrupted

#### Issue: "File too large"
**Cause:** File exceeds 50MB size limit
**Solution:**
- Reduce file size by compressing images
- Split large documents into smaller parts
- Use lower resolution for scanned documents

#### Issue: "Processing timeout"
**Cause:** Document processing takes too long
**Solution:**
- Retry with a simpler document
- Check document quality and readability
- Contact support if issue persists

#### Issue: "AI service error"
**Cause:** OpenAI API service issues
**Solution:**
- Check service status at `/health` endpoint
- Retry request after a few minutes
- Verify API credentials are valid

### 6.2 Error Codes Reference

| Error Code | Description | Action |
|------------|-------------|---------|
| `VALIDATION_ERROR` | Invalid request parameters | Check request format |
| `UNSUPPORTED_FORMAT` | File format not supported | Use supported formats |
| `FILE_TOO_LARGE` | File exceeds size limit | Reduce file size |
| `PROCESSING_ERROR` | Document processing failed | Retry or use different document |
| `AI_SERVICE_ERROR` | AI model service error | Check service status, retry |
| `RATE_LIMIT_EXCEEDED` | Too many requests | Reduce request frequency |

### 6.3 Debugging Tips

**Check Service Health:**
```bash
curl -X GET "http://localhost:8000/health"
```

**Validate File Format:**
```bash
file document.pdf  # Check actual file type
```

**Test with Simple Document:**
- Start with a simple, clean document
- Gradually test with more complex documents
- Check if specific content causes issues

**Monitor Processing Time:**
- Note processing times for different document types
- Large or complex documents take longer
- Typical processing time: 30-60 seconds

## 7. Best Practices

### 7.1 Document Preparation

**For CVs/Resumes:**
- Use clear, readable fonts
- Ensure proper document structure
- Include complete contact information
- Use standard section headings

**For Invoices:**
- Ensure all text is clearly visible
- Include complete billing information
- Use standard invoice formats
- Avoid handwritten annotations

**For Legal Documents:**
- Use high-quality scans
- Ensure text is searchable (not image-only)
- Include complete document headers
- Maintain original formatting

### 7.2 API Usage Best Practices

**Error Handling:**
```python
def process_with_retry(client, action, file_path, max_retries=3):
    for attempt in range(max_retries):
        try:
            result = client.process_document(action, file_path)
            if result["status"] == "success":
                return result
            elif result["status"] == "error":
                error_code = result["error"]["code"]
                if error_code in ["VALIDATION_ERROR", "UNSUPPORTED_FORMAT"]:
                    # Don't retry validation errors
                    return result
                # Retry for other errors
                time.sleep(2 ** attempt)  # Exponential backoff
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)
    
    return {"status": "error", "error": {"message": "Max retries exceeded"}}
```

**Concurrent Processing:**
```python
import asyncio

async def process_batch(client, documents, batch_size=4):
    results = []
    
    for i in range(0, len(documents), batch_size):
        batch = documents[i:i + batch_size]
        tasks = [
            client.process_document(doc["action"], doc["file_path"])
            for doc in batch
        ]
        
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        results.extend(batch_results)
        
        # Small delay between batches
        await asyncio.sleep(1)
    
    return results
```

### 7.3 Performance Optimization

**File Size Optimization:**
- Compress large PDF files
- Use appropriate image resolution
- Remove unnecessary pages or content

**Request Optimization:**
- Process documents in batches
- Use async/await for concurrent processing
- Implement proper error handling and retries

**Monitoring:**
- Track processing times
- Monitor error rates
- Set up alerts for service issues

## 8. Support

### 8.1 Getting Help

**Documentation:**
- API Documentation: `{base_url}/docs`
- Technical Specifications: See project documentation
- Integration Examples: Available in this manual

**Service Status:**
- Health Check: `GET /health`
- System Metrics: Available in health response
- Processing Queue: Monitor active and waiting tasks

### 8.2 Reporting Issues

When reporting issues, please include:
- Document type and action used
- File format and size
- Error message and code (if any)
- Request/response examples
- Processing time and system load

### 8.3 Feature Requests

For new features or enhancements:
- Describe the use case
- Provide example documents
- Explain expected behavior
- Consider integration requirements

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Upon user feedback or system changes
