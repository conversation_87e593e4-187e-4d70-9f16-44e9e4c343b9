#!/usr/bin/env python3
"""
ParserGPT POC startup script.

This script provides an easy way to start the ParserGPT server with proper
initialization and configuration validation.

Usage:
    python run.py [--port PORT] [--host HOST] [--reload]

Examples:
    python run.py                    # Start on default port 8000
    python run.py --port 8080        # Start on port 8080
    python run.py --reload           # Start with auto-reload for development
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from app.config import get_settings, validate_settings
    from app.database import init_database
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


async def initialize_system():
    """Initialize the system before starting the server."""
    print("🚀 Initializing ParserGPT POC...")
    
    try:
        # Validate configuration
        print("   Validating configuration...")
        validate_settings()
        print("   ✅ Configuration valid")
        
        # Initialize database
        print("   Initializing database...")
        await init_database()
        print("   ✅ Database initialized")
        
        # Create adapters directory
        settings = get_settings()
        adapters_dir = Path(settings.adapters_dir)
        adapters_dir.mkdir(exist_ok=True)
        print(f"   ✅ Adapters directory ready: {adapters_dir}")
        
        print("   🎉 System initialization complete!")
        return True
        
    except Exception as e:
        print(f"   ❌ Initialization failed: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Start ParserGPT POC server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging()
    
    print("=" * 50)
    print("🚀 ParserGPT POC Server")
    print("=" * 50)
    
    # Initialize system
    try:
        success = asyncio.run(initialize_system())
        if not success:
            print("\n❌ Failed to initialize system. Exiting.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Initialization interrupted by user.")
        sys.exit(1)
    
    # Start the server
    print(f"\n🌐 Starting server on {args.host}:{args.port}")
    print(f"   Reload: {'enabled' if args.reload else 'disabled'}")
    print(f"   Log level: {args.log_level}")
    print("\n📖 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("\n⚠️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        import uvicorn
        uvicorn.run(
            "app.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level.lower(),
            access_log=True
        )
    except ImportError:
        print("❌ uvicorn not found. Please install it with: pip install uvicorn")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
