"""Pytest configuration and fixtures."""

import asyncio
import os
import tempfile
from typing import Async<PERSON>enerator
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
from app.database import Base, get_async_session
from app.models import Job, Page, Extraction, Adapter
from app.main import app


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def test_db_engine():
    """Create a test database engine."""
    # Use in-memory SQLite for testing
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async with AsyncSession(test_db_engine) as session:
        yield session


@pytest.fixture
def override_get_session(test_session):
    """Override the get_async_session dependency."""
    async def _get_test_session():
        yield test_session
    
    app.dependency_overrides[get_async_session] = _get_test_session
    yield
    app.dependency_overrides.clear()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def sample_job_data():
    """Sample job data for testing."""
    return {
        "start_url": "https://example.com",
        "allowed_domains": ["example.com"],
        "max_depth": 2,
        "max_pages": 10,
        "field_spec": [
            {
                "name": "title",
                "dtype": "string",
                "description": "Page title",
                "required": True
            },
            {
                "name": "tags",
                "dtype": "string[]",
                "description": "Tags list",
                "required": False
            }
        ]
    }


@pytest.fixture
def sample_html():
    """Sample HTML content for testing."""
    return """
    <html>
        <head><title>Test Page</title></head>
        <body>
            <h1 class="title">Sample Article</h1>
            <div class="content">
                <p class="description">This is a test description.</p>
                <ul class="tags">
                    <li>python</li>
                    <li>web-scraping</li>
                    <li>testing</li>
                </ul>
                <span class="price">$29.99</span>
                <div class="metadata">
                    <span class="author">John Doe</span>
                    <span class="date">2024-01-01</span>
                </div>
            </div>
        </body>
    </html>
    """


@pytest.fixture
def sample_adapter():
    """Sample adapter data for testing."""
    return {
        "domain": "example.com",
        "version": 1,
        "url_patterns": {
            "detail": ["*/article/*", "*/post/*"],
            "list": ["*/category/*", "*/search*"]
        },
        "selectors": {
            "title": {
                "css": "h1.title",
                "xpath": "//h1[@class='title']",
                "regex": ""
            },
            "description": {
                "css": ".description",
                "xpath": "//p[@class='description']",
                "regex": ""
            },
            "tags": {
                "css": ".tags li",
                "xpath": "//ul[@class='tags']//li",
                "regex": ""
            },
            "price": {
                "css": ".price",
                "xpath": "",
                "regex": r'\$(\d+\.\d+)'
            }
        },
        "metadata": {
            "created_at": "2024-01-01T00:00:00Z",
            "sample_urls": [
                "https://example.com/article/1",
                "https://example.com/article/2"
            ]
        }
    }


@pytest_asyncio.fixture
async def sample_job(test_session, sample_job_data):
    """Create a sample job in the test database."""
    job = Job(
        start_url=sample_job_data["start_url"],
        allowed_domains=",".join(sample_job_data["allowed_domains"]),
        max_depth=sample_job_data["max_depth"],
        max_pages=sample_job_data["max_pages"],
        field_spec=sample_job_data["field_spec"],
        status="created"
    )
    
    test_session.add(job)
    await test_session.commit()
    await test_session.refresh(job)
    
    return job


@pytest_asyncio.fixture
async def sample_page(test_session, sample_job, sample_html):
    """Create a sample page in the test database."""
    page = Page(
        job_id=sample_job.id,
        url="https://example.com/article/1",
        domain="example.com",
        html_content=sample_html,
        status_code=200,
        content_type="text/html",
        content_length=len(sample_html),
        fetch_method="httpx"
    )
    
    test_session.add(page)
    await test_session.commit()
    await test_session.refresh(page)
    
    return page


@pytest_asyncio.fixture
async def sample_extraction(test_session, sample_job, sample_page):
    """Create a sample extraction in the test database."""
    extraction = Extraction(
        job_id=sample_job.id,
        page_id=sample_page.id,
        extracted_data={
            "title": "Sample Article",
            "tags": ["python", "web-scraping", "testing"]
        },
        extraction_method="deterministic",
        confidence_score=0.95
    )
    
    test_session.add(extraction)
    await test_session.commit()
    await test_session.refresh(extraction)
    
    return extraction


@pytest_asyncio.fixture
async def sample_adapter_db(test_session, sample_adapter):
    """Create a sample adapter in the test database."""
    adapter = Adapter(
        domain=sample_adapter["domain"],
        version=sample_adapter["version"],
        adapter_data=sample_adapter,
        sample_urls=sample_adapter["metadata"]["sample_urls"]
    )
    
    test_session.add(adapter)
    await test_session.commit()
    await test_session.refresh(adapter)
    
    return adapter


# Mock settings for testing
@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    from unittest.mock import Mock
    
    settings = Mock()
    settings.database_url = "sqlite+aiosqlite:///:memory:"
    settings.openai_api_key = "test-key"
    settings.debug = True
    settings.log_level = "INFO"
    settings.adapters_dir = "/tmp/adapters"
    settings.max_concurrent_requests = 5
    settings.request_delay = 1.0
    settings.user_agents = ["test-agent"]
    settings.playwright_headless = True
    settings.playwright_timeout = 30000
    
    return settings
