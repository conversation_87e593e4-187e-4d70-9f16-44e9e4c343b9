#!/usr/bin/env python3
"""
Debug script to test Langfuse connection step by step.
This will help identify where the connection is failing.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the parent directory to the path so we can import from the main app
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment_variables():
    """Test if all required environment variables are set."""
    print("🔍 Step 1: Checking Environment Variables")
    print("-" * 40)
    
    # Azure OpenAI variables
    API_KEY = os.getenv("API_KEY")
    API_VERSION = os.getenv("API_VERSION")
    AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
    MODEL = os.getenv("MODEL")
    
    # Langfuse variables
    LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
    LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
    LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")
    LANGFUSE_ENABLE_MASKING = os.getenv("LANGFUSE_ENABLE_MASKING", "false")
    
    print(f"API_KEY: {'✅ Set' if API_KEY else '❌ Missing'}")
    print(f"API_VERSION: {'✅ ' + API_VERSION if API_VERSION else '❌ Missing'}")
    print(f"AZURE_ENDPOINT: {'✅ ' + AZURE_ENDPOINT if AZURE_ENDPOINT else '❌ Missing'}")
    print(f"MODEL: {'✅ ' + MODEL if MODEL else '❌ Missing'}")
    print()
    print(f"LANGFUSE_PUBLIC_KEY: {'✅ Set' if LANGFUSE_PUBLIC_KEY else '❌ Missing'}")
    print(f"LANGFUSE_SECRET_KEY: {'✅ Set' if LANGFUSE_SECRET_KEY else '❌ Missing'}")
    print(f"LANGFUSE_HOST: {'✅ ' + LANGFUSE_HOST if LANGFUSE_HOST else '❌ Missing'}")
    print(f"LANGFUSE_ENABLE_MASKING: {'✅ ' + LANGFUSE_ENABLE_MASKING if LANGFUSE_ENABLE_MASKING else '❌ Missing'}")
    
    required_vars = [API_KEY, API_VERSION, AZURE_ENDPOINT, MODEL, LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY]
    if all(required_vars):
        print("\n✅ All required environment variables are set")
        return True
    else:
        print("\n❌ Some required environment variables are missing")
        return False

def test_langfuse_import():
    """Test if Langfuse can be imported."""
    print("\n🔍 Step 2: Testing Langfuse Import")
    print("-" * 40)
    
    try:
        from langfuse import Langfuse, get_client
        from langfuse.langchain import CallbackHandler
        print("✅ Langfuse imports successful")
        print(f"   - Langfuse class: {Langfuse}")
        print(f"   - CallbackHandler class: {CallbackHandler}")
        return True
    except ImportError as e:
        print(f"❌ Langfuse import failed: {e}")
        print("   Make sure langfuse is installed: pip install langfuse")
        return False
    except Exception as e:
        print(f"❌ Unexpected error importing Langfuse: {e}")
        return False

def test_langfuse_client_creation():
    """Test if Langfuse client can be created."""
    print("\n🔍 Step 3: Testing Langfuse Client Creation")
    print("-" * 40)
    
    try:
        from langfuse import Langfuse, get_client
        
        LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
        LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
        LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")
        
        # Create Langfuse client
        langfuse_client = Langfuse(
            public_key=LANGFUSE_PUBLIC_KEY,
            secret_key=LANGFUSE_SECRET_KEY,
            host=LANGFUSE_HOST
        )
        
        print("✅ Langfuse client created successfully")
        print(f"   - Client type: {type(langfuse_client)}")
        print(f"   - Host: {LANGFUSE_HOST}")
        
        # Test get_client()
        client = get_client()
        print(f"✅ get_client() successful: {type(client)}")
        
        return langfuse_client
        
    except Exception as e:
        print(f"❌ Langfuse client creation failed: {e}")
        return None

def test_callback_handler_creation():
    """Test if CallbackHandler can be created."""
    print("\n🔍 Step 4: Testing CallbackHandler Creation")
    print("-" * 40)
    
    try:
        from langfuse.langchain import CallbackHandler
        
        # Create CallbackHandler without masking
        handler = CallbackHandler()
        print("✅ CallbackHandler created successfully")
        print(f"   - Handler type: {type(handler)}")
        
        return handler
        
    except Exception as e:
        print(f"❌ CallbackHandler creation failed: {e}")
        return None

async def test_langchain_client():
    """Test if our LangChainClient can be created and used."""
    print("\n🔍 Step 5: Testing LangChainClient")
    print("-" * 40)
    
    try:
        from utils.langchain_client import LangChainClient
        
        # Get environment variables
        API_KEY = os.getenv("API_KEY")
        API_VERSION = os.getenv("API_VERSION")
        AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
        MODEL = os.getenv("MODEL")
        LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
        LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
        LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "http://**************:3000")
        
        # Create client with masking disabled
        client = LangChainClient(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=AZURE_ENDPOINT,
            model=MODEL,
            langfuse_public_key=LANGFUSE_PUBLIC_KEY,
            langfuse_secret_key=LANGFUSE_SECRET_KEY,
            langfuse_host=LANGFUSE_HOST,
            langfuse_enable_masking=False  # Explicitly disable masking for testing
        )
        
        print("✅ LangChainClient created successfully")
        
        # Test callback retrieval
        callbacks = client._get_callbacks()
        print(f"✅ Callbacks retrieved: {len(callbacks)} callback(s)")
        
        # Test a simple API call
        print("\n📞 Testing simple API call...")
        response = await client.get_simple_response("Hello! Please respond with 'Connection test successful'.")
        
        print("✅ API call successful")
        print(f"   - Response: {response.get('response', '')[:100]}...")
        print(f"   - Tokens: {response.get('token_usage', {}).get('total_tokens', 'N/A')}")
        
        # Flush Langfuse data
        print("\n💾 Flushing Langfuse data...")
        client.flush_langfuse()
        
        return True
        
    except Exception as e:
        print(f"❌ LangChainClient test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all diagnostic tests."""
    print("🔧 Langfuse Connection Diagnostic Tool")
    print("=" * 50)
    
    # Step 1: Environment variables
    if not test_environment_variables():
        print("\n❌ Environment variable test failed. Please fix and try again.")
        return
    
    # Step 2: Langfuse import
    if not test_langfuse_import():
        print("\n❌ Langfuse import test failed. Please install langfuse and try again.")
        return
    
    # Step 3: Langfuse client creation
    langfuse_client = test_langfuse_client_creation()
    if not langfuse_client:
        print("\n❌ Langfuse client creation failed. Check your credentials.")
        return
    
    # Step 4: CallbackHandler creation
    handler = test_callback_handler_creation()
    if not handler:
        print("\n❌ CallbackHandler creation failed.")
        return
    
    # Step 5: Full LangChainClient test
    success = await test_langchain_client()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
        print("\n📋 Next steps:")
        print("1. Check your Langfuse dashboard for traces")
        print(f"   Dashboard: {os.getenv('LANGFUSE_HOST', 'http://**************:3000')}")
        print("2. If traces appear, the basic connection is working")
        print("3. If no traces appear, there might be a network or credential issue")
        print("4. Once basic connection works, we can re-enable masking")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting tips:")
        print("1. Verify all environment variables are correct")
        print("2. Check network connectivity to Langfuse host")
        print("3. Verify Langfuse credentials in dashboard")
        print("4. Try restarting the application")

if __name__ == "__main__":
    asyncio.run(main())
