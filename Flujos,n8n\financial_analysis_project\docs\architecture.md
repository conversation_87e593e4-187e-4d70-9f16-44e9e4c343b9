# 🏗️ System Architecture

This document provides a comprehensive overview of the Financial Analysis Project's architecture, components, and data flow.

## 🎯 High-Level Architecture

```mermaid
graph TB
    User[👤 User] --> Frontend[🎨 Streamlit Frontend]
    Frontend --> API[🚀 FastAPI Backend]
    API --> Agents[🤖 LangChain Agents]
    API --> Services[⚙️ Business Services]
    Services --> Processors[📊 Data Processors]
    Agents --> LLM[🧠 ChatGroq LLM]
    
    subgraph "Data Flow"
        Excel[📄 Excel Files] --> Upload[📤 File Upload]
        Upload --> Validation[✅ Data Validation]
        Validation --> Analysis[🔍 Financial Analysis]
        Analysis --> Results[📈 Results]
    end
```

## 🏢 System Components

### 1. Frontend Layer (Streamlit)
**Location**: `frontend/`
**Purpose**: User interface and data visualization

#### Components:
- **Main Application** (`streamlit_app.py`)
  - File upload interface
  - Configuration panels
  - Results dashboard
  - Analysis history

- **UI Components** (`components/`)
  - `file_upload.py`: File handling and validation
  - `results_display.py`: Charts and tables visualization

#### Key Features:
- Drag-and-drop file upload
- Real-time progress indicators
- Interactive Plotly charts
- Responsive design
- Download functionality

### 2. Backend Layer (FastAPI)
**Location**: `backend/app/`
**Purpose**: API endpoints and business logic orchestration

#### Core Components:

##### **API Layer** (`main.py`)
- RESTful endpoints
- File upload handling
- Request/response validation
- Error handling and logging
- CORS configuration

##### **Services Layer** (`services/`)
- `financial_analyzer.py`: Main analysis orchestrator
- `excel_processor.py`: File processing and validation

##### **Agents Layer** (`agents/`)
- `pandas_agents.py`: LangChain AI agents implementation

##### **Models Layer** (`models/`)
- Data models and schemas
- Request/response structures

##### **Utils Layer** (`utils/`)
- Helper functions
- Common utilities

### 3. AI/ML Layer (LangChain Agents)
**Purpose**: Intelligent data analysis using AI

#### Agent Architecture:
```mermaid
graph LR
    Input[📊 Raw Data] --> Processor[🔍 Excel Processor Agent]
    Processor --> Gross[💰 Gross Amount Analyzer]
    Processor --> Voucher[🧾 Voucher Analyzer]
    Gross --> Margin[📊 Margin Calculator]
    Voucher --> Margin
    Margin --> Trend[📈 Trend Analyzer]
    Trend --> Report[📋 Report Generator]
    Report --> Output[📄 Final Report]
```

#### Individual Agents:

##### **ExcelProcessorAgent**
- **Purpose**: Data validation and preprocessing
- **LLM Model**: llama-3.1-8b-instant (fast processing)
- **Tasks**:
  - Column mapping and validation
  - Data quality assessment
  - Cleaning recommendations

##### **GrossAmountAnalyzer**
- **Purpose**: Calculate totals per supplier and voucher
- **LLM Model**: llama3-70b-8192 (complex calculations)
- **Tasks**:
  - Supplier-level aggregations
  - Voucher-level summaries
  - Top performer identification

##### **MarginCalculator**
- **Purpose**: Profit margin analysis
- **LLM Model**: llama3-70b-8192 (financial calculations)
- **Tasks**:
  - Margin percentage calculations
  - Cost assumption handling
  - Risk transaction identification

##### **TrendAnalyzer**
- **Purpose**: Time series and trend analysis
- **LLM Model**: llama3-70b-8192 (pattern recognition)
- **Tasks**:
  - Month-over-month comparisons
  - Growth rate calculations
  - Seasonal pattern detection

##### **FinancialReportGenerator**
- **Purpose**: Comprehensive report generation
- **LLM Model**: gemma2-9b-it (natural language generation)
- **Tasks**:
  - Executive summary creation
  - Insight synthesis
  - Recommendation generation

### 4. Data Layer
**Purpose**: Data models and schemas

#### Shared Schemas (`shared/schemas.py`):
- `FinancialAnalysisResult`: Complete analysis output
- `SupplierSummary`: Supplier-level metrics
- `VoucherSummary`: Transaction-level details
- `MonthlyTrend`: Time series data points
- `AnalysisRequest/Response`: API contracts

## 🔄 Data Flow Architecture

### 1. File Upload Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant P as Processor
    
    U->>F: Upload Excel file
    F->>A: POST /analyze
    A->>P: Validate file format
    P->>A: Validation result
    A->>F: Upload confirmation
    F->>U: Show progress
```

### 2. Analysis Flow
```mermaid
sequenceDiagram
    participant A as API
    participant S as Services
    participant AG as Agents
    participant L as LLM
    
    A->>S: Start analysis
    S->>AG: Initialize workflow
    AG->>L: Process data
    L->>AG: Analysis results
    AG->>S: Structured output
    S->>A: Final results
```

### 3. StateGraph Workflow
```mermaid
graph TD
    Start([START]) --> Process[Process Excel Data]
    Process --> Supplier[Analyze Suppliers]
    Process --> Voucher[Analyze Vouchers]
    Supplier --> Margin[Calculate Margins]
    Voucher --> Margin
    Margin --> Trend[Analyze Trends]
    Trend --> Report[Generate Report]
    Report --> End([END])
```

## 🔧 Technology Stack

### Frontend Technologies
- **Streamlit**: Web application framework
- **Plotly**: Interactive data visualization
- **Pandas**: Data manipulation
- **Requests**: HTTP client for API communication

### Backend Technologies
- **FastAPI**: Modern web framework
- **Uvicorn**: ASGI server
- **Pydantic**: Data validation
- **Python-multipart**: File upload handling

### AI/ML Technologies
- **LangChain**: AI agent framework
- **LangGraph**: Workflow orchestration
- **ChatGroq**: Large language model API
- **LangChain-Experimental**: Pandas agents

### Data Processing
- **Pandas**: Data analysis library
- **OpenPyXL**: Excel file handling
- **NumPy**: Numerical computations

## 🔐 Security Architecture

### API Security
- Input validation and sanitization
- File type and size restrictions
- Error handling without information leakage
- CORS configuration for frontend access

### Data Security
- No persistent data storage
- In-memory processing only
- Secure file upload handling
- API key protection via environment variables

## 📊 Performance Architecture

### Scalability Considerations
- **Stateless Design**: No session storage
- **Async Processing**: FastAPI async endpoints
- **Memory Management**: Efficient pandas operations
- **LLM Optimization**: Model selection by task complexity

### Performance Optimizations
- **Model Selection**: Different LLMs for different tasks
- **Batch Processing**: Multiple file analysis
- **Caching**: Results caching (future enhancement)
- **Streaming**: Progress updates during analysis

## 🔌 Integration Architecture

### API Integration Points
```python
# REST API Endpoints
POST /analyze              # Single file analysis
POST /analyze-batch        # Multiple file analysis
GET  /health              # Health check
GET  /analysis-info       # Capability information
```

### External Dependencies
- **GROQ API**: LLM inference
- **Python Package Index**: Dependency management

## 🏗️ Deployment Architecture

### Development Environment
```
Local Machine
├── Frontend (Port 8501)
├── Backend (Port 8000)
└── Sample Data
```

### Production Considerations
- **Containerization**: Docker support (planned)
- **Load Balancing**: Multiple backend instances
- **Monitoring**: Health checks and logging
- **Environment Management**: Configuration via environment variables

## 🔄 Extension Points

### Adding New Agents
1. Create agent class in `agents/pandas_agents.py`
2. Add to StateGraph workflow
3. Update state management
4. Add corresponding tests

### Adding New Analysis Features
1. Extend data schemas
2. Create new service methods
3. Update frontend components
4. Add API endpoints

### Integration Capabilities
- **Database Integration**: Add persistent storage
- **Message Queues**: Async processing
- **External APIs**: Third-party data sources
- **Webhooks**: Event-driven processing

---

**Next**: Review the [API Documentation](api-documentation.md) for detailed endpoint specifications.
