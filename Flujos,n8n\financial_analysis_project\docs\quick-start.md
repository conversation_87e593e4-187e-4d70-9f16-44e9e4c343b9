# 🚀 Quick Start Guide

Get up and running with the Financial Analysis Project in just 5 minutes!

## ⚡ 5-Minute Setup

### 1. Prerequisites Check
Ensure you have:
- Python 3.8+ installed
- GROQ API key (get free at https://console.groq.com/)

### 2. One-Command Setup
```bash
cd financial_analysis_project
python setup_and_run.py
```

### 3. Configure API Key
When prompted, edit `backend/.env` and add your GROQ API key:
```env
GROQ_API_KEY=your_actual_api_key_here
```

### 4. Access the Application
- **Frontend**: http://localhost:8501
- **Backend API**: http://localhost:8000/docs

## 📊 Your First Analysis

### Step 1: Upload Sample Data
1. Open http://localhost:8501
2. Click "Choose an Excel file"
3. Select `sample_data/sample_financial_data.xlsx`

### Step 2: Configure Analysis
- **Assume Cost Percentage**: 70% (default)
- **Low Margin Threshold**: 10% (default)

### Step 3: Run Analysis
Click "🚀 Analyze Financial Data" and wait 1-2 minutes

### Step 4: View Results
Explore the analysis tabs:
- **📊 Supplier Analysis**: Top suppliers by revenue
- **⚠️ Risk Analysis**: Low/negative margin transactions
- **📈 Trends**: Month-over-month profitability
- **📋 Details**: Assumptions and warnings

## 🎯 What You'll See

### Key Metrics Dashboard
```
Total Records: 507
Total Suppliers: 12
Total Gross Amount: $12,847,392.45
Processing Time: 45.2s
```

### Supplier Analysis
- Interactive bar chart of top 10 suppliers
- Color-coded by average margin percentage
- Detailed performance table

### Risk Analysis
- Count of problematic transactions
- Pie chart of risk distribution
- Detailed tables of flagged transactions

### Trend Analysis
- Monthly gross amount trends
- Top 5 suppliers performance over time
- Average profit margin trends

## 🔧 Configuration Options

### Analysis Parameters
| Parameter | Default | Description |
|-----------|---------|-------------|
| Assume Cost % | 70% | Cost assumption when data missing |
| Low Margin Threshold | 10% | Flag transactions below this margin |

### File Requirements
| Requirement | Details |
|-------------|---------|
| **Format** | Excel (.xlsx, .xls) or CSV |
| **Size** | Maximum 50MB |
| **Required Columns** | supplier, voucher, gross_amount |
| **Optional Columns** | cost, date |

## 📁 Sample Data Files

The project includes several sample files:

### `sample_financial_data.xlsx`
- **Records**: 507 transactions
- **Suppliers**: 12 companies
- **Date Range**: Last 12 months
- **Features**: Realistic financial data with various margin scenarios

### `problematic_financial_data.xlsx`
- **Purpose**: Testing edge cases
- **Contains**: Negative margins, duplicates, missing data
- **Use**: Validate error handling

### `datos_financieros_es.xlsx`
- **Language**: Spanish column names
- **Purpose**: Test multilingual support
- **Columns**: proveedor, comprobante, importe, costo, fecha

## 🔍 Understanding Results

### Supplier Summary
Each supplier shows:
- **Total Gross Amount**: Sum of all transactions
- **Transaction Count**: Number of vouchers
- **Average Margin**: Profit margin percentage
- **Risk Counts**: Low/negative margin transactions

### Voucher Details
Each transaction includes:
- **Voucher ID**: Transaction identifier
- **Supplier**: Company name
- **Gross Amount**: Transaction value
- **Margin %**: Calculated profit margin
- **Risk Flags**: Low/negative margin indicators

### Monthly Trends
Time series analysis showing:
- **Gross Amount Trends**: Revenue over time
- **Supplier Performance**: Top suppliers monthly
- **Margin Trends**: Profitability changes

## 🚨 Common First-Time Issues

### Issue: "Backend API not available"
**Solution**: Ensure backend is running on port 8000
```bash
cd backend
uvicorn app.main:app --reload --port 8000
```

### Issue: "GROQ API Key not found"
**Solution**: Add your API key to `backend/.env`
```env
GROQ_API_KEY=your_key_here
```

### Issue: "File format not supported"
**Solution**: Use Excel (.xlsx, .xls) or CSV files with proper column names

### Issue: Analysis takes too long
**Solution**: 
- Use smaller files for testing
- Check internet connection (API calls required)
- Ensure sufficient system resources

## 📈 Next Steps

### Explore Advanced Features
1. **Batch Analysis**: Upload multiple files
2. **Custom Parameters**: Adjust cost assumptions
3. **Export Results**: Download analysis as JSON/CSV
4. **API Integration**: Use REST endpoints programmatically

### Try Different Data
1. Upload your own Excel files
2. Test with different column names
3. Experiment with various margin thresholds
4. Analyze different time periods

### API Usage
```bash
# Direct API call
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@your_data.xlsx" \
  -F "assume_cost_percentage=75.0"
```

### Integration Examples
```python
import requests

# Upload and analyze file
with open('financial_data.xlsx', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/analyze',
        files={'file': f},
        data={'assume_cost_percentage': 70.0}
    )

results = response.json()
```

## 🎓 Learning Resources

### Understanding the Analysis
- [Financial Features Guide](financial-features.md)
- [Margin Calculations](margin-calculations.md)
- [Data Processing Pipeline](data-pipeline.md)

### Technical Deep Dive
- [System Architecture](architecture.md)
- [LangChain Agents](langchain-agents.md)
- [API Documentation](api-documentation.md)

### Customization
- [Development Guide](development.md)
- [Configuration Guide](configuration.md)
- [Contributing Guidelines](contributing.md)

## 💡 Tips for Success

1. **Start Small**: Use sample data first
2. **Check Columns**: Ensure proper column naming
3. **Monitor Resources**: Large files need more memory
4. **Save Results**: Download analysis for future reference
5. **Experiment**: Try different parameters and thresholds

---

**Ready to dive deeper?** Check out the [Financial Features Guide](financial-features.md) to understand all analysis capabilities.
