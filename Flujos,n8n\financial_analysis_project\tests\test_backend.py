"""
Unit tests for the FastAPI backend.
"""

import pytest
import pandas as pd
from fastapi.testclient import TestClient
import io
import sys
import os

# Add the backend to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.main import app
from app.services.excel_processor import ExcelProcessor, ExcelProcessingError
from app.services.financial_analyzer import FinancialAnalyzer

client = TestClient(app)


class TestAPI:
    """Test cases for the FastAPI endpoints."""
    
    def test_root_endpoint(self):
        """Test the root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "Financial Analysis API" in data["message"]
    
    def test_health_endpoint(self):
        """Test the health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_analysis_info_endpoint(self):
        """Test the analysis info endpoint."""
        response = client.get("/analysis-info")
        assert response.status_code == 200
        data = response.json()
        assert "supported_formats" in data
        assert "analysis_features" in data
        assert "required_columns" in data
    
    def test_analyze_endpoint_no_file(self):
        """Test analyze endpoint without file."""
        response = client.post("/analyze")
        assert response.status_code == 422  # Validation error
    
    def test_analyze_endpoint_invalid_file_type(self):
        """Test analyze endpoint with invalid file type."""
        # Create a fake text file
        fake_file = io.StringIO("This is not an Excel file")
        
        response = client.post(
            "/analyze",
            files={"file": ("test.txt", fake_file.getvalue(), "text/plain")}
        )
        assert response.status_code == 400
        assert "Unsupported file type" in response.json()["detail"]


class TestExcelProcessor:
    """Test cases for the Excel processor service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.processor = ExcelProcessor()
        
        # Create sample data
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd', 'Global Inc'],
            'voucher': ['INV-001', 'INV-002', 'INV-003'],
            'gross_amount': [10000, 15000, 8000],
            'cost': [7000, 10500, 6400],
            'date': pd.date_range('2024-01-01', periods=3)
        })
    
    def test_column_mapping(self):
        """Test column mapping functionality."""
        # Test with different column names
        test_data = pd.DataFrame({
            'vendor_name': ['ABC Corp'],
            'invoice_id': ['INV-001'],
            'total': [10000]
        })
        
        # Convert to Excel bytes
        excel_buffer = io.BytesIO()
        test_data.to_excel(excel_buffer, index=False)
        excel_bytes = excel_buffer.getvalue()
        
        # Process the file
        result = self.processor.process_file(excel_bytes, "test.xlsx")
        
        # Check that columns were mapped correctly
        assert 'supplier' in result.columns
        assert 'voucher' in result.columns
        assert 'gross_amount' in result.columns
    
    def test_missing_required_columns(self):
        """Test handling of missing required columns."""
        # Create data without required columns
        test_data = pd.DataFrame({
            'random_column': ['value1', 'value2'],
            'another_column': [100, 200]
        })
        
        excel_buffer = io.BytesIO()
        test_data.to_excel(excel_buffer, index=False)
        excel_bytes = excel_buffer.getvalue()
        
        # Should raise an exception
        with pytest.raises(ExcelProcessingError):
            self.processor.process_file(excel_bytes, "test.xlsx")
    
    def test_data_cleaning(self):
        """Test data cleaning functionality."""
        # Create data with issues
        test_data = pd.DataFrame({
            'supplier': ['  ABC Corp  ', 'xyz ltd', ''],
            'voucher': ['INV-001', 'INV-002', 'INV-003'],
            'gross_amount': ['$10,000.00', '15000', 'invalid'],
            'cost': [7000, None, 6400]
        })
        
        excel_buffer = io.BytesIO()
        test_data.to_excel(excel_buffer, index=False)
        excel_bytes = excel_buffer.getvalue()
        
        result = self.processor.process_file(excel_bytes, "test.xlsx")
        
        # Check cleaning results
        assert result['supplier'].iloc[0] == 'Abc Corp'  # Trimmed and title case
        assert result['supplier'].iloc[1] == 'Xyz Ltd'   # Title case
        assert pd.isna(result['gross_amount'].iloc[2])   # Invalid amount converted to NaN
    
    def test_get_data_summary(self):
        """Test data summary generation."""
        summary = self.processor.get_data_summary(self.sample_data)
        
        assert summary['total_records'] == 3
        assert summary['total_suppliers'] == 3
        assert summary['total_vouchers'] == 3
        assert summary['amount_summary']['total'] == 33000.0


class TestFinancialAnalyzer:
    """Test cases for the financial analyzer service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.analyzer = FinancialAnalyzer()
        
        # Create sample Excel data
        self.sample_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd', 'ABC Corp', 'Global Inc'],
            'voucher': ['INV-001', 'INV-002', 'INV-003', 'INV-004'],
            'gross_amount': [10000, 15000, 8000, 12000],
            'cost': [7000, 10500, 6400, None],  # One missing cost
            'date': pd.date_range('2024-01-01', periods=4)
        })
    
    def test_margin_calculation(self):
        """Test margin calculation with missing cost data."""
        # Test the internal margin calculation method
        result_df = self.analyzer._calculate_margins(self.sample_data, 70.0)
        
        # Check that missing cost was filled
        assert not result_df['cost'].isnull().any()
        
        # Check margin calculation
        expected_margin = ((10000 - 7000) / 10000) * 100
        assert abs(result_df['margin_percentage'].iloc[0] - expected_margin) < 0.01
    
    def test_supplier_summaries(self):
        """Test supplier summary generation."""
        df_with_margins = self.analyzer._calculate_margins(self.sample_data, 70.0)
        summaries = self.analyzer._generate_supplier_summaries(df_with_margins)
        
        # Should have 3 unique suppliers
        assert len(summaries) == 3
        
        # ABC Corp should have 2 transactions
        abc_summary = next(s for s in summaries if s.supplier_name == 'ABC Corp')
        assert abc_summary.total_transactions == 2
        assert abc_summary.total_gross_amount == 18000.0
    
    def test_voucher_summaries(self):
        """Test voucher summary generation."""
        df_with_margins = self.analyzer._calculate_margins(self.sample_data, 70.0)
        config = {"low_margin_threshold": 10.0}
        summaries = self.analyzer._generate_voucher_summaries(df_with_margins, config)
        
        # Should have 4 voucher summaries
        assert len(summaries) == 4
        
        # Check voucher details
        first_voucher = summaries[0]
        assert first_voucher.voucher_id == 'INV-001'
        assert first_voucher.supplier_name == 'ABC Corp'
        assert first_voucher.gross_amount == 10000.0
    
    def test_monthly_trends(self):
        """Test monthly trend analysis."""
        # Create data spanning multiple months
        extended_data = pd.DataFrame({
            'supplier': ['ABC Corp'] * 6,
            'voucher': [f'INV-{i:03d}' for i in range(1, 7)],
            'gross_amount': [10000] * 6,
            'cost': [7000] * 6,
            'date': pd.date_range('2024-01-01', periods=6, freq='M')
        })
        
        df_with_margins = self.analyzer._calculate_margins(extended_data, 70.0)
        trends = self.analyzer._generate_monthly_trends(df_with_margins)
        
        # Should have 6 monthly trends (one per month)
        assert len(trends) == 6
        
        # Check trend data
        first_trend = trends[0]
        assert first_trend.supplier_name == 'ABC Corp'
        assert first_trend.total_gross_amount == 10000.0


class TestDataValidation:
    """Test cases for data validation and edge cases."""
    
    def test_empty_dataframe(self):
        """Test handling of empty DataFrame."""
        processor = ExcelProcessor()
        empty_df = pd.DataFrame()
        
        excel_buffer = io.BytesIO()
        empty_df.to_excel(excel_buffer, index=False)
        excel_bytes = excel_buffer.getvalue()
        
        with pytest.raises(ExcelProcessingError):
            processor.process_file(excel_bytes, "empty.xlsx")
    
    def test_negative_amounts(self):
        """Test handling of negative amounts."""
        test_data = pd.DataFrame({
            'supplier': ['ABC Corp'],
            'voucher': ['INV-001'],
            'gross_amount': [-1000],  # Negative amount
            'cost': [500]
        })
        
        processor = ExcelProcessor()
        excel_buffer = io.BytesIO()
        test_data.to_excel(excel_buffer, index=False)
        excel_bytes = excel_buffer.getvalue()
        
        # Should process but generate warnings
        result = processor.process_file(excel_bytes, "negative.xlsx")
        assert len(result) == 1  # Data should still be processed
    
    def test_duplicate_vouchers(self):
        """Test detection of duplicate voucher IDs."""
        test_data = pd.DataFrame({
            'supplier': ['ABC Corp', 'XYZ Ltd'],
            'voucher': ['INV-001', 'INV-001'],  # Duplicate voucher
            'gross_amount': [1000, 2000]
        })
        
        processor = ExcelProcessor()
        excel_buffer = io.BytesIO()
        test_data.to_excel(excel_buffer, index=False)
        excel_bytes = excel_buffer.getvalue()
        
        # Should process but detect duplicates
        result = processor.process_file(excel_bytes, "duplicates.xlsx")
        assert len(result) == 2


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
