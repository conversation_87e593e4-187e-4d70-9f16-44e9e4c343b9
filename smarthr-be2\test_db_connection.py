#!/usr/bin/env python3
"""
Test script to check database connection and table existence.
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def test_database_connection():
    """Test database connection and check if ideal_candidates_smarthr table exists."""
    
    # Get database connection parameters
    user = os.getenv("POSTGRES_USER", "postgres")
    password = os.getenv("POSTGRES_PASSWORD", "")
    host = os.getenv("POSTGRES_HOST", "localhost")
    port = os.getenv("POSTGRES_PORT", "5432")
    database = os.getenv("POSTGRES_DB", "postgres")
    
    print(f"🔗 Connecting to database:")
    print(f"   Host: {host}:{port}")
    print(f"   Database: {database}")
    print(f"   User: {user}")
    
    try:
        # Connect to database
        connection = psycopg2.connect(
            user=user, 
            password=password, 
            host=host, 
            port=port, 
            database=database
        )
        cursor = connection.cursor()
        
        print("✅ Database connection successful!")
        
        # Check if ideal_candidates_smarthr table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'ideal_candidates_smarthr'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        
        if table_exists:
            print("✅ ideal_candidates_smarthr table exists!")
            
            # Get table structure
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'ideal_candidates_smarthr'
                ORDER BY ordinal_position;
            """)
            
            columns = cursor.fetchall()
            print("\n📋 Table structure:")
            for column_name, data_type, is_nullable in columns:
                nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                print(f"   {column_name}: {data_type} ({nullable})")
                
            # Check if there are any records
            cursor.execute("SELECT COUNT(*) FROM ideal_candidates_smarthr;")
            count = cursor.fetchone()[0]
            print(f"\n📊 Records in table: {count}")
            
        else:
            print("❌ ideal_candidates_smarthr table does NOT exist!")
            
            # List all tables to see what's available
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """)
            
            tables = cursor.fetchall()
            print("\n📋 Available tables:")
            for table in tables:
                print(f"   - {table[0]}")
        
        cursor.close()
        connection.close()
        
        return table_exists
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing database connection and table existence...")
    print("=" * 60)
    
    success = test_database_connection()
    
    print("=" * 60)
    if success:
        print("🎉 Database test completed successfully!")
    else:
        print("💥 Database test failed!")
