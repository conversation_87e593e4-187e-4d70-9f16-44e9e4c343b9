# utils/qdrant_utils.py
from qdrant_client import QdrantClient, models
import os
from dotenv import load_dotenv

load_dotenv()
QDRANT_HOST = os.getenv("QDRANT_HOST")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", 6333))

qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT, api_key=QDRANT_API_KEY)
def test_connection():
    return qdrant_client.get_collections()

test_connection()
async def match_candidates_qdrant(sparse_embedding, dense_embedding, limit: int):


    response = qdrant_client.query_points(
        collection_name="my-hybrid-collection",
        prefetch=[
            models.Prefetch(query=sparse_embedding.as_object(), using="bm42", limit=limit),
            models.Prefetch(query=dense_embedding, using="openai", limit=limit),
        ],
        query=models.FusionQuery(fusion=models.Fusion.RRF),
        limit=limit,
    )


    return [
        {
            "id": point.id, 
            "score": point.score,
            "pdf_name":point.payload['pdf_name'],
            "description":point.payload['formatted_cv']
        } for point in response.points  # Include relevant data
    ]



async def create_candidate_qdrant(candidate_data, file, sparse_embedding, dense_embedding, contents):  # Updated function signature


    payload = candidate_data.model_dump()  # Convert Pydantic model to dictionary
    payload.update({"formatted_cv": contents.decode(), "pdf_name": file.filename})


    qdrant_client.upsert(
        collection_name="my-hybrid-collection",
        points=[
            models.PointStruct(
                id=candidate_data.full_name,  # Unique ID, consider using UUID
                vector={"bm42": sparse_embedding.as_object(), "openai": dense_embedding},
                payload=payload
            )
        ],
    )

