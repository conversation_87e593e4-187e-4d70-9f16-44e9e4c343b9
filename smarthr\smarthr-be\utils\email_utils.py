import logging
from typing import List, <PERSON><PERSON>
from email_validator import validate_email, EmailNotValidError

logger = logging.getLogger(__name__)


def verify_emails(emails: str) -> Tuple[List[str], str]:
    """
    Validate and extract valid emails from a string.

    Args:
        emails (str): String containing one or more emails separated by ';'.

    Returns:
        Tuple[List[str], str]: List of valid emails and error message if any.
    """
    if not emails:
        return [], "Invalid email format"
    email_list = [e.strip() for e in emails.split(';') if e.strip()]
    valid_emails = []
    for email in email_list:
        if is_valid_email(email):
            valid_emails.append(email)
    if not valid_emails:
        return [], "No valid email found"
    return valid_emails, ""


def is_valid_email(email: str) -> bool:
    """
    Check if the provided email is valid.

    Args:
        email (str): The email address to validate.

    Returns:
        bool: True if valid, False otherwise.
    """
    try:
        validate_email(email.strip())
        return True
    except EmailNotValidError:
        logger.error(f"Invalid email format: {email}")
        return False
