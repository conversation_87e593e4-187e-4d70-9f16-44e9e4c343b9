ParserGPT (Public Beta Coming Soon): Turn messy websites into clean CSVs
<PERSON><PERSON><PERSON>

Follow
7 min read
·
Aug 28, 2025
93




Heads-up: I’ve built ParserGPT to solve “scrape site, reliably.” I’m polishing the release, beta is coming soon.

If you’ve tried scraping “any website,” you’ve met chaos: inconsistent markup, dynamic content, random anti-bot tricks, and that one page that puts the year in an image alt tag for no sane reason. A single BeautifulSoup script works for one site. I wanted something that generalizes:

Learn where data lives on a site (selectors, patterns).
Run deterministically and fast using those learned rules.
Use an LLM only when it actually helps (strict, validated JSON).
ParserGPT is that system. Think of it as a compiler: the LLM “compiles” selectors (CSS/XPath/regex) for each domain, then the runtime executes those rules fast and cheaply. When rules are missing, a guarded LLM step fills the gaps.

Sneak Peek
Press enter or click to view image in full size

ParserGPT is a two-part machine:
Learner (AI-assisted): Looks at a few pages from a site and figures out where the data lives. It outputs a little “recipe” for that site called an adapter (basically: CSS/XPath selectors and regexes). This is powered by LangChain (prompting and parsing) and LangGraph (a tiny state machine that runs “propose → validate → repair → save”).
Runner (deterministic): Uses that adapter to extract data fast and cheaply across many pages. If a value is missing, it asks the LLM once to fill the gap, but only for that field, only on that page, and only in strictly-validated JSON.
Architecture at a glance (with small, focused code snippets for understanding)
Press enter or click to view image in full size

Step 1: Fetching (FastAPI)
POST /jobs → start a job and return job_id
GET /jobs/{id} → check status
GET /jobs/{id}/csv → download (or stream) CSV
# app/main.py (minimal)
import asyncio, os
from fastapi import FastAPI, HTTPException
from fastapi.responses import FileResponse
from .models import Base, engine, SessionLocal, Job
from .orchestrator import run_job

app = FastAPI()

@app.on_event("startup")
def boot():
    Base.metadata.create_all(bind=engine)

@app.post("/jobs")
async def create_job(payload: dict):
    s = SessionLocal()
    try:
        job = Job(
          start_url=payload["start_url"],
          allowed_domains=",".join(payload.get("allowed_domains", [])),
          max_depth=int(payload.get("max_depth", 1)),
          max_pages=int(payload.get("max_pages", 10)),
          field_spec=payload["field_spec"],
          status="started",
        )
        s.add(job); s.commit(); s.refresh(job)
        asyncio.create_task(run_job(job, adapter=None, db=None))  # inject your adapter+db
        return {"job_id": job.id, "status": "started"}
    finally:
        s.close()

@app.get("/jobs/{job_id}/csv")
def csv(job_id: int):
    path = f"job_{job_id}.csv"
    if not os.path.exists(path): raise HTTPException(404, "csv not ready")
    return FileResponse(path, media_type="text/csv", filename=os.path.basename(path))
Step 2 — Orchestrator
This layer decides if the Adapter (i.e, learned data JSON is present or not)

Step 3— Learn the Adapter (LLM + LangGraph)
The goal is to produce a small JSON file per domain that captures where fields live (Minified version of JSON)

{
  "domain": "example.com",
  "version": 1,
  "url_patterns": {
    "detail": ["*/college/*", "*university*"],
    "list":   ["*/colleges/*", "*ranking*"]
  },
  "selectors": {
    "college_name":    { "css": "h1, .page-title", "xpath": "", "regex": "" },
    "courses_offered": { "css": ".courses li",     "xpath": "", "regex": "" },
    "year_founded":    { "css": "", "xpath": "//text()[contains(.,'Founded')]", "regex": "(?:Estd\\.?|Established)\\s*(\\d{4})" }
  },
  "tests": [
    { "url": "https://example.com/college/abc", "expects": { "college_name": "ABC College" } }
  ]
}
3.1 We collect a representative list and detailed pages. When a site blocks bots, we use Playwright (real Chrome, persistent profile, realistic headers).

# sampling.py
import httpx, asyncio
from playwright.async_api import async_playwright

async def fetch_httpx(url: str) -> str:
    async with httpx.AsyncClient(follow_redirects=True, timeout=30.0, http2=True) as c:
        r = await c.get(url, headers={"User-Agent": "Mozilla/5.0"})
        return r.text if r.status_code == 200 and "text" in r.headers.get("content-type","") else ""

async def fetch_playwright(url: str) -> str:
    async with async_playwright() as p:
        ctx = await p.chromium.launch_persistent_context(
            user_data_dir="./pw-profile", headless=False,
            args=["--disable-blink-features=AutomationControlled"]
        )
        page = await ctx.new_page()
        await page.add_init_script("Object.defineProperty(navigator,'webdriver',{get:()=>undefined});")
        await page.goto(url, wait_until="load", timeout=60000)
        await page.wait_for_timeout(2500)
        html = await page.content()
        await ctx.close()
        return html

async def sample_pages(seed_url: str, max_samples=6):
    # naive: start with seed, follow a few in-domain links (omitted for brevity)
    html = await fetch_httpx(seed_url) or await fetch_playwright(seed_url)
    return [{"url": seed_url, "html": html}]  # add more discovered samples
3.2 Propose selectors (LLM returns strict JSON)

Used LangChain to force valid JSON with PydanticOutputParser.

# propose.py
from pydantic import BaseModel, Field
from typing import Dict
from langchain.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

class AdapterDraft(BaseModel):
    url_patterns: Dict[str, list] = Field(default_factory=dict)
    selectors: Dict[str, Dict[str, str]] = Field(default_factory=dict)

parser = PydanticOutputParser(pydantic_object=AdapterDraft)
PROMPT = ChatPromptTemplate.from_messages([
    ("system",
     "You propose extraction rules for given HTML samples and a field spec. "
     "Return ONLY valid JSON with keys: url_patterns, selectors. "
     "selectors uses CSS/XPath/regex; keep them precise and minimal."),
    ("human",
     "FIELDS (name:dtype): {fields}\n\n"
     "SAMPLES (trimmed HTML):\n{samples}\n\n"
     "{format_instructions}")
])

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

async def propose_adapter(fields: list, samples: list) -> AdapterDraft:
    samples_txt = "\n\n---\n\n".join(s["html"][:8000] for s in samples)
    msg = {
        "fields": ", ".join(f"{f['name']}:{f['dtype']}" for f in fields),
        "samples": samples_txt,
        "format_instructions": parser.get_format_instructions()
    }
    return await (PROMPT | llm | parser).ainvoke(msg)
3.3 Validate, repair, save (LangGraph-style loop)

Get Ayush Shrivastava’s stories in your inbox
Join Medium for free to get updates from this writer.

Enter your email
Subscribe
We apply selectors to samples and measure coverage and shape. If coverage < threshold, we give the misses back to the model to ask for minimal diffs, not a rewrite.

Step 4: Run extraction (deterministic; all pages)
Now we crawl within scope and use the Adapter to extract. (Very Minimalist code example)

# extractor.py
from bs4 import BeautifulSoup
import json, re

def extract_with_adapter(html: str, adapter: dict, field_spec: list) -> dict:
    soup = BeautifulSoup(html, "lxml")
    out = {}
    for f in field_spec:
        name = f["name"]
        sel  = adapter["selectors"].get(name, {})
        values = []
        if sel.get("css"):
            values += [e.get_text(" ", strip=True) for e in soup.select(sel["css"])]
        if sel.get("xpath"):
            pass  # add lxml xpath if you need it
        if sel.get("regex"):
            values += re.findall(sel["regex"], soup.get_text(" ", strip=True), re.I)

        # normalize dtype
        if f["dtype"].endswith("[]"):
            out[name] = [v for i, v in enumerate(values) if v and v not in values[:i]]
        else:
            out[name] = values[0] if values else ""
    return out
Why this works: Extraction is now just reading from known places. It’s fast, predictable, and dirt cheap.

Step 5 — Fallback (LLM only for missing fields, strict JSON)
If a required field is empty on a given page, we ask the LLM for just that field. Nothing else. We parse with Pydantic, so bad JSON is rejected.

# fallback.py
from pydantic import BaseModel
from typing import List
from langchain.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

class Row(BaseModel):
    college_name: str = ""
    courses_offered: List[str] = []
    year_founded: str = ""

parser = PydanticOutputParser(pydantic_object=Row)
PROMPT = ChatPromptTemplate.from_messages([
    ("system",
     "Extract only the requested fields from HTML. "
     "Return ONLY valid JSON matching the schema. "
     "If unknown, use empty string or empty list."),
    ("human",
     "URL: {url}\n\nHTML (truncated):\n{snippet}\n\n"
     "Schema: college_name(string), courses_offered(string[]), year_founded(string)\n"
     "{format_instructions}")
])
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

async def llm_fill(url: str, html: str) -> Row:
    return await (PROMPT | llm | parser).ainvoke({
        "url": url,
        "snippet": html[:12000],
        "format_instructions": parser.get_format_instructions()
    })

def merge(det: dict, llm_row: Row, prefer=set()) -> dict:
    r = det.copy()
    for k, v in llm_row.dict().items():
        if k in prefer and v not in ("", [], None):
            r[k] = v
        elif not r.get(k) and v not in ("", [], None):
            r[k] = v
    return r
Note: We do not “LLM the whole page”. We only ask for what the adapter missed, one field at a time, strict JSON, then merge.

Step 6: Pushing data to Postgres (Store + export)
Store raw HTML and extracted rows in Postgres so we can reproduce results, re-export CSVs, or re-learn adapters later.
Stream or download the CSV. Arrays go into one cell as JSON (easy to explode later).
Let’s understand the tools & terminologies used:
LLM: The model that reads a chunk of HTML and the user’s requested fields and proposes where those fields are (selectors), or gives you values when rules fail. We keep it on a leash: strict JSON only, validated by code.
LangChain: a Python library for building LLM pipelines.
Think of Lego blocks:
PromptTemplate to build clear instructions with variables ({schema}, {html}).
Chat model (OpenAI or Ollama — Both are supported, but use Ollama for cost saving).
Output parser (e.g., PydanticOutputParser) that forces the LLM to return valid JSON that matches your schema. If it doesn’t, we catch the error instead of silently accepting garbage.
LangGraph: a tiny state machine on top of LangChain. We define nodes (“ProposeSelectors”, “ValidateSelectors”, “RepairSelectors”) and edges (what to do next). Why? Because selector learning is a loop with decisions, not a single call. LangGraph makes it predictable, resumable, and debuggable.
Adapter: a small JSON file per domain (e.g., adapters/shiksha.com.json)
That says:
for college_name → css: “h1, .page-title”, maybe a fallback xpath/regex.
for courses_offered[] → css: “.courses li”, etc.
It’s the “recipe” the deterministic extractor uses.
Field spec: user’s schema for this job. What columns the user wants and their types.
Deterministic extraction: Apply adapter selectors with BeautifulSoup/XPath/regex. It’s fast, cheap, and repeatable. This is our main engine.
LLM extraction (fallback, Costly operation 😭): only when deterministic misses required fields. We prompt the LLM with HTML + schema and parse the answer with a strict JSON parser. We merge results field-by-field (never overwrite good deterministic values).
Fetcher: first try httpx (fast). If the site blocks bots (hello “Access Denied”), we use Playwright with a persistent Chrome profile and realistic headers. This behaves like a real browser.
FastAPI: The service wrapper. Async POST /jobs schedules the crawl; CSV endpoints provide final files or a live stream.
Postgres: The database. We store jobs, pages (raw HTML), and extractions (JSON).
CSV: The artifact that the user downloads.
Questions that I asked myself before designing ParserGPT
“Why not just ask the model to read the page and give me a CSV?”
Because it’s slow, expensive, and prone to drifting, learning selectors once gives you speed + control. The LLM then fills only genuine gaps.

“Will this work beyond one site?”
Yes. The adapter is per-domain, but the learning loop is the same everywhere. New site? Run the Learner, then let the Runner scale.

“What if the site changes?”
Coverage drops → the Learner’s repair step tweaks the adapter. It’s a small update, not a rewrite.

ParserGPT’s idea is simple: teach once, then run fast. The LLM handles the fuzzy parts ,learning structure and patching gaps, while deterministic code chews through pages at speed.