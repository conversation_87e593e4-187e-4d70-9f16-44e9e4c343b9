import asyncio
import aiohttp
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class WebhookSender:
    """
    Simple webhook sender for notifying completion of document processing.
    """
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        """
        Initialize webhook sender.
        
        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
        """
        self.timeout = timeout
        self.max_retries = max_retries
    
    async def send_webhook(
        self, 
        webhook_url: str, 
        task_id: str, 
        status: str, 
        data: Dict[str, Any],
        processing_time: Optional[float] = None
    ) -> bool:
        """
        Send webhook notification asynchronously.
        
        Args:
            webhook_url: URL to send webhook to
            task_id: Task identifier
            status: Processing status (completed/failed)
            data: Result data or error information
            processing_time: Processing time in seconds
            
        Returns:
            bool: True if webhook sent successfully, False otherwise
        """
        payload = {
            "task_id": task_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "data": data,
            "processing_time": processing_time
        }
        
        logger.info(f"Sending webhook for task {task_id} to {webhook_url}")
        
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(
                        webhook_url,
                        json=payload,
                        headers={"Content-Type": "application/json"}
                    ) as response:
                        if response.status < 400:
                            logger.info(f"Webhook sent successfully for task {task_id} (attempt {attempt + 1})")
                            return True
                        else:
                            logger.warning(f"Webhook failed with status {response.status} for task {task_id} (attempt {attempt + 1})")
                            
            except asyncio.TimeoutError:
                logger.warning(f"Webhook timeout for task {task_id} (attempt {attempt + 1})")
            except Exception as e:
                logger.error(f"Webhook error for task {task_id} (attempt {attempt + 1}): {str(e)}")
            
            # Wait before retry (exponential backoff)
            if attempt < self.max_retries - 1:
                wait_time = 2 ** attempt
                logger.info(f"Retrying webhook for task {task_id} in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
        
        logger.error(f"Failed to send webhook for task {task_id} after {self.max_retries} attempts")
        return False
    
    def send_webhook_background(
        self, 
        webhook_url: str, 
        task_id: str, 
        status: str, 
        data: Dict[str, Any],
        processing_time: Optional[float] = None
    ):
        """
        Send webhook in background without blocking.
        
        Args:
            webhook_url: URL to send webhook to
            task_id: Task identifier
            status: Processing status (completed/failed)
            data: Result data or error information
            processing_time: Processing time in seconds
        """
        # Create background task
        asyncio.create_task(
            self.send_webhook(webhook_url, task_id, status, data, processing_time)
        )


# Global webhook sender instance
webhook_sender = WebhookSender()
