"""Configuration management for ParserGPT POC."""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Database Configuration
    database_url: str = Field(
        default="sqlite+aiosqlite:///./parsergpt.db",
        description="Database connection URL"
    )
    
    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(
        default=None,
        description="OpenAI API key for LLM operations"
    )
    
    # Optional: Alternative LLM Configuration
    ollama_base_url: Optional[str] = Field(
        default=None,
        description="Ollama base URL for local LLM"
    )
    ollama_model: str = Field(
        default="llama2",
        description="Ollama model name"
    )
    
    # Application Configuration
    debug: bool = Field(default=False, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # Playwright Configuration
    playwright_headless: bool = Field(
        default=True,
        description="Run Playwright in headless mode"
    )
    playwright_user_data_dir: str = Field(
        default="./pw-profile",
        description="Playwright user data directory"
    )
    
    # Scraping Configuration
    max_pages_per_job: int = Field(
        default=100,
        description="Maximum pages to scrape per job"
    )
    max_depth: int = Field(
        default=3,
        description="Maximum crawl depth"
    )
    request_timeout: int = Field(
        default=30,
        description="HTTP request timeout in seconds"
    )
    
    # Adapter Configuration
    adapters_dir: str = Field(
        default="./adapters",
        description="Directory to store adapter JSON files"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


def validate_settings() -> None:
    """Validate required settings are present."""
    if not settings.openai_api_key and not settings.ollama_base_url:
        raise ValueError(
            "Either OPENAI_API_KEY or OLLAMA_BASE_URL must be configured"
        )
    
    # Create adapters directory if it doesn't exist
    os.makedirs(settings.adapters_dir, exist_ok=True)
