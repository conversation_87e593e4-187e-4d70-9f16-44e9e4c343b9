"""
Test script for ideal candidate API endpoints.
"""

import sys
import os
import json
import logging
from typing import Dict, Any, List
import requests
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from fastapi import HTTPException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_app():
    """Create a test FastAPI app with ideal candidate routes."""
    try:
        from fastapi import FastAPI
        from routes.routes_ideal_candidate import router
        
        app = FastAPI()
        app.include_router(router)
        
        return app
    except Exception as e:
        logger.error(f"Failed to create test app: {e}")
        return None


def test_endpoint_structure():
    """Test that all expected endpoints are properly defined."""
    logger.info("Testing endpoint structure...")
    
    try:
        app = create_test_app()
        if not app:
            logger.error("Failed to create test app")
            return False
        
        client = TestClient(app)
        
        # Get OpenAPI schema to check endpoints
        response = client.get("/openapi.json")
        if response.status_code != 200:
            logger.error("Failed to get OpenAPI schema")
            return False
        
        schema = response.json()
        paths = schema.get("paths", {})
        
        # Expected endpoints
        expected_endpoints = [
            "/ideal-candidates/generate",
            "/ideal-candidates/regenerate/{position_id}",
            "/ideal-candidates/{ideal_candidate_id}",
            "/ideal-candidates/position/{position_id}",
            "/ideal-candidates/search",
            "/ideal-candidates/position/{position_id}/availability",
            "/ideal-candidates/embeddings/{ideal_candidate_id}/update",
            "/ideal-candidates/embeddings/statistics",
            "/ideal-candidates/positions/with-ideal-candidates",
            "/ideal-candidates/matching/statistics",
            "/ideal-candidates/similar/{ideal_candidate_id}",
            "/ideal-candidates/match"
        ]
        
        for endpoint in expected_endpoints:
            # Check if endpoint exists (allowing for path parameters)
            endpoint_found = False
            for path in paths.keys():
                if endpoint.replace("{", "").replace("}", "") in path.replace("{", "").replace("}", ""):
                    endpoint_found = True
                    break
            
            if not endpoint_found:
                logger.error(f"Expected endpoint not found: {endpoint}")
                return False
        
        logger.info("✓ All expected endpoints found in schema")
        
        return True
        
    except Exception as e:
        logger.error(f"Endpoint structure test failed: {e}")
        return False


def test_request_validation():
    """Test request validation for API endpoints."""
    logger.info("Testing request validation...")
    
    try:
        app = create_test_app()
        if not app:
            logger.error("Failed to create test app")
            return False
        
        client = TestClient(app)
        
        # Test generation endpoint with invalid request
        invalid_requests = [
            {},  # Missing position_id
            {"position_id": "invalid-uuid"},  # Invalid UUID
            {"position_id": ""},  # Empty position_id
        ]
        
        for invalid_request in invalid_requests:
            response = client.post("/ideal-candidates/generate", json=invalid_request)
            
            # Should return 422 (validation error) or 400 (bad request)
            if response.status_code not in [400, 422, 500]:
                logger.error(f"Invalid request should return error status, got {response.status_code}")
                return False
        
        logger.info("✓ Invalid requests correctly rejected")
        
        # Test valid request structure (will fail due to missing dependencies, but structure should be valid)
        valid_request = {
            "position_id": "123e4567-e89b-12d3-a456-************",
            "generation_options": {"test": True},
            "model_preference": "gpt-4o-mini"
        }
        
        response = client.post("/ideal-candidates/generate", json=valid_request)
        
        # Should not return 422 (validation error) - might return 500 due to missing DB/dependencies
        if response.status_code == 422:
            logger.error(f"Valid request structure should not return validation error: {response.json()}")
            return False
        
        logger.info("✓ Valid request structure accepted")
        
        return True
        
    except Exception as e:
        logger.error(f"Request validation test failed: {e}")
        return False


def test_query_parameter_validation():
    """Test query parameter validation."""
    logger.info("Testing query parameter validation...")
    
    try:
        app = create_test_app()
        if not app:
            logger.error("Failed to create test app")
            return False
        
        client = TestClient(app)
        
        # Test matching endpoint with invalid parameters
        test_cases = [
            {
                "params": {"limit": 150},  # Too high
                "description": "Limit too high"
            },
            {
                "params": {"hasFeedback": 5},  # Invalid value
                "description": "Invalid hasFeedback value"
            },
            {
                "params": {"position_id": "invalid-uuid"},  # Invalid UUID
                "description": "Invalid UUID"
            }
        ]
        
        for test_case in test_cases:
            params = test_case["params"]
            description = test_case["description"]
            
            response = client.post("/ideal-candidates/match", params=params)
            
            # Should return error status for invalid parameters
            if response.status_code not in [400, 422, 500]:
                logger.error(f"Test case '{description}' should return error status, got {response.status_code}")
                return False
        
        logger.info("✓ Invalid query parameters correctly rejected")
        
        return True
        
    except Exception as e:
        logger.error(f"Query parameter validation test failed: {e}")
        return False


def test_response_structure():
    """Test expected response structure for endpoints."""
    logger.info("Testing response structure...")
    
    try:
        # Test with mocked dependencies
        with patch('routes.routes_ideal_candidate.ideal_candidate_workflow') as mock_workflow:
            # Mock successful generation response
            mock_response = Mock()
            mock_response.ideal_candidate_id = "test-id-123"
            mock_response.position_id = "position-id-123"
            mock_response.ideal_candidate_info = {"test": "data"}
            mock_response.generation_success = True
            mock_response.generation_time_ms = 1500
            mock_response.error_message = None
            
            mock_workflow.generate_ideal_candidate_for_position.return_value = mock_response
            
            app = create_test_app()
            if not app:
                logger.error("Failed to create test app")
                return False
            
            client = TestClient(app)
            
            # Test generation endpoint
            valid_request = {
                "position_id": "123e4567-e89b-12d3-a456-************"
            }
            
            response = client.post("/ideal-candidates/generate", json=valid_request)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check required fields in response
                required_fields = [
                    "ideal_candidate_id",
                    "position_id", 
                    "ideal_candidate_info",
                    "generation_success",
                    "generation_time_ms"
                ]
                
                for field in required_fields:
                    if field not in data:
                        logger.error(f"Response missing required field: {field}")
                        return False
                
                logger.info("✓ Generation response structure validated")
            else:
                logger.warning(f"Generation endpoint returned {response.status_code}, skipping response structure validation")
        
        return True
        
    except Exception as e:
        logger.error(f"Response structure test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in API endpoints."""
    logger.info("Testing error handling...")
    
    try:
        app = create_test_app()
        if not app:
            logger.error("Failed to create test app")
            return False
        
        client = TestClient(app)
        
        # Test with non-existent resource
        response = client.get("/ideal-candidates/00000000-0000-0000-0000-000000000000")
        
        # Should return 404 or 500 (depending on implementation)
        if response.status_code not in [404, 500]:
            logger.error(f"Non-existent resource should return 404 or 500, got {response.status_code}")
            return False
        
        logger.info("✓ Non-existent resource correctly handled")
        
        # Test error response structure
        if response.status_code in [400, 404, 422, 500]:
            try:
                error_data = response.json()
                if "detail" not in error_data:
                    logger.error("Error response should contain 'detail' field")
                    return False
                
                logger.info("✓ Error response structure validated")
            except json.JSONDecodeError:
                logger.warning("Error response is not JSON, but that's acceptable")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


def test_endpoint_methods():
    """Test that endpoints accept correct HTTP methods."""
    logger.info("Testing endpoint HTTP methods...")
    
    try:
        app = create_test_app()
        if not app:
            logger.error("Failed to create test app")
            return False
        
        client = TestClient(app)
        
        # Test POST endpoints
        post_endpoints = [
            "/ideal-candidates/generate",
            "/ideal-candidates/search",
            "/ideal-candidates/match",
            "/ideal-candidates/similar/123e4567-e89b-12d3-a456-************"
        ]
        
        for endpoint in post_endpoints:
            # Test that GET is not allowed
            response = client.get(endpoint)
            if response.status_code == 200:
                logger.error(f"POST endpoint {endpoint} should not accept GET requests")
                return False
        
        logger.info("✓ POST endpoints correctly reject GET requests")
        
        # Test GET endpoints
        get_endpoints = [
            "/ideal-candidates/embeddings/statistics",
            "/ideal-candidates/positions/with-ideal-candidates",
            "/ideal-candidates/matching/statistics"
        ]
        
        for endpoint in get_endpoints:
            # Test that POST is not allowed (might return 405 Method Not Allowed)
            response = client.post(endpoint, json={})
            if response.status_code == 200:
                logger.error(f"GET endpoint {endpoint} should not accept POST requests")
                return False
        
        logger.info("✓ GET endpoints correctly reject POST requests")
        
        return True
        
    except Exception as e:
        logger.error(f"Endpoint methods test failed: {e}")
        return False


def run_all_api_tests():
    """Run all ideal candidate API tests."""
    logger.info("Starting ideal candidate API tests...")
    
    tests = [
        ("Endpoint Structure", test_endpoint_structure),
        ("Request Validation", test_request_validation),
        ("Query Parameter Validation", test_query_parameter_validation),
        ("Response Structure", test_response_structure),
        ("Error Handling", test_error_handling),
        ("Endpoint Methods", test_endpoint_methods)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("API TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All API tests passed!")
        return True
    else:
        logger.error(f"💥 {total - passed} API tests failed!")
        return False


if __name__ == "__main__":
    success = run_all_api_tests()
    sys.exit(0 if success else 1)
