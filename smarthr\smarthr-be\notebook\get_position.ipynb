{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c299e80c", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "def get_positions(url):\n", "    # Use a session for performance optimization\n", "    session = requests.Session()\n", "    session.headers.update({\"accept\": \"application/json\", \"Content-Type\": \"application/json\"})\n", "    try:\n", "        response = session.get(url)\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            print(f\"Failed to fetch positions. Status code: {response.status_code}. Response: {response.text}\")\n", "            return []\n", "    except Exception as e:\n", "        print(f\"Error fetching positions: {e}\")\n", "        return []\n", "            \n"]}, {"cell_type": "code", "execution_count": 2, "id": "18e3f338", "metadata": {}, "outputs": [], "source": ["def detete_position(url):\n", "    # Use a session for performance optimization\n", "    session = requests.Session()\n", "    session.headers.update({\"accept\": \"application/json\", \"Content-Type\": \"application/json\"})\n", "    try:\n", "        response = session.delete(url)\n", "        if response.status_code == 200:\n", "            return response.json()\n", "        else:\n", "            print(f\"Failed to fetch positions. Status code: {response.status_code}. Response: {response.text}\")\n", "            return []\n", "    except Exception as e:\n", "        print(f\"Error fetching positions: {e}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": 3, "id": "cfacac85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of positions fetched: 80\n", "Positions fetched successfully:\n", "d3bd40de-b710-4aeb-887a-8baec774f422\n", "9fb98525-525b-4c21-a2ad-c36740b04a31\n", "faf24a28-ded2-4c58-bc49-aaaca2bf6227\n", "efc73d2e-0faa-48da-a8be-e0e9f6df58b4\n", "0343ae10-ae20-4c22-8f86-2b229d8138a1\n", "5af9477c-703e-4a6e-a57b-a992730aca1d\n", "56f51d51-66c8-49c6-9e8d-0dfd805fd8e3\n", "4e0bfe99-b7f2-491c-8122-aba47cafcc0a\n", "65cc8d66-dce8-4b5a-a3fb-20455aac6adf\n", "bb93138c-0e42-451e-886e-5889543ca6f9\n", "9d425c24-50f8-4ec2-8e34-baa5aba1938b\n", "19920418-3666-44b6-8ea5-e05f7d08feb2\n", "e0302804-d20d-4167-b846-67b00948819a\n", "d9d0a4cb-5cbc-47d8-ac09-35bac9887402\n", "ec5dd49d-f98f-414e-bd67-44dbfe361b25\n", "6751f5c1-4bed-4063-af37-d83348123985\n", "4b462c40-3206-47aa-8dde-4d8fff1e800e\n", "cabc18f3-dabe-4739-add8-bf4eec3a437b\n", "be66703f-b43f-42a7-b55b-e968b822b303\n", "aa812e57-2897-4d1e-a273-31731ef95ba3\n", "be276eb9-adb3-4b12-998c-438daab1361a\n", "c6253c45-0bb5-4b7b-b790-0f54312d5fcc\n", "b99ef5a7-b01f-4629-a8b9-de9304ce492c\n", "8204e6f0-b1df-436f-be8d-ae2f12010c49\n", "9f4a2f66-107c-415d-90ec-0afe38574b1c\n", "63104b02-3f0f-4b79-9d43-5c2fcc72e8e6\n", "d2c8e3c7-b10c-4b2a-9a5a-fee188fabec5\n", "b80e29e9-8e2d-4592-81be-16a467644027\n", "69e64cf6-7e29-443b-a1e1-aef8e92670a0\n", "82a8875c-5a17-40b2-9e55-92e635951697\n", "55b092aa-a9ad-417f-8ba4-a08cd31f042d\n", "d69ab4e7-1af6-40f7-beaf-7cc07d2b4ca3\n", "a1e1f83e-22a7-4723-9ee1-d85716e1b30f\n", "a0456c34-0de1-4e81-822f-bf30133d1d18\n", "a5278acd-0eb5-40f7-a9ca-2ee0bf6e4b4e\n", "c7207451-09eb-411b-af12-5184330b03a7\n", "a1d969d3-1568-4b89-8be6-fadb21164ac3\n", "df2b3a88-9072-4dc6-93e0-b562b67da699\n", "3793e848-445a-4c11-8c2d-40b34a9add1f\n", "aee3accd-1cf2-4aa4-a908-8410ec509c1f\n", "e21ec138-052d-4582-b117-83dc82f10f8f\n", "04082854-fa24-432a-bd3b-0fcd5bc908a5\n", "91e7385e-fa5a-4c42-b3cf-57bf35ff42d1\n", "808bc294-46f5-4063-8670-6000ab823b82\n", "570344a4-9d21-4df3-b6a0-56d565177bf3\n", "6d6aecf6-ab28-4850-a33e-ff4c8c2d6ede\n", "7c727b96-a1a6-416d-b354-ef17b0eefe17\n", "42fe8b53-e7e4-4f54-86a3-cd36122c0b81\n", "cd347a4c-7836-42ac-9fc3-4e3d9081aa4e\n", "bc414bd7-6418-4ea5-8049-51ec63f4f15b\n", "0ab99805-a497-42d5-ad1f-6debcc06736b\n", "6cb809db-cd6b-464a-b156-49fff97c42ff\n", "3ccff076-4cdf-4a2a-ac39-2c7a07791b85\n", "d166ef0b-ec7f-4d53-a633-767523ee1dc6\n", "afd1d5d3-3826-474e-8bf8-7f5d85e203be\n", "e81aa9c8-40bf-4190-9925-b5c3c718b3d9\n", "d2439abd-9424-4e11-8b57-75d40ad1b644\n", "cc18daab-a932-460b-8eb3-c304607012bb\n", "422a868d-5c3b-4d59-893f-035e2b958944\n", "a007e4f6-b4da-47e2-9e5c-7cf8b6247778\n", "e1251087-0e87-4d49-a36b-8ae5e890aba7\n", "28196034-6e28-460d-856b-405c9a687635\n", "216c0d57-4efd-4c1d-b8d4-17d730b5cb1c\n", "259d7c6b-70c0-46b8-b61c-98fd2358585d\n", "7b02b76e-9283-4d2f-849e-28b6b0ab61da\n", "e8a12a6f-b3c1-487f-8c56-0935c6bfe9c6\n", "5b954265-43d9-4464-81e8-c8191c05c464\n", "42cc3a63-ddf8-486b-a049-8f8e766aa6ec\n", "fc77694f-fcc6-4dcf-bff7-72daa525a951\n", "8a047155-a339-4909-b9d3-c676f6499cea\n", "f91b3a96-9b36-479a-a0a6-cabe366ef582\n", "b8ef48e9-380b-40b4-9ba3-f21a68172563\n", "57759529-0006-4125-be54-e0bddc7d5272\n", "8be484f0-d2ac-4e36-96c7-4705660d5070\n", "2133a673-df5f-4c95-926c-2d9e1f102f65\n", "33c5cc69-97f7-4b64-9851-e8357db29bf0\n", "96c1793c-a290-499e-b39d-24a31da4b7e0\n", "53a767b1-ae94-4b92-b104-532c8867f045\n", "1e12b32e-41ff-4c5a-b56a-1719debc6114\n", "9a6173ea-bcb8-464f-941c-1d0e759acd07\n"]}], "source": ["url = \"https://apismarthrdevuse2.azurewebsites.net/position/\"\n", "\n", "positions = get_positions(url)\n", "print(f\"Number of positions fetched: {len(positions)}\")\n", "if positions:\n", "    print(\"Positions fetched successfully:\")\n", "    for position in positions:\n", "        print(position.get(\"id\", \"No name provided\"))\n", "        detete_position(f\"{url}{position.get('id', '')}\")\n", "else:\n", "    print(\"No positions found or an error occurred.\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}