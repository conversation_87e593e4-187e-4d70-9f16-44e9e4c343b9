"""Tests for LLM fallback extraction."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from app.fallback import FallbackExtractor, merge_extraction_results, _has_value
from app.schemas import FieldSpec


class TestFallbackExtractor:
    """Test cases for FallbackExtractor class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        with patch('app.fallback.get_settings') as mock_settings:
            mock_settings.return_value.openai_api_key = "test-key"
            self.extractor = FallbackExtractor()
    
    def test_create_dynamic_model(self):
        """Test dynamic Pydantic model creation."""
        field_specs = [
            FieldSpec(name="title", dtype="string", description="Page title"),
            FieldSpec(name="tags", dtype="string[]", description="Tags list"),
            FieldSpec(name="price", dtype="float", description="Price value")
        ]
        
        Model = self.extractor._create_dynamic_model(field_specs)
        
        # Test model creation
        instance = Model()
        assert hasattr(instance, 'title')
        assert hasattr(instance, 'tags')
        assert hasattr(instance, 'price')
        
        # Test default values
        assert instance.title == ""
        assert instance.tags == []
        assert instance.price == 0.0
    
    def test_create_dynamic_model_with_different_types(self):
        """Test dynamic model with various data types."""
        field_specs = [
            FieldSpec(name="count", dtype="int"),
            FieldSpec(name="active", dtype="bool"),
            FieldSpec(name="scores", dtype="float[]")
        ]
        
        Model = self.extractor._create_dynamic_model(field_specs)
        instance = Model()
        
        assert instance.count == 0
        assert instance.active is False
        assert instance.scores == []
    
    @pytest.mark.asyncio
    async def test_extract_missing_fields_empty_list(self):
        """Test extraction with empty missing fields list."""
        result = await self.extractor.extract_missing_fields(
            "https://example.com",
            "<html></html>",
            [],
            []
        )
        assert result == {}
    
    @pytest.mark.asyncio
    async def test_extract_missing_fields_no_specs(self):
        """Test extraction with missing fields but no matching specs."""
        field_specs = [FieldSpec(name="title", dtype="string")]
        missing_fields = ["description"]  # Not in field_specs
        
        result = await self.extractor.extract_missing_fields(
            "https://example.com",
            "<html></html>",
            field_specs,
            missing_fields
        )
        assert result == {}


class TestMergeExtractionResults:
    """Test cases for merge_extraction_results function."""
    
    def test_merge_basic(self):
        """Test basic merging of results."""
        deterministic = {"title": "Test Title", "description": ""}
        llm = {"title": "LLM Title", "description": "LLM Description"}
        
        result = merge_extraction_results(deterministic, llm)
        
        # Should keep deterministic title, use LLM description
        assert result["title"] == "Test Title"
        assert result["description"] == "LLM Description"
    
    def test_merge_with_preference(self):
        """Test merging with LLM preference for specific fields."""
        deterministic = {"title": "Det Title", "description": "Det Desc"}
        llm = {"title": "LLM Title", "description": "LLM Desc"}
        
        result = merge_extraction_results(
            deterministic, llm, prefer_llm_fields=["title"]
        )
        
        # Should prefer LLM for title, keep deterministic for description
        assert result["title"] == "LLM Title"
        assert result["description"] == "Det Desc"
    
    def test_merge_empty_deterministic(self):
        """Test merging when deterministic results are empty."""
        deterministic = {"title": "", "tags": []}
        llm = {"title": "LLM Title", "tags": ["tag1", "tag2"]}
        
        result = merge_extraction_results(deterministic, llm)
        
        assert result["title"] == "LLM Title"
        assert result["tags"] == ["tag1", "tag2"]
    
    def test_merge_empty_llm(self):
        """Test merging when LLM results are empty."""
        deterministic = {"title": "Det Title", "tags": ["tag1"]}
        llm = {"title": "", "tags": []}
        
        result = merge_extraction_results(deterministic, llm)
        
        # Should keep deterministic values
        assert result["title"] == "Det Title"
        assert result["tags"] == ["tag1"]


class TestHasValue:
    """Test cases for _has_value utility function."""
    
    def test_has_value_strings(self):
        """Test _has_value with string values."""
        assert _has_value("test") is True
        assert _has_value("") is False
        assert _has_value("   ") is False
        assert _has_value("  text  ") is True
    
    def test_has_value_lists(self):
        """Test _has_value with list values."""
        assert _has_value([1, 2, 3]) is True
        assert _has_value([]) is False
        assert _has_value(["item"]) is True
    
    def test_has_value_numbers(self):
        """Test _has_value with numeric values."""
        assert _has_value(42) is True
        assert _has_value(0) is False
        assert _has_value(0.0) is False
        assert _has_value(3.14) is True
        assert _has_value(-5) is True
    
    def test_has_value_booleans(self):
        """Test _has_value with boolean values."""
        assert _has_value(True) is True
        assert _has_value(False) is True  # Both True and False are valid
    
    def test_has_value_none(self):
        """Test _has_value with None."""
        assert _has_value(None) is False
    
    def test_has_value_dicts(self):
        """Test _has_value with dictionary values."""
        assert _has_value({"key": "value"}) is True
        assert _has_value({}) is False
