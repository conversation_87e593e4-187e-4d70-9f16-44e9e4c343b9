import os
from typing import List

from dotenv import load_dotenv
from langchain_openai import AzureOpenAIEmbeddings

load_dotenv()

AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS = os.getenv(
    "AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS"
)


def generate_openai_embedding(text: str) -> List[float]:
    model_openai = AzureOpenAIEmbeddings(model=AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS)
    max_retries = 3
    retries = 0
    while retries < max_retries:
        try:
            embedding = model_openai.embed_documents([text])[0]
            return embedding
        except Exception as e:
            retries += 1
            print(f"Error generating embedding for text: {text[:30]}...: {e}")

            # If retries exceed max_retries, log and return None to continue processing
            if retries >= max_retries:
                print("Max retries exceeded for embedding generation. Skipping...")
                return None


def format_vector(vector: List[float]) -> str:
    # Format vector as a PostgreSQL array if needed,
    # or store as a bytea if using binary.
    # pgvector typically accepts python lists directly if using psycopg2 `execute` with parameters.
    # If you face issues, convert to tuple or something. For now, assume direct:
    return vector
