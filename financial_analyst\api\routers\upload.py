"""
Data upload endpoints for file ingestion and processing.
Handles CSV, Excel, and JSON file uploads with validation and processing.
"""

import logging
import uuid
import tempfile
import os
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import JSONResponse

from api.models.upload_models import (
    UploadRequest, UploadResponse, UploadStatusResponse, UploadListResponse,
    DataPreviewResponse, UploadStatus, DataType, FileFormat
)
from api.models.common_models import ResponseStatus, SuccessResponse
from api.services.upload_service import UploadService
from api.dependencies import get_upload_service, require_database
from api.middleware.rate_limiting import limiter
from fastapi import Request

logger = logging.getLogger(__name__)

router = APIRouter()

# File upload constraints
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
ALLOWED_EXTENSIONS = {'.csv', '.xlsx', '.xls', '.json'}
ALLOWED_MIME_TYPES = {
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/json'
}

def validate_file(file: UploadFile) -> None:
    """Validate uploaded file."""
    # Check file size
    if hasattr(file, 'size') and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Check file extension
    if file.filename:
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not supported. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
            )
    
    # Check MIME type
    if file.content_type and file.content_type not in ALLOWED_MIME_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"MIME type not supported: {file.content_type}"
        )

@router.post("/", response_model=UploadResponse, status_code=status.HTTP_201_CREATED)
@limiter.limit("5/minute")
async def upload_file(
    request: Request,
    file: UploadFile = File(..., description="File to upload"),
    data_type: DataType = Form(DataType.FINANCIAL, description="Type of data"),
    table_name: Optional[str] = Form(None, description="Target table name"),
    description: Optional[str] = Form(None, description="Data description"),
    overwrite_existing: bool = Form(False, description="Overwrite existing table"),
    validate_data: bool = Form(True, description="Validate data quality"),
    upload_service: UploadService = Depends(get_upload_service),
    _: bool = Depends(require_database)
):
    """
    Upload a data file for processing and ingestion.
    
    Supports CSV, Excel, and JSON files up to 100MB.
    Files are validated, processed, and inserted into the database.
    
    - **file**: The data file to upload
    - **data_type**: Type of data (financial, general, time_series, custom)
    - **table_name**: Target table name (auto-generated if not provided)
    - **description**: Optional description of the data
    - **overwrite_existing**: Whether to overwrite existing table
    - **validate_data**: Whether to perform data quality validation
    """
    try:
        logger.info(f"Uploading file: {file.filename}, size: {getattr(file, 'size', 'unknown')}")
        
        # Validate file
        validate_file(file)
        
        # Create upload request
        upload_request = UploadRequest(
            data_type=data_type,
            table_name=table_name,
            description=description,
            overwrite_existing=overwrite_existing,
            validate_data=validate_data
        )
        
        # Process upload
        upload_response = await upload_service.process_upload(file, upload_request)
        
        logger.info(f"Upload {upload_response.upload_id} created successfully")
        return upload_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to process upload: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process file upload"
        )

@router.get("/{upload_id}/status", response_model=UploadStatusResponse)
async def get_upload_status(
    upload_id: str,
    upload_service: UploadService = Depends(get_upload_service)
):
    """
    Get the status of a file upload.
    
    Returns detailed information about upload progress, including:
    - Current status (pending, processing, completed, failed)
    - Progress percentage
    - Processing timestamps
    - Error details if applicable
    """
    try:
        status_response = await upload_service.get_upload_status(upload_id)
        return status_response
        
    except ValueError as e:
        logger.warning(f"Invalid upload ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Upload not found"
        )
    except Exception as e:
        logger.error(f"Failed to get upload status for {upload_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve upload status"
        )

@router.get("/", response_model=UploadListResponse)
async def list_uploads(
    status_filter: Optional[UploadStatus] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    upload_service: UploadService = Depends(get_upload_service)
):
    """
    List all file uploads with optional filtering and pagination.
    
    Returns a paginated list of uploads with their current status.
    
    - **status**: Filter uploads by status
    - **page**: Page number for pagination
    - **page_size**: Number of items per page (max 100)
    """
    try:
        logger.info(f"Listing uploads, status filter: {status_filter}, page: {page}")
        
        uploads_response = await upload_service.list_uploads(
            status_filter=status_filter,
            page=page,
            page_size=page_size
        )
        
        logger.info(f"Retrieved {len(uploads_response.uploads)} uploads")
        return uploads_response
        
    except Exception as e:
        logger.error(f"Failed to list uploads: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list uploads"
        )

@router.post("/{upload_id}/cancel", response_model=SuccessResponse)
async def cancel_upload(
    upload_id: str,
    upload_service: UploadService = Depends(get_upload_service)
):
    """
    Cancel a pending or processing upload.
    
    Stops the processing of an upload and marks it as cancelled.
    Only uploads in 'pending' or 'processing' status can be cancelled.
    """
    try:
        logger.info(f"Cancelling upload {upload_id}")
        
        success = await upload_service.cancel_upload(upload_id)
        
        if success:
            logger.info(f"Upload {upload_id} cancelled successfully")
            return SuccessResponse(
                message="Upload cancelled successfully",
                data={"upload_id": upload_id}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Upload cannot be cancelled in current state"
            )
        
    except ValueError as e:
        logger.warning(f"Invalid upload cancellation request: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Upload not found"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel upload {upload_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel upload"
        )

@router.get("/{upload_id}/preview", response_model=DataPreviewResponse)
async def preview_upload_data(
    upload_id: str,
    rows: int = Query(10, ge=1, le=100, description="Number of sample rows"),
    upload_service: UploadService = Depends(get_upload_service)
):
    """
    Preview uploaded data before final processing.
    
    Returns a sample of the uploaded data with column information,
    data types, and basic statistics.
    
    - **upload_id**: ID of the upload to preview
    - **rows**: Number of sample rows to return (max 100)
    """
    try:
        logger.info(f"Previewing data for upload {upload_id}")
        
        preview_response = await upload_service.preview_data(upload_id, rows)
        
        logger.info(f"Generated preview for upload {upload_id}")
        return preview_response
        
    except ValueError as e:
        logger.warning(f"Invalid preview request: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Upload not found or data not available for preview"
        )
    except Exception as e:
        logger.error(f"Failed to preview data for upload {upload_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to preview upload data"
        )

@router.delete("/{upload_id}", response_model=SuccessResponse)
async def delete_upload(
    upload_id: str,
    upload_service: UploadService = Depends(get_upload_service)
):
    """
    Delete an upload record and associated data.
    
    Removes the upload record from the system. If the upload was completed
    and data was inserted into a table, the table data is not affected.
    """
    try:
        logger.info(f"Deleting upload {upload_id}")
        
        success = await upload_service.delete_upload(upload_id)
        
        if success:
            logger.info(f"Upload {upload_id} deleted successfully")
            return SuccessResponse(
                message="Upload deleted successfully",
                data={"upload_id": upload_id}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Upload not found"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete upload {upload_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete upload"
        )
