"""
Text preparation utilities for ideal candidate data to be used in embeddings.
"""

from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


def format_technical_skills(technical_skills: Dict[str, Any]) -> str:
    """Format technical skills section for embedding text."""
    if not technical_skills:
        return ""
    
    sections = []
    
    # Core technologies
    if technical_skills.get("core_technologies"):
        sections.append(f"Core Technologies: {', '.join(technical_skills['core_technologies'])}")
    
    # Frameworks and tools
    if technical_skills.get("frameworks_tools"):
        sections.append(f"Frameworks & Tools: {', '.join(technical_skills['frameworks_tools'])}")
    
    # Programming languages
    if technical_skills.get("programming_languages"):
        sections.append(f"Programming Languages: {', '.join(technical_skills['programming_languages'])}")
    
    # Databases
    if technical_skills.get("databases"):
        sections.append(f"Databases: {', '.join(technical_skills['databases'])}")
    
    # Cloud platforms
    if technical_skills.get("cloud_platforms"):
        sections.append(f"Cloud Platforms: {', '.join(technical_skills['cloud_platforms'])}")
    
    # Other technical skills
    if technical_skills.get("other_technical"):
        sections.append(f"Other Technical Skills: {', '.join(technical_skills['other_technical'])}")
    
    return "\n".join(sections)


def format_professional_experience(experience_list: List[Dict[str, Any]]) -> str:
    """Format professional experience section for embedding text."""
    if not experience_list:
        return ""
    
    experience_sections = []
    
    for exp in experience_list:
        exp_parts = []
        
        if exp.get("role"):
            exp_parts.append(f"Role: {exp['role']}")
        
        if exp.get("company_type"):
            exp_parts.append(f"Company Type: {exp['company_type']}")
        
        if exp.get("duration"):
            exp_parts.append(f"Duration: {exp['duration']}")
        
        if exp.get("key_achievements"):
            achievements = "; ".join(exp["key_achievements"])
            exp_parts.append(f"Key Achievements: {achievements}")
        
        if exp.get("technologies_used"):
            technologies = ", ".join(exp["technologies_used"])
            exp_parts.append(f"Technologies Used: {technologies}")
        
        if exp_parts:
            experience_sections.append(" | ".join(exp_parts))
    
    return "\n".join(experience_sections)


def format_education(education: Dict[str, Any]) -> str:
    """Format education section for embedding text."""
    if not education:
        return ""
    
    edu_parts = []
    
    if education.get("degree"):
        edu_parts.append(f"Degree: {education['degree']}")
    
    if education.get("additional_certifications"):
        certs = ", ".join(education["additional_certifications"])
        edu_parts.append(f"Certifications: {certs}")
    
    if education.get("continuous_learning"):
        learning = ", ".join(education["continuous_learning"])
        edu_parts.append(f"Continuous Learning: {learning}")
    
    return " | ".join(edu_parts)


def format_industry_experience(industry_exp: Dict[str, Any]) -> str:
    """Format industry experience section for embedding text."""
    if not industry_exp:
        return ""
    
    industry_parts = []
    
    if industry_exp.get("domains"):
        domains = ", ".join(industry_exp["domains"])
        industry_parts.append(f"Industry Domains: {domains}")
    
    if industry_exp.get("company_sizes"):
        sizes = ", ".join(industry_exp["company_sizes"])
        industry_parts.append(f"Company Sizes: {sizes}")
    
    if industry_exp.get("project_types"):
        projects = ", ".join(industry_exp["project_types"])
        industry_parts.append(f"Project Types: {projects}")
    
    return " | ".join(industry_parts)


def format_leadership_management(leadership: Dict[str, Any]) -> str:
    """Format leadership and management section for embedding text."""
    if not leadership:
        return ""
    
    leadership_parts = []
    
    if leadership.get("team_leadership"):
        leadership_parts.append(f"Team Leadership: {leadership['team_leadership']}")
    
    if leadership.get("project_management"):
        leadership_parts.append(f"Project Management: {leadership['project_management']}")
    
    if leadership.get("mentoring"):
        leadership_parts.append(f"Mentoring: {leadership['mentoring']}")
    
    return " | ".join(leadership_parts)


def format_cultural_fit(cultural_fit: Dict[str, Any]) -> str:
    """Format cultural fit section for embedding text."""
    if not cultural_fit:
        return ""
    
    cultural_parts = []
    
    if cultural_fit.get("work_style"):
        cultural_parts.append(f"Work Style: {cultural_fit['work_style']}")
    
    if cultural_fit.get("values_alignment"):
        cultural_parts.append(f"Values: {cultural_fit['values_alignment']}")
    
    if cultural_fit.get("collaboration_style"):
        cultural_parts.append(f"Collaboration: {cultural_fit['collaboration_style']}")
    
    return " | ".join(cultural_parts)


def format_additional_qualifications(additional: Dict[str, Any]) -> str:
    """Format additional qualifications section for embedding text."""
    if not additional:
        return ""
    
    additional_parts = []
    
    if additional.get("languages"):
        languages = ", ".join(additional["languages"])
        additional_parts.append(f"Languages: {languages}")
    
    if additional.get("publications"):
        publications = ", ".join(additional["publications"])
        additional_parts.append(f"Publications: {publications}")
    
    if additional.get("speaking_conferences"):
        conferences = ", ".join(additional["speaking_conferences"])
        additional_parts.append(f"Conference Speaking: {conferences}")
    
    if additional.get("open_source_contributions"):
        oss = ", ".join(additional["open_source_contributions"])
        additional_parts.append(f"Open Source: {oss}")
    
    return " | ".join(additional_parts)


def prepare_ideal_candidate_for_embedding(ideal_candidate_info: Dict[str, Any]) -> str:
    """
    Converts ideal candidate information into a formatted text suitable for embedding generation.
    
    Args:
        ideal_candidate_info: Dictionary containing the ideal candidate profile data
        
    Returns:
        Formatted text string optimized for embedding generation
    """
    try:
        sections = []
        
        # Personal/Professional Info
        personal_info = ideal_candidate_info.get("personal_info", {})
        if personal_info:
            personal_parts = []
            
            if personal_info.get("professional_title"):
                personal_parts.append(f"Professional Title: {personal_info['professional_title']}")
            
            if personal_info.get("years_of_experience"):
                personal_parts.append(f"Experience: {personal_info['years_of_experience']}")
            
            if personal_info.get("location_preference"):
                personal_parts.append(f"Location: {personal_info['location_preference']}")
            
            if personal_info.get("summary"):
                personal_parts.append(f"Summary: {personal_info['summary']}")
            
            if personal_parts:
                sections.append("PROFESSIONAL PROFILE:\n" + "\n".join(personal_parts))
        
        # Technical Skills
        technical_skills = ideal_candidate_info.get("technical_skills", {})
        if technical_skills:
            tech_text = format_technical_skills(technical_skills)
            if tech_text:
                sections.append("TECHNICAL SKILLS:\n" + tech_text)
        
        # Professional Experience
        experience = ideal_candidate_info.get("professional_experience", [])
        if experience:
            exp_text = format_professional_experience(experience)
            if exp_text:
                sections.append("PROFESSIONAL EXPERIENCE:\n" + exp_text)
        
        # Education
        education = ideal_candidate_info.get("education", {})
        if education:
            edu_text = format_education(education)
            if edu_text:
                sections.append("EDUCATION:\n" + edu_text)
        
        # Soft Skills
        soft_skills = ideal_candidate_info.get("soft_skills", [])
        if soft_skills:
            soft_skills_text = ", ".join(soft_skills)
            sections.append("SOFT SKILLS:\n" + soft_skills_text)
        
        # Industry Experience
        industry_exp = ideal_candidate_info.get("industry_experience", {})
        if industry_exp:
            industry_text = format_industry_experience(industry_exp)
            if industry_text:
                sections.append("INDUSTRY EXPERIENCE:\n" + industry_text)
        
        # Leadership & Management
        leadership = ideal_candidate_info.get("leadership_management", {})
        if leadership:
            leadership_text = format_leadership_management(leadership)
            if leadership_text:
                sections.append("LEADERSHIP & MANAGEMENT:\n" + leadership_text)
        
        # Cultural Fit
        cultural_fit = ideal_candidate_info.get("cultural_fit", {})
        if cultural_fit:
            cultural_text = format_cultural_fit(cultural_fit)
            if cultural_text:
                sections.append("CULTURAL FIT:\n" + cultural_text)
        
        # Additional Qualifications
        additional = ideal_candidate_info.get("additional_qualifications", {})
        if additional:
            additional_text = format_additional_qualifications(additional)
            if additional_text:
                sections.append("ADDITIONAL QUALIFICATIONS:\n" + additional_text)
        
        # Combine all sections
        formatted_text = "\n\n".join(sections)
        
        if not formatted_text.strip():
            logger.warning("Generated empty text for ideal candidate embedding")
            return "No ideal candidate information available"
        
        return formatted_text.strip()
        
    except Exception as e:
        logger.error(f"Error preparing ideal candidate for embedding: {e}")
        return f"Error processing ideal candidate data: {str(e)}"


def validate_ideal_candidate_structure(ideal_candidate_info: Dict[str, Any]) -> bool:
    """
    Validate that the ideal candidate info has the expected structure.
    
    Args:
        ideal_candidate_info: Dictionary to validate
        
    Returns:
        True if structure is valid, False otherwise
    """
    try:
        # Check for required top-level keys
        expected_keys = [
            "personal_info",
            "technical_skills", 
            "professional_experience",
            "education",
            "soft_skills"
        ]
        
        for key in expected_keys:
            if key not in ideal_candidate_info:
                logger.warning(f"Missing required key in ideal candidate info: {key}")
                return False
        
        # Validate personal_info structure
        personal_info = ideal_candidate_info.get("personal_info", {})
        if not isinstance(personal_info, dict):
            logger.warning("personal_info should be a dictionary")
            return False
        
        # Validate technical_skills structure
        technical_skills = ideal_candidate_info.get("technical_skills", {})
        if not isinstance(technical_skills, dict):
            logger.warning("technical_skills should be a dictionary")
            return False
        
        # Validate professional_experience structure
        experience = ideal_candidate_info.get("professional_experience", [])
        if not isinstance(experience, list):
            logger.warning("professional_experience should be a list")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating ideal candidate structure: {e}")
        return False
