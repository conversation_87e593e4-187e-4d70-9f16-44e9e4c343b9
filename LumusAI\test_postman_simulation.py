#!/usr/bin/env python3
"""
Simulate a Postman request to demonstrate that API responses contain real data
while Langfuse traces contain masked data.
"""

import requests
import json
import time
import tempfile
import os

def simulate_postman_request():
    """Simulate what a client would see when calling the API via Postman"""
    print("📮 Postman API Response Simulation")
    print("=" * 60)
    print("This simulates what you would see in Postman when calling the API")
    print("=" * 60)
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health")
        if health_response.status_code != 200:
            print("❌ Service not healthy")
            return
        print("✅ Service is healthy")
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    # Create a test CV with sensitive data (same as user's example)
    cv_content = """Santiago <PERSON> Martínez
Senior Software Developer

Contact Information:
Email: <EMAIL>
Phone: (57) **********
LinkedIn: https://www.linkedin.com/in/santiago-garcia-m/
Location: Cali, Valle Del Cauca, Colombia

Professional Experience:
- Senior Developer at TechCorp SA (2022-2024)
  * Led development team
  * Contact: <EMAIL>

- Software Engineer at StartupXYZ LTDA (2020-2022)
  * Full-stack development

Education:
- Master's in Computer Science
  Universidad Nacional de Colombia (2018-2020)
  Student Email: <EMAIL>

Skills: Python, JavaScript, React, Django"""

    print(f"\n📤 Sending CV with sensitive data to API...")
    print(f"   (Same data that was showing as masked before)")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(cv_content)
        temp_file_path = f.name
    
    try:
        print(f"\n🔍 Making POST request to /process-test...")
        print(f"   (This is what Postman would do)")
        
        with open(temp_file_path, 'rb') as file:
            files = {'file': ('cv.txt', file, 'text/plain')}
            data = {
                'action': 'cv',
                'callback_url': 'http://httpbin.org/post'
            }
            
            response = requests.post(
                "http://localhost:8000/process-test",
                files=files,
                data=data
            )
        
        print(f"\n📋 API RESPONSE (what Postman shows):")
        print(f"=" * 50)
        print(f"Status Code: {response.status_code}")
        print(f"Response Body:")
        
        if response.status_code == 200:
            response_data = response.json()
            print(json.dumps(response_data, indent=2))
            
            task_id = response_data.get('task_id', 'unknown')
            
            print(f"\n✅ VERIFICATION:")
            print(f"   📊 Task ID: {task_id}")
            print(f"   📊 Status: {response_data.get('status', 'unknown')}")
            print(f"   📊 Timestamp: {response_data.get('timestamp', 'unknown')}")
            print(f"   ✅ All data is REAL (not masked) - this is CORRECT!")
            
            print(f"\n🔒 Meanwhile, in Langfuse traces:")
            print(f"   🌐 Dashboard: http://**************:3000")
            print(f"   🔍 Task: {task_id}")
            print(f"   🔒 Sensitive data should be masked there")
            
            print(f"\n🎯 CONCLUSION:")
            print(f"   ✅ API responses contain REAL data (for functionality)")
            print(f"   🔒 Langfuse traces contain MASKED data (for privacy)")
            print(f"   🎉 Problem SOLVED!")
            
        else:
            print(f"❌ Error Response:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    simulate_postman_request()
