from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class TutelaContestacionDocument(BaseModel):
    termino: str = Field(default="", description="El término declarado por el juzgado (en horas o días) para responder o cumplir un requerimiento. Si no se encuentra, usar cadena vacía ('').")
    juzgado: str = Field(default="", description="El juzgado que presenta el documento legal. Si no se encuentra, usar cadena vacía ('').")
    radicado: str = Field(default="", description="El número de proceso o código del documento. Retira guiones y espacios. Ejemplo: '1000 1234-2024' → '100012342024'. usar cadena vacía ('').")
    accionante: str = Field(default="", description="La persona o empresa que solicita la acción de tutela. Elimina referencias como CC o NIT si aparecen. Si no se encuentra, usar cadena vacía ('').")
    fecha_documento: str = Field(default="", description="Fecha en que se emitió el documento, en formato 'dd/mm/aaaa'. Si no se encuentra, usar cadena vacía ('').")
    accionado: List[str] = Field(default_factory=list, description="Lista de personas o empresas señaladas por el accionante. Si no se encuentra ninguna, devolver lista vacía ([]).")
    correo: List[str] = Field(default_factory=list, description="Lista de correos electrónicos presentes en el documento. Elimina duplicados. Si no hay correos, usar lista vacía ([]).")