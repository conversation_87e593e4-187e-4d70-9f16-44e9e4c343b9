# ❓ Frequently Asked Questions (FAQ)

Common questions and answers about the Financial Analysis Project.

## 🚀 Getting Started

### Q: What is the Financial Analysis Project?
**A:** It's an AI-powered financial analysis tool that processes Excel files to calculate margins, identify trends, and provide comprehensive financial insights using LangChain pandas agents.

### Q: Do I need programming knowledge to use this?
**A:** No! The Streamlit frontend provides a user-friendly interface. Just upload your Excel file and view the results. Programming knowledge is only needed for development or customization.

### Q: Is this free to use?
**A:** Yes, the software is free. You only need a free GROQ API key for the AI analysis features.

### Q: What file formats are supported?
**A:** 
- Excel files: `.xlsx`, `.xls`
- CSV files: `.csv`
- Maximum file size: 50MB

## 🔧 Setup and Installation

### Q: What are the system requirements?
**A:** 
- Python 3.8 or higher
- 4GB RAM minimum (8GB recommended)
- 2GB free disk space
- Internet connection (for AI API calls)

### Q: How do I get a GROQ API key?
**A:** 
1. Visit https://console.groq.com/
2. Sign up for a free account
3. Navigate to API Keys section
4. Generate a new API key
5. Copy it to your `.env` file

### Q: Can I run this without an API key?
**A:** No, the AI analysis features require a GROQ API key. However, you can use the data processing features without AI analysis.

### Q: Why do I need an internet connection?
**A:** The AI agents make API calls to GROQ's language models for intelligent analysis. Local processing is planned for future versions.

## 📊 Data and Analysis

### Q: What columns does my Excel file need?
**A:** **Required columns** (flexible naming):
- **Supplier**: supplier, vendor, proveedor, supplier_name
- **Voucher**: voucher, invoice, factura, comprobante
- **Amount**: gross_amount, amount, total, importe

**Optional columns**:
- **Cost**: cost, cost_amount, costo
- **Date**: date, transaction_date, fecha

### Q: What if my file has different column names?
**A:** The system automatically maps common variations. For example:
- "Vendor Name" → supplier
- "Invoice ID" → voucher  
- "Total Amount" → gross_amount

### Q: What happens if cost data is missing?
**A:** The system assumes cost as a percentage of gross amount (default: 70%). You can adjust this assumption in the configuration.

### Q: How are margins calculated?
**A:** 
```
Margin % = ((Gross Amount - Cost) / Gross Amount) × 100
```
When cost is missing: `Assumed Cost = Gross Amount × Cost Assumption %`

### Q: What languages are supported for column names?
**A:** Currently supports English and Spanish column names. More languages can be added easily.

## 🔍 Analysis Features

### Q: What analysis does the system provide?
**A:** 
- Total gross amounts per supplier and voucher
- Profit margin calculations
- Low-margin and negative-margin transaction identification
- Month-over-month profitability trends
- Supplier performance analysis
- Risk assessment and recommendations

### Q: How long does analysis take?
**A:** Typically 1-3 minutes depending on:
- File size (more records = longer processing)
- Internet connection speed
- System resources

### Q: Can I analyze multiple files at once?
**A:** Yes! Use the batch analysis endpoint or upload multiple files through the API.

### Q: What's considered a "low margin" transaction?
**A:** By default, transactions with margins below 10% are flagged as low margin. This threshold is configurable.

## 🖥️ Technical Questions

### Q: Can I use this in production?
**A:** The current version is suitable for analysis and reporting. For production use, consider:
- Adding authentication
- Implementing data persistence
- Setting up monitoring
- Scaling infrastructure

### Q: How do I integrate this with my existing systems?
**A:** Use the REST API endpoints:
```bash
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@your_data.xlsx"
```

### Q: Can I customize the analysis?
**A:** Yes! You can:
- Adjust cost assumption percentages
- Change margin thresholds
- Modify the LangChain agents
- Add new analysis features

### Q: Is my data secure?
**A:** 
- No data is stored permanently
- Files are processed in memory only
- API calls to GROQ are encrypted
- Consider running locally for sensitive data

## 🐛 Troubleshooting

### Q: The backend won't start - "Port 8000 already in use"
**A:** 
```bash
# Kill existing process
lsof -ti:8000 | xargs kill -9

# Or use different port
uvicorn app.main:app --port 8001
```

### Q: "Cannot connect to backend API" error
**A:** 
1. Ensure backend is running: `curl http://localhost:8000/health`
2. Check firewall settings
3. Verify port configuration

### Q: Analysis fails with "API key invalid"
**A:** 
1. Check your GROQ API key in `backend/.env`
2. Ensure no extra spaces or quotes
3. Verify the key is active at https://console.groq.com/

### Q: File upload fails - "Unsupported file type"
**A:** 
- Ensure file extension is .xlsx, .xls, or .csv
- Check file isn't password protected
- Verify file isn't corrupted

### Q: "Missing required columns" error
**A:** 
1. Check your column names match expected patterns
2. Ensure data starts from row 1 with headers
3. Remove merged cells and summary rows

## 💡 Best Practices

### Q: How should I prepare my data?
**A:** 
- Use clear, descriptive column headers
- Remove summary rows and totals
- Ensure numeric columns contain only numbers
- Use consistent date formats
- Remove empty rows and columns

### Q: What's the optimal file size?
**A:** 
- Under 10MB: Fast processing (< 1 minute)
- 10-25MB: Moderate processing (1-2 minutes)
- 25-50MB: Slower processing (2-5 minutes)
- Over 50MB: Consider splitting the file

### Q: How can I improve analysis performance?
**A:** 
- Remove unnecessary columns
- Filter to relevant date ranges
- Use CSV format instead of Excel
- Close other applications to free memory

## 🔮 Future Features

### Q: What features are planned?
**A:** 
- Predictive analytics and forecasting
- Real-time data integration
- Advanced visualization options
- Multi-currency support
- Custom report templates
- Database integration

### Q: Can I contribute to the project?
**A:** Yes! Check the [Contributing Guidelines](contributing.md) for information on:
- Reporting bugs
- Suggesting features
- Submitting code changes
- Improving documentation

### Q: Will there be a cloud version?
**A:** A hosted cloud version is under consideration. Currently, you can deploy to your own cloud infrastructure.

## 📞 Support

### Q: Where can I get help?
**A:** 
1. Check this FAQ
2. Review the [Troubleshooting Guide](troubleshooting.md)
3. Read the [Documentation](README.md)
4. Create an issue in the project repository

### Q: How do I report bugs?
**A:** Create an issue with:
- Detailed description of the problem
- Steps to reproduce
- Error messages
- System information (OS, Python version)
- Sample data (if possible)

### Q: Can I request new features?
**A:** Yes! Create a feature request issue with:
- Clear description of the feature
- Use case and benefits
- Examples of how it would work

## 📚 Learning Resources

### Q: Where can I learn more about the technology used?
**A:** 
- **LangChain**: https://langchain.readthedocs.io/
- **FastAPI**: https://fastapi.tiangolo.com/
- **Streamlit**: https://streamlit.io/
- **Pandas**: https://pandas.pydata.org/
- **GROQ**: https://groq.com/

### Q: Are there video tutorials available?
**A:** Currently, documentation is text-based. Video tutorials may be added in the future.

### Q: Can I use this for learning purposes?
**A:** Absolutely! The project demonstrates:
- AI agent development with LangChain
- Modern web API development with FastAPI
- Data visualization with Streamlit
- Financial data analysis techniques

---

**Still have questions?** Check the [Troubleshooting Guide](troubleshooting.md) or create an issue in the project repository.
