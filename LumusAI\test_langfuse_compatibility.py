#!/usr/bin/env python3
"""
Test script to verify Langfuse compatibility without mask parameter.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

def test_basic_langfuse():
    """Test basic Langfuse functionality without masking complications."""
    
    print("🔧 Testing Basic Langfuse Compatibility")
    print("=" * 60)
    
    # Simple test data
    test_data = """
    Test User
    Software Developer
    
    Email: <EMAIL>
    Phone: ************
    
    Experience:
    - Developer at TestCorp (2020-2024)
    
    Skills: Python, JavaScript
    """
    
    print("📞 Testing /process endpoint...")
    print(f"📊 Data length: {len(test_data)} characters")
    
    try:
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/process",
            data={
                "action": "cv",
                "data": test_data
            },
            timeout=30
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📋 Response Status: {response.status_code}")
        print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            
            # Check if we got structured data
            if isinstance(result, dict):
                print("📄 Structured data received")
                
                if 'token_usage' in result:
                    tokens = result['token_usage']
                    print(f"🎯 Tokens used: {tokens.get('total_tokens', 'N/A')}")
                    print(f"💰 Cost: ${tokens.get('cost', 0):.4f}")
                
                print(f"\n📊 Response size: {len(json.dumps(result))} characters")
            
            return True
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🚀 Testing Langfuse Compatibility")
    print("=" * 50)
    
    # Check service health
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Service not healthy: {health_response.status_code}")
            return
        print("✅ Service is running and healthy")
    except requests.exceptions.RequestException as e:
        print(f"❌ Service not reachable: {e}")
        return
    
    # Run the test
    success = test_basic_langfuse()
    
    print("\n" + "=" * 50)
    print("🎯 Summary")
    print("=" * 50)
    
    if success:
        print("✅ Test completed successfully!")
        print("\n📋 What to check now:")
        print("1. Application startup logs should show:")
        print("   - '✅ Langfuse client initialized successfully'")
        print("   - '✅ STARTUP DEBUG: LangChain client has Langfuse handler'")
        print("   - '🔒 Langfuse masking enabled' (if masking is on)")
        
        print("\n2. Processing logs should show:")
        print("   - '🔍 _get_callbacks: Added Langfuse handler, total callbacks: 1'")
        print("   - '🔍 get_structured_data: Using config with callbacks for LLM call'")
        print("   - '🔍 structured_model.ainvoke completed successfully'")
        
        print("\n3. Langfuse dashboard should show new traces:")
        print("   - URL: http://157.230.167.30:3000")
        print("   - Look for traces with current timestamp")
        print("   - Should show CV processing operations")
        
        print("\n4. Next steps:")
        print("   - If traces appear, Langfuse is working correctly")
        print("   - We can then work on implementing masking properly")
        
    else:
        print("❌ Test failed")
        print("\n🔧 Troubleshooting:")
        print("1. Check application startup logs for errors")
        print("2. Verify Langfuse is installed correctly")
        print("3. Check network connectivity to Langfuse server")
        print("4. Verify environment variables are set")
    
    print("\n🔍 Expected behavior:")
    print("- No 'unexpected keyword argument mask' errors")
    print("- Langfuse handler should be created successfully")
    print("- Traces should appear in Langfuse dashboard")
    print("- Processing should work normally")

if __name__ == "__main__":
    main()
